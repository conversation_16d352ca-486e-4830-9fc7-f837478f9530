<template>
  <el-config-provider :locale="locale">
    <router-view v-show="getThemeConfig.lockScreenTime !== 0" />
    <LockScreen v-if="getThemeConfig.isLockScreen" />
    <Setings ref="setingsRef" v-show="getThemeConfig.lockScreenTime !== 0" />
    <CloseFull />
    <GlobalToolbox />
  </el-config-provider>
</template>

<script lang="ts">
import {
  computed,
  ref,
  getCurrentInstance,
  onBeforeMount,
  onMounted,
  onUnmounted,
  nextTick,
  defineComponent,
  watch
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store/index'
import { useTitle } from '/@/utils/setWebTitle'
import { Local } from '/@/utils/storage'
import setIntroduction from '/@/utils/setIconfont'
import LockScreen from '/@/layout/lockScreen/index.vue'
import Setings from '/@/layout/navBars/breadcrumb/setings.vue'
import CloseFull from '/@/layout/navBars/breadcrumb/closeFull.vue'
import GlobalToolbox from '/@/components/GlobalToolbox/index.vue'

import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { ElConfigProvider } from 'element-plus'

export default defineComponent({
  name: 'app',
  components: {
    LockScreen,
    Setings,
    CloseFull,
    ElConfigProvider,
    GlobalToolbox
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const setingsRef = ref()
    const route = useRoute()
    const store = useStore()
    const title = useTitle()
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 布局配置弹窗打开
    const openSetingsDrawer = () => {
      setingsRef.value.openDrawer()
    }
    // 设置初始化，防止刷新时恢复默认
    onBeforeMount(() => {
      // 设置批量第三方 icon 图标
      setIntroduction.cssCdn()
      // 设置批量第三方 js
      setIntroduction.jsCdn()
    })
    // 页面加载时
    onMounted(() => {
      nextTick(() => {
        // 监听布局配置弹窗点击打开
        proxy.mittBus.on('openSetingsDrawer', () => {
          openSetingsDrawer()
        })
        // 获取缓存中的布局配置
        if (Local.get('themeConfig')) {
          store.dispatch('themeConfig/setThemeConfig', Local.get('themeConfig'))
          document.documentElement.style.cssText = Local.get('adminThemeConfigStyle')
        }
        // 添加全局事件监听
        document.addEventListener('copy', handleCopy)
        document.addEventListener('cut', handleCopy)
        document.addEventListener('paste', handleClipboardChange)
      })
    })
    // 页面销毁时，关闭监听布局配置
    onUnmounted(() => {
      proxy.mittBus.off('openSetingsDrawer', () => {})
      // 移除全局事件监听
      document.removeEventListener('copy', handleCopy)
      document.removeEventListener('cut', handleCopy)
      document.removeEventListener('paste', handleClipboardChange)
    })
    // 监听路由的变化，设置网站标题
    watch(
      () => route.path,
      () => {
        title()
      }
    )

    // 监听复制事件
    const handleCopy = () => {
      // 延迟一点执行，确保复制操作完成
      setTimeout(() => {
        store.dispatch('clipboard/handleCopy')
      }, 100)
    }

    // 监听剪贴板
    const handleClipboardChange = (e: ClipboardEvent) => {
      const clipboardData = e.clipboardData
      if (!clipboardData) return

      const text = clipboardData.getData('text')
      if (text) {
        store.commit('clipboard/addItem', {
          type: 'text',
          content: text,
          timestamp: Date.now()
        })
      }
    }

    return {
      setingsRef,
      getThemeConfig,
      locale: zhCn
    }
  }
})
</script>
