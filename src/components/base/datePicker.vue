<!-- 时间(包括创建、发布、刷新等等) -->
<template>
  <el-date-picker
    :value-format="valueFormat"
    :disabledDate="disabledDate"
    style="width: 100%"
    :placeholder="placeholder"
    :type="type"
    :size="size"
    clearable
    :editable="false"
    @focus="handleClick"
    ref="datePickerRef"
  >
  </el-date-picker>
</template>
<script lang="ts" setup>
import { ref } from 'vue'

defineProps({
  type: {
    type: String,
    default() {
      return 'date'
    }
  },
  placeholder: {
    type: String,
    default() {
      return ''
    }
  },
  format: {
    type: String,
    default() {
      return 'YYYY 年 MM 月 DD 日'
    }
  },
  valueFormat: {
    type: String,
    default() {
      return 'YYYY-MM-DD'
    }
  },
  disabledDate: {
    type: Function,
    default() {
      return false
    }
  },
  size: {
    type: String,
    default() {
      return 'default'
    }
  }
})

const datePickerRef = ref()

const handleClick = () => {
  datePickerRef.value.handleOpen()
}
</script>
