<!-- 单个checkbox，绑定值已处理成字符串 -->
<template>
  <el-checkbox v-model="value" :true-label="trueLabel" :false-label="falseLabel">{{
    label
  }}</el-checkbox>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'checkBox',

  props: {
    label: {
      type: String,
      default: () => ''
    },
    trueLabel: {
      type: String,
      default: () => '1'
    },
    falseLabel: {
      type: String,
      default: () => ''
    }
  },

  setup() {
    const state = reactive({
      value: ''
    })

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
