<template>
  <div class="ai-end">
    <el-tag
      size="small"
      effect="plain"
      :key="tag"
      v-for="tag in dynamicTags"
      closable
      :disable-transitions="false"
      @close="handleDelete(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      size="small"
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      ref="addTagInput"
      @keyup.enter="handleInputConfirm"
      placeholder="最长12字"
      :maxlength="12"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button size="small" v-else class="button-new-tag" @click="showInput">+ 标签</el-button>
  </div>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs, nextTick, ref, watch } from 'vue'

export default {
  name: 'Tag',
  emits: ['change', 'delete', 'add'],
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    other: {
      type: [Object, Array],
      default() {
        return {}
      }
    }
  },
  setup(props, ctx) {
    const state = reactive({
      dynamicTags: [] as Array<string>,
      inputVisible: false,
      inputValue: ''
    })

    watch(
      () => (props as any).data,
      (val) => {
        state.dynamicTags = val
      }
    )
    const change = () => {
      ctx.emit('change', state.dynamicTags, (props as any).other)
    }

    onMounted(() => {
      state.dynamicTags = (props as any).data
    })
    const handleDelete = (tag: any) => {
      state.dynamicTags.splice(state.dynamicTags.indexOf(tag), 1)
      ctx.emit('delete', tag, (props as any).other)
      change()
    }

    const addTagInput = ref()
    const showInput = () => {
      state.inputVisible = true
      nextTick(() => {
        addTagInput.value.input.focus()
      })
    }

    const handleInputConfirm = () => {
      const addTag = state.inputValue
      if (addTag) {
        state.dynamicTags.push(addTag)
        ctx.emit('add', addTag, (props as any).other)
        change()
      }
      state.inputVisible = false
      state.inputValue = ''
    }

    return {
      addTagInput,
      handleDelete,
      handleInputConfirm,
      showInput,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tag {
  margin-left: 10px;
  border-radius: 20px;
  &:first-child {
    margin-left: 0;
  }
}
.button-new-tag {
  border-radius: 20px;
  margin-left: 10px;
  padding-top: 0;
  padding-bottom: 0;
  height: 20px;
  line-height: 20px;
  min-height: auto;
}
.input-new-tag {
  height: 20px;
  width: 90px;
  line-height: 20px;
  min-height: auto;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
