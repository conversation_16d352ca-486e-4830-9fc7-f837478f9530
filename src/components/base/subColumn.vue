<template>
  <!-- 所属副栏目 -->
  <div class="full-width">
    <el-cascader
      class="full-width"
      :show-all-levels="false"
      collapse-tags
      v-model="subColumnIds"
      :options="columnList"
      :props="{
        label: 'v',
        value: 'k',
        emitPath: false,
        multiple: true,
        checkStrictly: true
      }"
      filterable
      clearable
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'subColumn',

  props: {
    columnList: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: String,
      default: () => ''
    }
  },
  setup(prop, { emit }) {
    const state = reactive({
      subColumnIds: computed({
        get() {
          const value = prop.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val.join())
        }
      })
    })

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped>
:deep(.full-width) {
  width: 100%;
}
.full-width {
  width: 100%;
}
</style>
