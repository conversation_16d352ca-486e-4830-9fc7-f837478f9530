<template>
  <!-- 所属行业组件 -->
  <div>
    <el-cascader
      :options="industryList"
      @change="handleChange"
      :props="{ value: 'k', label: 'v', multiple: multiple, emitPath: false }"
      placeholder="请选择所属行业"
      :teleported="false"
      :show-all-levels="false"
      v-model="data"
      :filterable="fliter"
      collapse-tags
      style="width: 100%"
      clearable
    ></el-cascader>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue'
import { getUnitList } from '/@/api/unitManage'

export default defineComponent({
  name: 'industryComponent',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      default: () => '' || []
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    fliter: {
      type: Boolean,
      default: () => true
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      industryList: [],
      data: '',
      propsValue: { value: 'k', label: 'v' }
    })
    const getIndustryList = async () => {
      const res = await getUnitList()
      state.industryList = res.companyIndustryList
    }
    const handleChange = (val: any) => {
      emit('update:modelValue', val)
    }

    watch(
      () => (props as any).modelValue,
      (value: any) => {
        state.data = value
      },
      { immediate: true }
    )
    onMounted(() => {
      getIndustryList()
    })

    return { ...toRefs(state), handleChange }
  }
})
</script>

<style lang="scss" scoped></style>
