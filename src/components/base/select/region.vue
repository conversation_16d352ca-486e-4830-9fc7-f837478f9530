<template>
  <el-cascader
    class="w100"
    :disabled="disabled"
    :options="list"
    :props="{
      value: 'k',
      label: 'v',
      multiple: multiple,
      emitPath: !multiple,
      checkStrictly: checkStrictly
    }"
    :collapse-tags="collapseTags"
    separator="-"
    :placeholder="placeholder"
    :teleported="false"
    :show-all-levels="showAllLevels"
    @change="handleChange"
    v-model="value"
    clearable
    :filterable="filterable"
  ></el-cascader>
</template>
<script lang="ts">
import { onMounted, toRefs, reactive, watch } from 'vue'
import { useStore } from '/@/store/index'
import { getSecondAreaList, getFullAreaList } from '/@/api/config'

export default {
  name: 'region',
  props: {
    multiple: {
      type: Boolean,
      default: () => false
    },
    collapseTags: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    filterable: {
      type: Boolean,
      default: () => true
    },
    deep: {
      type: Number,
      default: () => 2
    },
    placeholder: {
      type: String,
      default: () => '请选择地区'
    },
    showAllLevels: {
      type: Boolean,
      default: () => true
    },
    checkStrictly: {
      type: Boolean,
      default: () => false
    },
    modelValue: {
      type: Array,
      default: () => []
    },
    province: {
      type: [String, Number],
      default: () => ''
    },
    city: {
      type: [String, Number],
      default: () => ''
    },
    district: {
      type: [String, Number],
      default: () => ''
    }
  },
  emits: ['change', 'update:modelValue', 'update:province', 'update:city', 'update:district'],
  setup(props, ctx) {
    const store = useStore()
    const { selectList } = store.state.selectList

    const state = reactive({
      value: <any>[],
      list: []
    })
    const getSecondList = () => {
      if (selectList.regionSecondList.length > 0) {
        state.list = <any>selectList.regionSecondList
        return
      }
      getSecondAreaList().then((resp: any) => {
        state.list = resp
        selectList.regionSecondList = resp
      })
    }

    const getFullList = () => {
      if (selectList.regionFullList.length > 0) {
        state.list = <any>selectList.regionFullList
        return
      }
      getFullAreaList().then((resp: any) => {
        state.list = resp
        selectList.regionFullList = resp
      })
    }

    watch(
      () => (props as any).modelValue,
      (val: any) => {
        if (val instanceof Array) {
          state.value = val.map((item: any) => {
            return String(item)
          })
        } else {
          state.value = val
        }
      },
      { deep: true, immediate: true }
    )
    watch(
      () => (props as any).province,
      (val: String) => {
        if (val) return
        ctx.emit('update:modelValue', [])
        state.value = []
      }
    )
    watch(
      () => (props as any).city,
      (val: String) => {
        if (val) return
        ctx.emit('update:modelValue', [])
        state.value = []
      }
    )
    watch(
      () => (props as any).district,
      (val: String) => {
        if (val) return
        ctx.emit('update:modelValue', [])
        state.value = []
      }
    )

    onMounted(() => {
      if (props.deep === 2) {
        getSecondList()
      } else {
        getFullList()
      }
    })

    const handleChange = (val: any) => {
      if (!val || val === null) {
        // 清空的时候将值置空
        const data = {
          region: '',
          text: '',
          province: '',
          city: '',
          district: ''
        }
        ctx.emit('change', data)
        ctx.emit('update:modelValue', '')
        ctx.emit('update:province', '')
        ctx.emit('update:city', '')
        ctx.emit('update:district', '')
        return
      }
      if (props.multiple) {
        ctx.emit('update:modelValue', val)
        return
      }
      let text = ''
      const province = <any>state.list.filter((item) => (item as any).k === val[0])[0]
      text += province.v
      const city = (province as any).children.filter((item: any) => item.k === val[1])[0]
      text += city.v
      let district = <any>''
      if (props.deep === 3) {
        district = <any>city.children.filter((item: any) => item.k === val[2])[0]
        text += district.v
      }
      const data = {
        region: val,
        text,
        province,
        city,
        district
      }
      ctx.emit('change', data)
      ctx.emit('update:modelValue', val)
      ctx.emit('update:province', val[0])
      ctx.emit('update:city', val[1])
      if (props.deep === 3) {
        ctx.emit('update:district', val[2])
      }
    }

    return {
      handleChange,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped></style>
