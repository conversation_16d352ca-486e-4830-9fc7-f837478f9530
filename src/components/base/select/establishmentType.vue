<!-- 编制类型 -->
<template>
  <el-select multiple class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getEstablishmentTypeList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'establishmentTypeSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '不限'
      }
    }
  },
  setup(props: any) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.establishmentTypeList.length > 0) {
        state.list = selectList.establishmentTypeList
      } else {
        await getEstablishmentTypeList().then((resp: any) => {
          const beforeArr = [
            {
              k: '-1',
              v: '不含编制'
            }
          ]
          state.list = beforeArr.concat(resp)
          selectList.establishmentTypeList = state.list
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
