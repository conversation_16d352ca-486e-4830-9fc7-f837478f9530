<!-- 政治面貌 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getPoliticalList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'politicalSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    isLimit: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.politicalList.length > 0) {
        state.list = selectList.politicalList
      } else {
        await getPoliticalList().then((resp: any) => {
          selectList.politicalList = resp
          state.list = resp
        })
      }
      if (props.isLimit === true && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
