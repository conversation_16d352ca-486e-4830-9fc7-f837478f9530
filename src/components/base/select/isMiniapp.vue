<!-- 投递方式 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { reactive, toRefs } from 'vue'

export default {
  name: 'resumeSourceSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    dataType: {
      type: Number,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      list: [
        {
          k: '',
          v: '全部'
        },
        {
          k: '1',
          v: '是'
        },
        {
          k: '2',
          v: '否'
        }
      ]
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
