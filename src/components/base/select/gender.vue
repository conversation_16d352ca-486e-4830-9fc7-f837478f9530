<!-- 性别 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'genderSelect',
  props: {
    isLimit: {
      type: Boolean,
      default() {
        return false
      }
    },
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup(props: any) {
    const state = reactive({
      list: [
        {
          k: 1,
          v: '男'
        },
        {
          k: 2,
          v: '女'
        }
      ]
    })

    onMounted(() => {
      if (props.isLimit) {
        state.list.push({
          k: 3,
          v: '不限'
        })
      }
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>
