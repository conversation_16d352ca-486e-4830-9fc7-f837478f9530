<!-- 会员类型 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'vipTypeSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: -1,
          v: '过期会员'
        },
        {
          k: 0,
          v: '普通用户'
        },
        {
          k: 1,
          v: '黄金VIP会员'
        },
        {
          k: 2,
          v: '钻石VIP会员'
        }
      ]
    })
    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
