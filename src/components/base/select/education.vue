<!-- 学历要求 -->
<template>
  <el-select class="w100" :placeholder="placeholder" clearable :multiple="data">
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getEducationList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'educationSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    is_limit: {
      type: Boolean,
      default: false
    },
    data: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.educationList.length > 0) {
        state.list = selectList.educationList
      } else {
        await getEducationList().then((resp: any) => {
          state.list = resp
          selectList.educationList = resp
        })
      }
      if (props.is_limit === true && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
