<template>
  <el-cascader
    :options="data"
    :props="{ value: 'k', label: 'v', multiple: true, emitPath: false }"
    collapse-tags
    v-model="groupsIds"
  >
  </el-cascader>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const emits = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  }
})

const groupsIds = computed({
  get() {
    const value = props.modelValue
    return value.length ? value.split(',') : []
  },

  set(val: any[]) {
    emits('update:modelValue', val.join())
  }
})
</script>

<style lang="scss" scoped></style>
