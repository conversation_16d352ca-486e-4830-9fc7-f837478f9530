<template>
  <el-select class="w100" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getJobTopStatusList } from '/@/api/config'

export default defineComponent({
  name: 'jobTopStatus',

  setup() {
    const state = reactive({
      list: [] as any
    })

    const getList = async () => {
      state.list = await getJobTopStatusList()
    }

    getList()

    return { ...toRefs(state), getList }
  }
})
</script>
