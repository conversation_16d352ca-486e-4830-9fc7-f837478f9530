<template>
  <el-select clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"></el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getJobDeliveryType } from '/@/api/config'

export default defineComponent({
  name: 'deliveryType',

  setup() {
    const state = reactive({
      list: []
    })

    const getList = async () => {
      state.list = await getJobDeliveryType()
    }

    getList()
    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
