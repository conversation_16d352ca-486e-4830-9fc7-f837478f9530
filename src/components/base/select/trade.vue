<!-- 行业类型 -->
<template>
  <el-cascader
    class="w100"
    :options="tradeList"
    :props="{ value: 'k', label: 'v' }"
    separator="-"
    collapse-tags
    placeholder="请选择行业"
    :teleported="false"
    @change="handleChange"
    filterable
  ></el-cascader>
</template>
<script lang="ts">
import { onMounted, toRefs, reactive } from 'vue'
import { getTradeList } from '/@/api/config.ts'
import { useStore } from '/@/store/index'

export default {
  name: 'trade',
  emits: ['change'],
  setup(props, ctx) {
    const state = reactive({
      tradeList: <any>[]
    })
    const getArea = () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.tradeList.length > 0) {
        state.tradeList = selectList.tradeList
        return
      }
      getTradeList().then((resp: any) => {
        state.tradeList = resp
        selectList.tradeList = resp
      })
    }

    const handleChange = (val: any) => {
      const data = {
        trade: val
      }
      ctx.emit('change', data)
    }

    onMounted(() => {
      getArea()
    })

    return {
      handleChange,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped></style>
