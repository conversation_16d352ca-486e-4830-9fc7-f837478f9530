<!-- 职称 -->
<template>
  <template v-if="elType === 'select'">
    <el-select
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :disabled="disabled"
      class="w100"
      :placeholder="placeholder"
      clearable
    >
      <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
    </el-select>
  </template>
  <template v-else>
    <el-cascader
      class="w100"
      :disabled="disabled"
      :options="list"
      :props="{ value: 'k', label: 'v', emitPath: false, multiple: multiple }"
      :collapse-tags="collapseTags"
      :placeholder="placeholder"
      :show-all-levels="showAllLevels"
      style="width: 100%"
      :filterable="filterable"
      clearable
    ></el-cascader>
  </template>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { useStore } from '/@/store/index'
import { getTitleList, getFirstTitleList } from '/@/api/config'

export default {
  name: 'levelTitleSelect',
  props: {
    elType: {
      type: String,
      default() {
        return 'select' // 可选cascader
      }
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    collapseTags: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => true
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    showAllLevels: {
      type: Boolean,
      default: () => false
    },
    isLimit: {
      type: Boolean,
      default: () => false
    }
  },
  setup(props: any) {
    const store = useStore()
    const state = reactive({
      list: <any>[]
    })
    const getCascaderList = async () => {
      const { selectList } = store.state.selectList
      if (selectList.levelTitleMultistageList.length > 0) {
        state.list = <any>selectList.levelTitleMultistageList
      } else {
        await getTitleList().then((resp: any) => {
          state.list = resp
          selectList.levelTitleMultistageList = resp
        })
      }
    }

    const getSelectList = async () => {
      const { selectList } = store.state.selectList
      if (selectList.levelTitleList.length > 0) {
        state.list = <any>selectList.levelTitleList
      } else {
        await getFirstTitleList().then((resp: any) => {
          state.list = resp
          selectList.levelTitleList = resp
        })
      }
      if (props.isLimit && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      if (props.elType === 'select') {
        getSelectList()
      } else {
        getCascaderList()
      }
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
