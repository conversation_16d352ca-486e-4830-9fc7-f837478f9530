<!-- 代投 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'helpSendSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    isLimit: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  setup(props: any) {
    const state = reactive({
      list: [
        {
          k: 1,
          v: '是'
        },
        {
          k: 2,
          v: '否'
        }
      ]
    })

    onMounted(() => {
      if (props.isLimit) {
        state.list.push({
          k: -1,
          v: '不限'
        })
      }
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
