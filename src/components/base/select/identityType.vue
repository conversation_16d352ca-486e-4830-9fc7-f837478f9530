身份类型
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'genderSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        // {
        //   k: '0',
        //   v: '不限'
        // },
        {
          k: '-1',
          v: '-'
        },
        {
          k: '2',
          v: '应届生/在校生'
        },
        {
          k: '1',
          v: '职场人'
        }
      ]
    })

    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
