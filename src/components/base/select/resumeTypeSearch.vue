<!-- 简历类型(在线、附件等) -->
<template>
  <el-select multiple class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'resumeTypeSearchSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 1,
          v: '精英简历'
        },
        {
          k: 2,
          v: '优质简历'
        },
        {
          k: 3,
          v: '普通简历'
        }
      ]
    })

    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
