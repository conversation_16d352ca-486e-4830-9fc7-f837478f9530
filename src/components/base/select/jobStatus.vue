<!-- 招聘状态/职位状态 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'jobStatusSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 0,
          v: '已下线'
        },
        {
          k: 1,
          v: '上线'
        },
        // {
        //   k: 3,
        //   v: '编辑中'
        // },
        // {
        //   k: 7,
        //   v: '等待审核'
        // },
        {
          k: 9,
          v: '已删除'
        },
        { k: '-1', v: '不限' }
      ]
    })
    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
