<!-- 高才海外广告位置 -->
<template lang="">
  <el-cascader
    class="w100"
    :options="list"
    :props="propsOption"
    :placeholder="placeholder"
    clearable
    filterable
    :change-on-select="true"
  ></el-cascader>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getHomeAbroadPositionList } from '/@/api/advertising'
import { useStore } from '/@/store/index'

const props = defineProps({
  showAllLevels: {
    type: Boolean,
    default: () => false
  },
  placeholder: {
    type: String,
    default: () => '请选择'
  },
  multiple: {
    type: Boolean,
    default: () => false
  }
})

const propsOption = reactive({ value: 'k', label: 'v', emitPath: false, multiple: props.multiple })
const list: any = ref([])

const getList = async () => {
  const store = useStore()
  const {
    selectList,
    selectList: {
      adAbroadPositionList,
      adAbroadPositionList: { length }
    }
  } = store.state.selectList

  if (length > 0) {
    list.value = adAbroadPositionList.map((item: any) => {
      return {
        k: item.k,
        v: item.v,
        children: item.children
      }
    })
    return
  }
  await getHomeAbroadPositionList({ homePositionName: '' }).then((resp: any) => {
    selectList.adAbroadPositionList = resp
    list.value = resp
  })
}

getList()
</script>
