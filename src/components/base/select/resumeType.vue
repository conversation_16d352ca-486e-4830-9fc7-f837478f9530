<!-- 简历类型(在线、附件等) -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'resumeTypeSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 1,
          v: '在线简历'
        },
        {
          k: 2,
          v: '附件简历'
        },
        {
          k: -1,
          v: '不限简历类型'
        }
      ]
    })

    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
