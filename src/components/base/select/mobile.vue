<template>
  <el-input
    v-model="mobile"
    placeholder="请输入手机号"
    oninput="value = value.replace(/[^\d]+/g, '')"
    clearable
  >
    <template #prepend>
      <el-select v-model="mobileCode" style="width: 83px" placeholder="号段">
        <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
          <el-option v-for="{ country, code } in list" :key="code" :value="code">
            <span style="float: left">{{ country }}</span>
            <span style="float: right"> {{ code }} </span>
          </el-option>
        </el-option-group>
      </el-select>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

import { getCountrytMobileCode } from '/@/api/config'

const emits = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  }
})

const prefixOptions = ref([])

const mobile = computed({
  get() {
    return props.modelValue?.mobile
  },
  set(val) {
    emits('update:modelValue', { ...props.modelValue, mobile: val })
  }
})

const mobileCode = computed({
  get() {
    return props.modelValue?.mobileCode
  },
  set(val) {
    emits('update:modelValue', { ...props.modelValue, mobileCode: val })
  }
})

const getMobileCode = async () => {
  prefixOptions.value = await getCountrytMobileCode()
}

getMobileCode()
</script>

<style lang="scss" scoped></style>
