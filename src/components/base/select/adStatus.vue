<!-- 广告状态 -->
<template>
  <el-select
    class="w100"
    :placeholder="placeholder"
    filterable
    :multiple="multiple"
    :collapseTags="collapseTags"
    clearable
  >
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'adStatusSelect',
  props: {
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    collapseTags: {
      type: Boolean,
      default() {
        return false
      }
    },
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 1,
          v: '生效中'
        },
        {
          k: 2,
          v: '未生效'
        },
        {
          k: 0,
          v: '已下线'
        }
      ]
    })
    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
