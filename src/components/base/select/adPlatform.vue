<!-- 广告所属平台 -->
<template>
  <el-select
    class="w100"
    :placeholder="placeholder"
    :multiple="multiple"
    :collapseTags="collapseTags"
    filterable
    clearable
  >
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getPlatform } from '/@/api/advertising'
import { useStore } from '/@/store/index'

export default {
  name: 'adPlatformSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    collapseTags: {
      type: <PERSON>olean,
      default() {
        return false
      }
    },
    isLimit: {
      type: Boolean,
      default: false
    },
    isAll: {
      type: Boolean,
      default: false
    }
  },
  setup(props: any) {
    const state = reactive({
      list: <any>[]
    })
    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList

      if (selectList.adPlatFormList.length > 0) {
        state.list = selectList.adPlatFormList
      } else {
        await getPlatform().then((resp: any) => {
          state.list = resp
          selectList.adPlatFormList = resp
        })
      }

      if (!props.isAll) {
        // 过滤掉小程序和app和海外
        state.list = state.list.filter((i: any) => i.k !== '4' && i.k !== '6')
      }

      if (props.isLimit === true && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
