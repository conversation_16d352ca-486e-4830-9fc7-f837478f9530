<template>
  <!-- 报名方式 -->
  <el-select v-model="applyType" :multiple="multiple" clearable collapse-tags>
    <el-option
      v-for="item in list"
      :key="item.code"
      :label="item.name"
      :value="item.code"
    ></el-option>
  </el-select>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'
import { getJobSignUpList } from '/@/api/cmsJob'

export default defineComponent({
  name: 'applyType',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      list: [],
      applyType: computed({
        get() {
          const value = props.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val.join())
        }
      })
    })

    const getList = async () => {
      state.list = await getJobSignUpList()
    }
    getList()

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
