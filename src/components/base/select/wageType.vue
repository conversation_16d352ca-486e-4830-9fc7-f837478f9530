<!-- 薪资类型(年、月、日) -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'wageTypeSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: '1',
          v: '月'
        },
        {
          k: '2',
          v: '年'
        },
        {
          k: '3',
          v: '日'
        }
      ]
    })

    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
