<!-- 专业类型/学科专业 -->
<template lang="">
  <el-cascader
    class="w100"
    :options="list"
    :props="propsOption"
    :placeholder="placeholder"
    clearable
    filterable
    :change-on-select="true"
  ></el-cascader>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getHomeMiniPositionList } from '/@/api/advertising'
import { useStore } from '/@/store/index'

export default {
  name: 'adMiniPositionList',
  props: {
    showAllLevels: {
      type: Boolean,
      default: () => false
    },
    placeholder: {
      type: String,
      default: () => '请选择'
    },
    multiple: {
      type: Boolean,
      default: () => false
    }
  },
  setup(props: any) {
    const obj = { value: 'k', label: 'v', emitPath: false, multiple: props.multiple }
    const state = reactive({
      list: <any>[],
      propsOption: obj
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.adMiniPositionList.length > 0) {
        state.list = selectList.adMiniPositionList.map((item: any) => {
          return {
            k: item.k,
            v: item.v,
            children: item.children
          }
        })
        return
      }
      await getHomeMiniPositionList({ homePositionName: '' }).then((resp: any) => {
        selectList.adMiniPositionList = resp
        state.list = resp
      })
    }

    const setCascaderDisabled = (cityOptions) => {
      cityOptions.forEach((item) => {
        item.disabled = true
      })
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
