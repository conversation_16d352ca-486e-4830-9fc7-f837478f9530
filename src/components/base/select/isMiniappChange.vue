<!-- 海外经历 -->
<template>
  <el-select :placeholder="placeholder" v-model="values" @change="changeSelect">
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { computed, onMounted, reactive, toRefs } from 'vue'
import { getIsMiniappList, changeIsMiniapp } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'isMiniappChange',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    value: {
      type: String,
      default() {
        return '0'
      }
    },
    type: {
      type: String,
      default() {
        return 'job'
      }
    },
    id: {
      type: String,
      default() {
        return ''
      }
    }
  },
  setup(props: any, { emit }) {
    const state = reactive({
      list: <any>[],
      values: computed({
        get() {
          return props.value
        },
        set(val) {
          emit('update:modelValue', val)
        }
      })
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.isMiniappList.length > 0) {
        state.list = selectList.isMiniappList
      } else {
        await getIsMiniappList().then((resp: any) => {
          state.list = resp
          selectList.isMiniappList = resp
        })
      }
    }
    onMounted(() => {
      getList()
    })

    const changeSelect = (value) => {
      changeIsMiniapp({
        id: props.id,
        type: props.type,
        value
      })
    }

    return {
      ...toRefs(state),
      changeSelect
    }
  }
}
</script>
