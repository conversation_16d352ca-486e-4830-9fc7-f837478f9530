<template>
  <!-- 公告操作类型列表 -->
  <el-select class="w100" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"></el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getAnnouncementTypeList } from '/@/api/config'

export default defineComponent({
  name: 'announcementTypeList',

  components: {},

  setup() {
    const state = reactive({
      list: []
    })
    const getList = async () => {
      state.list = await getAnnouncementTypeList()
    }
    getList()

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
