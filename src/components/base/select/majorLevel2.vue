<template>
  <div>
    <el-tag
      v-for="tag in level2"
      :key="tag.name"
      closable
      :type="tag.type"
      @close="handelClose(tag.id)"
      class="tag"
    >
      {{ tag.name }}
    </el-tag>
    <br />
    <el-row :gutter="20" v-for="(item, key) in checkBoxList" :key="key">
      <el-col :span="6" class="left">
        <el-checkbox
          :disabled="checkedLimit"
          v-model="item.checkAll"
          :indeterminate="item.isIndeterminate"
          @change="handleCheckAllChange($event, key)"
          >{{ item.name }}({{ item.code }})</el-checkbox
        >
      </el-col>
      <el-col :span="18" class="rigth">
        <el-checkbox-group v-model="item.checked" @change="handleChildrenChange($event, key)">
          <el-checkbox
            class="checkbox"
            :disabled="checkedLimit"
            v-for="child in item.children"
            :key="child.id"
            :label="child.id"
            >{{ child.name }}({{ child.code }})</el-checkbox
          >
        </el-checkbox-group>
      </el-col>
    </el-row>
    <!-- hotfix/87需求改动要求去掉 -->
    <!-- 是否添加不限 -->
    <!-- <el-row v-if="isLimit" :gutter="20">
      <el-col :span="6" class="left">
        <el-checkbox v-model="checkedLimit">不限</el-checkbox>
      </el-col>
      <el-col :span="18" class="rigth">
        <el-checkbox v-model="checkedLimit">不限</el-checkbox>
      </el-col>
    </el-row> -->
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue'
import { getLevel2MajorList } from '/@/api/config'

export default defineComponent({
  name: 'majorLevel2',
  props: {
    // isLimit: {
    //   type: Boolean,
    //   default: () => false
    // },
    modelValue: {
      type: Array,
      default: <any>[]
    },
    select: {
      type: Array,
      default: <any>[]
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      checkBoxList: <any>[],
      level2: <any>[],
      level2Id: <any>[],

      checkedLimit: false
    })

    const handleLimitCheck = () => {
      state.checkBoxList.forEach((item, index) => {
        state.checkBoxList[index].checkAll = false
        state.checkBoxList[index].checked = []

        emit('update:modelValue', state.level2Id)
        emit('update:select', state.level2)
      })
    }

    watch(
      () => state.checkedLimit,
      (value) => {
        if (value) {
          state.level2 = [{ id: '-1', name: '不限' }]
          state.level2Id = ['-1']
          handleLimitCheck()
        }
      }
    )

    const allLevel2 = <any>{}
    let list = <any>{}

    const afterCheck = () => {
      state.level2 = []
      state.level2Id = []
      for (let index = 0; index < state.checkBoxList.length; index += 1) {
        const element = state.checkBoxList[index].checked

        if (element.length > 0) {
          for (let idx = 0; idx < element.length; idx += 1) {
            const checked = element[idx]
            state.level2.push({ name: allLevel2[checked].name, id: allLevel2[checked].id })
            state.level2Id.push(allLevel2[checked].id)
          }
        }
      }
      emit('update:modelValue', state.level2Id)
      emit('update:select', state.level2)
    }

    const handleCheckAllChange = (val, key) => {
      if (val) {
        state.checkBoxList[key].checked = state.checkBoxList[key].children.map((item) => item.id)
        state.checkBoxList[key].checkAll = true
        state.checkBoxList[key].isIndeterminate = false
      } else {
        state.checkBoxList[key].checked = []
      }
      afterCheck()
    }
    const handleChildrenChange = (val, key) => {
      state.checkBoxList[key].checkAll = val.length === state.checkBoxList[key].children.length
      state.checkBoxList[key].isIndeterminate =
        val.length > 0 && val.length < state.checkBoxList[key].children.length
      afterCheck()
    }

    const setInit = (ids: any) => {
      state.level2 = []
      state.level2Id = []
      state.checkBoxList = list.map((r) => {
        const item = {
          name: r.v,
          code: r.code,
          id: r.k,
          checkAll: false,
          isIndeterminate: false,
          checked: <any>[],
          children: []
        }
        item.children = r.children.map((c) => {
          allLevel2[c.k] = { name: c.v, id: c.k, code: c.code }

          if (ids.includes(c.k)) {
            item.checked.push(c.k)
            state.level2.push({ name: c.v, id: c.k })
            state.level2Id.push(c.k)
          }

          return {
            name: c.v,
            id: c.k,
            code: c.code
          }
        })

        if (item.checked.length > 0) {
          if (item.checked.length === item.children.length) {
            item.checkAll = true
          } else {
            item.isIndeterminate = true
          }
        }

        return item
      })
    }

    watch(
      () => props.modelValue,
      (value) => {
        setInit(value)
      },
      { deep: true }
    )

    onMounted(() => {
      getLevel2MajorList().then((res) => {
        list = res
        setInit(props.modelValue)
      })
    })

    /**
     * 删除tag
     */
    const handelClose = (id) => {
      const newLevel2Id = state.level2Id.reduce((acc, cur) => {
        if (cur === id) {
          return acc
        }
        acc.push(cur)
        return acc
      }, [])
      setInit(newLevel2Id)
      emit('update:modelValue', state.level2Id)
    }

    return {
      ...toRefs(state),
      handleCheckAllChange,
      handleChildrenChange,
      handelClose
    }
  }
})
</script>
<style scoped lang="scss">
.left {
  text-align: right;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.rigth {
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
.checkbox {
  min-width: 210px;
}
.tag {
  margin-right: 5px;
  // min-width: 180px;
}
</style>
