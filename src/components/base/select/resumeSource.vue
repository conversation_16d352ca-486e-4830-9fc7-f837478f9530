<!-- 投递方式 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in deliveryWayList" :key="item.k" :label="item.v" :value="item.k">
    </el-option>
  </el-select>
</template>
<script lang="ts">
import { reactive, toRefs } from 'vue'
import { getDeliveryWay } from '/@/api/config'

export default {
  name: 'resumeSourceSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    dataType: {
      type: Number,
      default: null
    }
  },
  setup(props) {
    const state = reactive({
      deliveryWayList: []
    })

    const getList = async () => {
      const { deliveryWay } = await getDeliveryWay({ type: props.dataType })
      state.deliveryWayList = deliveryWay
    }

    getList()

    return {
      ...toRefs(state)
    }
  }
}
</script>
