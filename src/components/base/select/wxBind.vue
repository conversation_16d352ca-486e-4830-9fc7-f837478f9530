<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.v" :label="item.k" :value="item.v"> </el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getWxBind } from '/@/api/config'

export default defineComponent({
  name: 'WxBind',

  props: {
    placeholder: {
      type: String,
      default: '请选择'
    }
  },

  setup() {
    const state = reactive({
      list: []
    })
    const getList = async () => {
      state.list = await getWxBind()
    }

    getList()
    return { ...(toRefs(state) as any) }
  }
})
</script>

<style lang="scss" scoped></style>
