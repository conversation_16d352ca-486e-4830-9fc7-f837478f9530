<!-- 人才项目经验项目类型 -->
<template>
  <el-select multiple class="w100" :placeholder="placeholder" clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { useStore } from '/@/store'
import { getAgeList, getProjectCateList } from '/@/api/config'

export default {
  name: 'projectCateSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      await getProjectCateList().then((resp: any) => {
        state.list = resp
      })
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
