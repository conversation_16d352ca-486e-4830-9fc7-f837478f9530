<!-- 年龄要求 -->
<template>
  <template v-if="elType === 'select'">
    <el-select
      class="w100"
      :placeholder="placeholder ? placeholder : '请选择年龄'"
      filterable
      clearable
    >
      <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
    </el-select>
  </template>
  <template v-else>
    <el-input class="w100" :placeholder="placeholder ? placeholder : '请填写年龄'" clearable />
  </template>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { useStore } from '/@/store/index'
import { getAgeList } from '/@/api/config'

export default {
  name: 'ageSelect',
  props: {
    elType: {
      type: String,
      default() {
        return 'input'
      }
    },
    placeholder: {
      type: String,
      default() {
        return ''
      }
    },
    isLimit: {
      type: Boolean,
      default: () => false
    }
  },
  setup(props: any) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      if (selectList.ageList.length > 0) {
        state.list = <any>selectList.ageList
      }
      await getAgeList().then((resp: any) => {
        state.list = resp
        selectList.ageList = resp
      })
      if (props.isLimit && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
