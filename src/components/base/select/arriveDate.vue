<!-- 到岗时间 -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getArriveDateList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'arrvieDate',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    isLimit: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList

      if (selectList.arriveDateList.length > 0) {
        state.list = selectList.ageList
      } else {
        await getArriveDateList().then((resp: any) => {
          state.list = resp
          selectList.ageList = resp
        })
      }
      if (props.isLimit === true && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
