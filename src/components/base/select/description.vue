<template>
  <!-- 账号配置 -->
  <el-select clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"></el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getCompanyDeliverySearch } from '/@/api/configuration'

export default defineComponent({
  name: 'description',

  setup() {
    const state = reactive({
      list: []
    })

    const getList = async () => {
      const { deliveryList } = await getCompanyDeliverySearch()
      state.list = deliveryList
    }

    getList()
    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
