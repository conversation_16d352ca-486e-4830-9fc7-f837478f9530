<!-- 广告显示 -->
<template>
  <el-select
    class="w100"
    :placeholder="placeholder"
    :multiple="multiple"
    :collapseTags="collapseTags"
    filterable
    clearable
  >
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'adShowStatusSelect',
  props: {
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    collapseTags: {
      type: Boolean,
      default() {
        return false
      }
    },
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 0,
          v: '全部'
        },
        {
          k: 1,
          v: '显示'
        },
        {
          k: 2,
          v: '隐藏'
        }
      ]
    })
    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
