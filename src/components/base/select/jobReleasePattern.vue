<!-- 发布类型(模式) -->
<template>
  <el-select class="w100" :placeholder="placeholder" filterable clearable>
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'releasePatternSelect',
  props: {
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    }
  },
  setup() {
    const state = reactive({
      list: [
        {
          k: 2,
          v: '纯职位'
        },
        {
          k: 1,
          v: '公告+职位'
        },
        {
          k: 0,
          v: '不限'
        }
      ]
    })

    onMounted(() => {})

    return {
      ...toRefs(state)
    }
  }
}
</script>
