<!-- 职位类型 -->
<template>
  <el-cascader
    class="w100"
    :options="categoryJobList"
    :props="{ value: 'k', label: 'v', emitPath: false, multiple: multiple }"
    collapse-tags
    :disabled="disabled"
    placeholder="请选择职位类型"
    :show-all-levels="showAllLevels"
    clearable
    :filterable="filter"
    :size="size"
  ></el-cascader>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getCategoryJobList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'jobCategorySelect',
  props: {
    disabled: {
      type: Boolean,
      default: () => false
    },
    showAllLevels: {
      type: Boolean,
      default: () => false
    },
    multiple: {
      type: Boolean,
      default: () => true
    },
    filter: {
      type: Boolean,
      default: () => true
    },
    size: {
      type: String,
      default: 'default'
    }
  },
  setup() {
    const state = reactive({
      categoryJobList: <any>[]
    })

    const getCategoryJob = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.categoryJobList.length > 0) {
        state.categoryJobList = selectList.categoryJobList
        return
      }
      await getCategoryJobList().then((resp: any) => {
        state.categoryJobList = resp
        selectList.categoryJobList = resp
      })
    }
    onMounted(() => {
      getCategoryJob()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
