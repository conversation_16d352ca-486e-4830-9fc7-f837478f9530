<!-- 户籍/国籍 -->
<template>
  <el-cascader
    class="w100"
    :options="list"
    :props="{
      value: 'k',
      label: 'v',
      multiple: true
    }"
    :placeholder="placeholder"
    collapse-tags
    clearable
    filterable
  />
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getNativePlaceList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'nativePlaceSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    isLimit: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      // 尝试做一个缓存,在spa不真正刷新的情况下,是不需要重新去拿这个东西的(这样可以在前端做一个简单的缓存)
      if (selectList.nativePlaceList.length > 0) {
        state.list = selectList.nativePlaceList
      } else {
        await getNativePlaceList().then((resp: any) => {
          state.list = resp
          selectList.nativePlaceList = resp
        })
      }
      if (props.isLimit === true && !state.list.filter((i) => i.k === '-1').length) {
        state.list.unshift({
          k: '-1',
          v: '不限'
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
