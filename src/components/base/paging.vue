<template>
  <div
    :class="{
      'jc-start': align == 'left',
      'jc-end': align == 'right',
      'jc-center': align == 'center'
    }"
  >
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      background
      :default-current-page="page"
      :current-page="currentPage"
      :page-sizes="pageSizes"
      :default-page-size="defaultPageSize"
      :layout="layout"
      :total="total"
      :pager-count="pagerCount"
    ></el-pagination>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, onMounted, watch } from 'vue'

export default {
  name: 'pagination',
  props: {
    page: {
      type: [String, Number],
      default() {
        return 1
      }
    },
    size: {
      type: Number,
      default() {
        return 20
      }
    },
    total: {
      type: Number,
      default() {
        return 0
      }
    },
    layout: {
      type: String,
      default() {
        return 'total,sizes,prev, pager, next, jumper'
      }
    },
    pageSizes: {
      type: Array,
      default() {
        return [20, 30, 50, 100]
      }
    },
    pagerCount: {
      type: Number,
      default() {
        return 5
      }
    },
    defaultPageSize: {
      type: Number,
      default() {
        return 20
      }
    },
    align: {
      type: String,
      default() {
        return 'right'
      }
    }
  },
  emits: ['sizeChange', 'update:page', 'change'],
  setup(props, ctx) {
    const state = reactive({
      currentPage: 1,
      limit: 20
    })

    onMounted(() => {
      // eslint-disable-next-line no-unused-expressions
      ;(props as any).page && (state.currentPage = (props as any).page)
    })

    watch(
      () => (props as any).page,
      (val) => {
        // eslint-disable-next-line no-unused-expressions
        val && (state.currentPage = val)
      }
    )
    const change = () => {
      ctx.emit('change', {
        page: state.currentPage,
        limit: state.limit
      })
    }
    const handleSizeChange = (val: any) => {
      state.limit = val
      ctx.emit('sizeChange', val)
      change()
    }
    const handleCurrentChange = (val: any) => {
      state.currentPage = val
      ctx.emit('update:page', val)
      change()
    }

    return {
      handleSizeChange,
      handleCurrentChange,
      change,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-icon) {
  width: auto;
}
</style>
