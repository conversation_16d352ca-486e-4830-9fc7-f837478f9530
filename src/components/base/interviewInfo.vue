<template>
  <el-dialog v-model="dialogVisible" width="30%">
    <span class="inter-title">面试信息</span>
    <span class="status">{{ details.invitationStatusAmount }}面</span>
    <div class="box">
      <div>面试职位：{{ details.jobName }}</div>
      <div>面试时间：{{ details.interviewTime }}</div>
      <div>面试地址：{{ details.address }}</div>
      <div>联系人：{{ details.contact }}</div>
      <div>联系电话：{{ details.telephone }}</div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue'

export default defineComponent({
  name: 'interviewInfo',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    details: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, { emit }) {
    // 控制弹窗的开关
    const dialogVisible = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      }
    })

    return { dialogVisible }
  }
})
</script>

<style lang="scss" scoped>
.inter-title {
  margin: 10px;
  font-size: 18px;
  font-weight: 700;
}
.box {
  margin-top: 20px;
  margin-left: 10px;
  div {
    margin: 10px 0;
    color: #747474;
  }
}
.status {
  margin-left: 10px;
  color: #5dae23;
  font-size: 12px;
}
</style>
