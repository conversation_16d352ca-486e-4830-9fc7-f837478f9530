<template>
  <el-date-picker
    v-model="value"
    :disabledDate="disabledDate"
    style="width: 100%"
    :placeholder="placeholder"
    :type="type"
    :size="size"
    :value-format="valueFormat"
    clearable
  >
  </el-date-picker>
</template>
<script lang="ts">
import { reactive, toRefs, ref, watch } from 'vue'

export default {
  name: 'datePicker',
  props: {
    type: {
      type: String,
      default() {
        return 'datetime'
      }
    },
    /**
     * v-model默认名称为modeValue 即v-model:modeValue
     */
    modelValue: {
      type: [String, Date]
    },
    placeholder: {
      type: String,
      default() {
        return ''
      }
    },
    format: {
      type: String,
      default() {
        return 'YYYY 年 MM 月 DD 日 HH时mm分'
      }
    },
    valueFormat: {
      type: String,
      default() {
        return 'YYYY-MM-DD HH:mm:ss'
      }
    },
    disabledDate: {
      type: Function,
      default() {
        return false
      }
    },
    size: {
      type: String,
      default() {
        return 'small'
      }
    },
    defaultTime: {
      type: Date,
      default: () => new Date(2000, 1, 1, 14, 0, 0) // '14:00:00'
    }
  },
  setup(props, ctx) {
    const state = reactive({
      value: ref((props as any).modelValue)
    })

    watch(
      () => state.value,
      (newVal) => {
        ctx.emit('update:modelValue', newVal)
      }
    )
    watch(
      () => (props as any).modelValue,
      (value: string) => {
        // 用于重置表单
        if (!value) {
          state.value = ''
        }
      }
    )
    return {
      ...toRefs(state)
    }
  }
}
</script>
