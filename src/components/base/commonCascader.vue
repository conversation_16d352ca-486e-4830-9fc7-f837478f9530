<template>
  <div class="full-width">
    <el-cascader
      class="full-width"
      :show-all-levels="false"
      collapse-tags
      v-model="ids"
      :options="list"
      :props="{
        label: 'v',
        value: 'k',
        emitPath: false,
        multiple: true,
        checkStrictly: checkStrictly
      }"
      filterable
      clearable
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'commonCascader',

  props: {
    list: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: String,
      default: () => ''
    },
    checkStrictly: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      ids: computed({
        get() {
          const value = props.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val.join(','))
        }
      })
    })

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped>
:deep(.full-width) {
  width: 100%;
}
.full-width {
  width: 100%;
}
</style>
