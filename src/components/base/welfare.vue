<template>
  <el-dialog v-model="dialogVisible" top="13vh" width="800px" class="custom-dialog">
    <div class="content mt-10">
      <div class="default wrap">
        <el-tag
          :class="{ checked: tag.checked }"
          :key="tag.k"
          v-for="tag in systemTags"
          :disable-transitions="false"
          color="transparent"
          @click="handleTagClick(tag)"
          >{{ tag.v }}
        </el-tag>
      </div>
      <div v-if="memberId" class="custom mt-15">
        <el-tag
          :class="{ checked: tag.checked }"
          :key="tag.k"
          v-for="tag in customTags"
          :disable-transitions="false"
          @close="handleDeleteMyCumstom(tag)"
          closable
          color="transparent"
          @click="handleTagClick(tag)"
          >{{ tag.v }}
        </el-tag>
        <!-- @submit.prevent 防止input回车触发提交 -->
        <el-form
          class="inline-block"
          :rules="rules"
          :model="formData"
          ref="form"
          label-width="10px"
          v-if="inputVisible"
          @submit.prevent
        >
          <el-form-item prop="inputValue" size="small">
            <el-input
              class="input-new-tag"
              v-model="formData.inputValue"
              ref="addTagInput"
              size="small"
              @blur="handleHide"
              @keyup.enter="handleInputConfirm"
            >
            </el-input>
          </el-form-item>
        </el-form>
        <el-button v-else class="button-new-tag" size="small" @click="showInput"
          >+ 自定义</el-button
        >
      </div>
      <div class="mt-20 flex">
        <div class="selected">已选：</div>
        <div class="flex-1 wrap">
          <el-tag
            :class="{ checked: true }"
            :key="tag.k"
            v-for="tag in selectTags"
            :disable-transitions="false"
            color="transparent"
            checked
            closable
            @close="handleClose(tag)"
            >{{ tag.v }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { toRefs, reactive, ref, onMounted, nextTick, watch, computed } from 'vue'
import { createWelfareTag, deleteWelfareTag } from '/@/api/job.ts'
import { getWelfareLabelList } from '/@/api/config.ts'

export default {
  name: 'dialogWelfare',
  components: {},
  props: {
    // 职位对应memberId(企业id)
    memberId: {
      type: [String, Number],
      default: () => ''
    }
  },
  emits: ['confirm'],
  setup(props, ctx) {
    const form = ref()
    const state = reactive({
      paramId: computed(() => props.memberId),
      dialogVisible: false,
      inputVisible: false,
      formData: {
        inputValue: ''
      },
      rules: {
        inputValue: [{ required: true, message: '不能为空', trigger: ['blur', 'change'] }]
      },
      systemTags: [] as Array<{ v: string; k: string; checked: boolean }>,
      customTags: [] as Array<{ v: string; k: string; checked: boolean }>,
      selectTags: [] as Array<{ v: string; k: string; checked: boolean }>
    })

    const getWelfareLabel = (id: any = '') => {
      getWelfareLabelList({ memberId: id }).then((resp: any) => {
        state.systemTags = resp.system.map((item: object) => {
          return {
            ...item,
            checked: false
          }
        })
        const customTags = resp.self || []
        state.customTags = customTags.map((item: object) => {
          return {
            ...item,
            checked: false
          }
        })
      })
    }
    onMounted(() => {
      if (props.memberId !== -1) return
      getWelfareLabel()
    })

    const handleSelectTag = (value: any) => {
      state.selectTags = value.map((item: any) => {
        return {
          ...item,
          checked: true
        }
      })
      const k = value.map((item) => item.k)
      state.systemTags = state.systemTags.map((item: any) => {
        let checked = false
        if (k.includes(item.k)) {
          checked = true
        }
        return {
          ...item,
          checked
        }
      })
      state.customTags = state.customTags.map((item: any) => {
        let checked = false
        if (k.includes(item.k)) {
          checked = true
        }
        return {
          ...item,
          checked
        }
      })
    }

    watch(
      () => props.memberId,
      (id) => {
        getWelfareLabel(id)
        if (!id) {
          state.customTags = []
        }
      },
      {
        immediate: true
      }
    )

    const openDialog = (data: any) => {
      handleSelectTag(data)
      state.dialogVisible = true
    }

    const handleClose = (tag: any) => {
      state.selectTags.splice(state.selectTags.indexOf(tag), 1)
      for (let i = 0; i < state.systemTags.length; i += 1) {
        if (tag.k === state.systemTags[i].k) {
          state.systemTags[i].checked = false
        }
      }
      for (let i = 0; i < state.customTags.length; i += 1) {
        if (tag.k === state.customTags[i].k) {
          state.customTags[i].checked = false
        }
      }
    }

    const addTagInput = ref()
    const showInput = () => {
      state.inputVisible = true
      nextTick(() => {
        addTagInput.value.input.focus()
      })
    }

    const handleInputConfirm = () => {
      const { inputValue } = state.formData
      if (inputValue) {
        form.value.validate((valid: any) => {
          if (valid) {
            createWelfareTag({
              memberId: props.memberId !== -1 ? props.memberId : '',
              name: inputValue
            }).then((resp: any) => {
              state.customTags.push({ k: resp.id, v: inputValue, checked: false })
              state.inputVisible = false
              state.formData.inputValue = ''
            })
          }
        })
      }
    }
    const handleSelect = () => {
      state.selectTags = [
        ...state.systemTags.filter((item) => item.checked),
        ...state.customTags.filter((item) => item.checked)
      ]
    }
    const handleDeleteMyCumstom = (tag: any) => {
      deleteWelfareTag({
        memberId: props.memberId !== -1 ? props.memberId : '',
        id: tag.k
      }).then(() => {
        state.customTags.splice(state.customTags.indexOf(tag), 1)
        handleSelect()
      })
    }

    const handleTagClick = (tag: any) => {
      // eslint-disable-next-line no-param-reassign
      tag.checked = !tag.checked
      handleSelect()
    }

    const handleHide = () => {
      state.inputVisible = false
      state.formData.inputValue = ''
    }

    const confirm = () => {
      state.dialogVisible = false
      const newsArr = state.selectTags.map((item) => {
        return { k: item.k, v: item.v }
      })
      ctx.emit('confirm', newsArr)
    }

    return {
      form,
      handleDeleteMyCumstom,
      handleClose,
      handleTagClick,
      handleHide,
      handleInputConfirm,
      showInput,
      addTagInput,
      openDialog,
      confirm,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  .el-tag {
    margin-left: 10px;
    border-radius: 2px;
    min-width: 82px;
    text-align: center;
    font-size: 13px;
    height: 32px;
    line-height: 30px;
    margin-bottom: 10px;
    color: #666;
    border-color: #e1e1e1;
    cursor: default;
    &.checked {
      color: var(--color-primary);
      border-color: var(--color-primary);
    }
  }
  .button-new-tag {
    height: 32px;
    line-height: 30px;
    min-width: 80px;
    border: none;
    color: var(--color-primary);
    padding-top: 0;
    padding-bottom: 0;
    margin-left: 10px;
  }
  .input-new-tag {
    width: 150px;
    height: 32px;
    box-sizing: border-box;
    .el-form-item--small {
      :deep(.el-form-item__content) {
        line-height: 30px !important;
      }
    }
  }
}
</style>
