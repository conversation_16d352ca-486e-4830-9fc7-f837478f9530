<!-- 时间范围(包括创建、发布、刷新等等) -->
<template>
  <el-date-picker
    :value-format="valueFormat"
    class="w100"
    v-model="value"
    :type="type"
    :size="size"
    range-separator="-"
    start-placeholder="开始"
    end-placeholder="结束"
    clearable
    :disabled-date="disabledDate"
  >
  </el-date-picker>
</template>
<script lang="ts">
import { reactive, toRefs, watch } from 'vue'

export default {
  name: 'datePicker',
  props: {
    type: {
      type: String,
      default() {
        return 'daterange'
      }
    },
    start: {
      type: [String, Date]
    },
    end: {
      type: [String, Date]
    },
    placeholder: {
      type: String,
      default() {
        return ''
      }
    },
    format: {
      type: String,
      default() {
        return 'YYYY 年 MM 月 DD 日'
      }
    },
    valueFormat: {
      type: String,
      default() {
        return 'YYYY-MM-DD'
      }
    },
    disabledDate: {
      type: Function,
      default() {
        return false
      }
    },
    size: {
      type: String,
      default() {
        return 'default'
      }
    }
  },
  emits: ['update:start', 'update:end', 'update:value'],
  setup(props, { emit }) {
    const state = reactive({
      value: <any>[]
    })

    watch(
      () => (props as any).start,
      (value: string) => {
        const { start, end } = props
        // 用于重置表单
        if (!value) {
          state.value = []
          return
        }

        if (start && end) {
          state.value = [start, end]
        }
      },
      { immediate: true, deep: true }
    )

    watch(
      () => state.value,
      (value: any) => {
        if (!value || !value.length) {
          emit('update:value', [])
          emit('update:start', '')
          emit('update:end', '')
        } else {
          emit('update:value', value)
          emit('update:start', value[0])
          emit('update:end', value[1])
        }
      },
      {
        deep: true
      }
    )
    return {
      ...toRefs(state)
    }
  }
}
</script>
