<template>
  <el-popover v-model:visible="popoverVisible" placement="bottom" :trigger="trigger" :width="width">
    <template #reference>
      <el-input
        id="input"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        clearable
        v-model="inputValue"
        @focus="handleFocus"
        @blur=";(popoverVisible = false), (isFocus = false)"
        @input="handleInput"
        @cleawr="handleClear"
      ></el-input>
    </template>
    <el-scrollbar
      v-loading="loading"
      element-loading-spinner="el-icon-loading fs-20"
      element-loading-background="rgba(255, 255, 255, 1)"
      :max-height="280"
      class="scroll-content"
    >
      <div v-show="list.length">
        <div @click="handleSelect(item)" class="item" v-for="item in list" :key="item">
          <slot :row="item">
            <div>{{ item }}</div>
          </slot>
        </div>
      </div>
      <div v-show="!list.length && showEmpty">
        <slot name="empty">
          <el-empty class="py-10" :description="description" :image-size="imageSize"></el-empty>
        </slot>
      </div>
    </el-scrollbar>
  </el-popover>
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { debounce } from 'throttle-debounce'

export default defineComponent({
  name: 'fuzzySearch',
  emits: ['focus', 'select', 'change'],
  props: {
    clearable: {
      type: Boolean,
      default() {
        return true
      }
    },
    imageSize: {
      type: Number,
      default() {
        return 80
      }
    },
    description: {
      type: String,
      default() {
        return '暂无数据'
      }
    },
    hideLoading: {
      type: Boolean,
      default() {
        return false
      }
    },
    trigger: {
      type: String,
      default() {
        return 'manual'
      }
    },
    valueKey: {
      type: String,
      default() {
        return ''
      }
    },
    visible: {
      type: Boolean,
      default: () => true
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    readonly: {
      type: Boolean,
      default: () => true
    },
    triggerOnFocus: {
      type: Boolean,
      default: () => true
    },
    value: {
      type: String,
      default() {
        return ''
      }
    },
    placeholder: {
      type: String,
      default() {
        return '请填写关键词'
      }
    },
    debounce: {
      type: Number,
      default() {
        return 300
      }
    },
    showEmpty: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  setup(props: any, { emit }) {
    const state = reactive({
      popoverVisible: false,
      loading: false,
      isFocus: false,
      width: 150,
      list: <any>[],
      inputValue: ''
    })

    onMounted(() => {
      const input = <any>document.querySelector('#input')
      state.width = input.offsetWidth
    })

    const isShowEmpty = (length: number) => {
      state.popoverVisible = !(!props.showEmpty && !length)
    }

    const handleSearch = () => {
      if (!state.isFocus) return
      state.loading = true
      emit('change', state.inputValue, (data: any) => {
        isShowEmpty(data.length)
        setTimeout(() => {
          state.loading = false
          state.list = data
        }, 200)
      })
    }

    const handleFocus = () => {
      if (props.visible) {
        state.isFocus = true
        state.popoverVisible = true
        if (props.triggerOnFocus) {
          state.loading = true
          handleSearch()
        }
      }
      emit('focus')
    }
    // 添加防抖
    const debounceFn = debounce(props.debounce, false, handleSearch)

    const handleInput = () => {
      debounceFn()
    }

    const handleClear = () => {
      state.popoverVisible = false
      state.loading = false
      emit('select', '')
    }

    const handleSelect = (item: any) => {
      state.inputValue = props.valueKey ? item[props.valueKey] : item
      emit('select', item)
    }

    return {
      handleSearch,
      handleSelect,
      handleFocus,
      handleInput,
      handleClear,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.scroll-content {
  margin: 0 -12px;
}
.item {
  padding: 7px 12px;
  cursor: pointer;
  &:hover {
    background-color: var(--el-background-color-base);
  }
}
</style>
