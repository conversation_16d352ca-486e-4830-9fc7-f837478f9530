<template>
  <el-autocomplete
    v-model="val"
    class="block"
    :readonly="readonly"
    :disabled="disabled"
    :fetch-suggestions="querySearch"
    :placeholder="placeholder"
    :value-key="valueKey"
    @select="handleSelect"
    :clearable="clearable"
    :trigger-on-focus="triggerOnFocus"
  >
    <template #default="{ item }">
      <slot :row="item">
        <div>{{ item }}</div>
      </slot>
    </template>
  </el-autocomplete>
</template>
<script lang="ts">
import { reactive, toRefs, ref, watch } from 'vue'

export default {
  name: 'inputAutocomplete',
  emits: ['select', 'change'],
  props: {
    clearable: {
      type: Boolean,
      default() {
        return true
      }
    },
    valueKey: {
      type: String,
      default() {
        return 'value'
      }
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    triggerOnFocus: {
      type: <PERSON><PERSON><PERSON>,
      default: () => true
    },
    value: {
      type: String,
      default() {
        return ''
      }
    },
    placeholder: {
      type: String,
      default() {
        return ''
      }
    },
    data: {
      type: Array,
      default() {
        return []
      }
    }
  },
  setup(props: any, { emit }) {
    const state = reactive({
      list: [],
      queryKw: null,
      val: ref(props.value)
    })

    watch(
      () => props.value,
      (value: string) => {
        // 用于重置表单
        if (!value) {
          state.val = ''
        } else {
          state.val = value
        }
      }
    )

    const querySearch = (queryString: any, cb: any) => {
      if (state.queryKw === queryString) {
        cb(state.list)
        return
      }
      emit('change', queryString, (data: any) => {
        state.list = data
        state.queryKw = queryString
        setTimeout(() => {
          cb(data)
        }, 200)
      })
    }
    const handleSelect = (item: any) => {
      emit('select', item)
    }

    return {
      querySearch,
      handleSelect,
      ...toRefs(state)
    }
  }
}
</script>
