<template>
  <div>
    <el-checkbox-group v-model="data" :disabled="disabled">
      <el-checkbox v-for="item in checkBoxList" :key="item.k" :label="item.k">
        {{ item.v }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'announcementCheckBox',
  props: {
    checkBoxList: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: <PERSON>olean,
      default: () => false
    },
    modelValue: {
      type: String,
      default: ''
    }
  },

  setup(props, { emit }) {
    const state = <any>reactive({
      data: computed({
        get() {
          const value = props.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val.join())
        }
      })
    })

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
