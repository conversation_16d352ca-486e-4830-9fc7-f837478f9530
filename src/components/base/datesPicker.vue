<!-- 一个或多个日期选择，绑定值已处理成字符串 -->
<template>
  <el-date-picker
    v-model="value"
    type="dates"
    :placeholder="placeholder"
    :value-format="valueFormat"
    :disabled-date="disabledDate"
    clearable
  />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed } from 'vue'

export default defineComponent({
  name: 'datesPicker',

  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD'
    },
    modelValue: {
      type: String,
      default: ''
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      value: computed({
        get() {
          const value = props.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val?.join())
        }
      })
    })

    const disabledDate = (time: Date) => {
      // 不能早于今日
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    return { ...toRefs(state), disabledDate }
  }
})
</script>

<style lang="scss" scoped></style>
