<template>
  <div>
    <el-checkbox-group v-model="attribute">
      <el-checkbox
        v-for="item in checkBoxList"
        :key="item.k"
        :label="item.k"
        @change="(e) => checkboxChange(e, item.k)"
      >
        {{ item.v }}
      </el-checkbox>
    </el-checkbox-group>
    <el-form-item class="flex">
      <el-date-picker
        v-for="(item, index) in dateList"
        :key="index"
        class="mb-10"
        v-model="item.value"
        :placeholder="item.placeholder"
        value-format="YYYY-MM-DD"
        :disabled="item.disabled"
        ref="dateRef"
      ></el-date-picker>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs, watch } from 'vue'

export default defineComponent({
  name: 'attributeCheck',
  props: {
    checkBoxList: {
      type: Array,
      default: () => []
    },

    modelValue: {
      type: Array,
      default: () => []
    },

    dateType: {
      type: String,
      default: () => 'attributeTimeDate'
    },

    date: {
      type: Object,
      default: () => {}
    }
  },

  emits: ['update:date', 'update:modelValue'],

  setup(props, { emit }) {
    const dateRef = ref()

    const state = reactive({
      attribute: computed({
        get() {
          const value = props.modelValue
          return value
        },
        set(val) {
          emit('update:modelValue', val)
        }
      }),

      attributeTimeDate: [
        {
          placeholder: '请选择首页置顶结束时间',
          disabled: computed(() => !state.attribute.includes('3')),
          id: '3',
          value: computed({
            get() {
              const value = props.date.indexTopEndTime
              return !state.attribute.includes('3') ? '' : value
            },

            set(val) {
              emit('update:date', { ...props.date, indexTopEndTime: val })
            }
          })
        },
        {
          placeholder: '请选择栏目置顶结束时间',
          disabled: computed(() => !state.attribute.includes('4')),
          id: '4',
          value: computed({
            get() {
              const value = props.date.columnTopEndTime
              return !state.attribute.includes('4') ? '' : value
            },

            set(val) {
              emit('update:date', { ...props.date, columnTopEndTime: val })
            }
          })
        },
        {
          placeholder: '请选择博后加推结束时间',
          disabled: computed(() => !state.attribute.includes('22')),
          id: '22',
          value: computed({
            get() {
              const value = props.date.doctorPushEndTime
              return !state.attribute.includes('22') ? '' : value
            },

            set(val) {
              emit('update:date', { ...props.date, doctorPushEndTime: val })
            }
          })
        }
      ],

      globalAttributeTimeDate: [
        {
          placeholder: '请选择首页置顶结束时间',
          disabled: computed(() => !state.attribute.includes('202')),
          id: '202',
          value: computed({
            get() {
              const value = props.date.overseasIndexTopEndTime
              return !state.attribute.includes('202') ? '' : value
            },

            set(val) {
              emit('update:date', { ...props.date, overseasIndexTopEndTime: val })
            }
          })
        },

        {
          placeholder: '请选择栏目置顶结束时间',
          disabled: computed(() => !state.attribute.includes('207')),
          id: '207',
          value: computed({
            get() {
              const value = props.date.overseasColumnTopEndTime
              return !state.attribute.includes('207') ? '' : value
            },

            set(val) {
              emit('update:date', { ...props.date, overseasColumnTopEndTime: val })
            }
          })
        }
      ],

      dateList: computed(() => state[props.dateType])
    })

    const checkboxChange = (e, id) => {
      const dateIds = state.dateList.reduce((arr, nextItem) => {
        arr.push(nextItem.id)
        return arr
      }, [])
      const inInclude = dateIds.includes(id)
      const index = dateIds.indexOf(id)
      if (inInclude && e) {
        dateRef?.value[index]?.handleOpen()
      }
    }

    return { ...toRefs(state), dateRef, checkboxChange }
  }
})
</script>
