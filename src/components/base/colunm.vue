<template>
  <!-- 所属栏目 -->
  <div>
    <el-cascader
      v-model="columnId"
      :options="columnList"
      :props="{ label: 'v', value: 'k', checkStrictly: check, emitPath: false }"
      :filterable="filter"
      @change="changeColumnId"
      clearable
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'

export default defineComponent({
  name: 'colunm',

  components: {},
  props: {
    modelValue: {
      type: String,
      default: () => ''
    },
    columnList: {
      type: Array,
      default: () => []
    },
    filter: {
      type: Boolean,
      default: true
    },
    check: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      columnId: ''
    })
    watch(
      () => props.modelValue,
      (val: any) => {
        state.columnId = val
      },
      { deep: true }
    )
    const changeColumnId = (val: any) => {
      emit('update:modelValue', val)
    }
    return { ...toRefs(state), changeColumnId }
  }
})
</script>

<style lang="scss" scoped></style>
