<template>
  <el-popover
    ref="popover"
    placement="bottom-start"
    width="100%"
    trigger="click"
    :teleported="false"
    @hide="handleHide"
  >
    <template #reference>
      <el-input
        class="cursor-default"
        v-model="value"
        placeholder="请选择工作地点"
        readonly
      ></el-input>
    </template>
    <div class="content">
      <div class="address" v-show="!isAdd">
        <div class="list-content">
          <template v-if="list.length">
            <div
              class="list"
              v-for="(item, index) in list"
              :key="index"
              @click="selectAddress(item)"
            >
              {{ item.areaName }}{{ item.address }}
            </div>
          </template>
          <el-empty v-else :image-size="80" description="暂无地址"></el-empty>
        </div>
        <div class="add" @click="isAdd = true">+ 添加新的工作地址</div>
      </div>
      <el-form
        v-show="isAdd"
        class="flex-1 column"
        :model="formData"
        status-icon
        :rules="rules"
        ref="form"
        label-width="0"
      >
        <div class="flex-1">
          <el-form-item label="" prop="areaArray" class="mb-18">
            <Region v-model="formData.areaArray" :teleported="false" @change="areaChange" />
          </el-form-item>
          <el-form-item label="" prop="address">
            <el-input
              class="w100"
              v-model="formData.address"
              placeholder="请输入详情地址"
            ></el-input>
          </el-form-item>
        </div>
        <div class="jc-end">
          <el-button type="primary" plain @click="isAdd = false">返回</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </div>
      </el-form>
    </div>
  </el-popover>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref, watch, reactive, toRefs } from 'vue'
import Region from '/@select/region.vue'
import { getAddressList } from '/@/api/job.ts'

export default defineComponent({
  name: 'jobAddress',
  emits: ['confirm'],
  props: {
    memberId: {
      type: [String, Number],
      default: () => ''
    },
    address: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: { Region },
  setup(props: any, { emit }) {
    const form = ref()
    const popover = ref()

    const state = reactive({
      value: '',
      isAdd: false,
      list: [],
      formData: {
        areaArray: [],
        provinceId: '',
        cityId: '',
        districtId: '',
        areaName: '',
        address: ''
      },
      rules: {
        areaArray: [{ required: true, message: '请选择省市区', trigger: 'change' }]
        // address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
      }
    })

    watch(
      () => props.address,
      (value) => {
        if (!value.provinceId) {
          state.value = ''
          return
        }
        state.value = `${value.areaName}${value.address}`
      }
    )

    const getAddress = (id: any) => {
      getAddressList({ memberId: id }).then((resp: any) => {
        state.list = resp
      })
    }

    watch(
      () => props.memberId,
      (id: '') => {
        if (id) {
          getAddress(id)
        } else {
          state.list = []
        }
      }
    )

    onMounted(() => {})

    const handleClosePopover = () => {
      popover.value.hide()
    }

    const selectAddress = (address: any) => {
      emit('confirm', {
        provinceId: address.provinceId,
        cityId: address.cityId,
        districtId: address.districtId,
        address: address.address,
        areaName: address.areaName
      })
      handleClosePopover()
    }

    const areaChange = (value: any) => {
      state.formData.areaName = value.text
      state.formData.areaArray = value.region
      const [provinceId, cityId, districtId] = value.region
      state.formData.provinceId = provinceId
      state.formData.cityId = cityId
      state.formData.districtId = districtId
    }

    const confirm = () => {
      form.value.validate((valid: boolean) => {
        if (valid) {
          const { provinceId, cityId, districtId, address, areaName } = state.formData
          emit('confirm', {
            provinceId,
            cityId,
            districtId,
            areaName,
            address
          })
          state.formData.areaArray = []
          state.formData.address = ''
          handleClosePopover()
        }
      })
    }

    const handleHide = () => {
      setTimeout(() => {
        state.isAdd = false
      }, 300)
    }

    return {
      form,
      selectAddress,
      areaChange,
      confirm,
      handleHide,
      getAddress,
      popover,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.cursor-default {
  :deep(.el-input__inner) {
    cursor: inherit;
  }
}
.content {
  min-height: 250px;
  max-height: 350px;
  // overflow: hidden;
  display: flex;
  .address {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    .list-content {
      flex-grow: 1;
      height: 100%;
      overflow-y: scroll;
      .list {
        cursor: pointer;
        padding: 8px 0;
        &:hover {
          color: var(--color-primary);
        }
      }
    }
    .add {
      opacity: 0.7;
      margin-top: 15px;
      cursor: pointer;
    }
  }
}
</style>
