<template>
  <el-dialog v-model="visible" width="450px">
    <template #header>
      <div class="header">
        <div class="title">排序</div>
        <div class="tips">此排序仅对单位主页中的公告排序生效</div>
      </div>
    </template>
    <div class="content">
      <div class="tips">默认为0，可输入0～999的序号，数字越大排越前</div>
      <el-form ref="form" :form="formData" :label-width="0">
        <el-form-item>
          <el-input
            v-model="formData.sort"
            :maxlength="3"
            @input="handleInput($event)"
            :input-style="{ 'text-align': 'center' }"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="footer">
        <el-button :loading="loading" type="primary" @click="handleConfirm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts">
import { ElMessage } from 'element-plus'
import { defineComponent, reactive, toRefs } from 'vue'

import { verifiyNumberInteger } from '/@/utils/toolsValidate'

import { announcementSortChange } from '/@/api/announcement'

let callbackFn

export default defineComponent({
  name: 'sortChangeDialog',
  setup() {
    const state = reactive({
      visible: false,
      loading: false,
      formData: {
        id: '',
        sort: '0'
      }
    })

    const open = ({ id, sort }, callback) => {
      state.formData.id = id
      state.formData.sort = sort
      state.visible = true
      callbackFn = callback
    }

    const handleInput = (val: string) => {
      state.formData.sort = verifiyNumberInteger(val)
    }

    const handleConfirm = () => {
      const { sort } = state.formData
      if (!sort) {
        ElMessage.error('不能为空')
        return
      }
      const {
        formData: { id, sort: homeSort }
      } = state
      state.loading = true
      announcementSortChange({ id, homeSort })
        .then(() => {
          callbackFn(sort)
          state.loading = false
          state.visible = false
        })
        .catch(() => {
          state.loading = false
          ElMessage.error('修改失败')
        })
    }

    return {
      open,
      handleInput,
      handleConfirm,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  align-items: flex-end;
  font-size: 18px;
  font-weight: bold;
  .tips {
    font-size: 13px;
    opacity: 0.6;
    margin-left: 5px;
    font-weight: normal;
    padding-bottom: 2px;
  }
}
.content {
  padding: 18px 10px 0;
  .tips {
    text-align: center;
    margin-bottom: 10px;
  }
}
.footer {
  width: 100%;
  display: flex;
  justify-content: center;
}
:deep() {
  .el-form-item--default {
    margin-bottom: 0;
  }
}
</style>
