<template>
  <div>
    <el-form-item label="投递方式" v-if="isCooperation && isAnnouncement" prop="deliveryWay">
      <el-checkbox-group v-model="form.deliveryWay" :max="1" @change="handleDeliveryWayChange">
        <el-checkbox v-for="item in deliveryWay" :key="item.k" :label="item.k">{{
          item.v
        }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item
      label="投递方式"
      v-if="!isAnnouncement && isAccountA"
      prop="deliveryWay"
      :rules="deliveryTypeRules"
    >
      <el-radio-group v-model="form.deliveryWay" @change="handleDeliveryWayChange">
        <el-radio v-for="item in deliveryWay" :key="item.k" :label="item.k">{{ item.v }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="报名方式" :rules="applyMethodsRules" prop="applyType">
      <el-checkbox-group v-model="form.applyType" :disabled="isOnSite">
        <el-checkbox v-for="item in applyTypeList" :key="item.code" :label="item.code">
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="投递地址" :rules="applyAddressRules" prop="applyAddress">
      <el-input
        v-model="form.applyAddress"
        :placeholder="applyPlaceholderText"
        :disabled="isOnSite"
      ></el-input>
    </el-form-item>
    <el-form-item
      label="投递通知邮箱"
      label-width="96px"
      v-if="(!isAnnouncement && isAccountA) || (isAnnouncement && isCooperation)"
      prop="extraNotifyAddress"
      :rules="extraNotifyAddressRules"
    >
      <el-input
        :disabled="!(isOnSite && isAccountA)"
        class="flex-1"
        v-model="form.extraNotifyAddress"
        placeholder="本处设置的邮箱将同步收到简历投递通知"
      />
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs } from 'vue'
import { getDeliveryApply } from '/@/api/config'
import { verifyEmail, verifyUrl } from '/@/utils/toolsValidate'

export default defineComponent({
  name: 'applyMethods',
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    },
    isAnnouncement: {
      type: Boolean,
      default: true
    },
    isCooperation: {
      type: Boolean,
      default: false
    },
    accountType: {
      require: true,
      type: Number,
      default: 2
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isCooperationJob: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const emailText = {
      emailMax: '最多可输入3个邮箱地址',
      emailError: '请输入正确的邮箱地址',
      emailRepeat: '不支持输入重复的邮箱，请确认',
      emailNone: '请填写邮箱或单位报名网址',
      emailCheck: '多个邮箱请用英文“,”隔开，最多3个',
      netCheck: '请输入单位报名网址',
      netError: '请输入正确的网址'
    }

    const state = reactive({
      form: {
        applyAddress: computed({
          get() {
            return props.modelValue.applyAddress
          },
          set(val) {
            emit('update:modelValue', {
              ...props.modelValue,
              applyAddress: val
            })
          }
        }),
        applyType: computed({
          get() {
            const value = props.modelValue.applyType
            return value.length ? value.split(',') : []
          },
          set(val: any) {
            emit('update:modelValue', {
              ...props.modelValue,
              applyType: val.join()
            })
          }
        }),
        companyDeliveryTypeTxt: computed({
          get() {
            return props.modelValue.companyDeliveryTypeTxt
          },
          set(val) {
            emit('update:modelValue', {
              ...props.modelValue,
              companyDeliveryTypeTxt: val
            })
          }
        }),
        deliveryWay: computed({
          get() {
            const val = props.modelValue.deliveryWay
            return typeof val === 'number' ? val.toString() : val
          },
          set(val) {
            emit('update:modelValue', {
              ...props.modelValue,
              deliveryWay: val
            })
          }
        }),
        extraNotifyAddress: computed({
          get() {
            return props.modelValue.extraNotifyAddress
          },
          set(val) {
            emit('update:modelValue', {
              ...props.modelValue,
              extraNotifyAddress: val
            })
          }
        })
      },
      deliveryWay: [],
      applyTypeList: [],
      isAccountA: computed(() => props.accountType === 2),
      isAccountB: computed(() => props.accountType === 3),
      isOnSite: computed(() => state.form.deliveryWay?.includes('1')),
      isOnLine: computed(() => state.form.deliveryWay?.includes('66')),

      applyPlaceholderText: computed(() => {
        if (state.form.applyType.includes('1')) {
          return emailText.emailCheck
        }
        if (state.form.applyType.includes('2')) {
          return emailText.netCheck
        }

        return emailText.emailNone
      }),

      applyMethodsRules: [
        {
          required: computed(
            () =>
              (!props.isCooperation && props.isCooperationJob) ||
              (!props.isAnnouncement && state.isOnLine)
          ),
          trigger: 'blur',
          message: '请选择报名方式'
        }
      ],
      deliveryTypeRules: [
        {
          required: true,
          trigger: 'change',
          message: '请选择投递方式'
        }
      ]
    })

    const validateEmailAddress = (rule, value, callback, regEmail) => {
      const isAllEmail = regEmail.every((item: string) => verifyEmail(item))
      const isNotRepeatEmail = regEmail
        .map((item: string, index: number) => {
          const repeatIndex = regEmail.findIndex((it: string) => it === item)
          return repeatIndex !== -1 && repeatIndex !== index
        })
        .every((item) => item === false)
      if (!isAllEmail) {
        callback(emailText.emailError)
      }
      if (!isNotRepeatEmail) {
        callback(emailText.emailRepeat)
      }
      if (regEmail.length > 3) {
        callback(emailText.emailMax)
      }
      callback()
    }

    const valideteApplyAddress = (rule, value, callback) => {
      if (state.form.applyType.includes('1')) {
        const regEmail = state.form.applyAddress.split(',')
        validateEmailAddress(rule, value, callback, regEmail)
      } else if (state.form.applyType.length) {
        if (!verifyUrl(state.form.applyAddress)) {
          callback(emailText.netError)
        }
        callback()
      }
      callback()
    }

    const validateExtraNotifyAddress = (rule, value, callback) => {
      if (state.form.extraNotifyAddress) {
        const regEmail = state.form.extraNotifyAddress.split(',')
        validateEmailAddress(rule, value, callback, regEmail)
      }
      callback()
    }

    const applyAddressRules = ref({
      required: computed(() => state.form.applyType?.length),
      trigger: 'blur',
      validator: valideteApplyAddress
    })

    const extraNotifyAddressRules = ref({
      trigger: 'blur',
      validator: validateExtraNotifyAddress
    })

    const getData = async () => {
      const { deliveryWay, applyType } = await getDeliveryApply()
      state.deliveryWay = deliveryWay
      state.applyTypeList = applyType
    }

    const handleDeliveryWayChange = () => {
      emit('update:modelValue', {
        ...props.modelValue,
        applyType: [],
        extraNotifyAddress: '',
        applyAddress: ''
      })
    }

    getData()

    return {
      ...toRefs(state),
      applyAddressRules,
      extraNotifyAddressRules,
      handleDeliveryWayChange
    } as any
  }
})
</script>

<style lang="scss" scoped></style>
