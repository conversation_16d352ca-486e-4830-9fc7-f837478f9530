<template>
  <el-input
    @keyup.enter="handleSearch"
    v-model="queryNumberValue"
    clearable
    :placeholder="queryPlaceholder"
    filterable
  >
    <template #prepend>
      <el-select v-model="queryNumber" style="width: 100px">
        <el-option
          v-for="item in optionsList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { computed, unref } from 'vue'

const emits = defineEmits(['update:modelValue', 'keyup.enter'])

const prop = defineProps({
  optionsList: {
    type: Array,
    default: () => []
  },

  modelValue: {
    type: String,
    default: '1'
  },

  formData: {
    type: Object,
    default: () => {}
  }
})

const queryNumber = computed({
  get() {
    return prop.modelValue
  },

  set(v: string) {
    emits('update:modelValue', v)
  }
})

const handleSearch = () => {
  emits('keyup.enter')
}

const findItemData = (target, value) => target.find((item) => item.value === value)

const queryNumberData = computed(() => findItemData(prop.optionsList, unref(queryNumber)))

const queryPlaceholder = computed(() => unref(queryNumberData).placeholder)

const queryNumberValue = computed({
  get() {
    return prop.formData[unref(queryNumberData).key]
  },

  set(value: string) {
    prop.formData[unref(queryNumberData).key] = value
  }
})
</script>

<style lang="scss" scoped>
.padding-left-reset {
  :deep(.el-input__wrapper) {
    padding-left: 1px;
  }

  .el-select {
    max-width: 100px;

    :deep(.el-input__wrapper) {
      padding-left: 11px;
    }
  }
}
</style>
