<template>
  <el-image-viewer
    v-if="visible"
    @close="handleClose"
    :z-index="zIndex"
    :url-list="list"
    :initial-index="initialIndex"
    :hide-on-click-modal="hideOnClickModal"
    :infinite="false"
    :teleported="teleported"
  />
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'imagePreview',
  props: {
    initialIndex: {
      type: Number,
      default: () => 0
    },
    hideOnClickModal: {
      type: Boolean,
      default: () => true
    },
    infinite: {
      type: Boolean,
      default: () => false
    },
    teleported: {
      type: Boolean,
      default: () => true
    },
    zIndex: {
      type: Number,
      default: () => 1
    }
  },
  setup() {
    const state = <any>reactive({
      visible: false,
      list: []
    })

    const open = (data: [Array<string>, string]) => {
      if (!data) return
      const isArray = data instanceof Array
      if (isArray) {
        if (!data.length) return
        state.list = data.map((item: any) => {
          return item.url ? item.url : item
        })
      } else {
        state.list = [data]
      }
      state.visible = true
    }

    const handleClose = () => {
      state.visible = false
    }

    return {
      open,
      handleClose,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped></style>
