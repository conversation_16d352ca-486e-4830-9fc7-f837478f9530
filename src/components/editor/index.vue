<template>
  <el-upload class="hide" action="/upload/image" :show-file-list="false" ref="wordUpload">
  </el-upload>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'editor',
  props: {
    id: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const submitUpload = () => {
      this.$refs.upload.submit()
    }
  }
})
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
