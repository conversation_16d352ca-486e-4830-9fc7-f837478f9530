<template>
  <div>
    <el-dialog v-model="batchCopyVisible" :title="title" width="500px">
      <el-form :model="form" v-if="batchCopy" :rules="copyFormDataRules" ref="formData">
        <el-form-item label="复制到栏目" prop="homeColumnId">
          <Colunm
            v-model="form.homeColumnId"
            :columnList="columnList"
            :filter="false"
            :check="false"
          />
        </el-form-item>
        <el-form-item label="每篇复制数量" prop="amount">
          <el-input v-model="form.amount" />
        </el-form-item>
        <el-alert :closable="false" type="warning"
          >复制到的目标栏目必须与当前模型类型一致，否则程序会自动忽略不符合的文档</el-alert
        >
      </el-form>
      <el-form v-if="moveDocument" :model="moveForm" ref="moveData" :rules="moveFormDataRules">
        <el-form-item label="移动到栏目" prop="columnId">
          <Colunm
            v-model="moveForm.columnId"
            :columnList="columnList"
            :filter="false"
            :check="false"
          />
        </el-form-item>
        <el-alert :closable="false" type="warning"
          >移动到的目标栏目必须与当前模型类型一致，否则程序会自动忽略不符合的文档</el-alert
        >
      </el-form>
      <el-row>
        <el-button type="primary" @click="submit">确认提交</el-button>
      </el-row>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { defineComponent, reactive, ref, toRefs } from 'vue'
import Colunm from '../base/colunm.vue'
import { announcementBatchCopy, announcementBatchMove, getAddParams } from '/@/api/announcement'

export default defineComponent({
  name: 'announcementBatchCopy',

  components: { Colunm },

  setup() {
    const state = reactive({
      batchCopyVisible: false,
      title: '',
      batchCopy: false,
      moveDocument: false,
      columnList: [],
      moveForm: {
        columnId: ''
      },
      val: '' as any,
      form: {
        amount: '',
        homeColumnId: '',
        ids: ''
      },
      copyFormDataRules: {
        homeColumnId: [{ required: true, message: '请选择', trigger: 'change' }],
        amount: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            pattern: /^([1-9]|10)$/,
            message: '不能超过10个',
            trigger: 'blur'
          }
        ]
      },
      moveFormDataRules: {
        columnId: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    })

    const formData = ref()
    const moveData = ref()
    const openBatchCopy = (val: any, id: any) => {
      if (val === 5) {
        state.title = '批量复制'
        state.moveDocument = false
        state.batchCopy = true
      } else {
        state.title = '移动文档'
        state.batchCopy = false
        state.moveDocument = true
      }
      state.val = val
      state.form.ids = id
      state.batchCopyVisible = true
    }
    const getData = async () => {
      const { columnList } = await getAddParams()
      state.columnList = columnList
    }
    getData()
    const submit = () => {
      if (state.val === 5) {
        formData.value.validate(async (valide: boolean) => {
          if (valide) {
            const { type, fail } = await announcementBatchCopy(state.form)
            if (type === 2) {
              ElMessage.warning(fail)
            } else {
              ElMessage.success('操作成功')
              state.batchCopyVisible = false
            }
            formData.value.resetFields()
          } else {
            return false
          }
          return valide
        })
      } else {
        moveData.value.validate(async (validate: boolean) => {
          if (validate) {
            const { type, fail } = await announcementBatchMove(state.form)
            if (type === 2) {
              ElMessage.warning(fail)
            } else {
              ElMessage.success('操作成功')
              state.batchCopyVisible = false
            }
            moveData.value.resetFields()
          } else {
            return false
          }
          return validate
        })
      }
    }
    // const amountValid = (rule, value, callback) => {
    //   console.log(value)
    //   console.log(rule)
    //   if (!value) {
    //     callback('请填写复制数量')
    //   }
    //   if (value > 10) {
    //     callback('请填写数量必须小于10')
    //   }
    //   callback()
    // }
    // const copyFormDataRules = ref({
    //   amount: [
    //     {
    //       validator: amountValid,
    //       trigger: ['blur', 'change']
    //     }
    //   ]
    // })

    return { ...toRefs(state), openBatchCopy, getData, submit, formData, moveData }
  }
})
</script>

<style lang="scss" scoped>
.el-row {
  justify-content: center;
  margin-top: 20px;
}
</style>
