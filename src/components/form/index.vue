<template>
  <el-form :model="formData" ref="formVm" v-bind="attrs" :rules="rules">
    <template v-if="showHeader">
      <slot name="header"></slot>
    </template>

    <el-row v-for="(row, idx) in rowItems" :key="idx" :gutter="gutter">
      <el-col
        v-for="{ dataIndex, itemAttrs, type = 'input', attrs, options, scopedSlots } in row"
        :key="dataIndex"
        :span="span"
      >
        <el-form-item v-bind="itemAttrs" :prop="dataIndex">
          <!-- Custom Render -->
          <template v-if="scopedSlots && scopedSlots.customRender">
            <slot
              :name="scopedSlots.customRender"
              v-bind="{ dataIndex, itemAttrs, type, attrs, options, scopedSlots }"
            ></slot>
          </template>
          <!-- Default Render -->
          <template v-else>
            <el-input
              v-if="/^(input|textarea)$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
              :type="type"
            ></el-input>

            <el-input-number
              v-if="/^inputNumber$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
            ></el-input-number>

            <el-radio-group v-if="/^radio$/i.test(type)" v-model="formData[dataIndex]">
              <el-radio
                v-for="{ label, value } in options"
                :key="value"
                :label="value"
                v-bind="attrs"
              >
                {{ label }}
              </el-radio>
            </el-radio-group>

            <el-checkbox-group v-if="/^checkbox$/i.test(type)" v-model="formData[dataIndex]">
              <el-checkbox
                v-for="{ label, value } in options"
                :key="value"
                :label="value"
                v-bind="attrs"
              >
                {{ label }}
              </el-checkbox>
            </el-checkbox-group>

            <el-select v-if="/^select$/i.test(type)" v-model="formData[dataIndex]" v-bind="attrs">
              <el-option
                v-for="{ label, value } in options"
                :key="value"
                :label="label"
                :value="value"
              >
              </el-option>
            </el-select>

            <el-select-v2
              v-if="/^selectV2$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
              :options="options"
            />

            <el-cascader
              v-if="/^cascader$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
              :options="options"
            ></el-cascader>

            <el-switch
              v-if="/^switch$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
            ></el-switch>

            <el-slider
              v-if="/^slider$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
            ></el-slider>

            <el-time-picker
              v-if="/^timePicker$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
            >
            </el-time-picker>

            <el-time-select
              v-if="/^timeSelect$/i.test(type)"
              v-model="formData[dataIndex]"
              v-bind="attrs"
            >
            </el-time-select>

            <el-date-picker
              v-if="
                /^(year|month|date|dates|week|datetime|datetimerange|daterange|monthrange)$/i.test(
                  type
                )
              "
              v-model="formData[dataIndex]"
              v-bind="attrs"
              :type="type"
            >
            </el-date-picker>
          </template>
        </el-form-item>
      </el-col>
    </el-row>

    <template v-if="showFooter">
      <slot name="footer"></slot>
    </template>
  </el-form>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'

const defaultSpan = 24

export default defineComponent({
  name: 'Form',

  props: {
    modelValue: {
      type: Object,
      required: true
    },
    span: {
      type: Number,
      default: defaultSpan
    },
    gutter: {
      type: Number,
      default: 0
    },
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    attrs: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit, slots }) {
    const formVm = ref()
    const showHeader = computed(() => slots.header)
    const showFooter = computed(() => slots.footer)
    const rows = computed(() => Math.floor(defaultSpan / props.span))
    const rowItems = computed(() =>
      props.items.reduce((pre: any[], cur: any, idx) => {
        if (idx % rows.value) {
          pre[pre.length - 1].push(cur)
        } else {
          pre.push([cur])
        }
        return pre
      }, [])
    )
    const formData = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      }
    })
    // * 表单校验
    const validate = (callback: Function) => {
      formVm.value.validate(callback)
    }
    const validateField = (fields: string[] | string, callback: Function) => {
      formVm.value.validateField(fields, callback)
    }
    const resetFields = () => {
      formVm.value.resetFields()
    }
    const clearValidate = (fields?: string[] | string) => {
      formVm.value.clearValidate(fields)
    }

    return {
      formVm,
      showHeader,
      showFooter,
      rows,
      rowItems,
      formData,
      validate,
      validateField,
      resetFields,
      clearValidate
    }
  }
})
</script>

<style lang="scss" scoped></style>
