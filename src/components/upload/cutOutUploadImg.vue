<template>
  <div class="image">
    <div class="select">
      <div v-if="baseHeadBannerUrl" class="banner-img">
        <img :src="baseHeadBannerUrl" alt="" />
        <span @click="handleRemoveBannerSrc" class="remove-img-btn"></span>
      </div>
      <el-upload
        v-else
        ref="bannerRef"
        accept="image/jpg,image/png,image/jpeg"
        :style="{ width: width, height: height }"
        :action="action"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleBannerChange"
        :data="{ uploadType: 'banner' }"
      >
        <el-button type="primary">上传图片</el-button>
      </el-upload>

      <span class="tips">{{ uploadText }}</span>
    </div>
  </div>
  <el-dialog
    title="图片剪裁"
    append-to-body
    v-model="visible"
    width="80%"
    top="8vh"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="cropper-warp">
      <div class="cropper">
        <img :src="bannerBase64" class="cropper-img" />
      </div>
      <div class="cropper-preview">
        <div class="title">预览</div>
        <div class="company-bg-preview"></div>
      </div>
    </div>

    <template #footer>
      <div class="center">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref, nextTick } from 'vue'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'

import { uploadBase64Img } from '/@/api/upload'

const emits = defineEmits(['update:fileId', 'update:fullUrl'])

const props = defineProps({
  width: {
    type: String,
    default: () => '200px'
  },

  height: {
    type: String,
    default: () => '200px'
  },

  uploadText: {
    type: String,
    default: () => '支持png、jpg、jpge格式'
  },

  fileId: {
    type: String,
    default: () => ''
  },

  action: {
    type: String,
    default: () => '/upload/base64'
  },

  widthSize: {
    type: Number,
    default: () => 488
  },

  heightSize: {
    type: Number,
    default: () => 258
  },

  fullUrl: {
    type: String,
    default: () => ''
  }
})

const visible = ref(false)
const submitLoading = ref(false)

const bannerBase64 = ref('')

const bannerRef = ref()

const fileId = computed({
  get() {
    return props.fileId
  },
  set(value) {
    emits('update:fileId', value)
  }
})

const baseHeadBannerUrl = computed({
  get() {
    return props.fullUrl
  },
  set(value) {
    emits('update:fullUrl', value)
  }
})

const closeDialog = () => {
  visible.value = false
}

const handleSubmit = async () => {
  submitLoading.value = true
  const { fullUrl, id } = await uploadBase64Img(bannerBase64.value)
  baseHeadBannerUrl.value = fullUrl
  fileId.value = id
  submitLoading.value = false

  visible.value = false
}

// 初始化cropperjs图片裁剪
const initCropper = () => {
  const proportion = props.widthSize / props.heightSize

  const letImg: any = document.querySelector('.cropper-img')

  const cropper = new Cropper(letImg, {
    viewMode: 2,
    center: true,
    dragMode: 'move',
    initialAspectRatio: proportion,
    aspectRatio: proportion,
    autoCropArea: 0.8,
    zoomOnWheel: false,
    preview: '.company-bg-preview',
    ready: () => {
      bannerBase64.value = cropper.getCroppedCanvas().toDataURL('image/jpeg')
    },
    cropend: () => {
      bannerBase64.value = cropper.getCroppedCanvas().toDataURL('image/jpeg')
    }
  })
}

const handleBannerChange = (file) => {
  if (file.status === 'ready') {
    const reader = new FileReader()
    reader.readAsDataURL(file.raw)
    reader.onload = (e) => {
      const { target } = <any>e
      visible.value = true
      bannerBase64.value = <string>target.result
      nextTick(() => {
        initCropper()
      })
    }
  }
}

const handleRemoveBannerSrc = () => {
  baseHeadBannerUrl.value = ''
  fileId.value = ''
}
</script>

<style lang="scss" scoped>
.select {
  display: flex;
  flex-direction: column;

  .tips {
    color: rgba($color: #333, $alpha: 0.8);
    margin-left: 8px;
    display: flex;
    align-items: center;
  }
}

.remove-img-btn {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba($color: #333, $alpha: 0.6);
  right: 8px;
  top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 2px;
    border-radius: 2px;
    background: #fff;
    transform: rotate(45deg);
  }
  &::after {
    transform: rotate(-45deg);
  }
}

.cropper-main {
  display: flex;
  align-items: center;
}

.banner-img {
  width: 100%;
  height: auto;
  position: relative;
  margin-top: 20px;
  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.style {
  .content {
    display: flex;
    flex-wrap: wrap;
    .list {
      margin-top: 20px;
      width: 160px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      margin-right: 16px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
.banner-img-mobile {
  margin-left: 20px;
  width: 400px;
  height: 80px;
  position: relative;
  margin-top: 20px;
  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.cropper-warp {
  .cropper {
    height: 350px;
    border: var(--el-border-base);
    background: var(--color-whites);
    overflow: hidden;
    background-repeat: no-repeat;
    cursor: move;
    border-radius: var(--el-border-radius-base);
    .cropper-img {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
  .cropper-preview {
    .title {
      padding: 20px 0 15px;
    }
    .company-bg-preview {
      width: 600px;
      height: 115px;
      overflow: hidden;
      margin: 0 auto;
    }
  }
}
.cropper-mobile {
  margin-left: 20px;
  .cropper-mobile-img {
    height: 250px;
    border: var(--el-border-base);
    background: var(--color-whites);
    overflow: hidden;
    background-repeat: no-repeat;
    cursor: move;
    border-radius: var(--el-border-radius-base);
    .cropper-img-mobile {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
}
.cropper-mobile-preview {
  .title {
    padding: 20px 0 15px;
  }
  .preview-mobile {
    width: 300px;
    height: 115px;
    overflow: hidden;
  }
}
</style>
