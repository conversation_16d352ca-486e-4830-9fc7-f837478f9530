<template>
  <el-upload
    class="uploader"
    :disabled="disabled"
    :style="{ width: width, height: height }"
    :action="action"
    :show-file-list="false"
    drag
    :on-success="handleAvatarSuccess"
    :before-upload="beforeAvatarUpload"
    v-loading="loading"
  >
    <template v-if="url">
      <el-image class="img" :src="url" :fit="fit" />
      <i class="el-icon-close" @click="handleClear"></i>
    </template>

    <slot v-else>
      <i class="el-icon-plus uploader-icon"></i>
      <div class="opacity-80" v-html="uploadText"></div>
    </slot>
  </el-upload>
</template>
<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'uploadsingleImage',
  props: {
    width: {
      type: String,
      default: () => '360px'
    },
    height: {
      type: String,
      default: () => '180px'
    },
    uploadText: {
      type: String,
      default: () => '点击上传图片'
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    action: {
      type: String,
      default: () => '/upload/image'
    },
    maxSize: {
      type: Number,
      default: () => 10
    },
    fit: {
      type: String,
      default: 'cover'
    },
    modelValue: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },

  emits: ['update:modelValue', 'update:id', 'success'],
  setup(props, { emit }) {
    const loading = ref(false)
    const url = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      }
    })
    const handleAvatarSuccess = (r: any) => {
      if (r.result === 1) {
        // 上传成功
        url.value = r.data.fullUrl
        emit('success', r.data)
        emit('update:id', r.data?.id)
        loading.value = false
      } else {
        // 上传失败
        ElMessage.error(r.msg)
        loading.value = false
      }
    }
    const beforeAvatarUpload = (file: any) => {
      const typeMap = ['image/jpg', 'image/gif', 'image/png', 'image/jpeg']
      const { type } = file
      if (!typeMap.includes(type)) {
        ElMessage.error('图片格式有误')
        return false
      }
      const { maxSize } = props as any
      const size = file.size / 1024 / 1024
      if (maxSize < size) {
        ElMessage.error(`大小不能超过${maxSize}M`)
        return false
      }

      loading.value = true
      return true
    }

    const handleClear = (e) => {
      e.stopPropagation()
      url.value = ''
      emit('update:id', '')
    }

    return {
      handleAvatarSuccess,
      beforeAvatarUpload,
      handleClear,
      loading,
      url
    }
  }
})
</script>
<style lang="scss">
.el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
  }
  .el-upload-dragger {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 10px;
    top: 10px;
    background: #aaa;
    padding: 3px;
    border-radius: 50%;
    color: #ffff;
    font-weight: bold;
  }
  .uploader-icon {
    font-size: 28px;
    color: #ccc;
    text-align: center;
  }
  &:hover .uploader-icon {
    color: var(--color-primary);
  }
}
</style>
