<template>
  <el-upload
    class="uploader"
    :style="{ width: width, height: height }"
    :action="action"
    :show-file-list="false"
    drag
    :on-success="handleAvatarSuccess"
    :before-upload="beforeAvatarUpload"
    v-loading="loading"
  >
    <div class="img-box" v-if="imgUrl">
      <el-image class="img" :src="imgUrl" fit="contain" />
      <i class="el-icon-plus el-icon-circle-close icon" @click.stop="handleDelete"></i>
    </div>
    <slot v-else>
      <i class="el-icon-plus uploader-icon"></i>
      <div class="opacity-80">{{ uploadText }}</div>
    </slot>
  </el-upload>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  width: {
    type: String,
    default: () => '360px'
  },

  height: {
    type: String,
    default: () => '180px'
  },

  uploadText: {
    type: String,
    default: () => '点击上传图片'
  },

  action: {
    type: String,
    default: () => '/upload/image'
  },

  maxSize: {
    type: Number,
    default: () => 10
  },

  fileId: {
    type: String,
    default: ''
  },

  fullUrl: {
    type: String,
    default: () => ''
  }
})

const emits = defineEmits(['update:fileId', 'update:fullUrl'])

const loading = ref(false)
const imgUrl = computed({
  get() {
    return props.fullUrl
  },
  set(value) {
    emits('update:fullUrl', value)
  }
})

const fileId = computed({
  get() {
    return props.fileId
  },
  set(value) {
    emits('update:fileId', value)
  }
})

const handleAvatarSuccess = (r: any) => {
  if (r.result === 1) {
    // 上传成功
    imgUrl.value = r.data.fullUrl
    fileId.value = r.data.id

    loading.value = false
  } else {
    // 上传失败
    ElMessage.error(r.msg)
    loading.value = false
  }
}

const handleDelete = () => {
  imgUrl.value = ''
  fileId.value = ''
}

const beforeAvatarUpload = (file: any) => {
  const typeMap = ['image/jpg', 'image/gif', 'image/png', 'image/jpeg']
  const { type } = file
  if (!typeMap.includes(type)) {
    ElMessage.error('图片格式有误')
    return false
  }
  const { maxSize } = props as any
  const size = file.size / 1024 / 1024
  if (maxSize < size) {
    ElMessage.error(`大小不能超过${maxSize}M`)
    return false
  }

  loading.value = true
  return true
}
</script>
<style lang="scss">
.el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
  }

  .img-box {
    position: relative;

    .icon {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 26px;
    }
  }

  .el-upload-dragger {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  .uploader-icon {
    font-size: 28px;
    color: #ccc;
    text-align: center;
  }
  &:hover .uploader-icon {
    color: var(--color-primary);
  }
}
</style>
