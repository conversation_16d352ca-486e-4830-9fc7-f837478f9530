<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleChange"
    placeholder="请选择适用平台"
    style="width: 100%"
  >
    <el-option
      v-for="option in platformOptions"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    />
  </el-select>
</template>

<script lang="ts" setup>
interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

const emit = defineEmits<Emits>()

// 平台选项
const platformOptions = [
  { label: '全平台', value: 'ALL' },
  { label: 'PC端', value: 'PC' },
  { label: 'H5端', value: 'H5' },
  { label: '小程序', value: 'MINI' }
]

// 处理值变化
const handleChange = (value: string) => {
  emit('update:modelValue', value)
}
</script>
