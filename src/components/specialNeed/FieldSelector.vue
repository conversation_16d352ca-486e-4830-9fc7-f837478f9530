<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleChange"
    placeholder="请选择字段名称"
    filterable
    :loading="loading"
    style="width: 100%"
  >
    <el-option
      v-for="(label, value) in options"
      :key="value"
      :label="label"
      :value="value"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { getFieldOptions } from '/@/api/specialNeed'

interface Props {
  modelValue?: string
  configType?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  configType: ''
})

const emit = defineEmits<Emits>()

const options = ref<Record<string, string>>({})
const loading = ref(false)

// 加载字段选项
const loadOptions = async (type: string) => {
  if (!type) {
    options.value = {}
    return
  }
  
  loading.value = true
  try {
    const res = await getFieldOptions({ type })
    options.value = res || {}
  } catch (error) {
    console.error('获取字段选项失败:', error)
    options.value = {}
  } finally {
    loading.value = false
  }
}

// 处理值变化
const handleChange = (value: string) => {
  emit('update:modelValue', value)
}

// 监听配置类型变化
watch(
  () => props.configType,
  (newType) => {
    loadOptions(newType)
  },
  { immediate: true }
)

onMounted(() => {
  if (props.configType) {
    loadOptions(props.configType)
  }
})
</script>
