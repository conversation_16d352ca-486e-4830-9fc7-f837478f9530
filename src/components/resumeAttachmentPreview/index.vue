<template>
  <div id="pdf-container" v-loading="loadLoading">
    <!-- <canvas id="pdf-canvas"></canvas> -->
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import * as PdfJs from 'pdfjs-dist/legacy/build/pdf.js' // 注意导入的写法
import { nextTick, onMounted, reactive, toRefs, defineComponent } from 'vue'

export default defineComponent({
  name: 'resumeAttachmentPreview',
  props: {
    resumeId: {
      type: String,
      default: () => ''
    },
    token: {
      type: String,
      default: () => ''
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const state = reactive({
      loadLoading: false
    })
    let pdfDoc: any = '' // 保存加载的pdf文件流
    // eslint-disable-next-line
    let pdfPages = 0
    const pdfScale = 1.0
    let numPages = 0

    const renderPage = (num: any) => {
      pdfDoc.getPage(num).then((page: any) => {
        const canvas: any = document.createElement('canvas')
        const ctx: any = canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const bsr =
          ctx.webkitBackingStorePixelRatio ||
          ctx.mozBackingStorePixelRatio ||
          ctx.msBackingStorePixelRatio ||
          ctx.oBackingStorePixelRatio ||
          ctx.backingStorePixelRatio ||
          1
        const ratio = dpr / bsr
        const viewport = page.getViewport({ scale: pdfScale })
        canvas.width = viewport.width * ratio
        canvas.height = viewport.height * ratio
        ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
        const renderContext = {
          canvasContext: ctx,
          viewport
        }
        page.render(renderContext)
        document.getElementById('pdf-container').appendChild(canvas)
        if (num < numPages) {
          renderPage(num + 1)
        } else {
          state.loadLoading = false
        }
      })
    }

    const loadFile = async () => {
      PdfJs.GlobalWorkerOptions.workerSrc = await import('pdfjs-dist/build/pdf.worker.entry')
      const loadingTask = PdfJs.getDocument(
        `/person/resume-attachment-preview?resumeId=${props.resumeId}&token=${props.token}`
      )
      console.log('pdfs')
      loadingTask.promise
        .then((pdf) => {
          console.log(pdf, 'pdf')
          numPages = pdf.numPages
          pdfDoc = pdf
          pdfPages = pdfDoc.numPages
          nextTick(() => {
            renderPage(1)
          })
        })
        .catch(() => {
          ElMessage.error('文件不存在或加载错误')
          emit('close')
        })
    }

    // 页面加载时
    onMounted(() => {
      state.loadLoading = true
      loadFile()
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.pdf-container {
  height: 100%;
}
.paging {
  margin-top: 30px;
}
</style>
