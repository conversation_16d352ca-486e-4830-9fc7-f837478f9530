import WangEditor from 'wangeditor'

const { $, BtnMenu } = WangEditor

class CustomUploadFile extends BtnMenu {
  constructor(editor) {
    // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
    const $elem = $(
      `
        <div class="w-e-menu" data-title="文件上传">
          <i class="el-icon el-icon-upload"></i>
        </div>
      `
    )
    super($elem, editor)
  }

  // 菜单点击事件
  public clickHandler() {
    const {
      editor: {
        config: { customUpload }
      }
    }: any = this

    customUpload.call(this)
  }

  // eslint-disable-next-line class-methods-use-this
  public tryChangeActive() {
    //
  }
}

export default CustomUploadFile
