<template>
  <div class="editor-container" v-loading="loading">
    <el-upload
      v-show="false"
      :on-success="uploadSuccess"
      :on-progress="uploadLoading"
      action="upload/file"
      :on-error="uploadError"
    >
      <span ref="uploadRef"></span>
    </el-upload>

    <div ref="editorRef" class="flex-1"></div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, unref, onMounted } from 'vue'
import { watchOnce } from '@vueuse/shared'

import { ElMessage } from 'element-plus'

import WangEditor from 'wangeditor'
import CustomUploadFile from './extendEditor'

export default {
  name: 'wangEditor',

  props: {
    modelValue: {
      type: String,
      required: true
    },
    showCustomUploadFile: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    }
  },

  setup(props, { emit }) {
    const uploadRef = ref()
    const editorRef = ref()

    const state: any = reactive({
      loading: false,

      editorInstance: null,

      initEditor() {
        if (props.showCustomUploadFile) {
          // 注册菜单
          WangEditor.registerMenu('customUploadFile', CustomUploadFile)
        }

        state.editorInstance = new WangEditor(unref(editorRef))

        state.editorInstance.config = {
          ...state.editorInstance.config,
          pasteFilterStyle: false,
          placeholder: props.placeholder,
          uploadImgServer: '/upload/editor-image',
          pasteIgnoreImg: true,
          height: props.height
        }

        state.editorInstance.config.customUpload = () => {
          unref(uploadRef).click()
        }

        state.editorInstance.config.onchange = (html: string) => {
          emit('update:modelValue', html)
        }

        state.editorInstance.create()
      },

      updateEditor(val) {
        state.editorInstance.txt.html(val)
      },

      clearEditor() {
        state.updateEditor('')
      },

      uploadSuccess(val: any) {
        if (val.msg.length) {
          ElMessage.error(val.msg)
          return
        }

        const { data } = val
        const fileFullPath = `<a href="${data.fullUrl}">${data.name}</a>`

        state.loading = false
        ElMessage.success('上传成功')
        state.editorInstance.txt.append(fileFullPath)
      },

      uploadLoading(uploadProgressEvent: any) {
        if (uploadProgressEvent.percent !== 100) state.loading = true
      },

      uploadError(error: any) {
        ElMessage.error(error)
      }
    })

    watchOnce(
      () => props.modelValue,
      (val) => {
        if (val === '<p><br/></p>') return
        state.editorInstance.txt.html(val)
      }
    )

    onMounted(() => {
      state.initEditor()
    })

    return { ...toRefs(state), uploadRef, editorRef } as any
  }
}
</script>

<style scoped lang="scss"></style>
