/* eslint-disable class-methods-use-this */
import { <PERSON><PERSON>, I<PERSON>uttonMenu, IDomEditor } from '@wangeditor/editor'

class CustomUploadButtonMenu implements IButtonMenu {
  constructor() {
    this.title = '上传文件' // 自定义菜单标题
    this.iconSvg =
      '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-029747aa=""><path fill="currentColor" d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"></path></svg>' // 可选
    this.tag = 'button'
  }

  title: string

  iconSvg?: string | undefined

  hotkey?: string | undefined

  alwaysEnable?: boolean | undefined

  tag: string

  width?: number | undefined

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(): string | boolean {
    return false
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(): boolean {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(): boolean {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor: IDomEditor | any) {
    editor?.customUploadFile()
  }
}

const customUploadMenuConfig = {
  key: 'custom-upload-file',
  factory() {
    return new CustomUploadButtonMenu()
  }
}

Boot.registerMenu(customUploadMenuConfig)
