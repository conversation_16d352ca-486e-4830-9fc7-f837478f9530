<template>
  <div>
    <el-dialog v-model="jobDetailDialog" title="职位详情" width="70%">
      <el-row>
        <el-col :span="5" class="name">{{ baseInfo.name }}</el-col>
        <el-col :span="18" class="salary">{{ baseInfo.wage }}</el-col>
      </el-row>
      <el-row>
        {{ baseInfo.companyName }}
      </el-row>
      <el-row class="info">
        <el-col :span="3">{{ baseInfo.areaName }}</el-col>
        <el-col :span="3">{{ baseInfo.experienceTypeTitle }}</el-col>
        <el-col :span="2">{{ baseInfo.educationTypeTitle }}</el-col>
        <el-col :span="2">招{{ baseInfo.amount }}人</el-col>
        <el-col :span="6">{{ baseInfo.releaseTime }}发布</el-col>
        <el-col :span="8">
          <el-tag v-for="item in baseInfo.welfareTage" :key="item.k">{{ item.v }}</el-tag>
        </el-col>
      </el-row>
      <el-row class="title">职位详情</el-row>
      <el-row class="base">基本信息</el-row>
      <el-row>
        <el-col :span="12">职位名称：{{ baseInfo.name }}</el-col>
        <el-col :span="12">职位类型：{{ baseInfo.jobCategoryTitle }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">用人部门：{{ baseInfo.department }}</el-col>
        <el-col :span="12">工作性质：{{ baseInfo.natureTypeTitle }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">工作地点：{{ baseInfo.areaName }}</el-col>
        <el-col :span="12">招聘人数：{{ baseInfo.amount }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">投递类型：{{ baseInfo.deliveryTypeTxt }}</el-col>
        <el-col :span="12">截止日期：{{ baseInfo.periodDate }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">职位编制：{{ baseInfo.establishmentTxt }}</el-col>
      </el-row>
      <el-row class="base">其他要求</el-row>
      <el-row class="info">
        <el-col :span="12" class="major">专业要求：{{ baseInfo.majorTitle }}</el-col>
        <el-col :span="12">学历要求：{{ baseInfo.educationTypeTitle }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">职称要求：{{ baseInfo.titleTypeTitle }}</el-col>
        <el-col :span="12">工作经验：{{ baseInfo.experienceTypeTitle }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">年龄要求：{{ baseInfo.ageType }}</el-col>
        <el-col :span="12">政治面貌：{{ baseInfo.politicalTypeTitle }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="12">海外经历：{{ baseInfo.abroadTypeTitle }}</el-col>
        <el-col :span="12">职位编制：{{ baseInfo.establishmentTxt }}</el-col>
      </el-row>
      <el-row class="base">岗位职责</el-row>
      <el-row>{{ baseInfo.duty }}</el-row>

      <el-row class="base">任职要求</el-row>
      <el-row>{{ baseInfo.requirement }}</el-row>

      <el-row class="base">其他说明</el-row>
      <el-row>{{ baseInfo.remark }}</el-row>

      <div class="base" v-if="baseInfo.companyDeliveryType === '2'">
        <el-row>投递通知邮箱</el-row>
        <el-row>{{ baseInfo.extraNotifyAddress }}</el-row>
      </div>

      <el-row class="base">报名方式：</el-row>
      <el-row>报名方式：{{ baseInfo.applyTypeTitle }}</el-row>
      <el-row>投递地址：{{ baseInfo.applyAddress }}</el-row>

      <el-row class="base">投递限制：{{ baseInfo.deliveryLimitTypeTxt }}</el-row>
      <JobAttachment v-if="baseInfo.fileList?.length" :fileList="baseInfo.fileList" />

      <el-row class="base">职位协同子账号</el-row>

      <table class="info-table" border="1" v-if="baseInfo.jobContactSynergy?.length">
        <thead>
          <tr>
            <td class="table-title">账号ID</td>
            <td class="table-title">姓名</td>
            <td class="table-title">所在部门</td>
            <td class="table-title">邮箱</td>
            <td class="table-title">手机号</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in baseInfo.jobContactSynergy" :key="item.id">
            <td>{{ item.memberId }}</td>
            <td>{{ item.contact }}</td>
            <td>{{ item.department }}</td>
            <td>{{ item.email }}</td>
            <td>{{ item.mobile }}</td>
          </tr>
        </tbody>
      </table>

      <el-row class="base">职位联系人</el-row>
      <div class="flex">
        <span class="mr-5">{{ baseInfo.jobContact?.companyMemberType === '0' ? '主' : '子' }}</span>
        <div>{{ baseInfo.jobContact?.contact }}</div>
        <div v-if="baseInfo.jobContact?.department">/{{ baseInfo.jobContact?.department }}</div>
      </div>
      <div class="flex">
        <div v-if="baseInfo.jobContact?.email" class="mr-10">{{ baseInfo.jobContact?.email }}</div>
        <div>{{ baseInfo.jobContact?.mobile }}</div>
      </div>

      <Paging
        v-if="announcementId"
        :total="page.count"
        layout="total,prev, pager, next"
        @change="handelChange"
        :defaultPageSize="1"
        :page="page.page"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getJobDetailList } from '/@/api/announcement'
import { getJobDetails } from '/@/api/job'
import Paging from '/@/components/base/paging.vue'
import JobAttachment from '/@/views/cms/announcement/component/jobAttachment.vue'

export default defineComponent({
  name: 'jobDetailDialog',

  components: {
    Paging,
    JobAttachment
  },

  setup() {
    const state = reactive({
      jobDetailDialog: false,
      baseInfo: <any>{},
      page: {
        count: 1,
        page: 1
      },
      announcementId: ''
    })
    const getData = async (id: string, currentPage: number) => {
      if (currentPage) {
        state.page.page = currentPage
        state.announcementId = id
        const jobData = { id, page: state.page.page }
        const { list, page } = await getJobDetailList(jobData)
        state.baseInfo = <any>list[0]
        state.page = page
        return
      }
      state.baseInfo = await getJobDetails({ id })
    }
    const open = (id: string, currentPage: number) => {
      state.jobDetailDialog = true
      getData(id, currentPage)
    }

    const handelChange = (data: any) => {
      state.page.page = data.page
      getData(state.announcementId, state.page.page)
    }
    return {
      ...toRefs(state),
      open,
      handelChange
    }
  }
})
</script>

<style lang="scss" scoped>
.name {
  font-weight: bold;
  font-size: 16px;
  margin: 10px 0;
}
.title {
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.322);
  padding: 0px 5px 10px 0px;
  margin-bottom: 15px;
}
.info {
  margin: 10px 0;
}

.info-table {
  margin: 20px;
  color: #606266;
  border-collapse: collapse;
  border-color: rgba(#909399, 0.3);

  td {
    padding: 10px;
  }

  .table-title {
    background-color: #f3f6f9;
  }
}

.salary {
  margin: 10px 0;
  color: red;
}
.base {
  font-weight: bold;
  margin: 10px 0;
}
.major {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-dialog) {
  --el-dialog-margin-top: 5vh;
}
:deep(.el-dialog__body) {
  min-height: 900px;
}
</style>
