<template>
  <div class="synergy">
    <el-form-item label="职位协同设置">
      <div class="check">
        <div class="content-box">
          <el-tooltip trigger="click" effect="light" ref="contentRef" popper-class="account-popper">
            <template #default>
              <el-input
                class="name"
                v-model="query"
                @input="handleSearch"
                :placeholder="accountPlaceholder"
              >
                <template #prefix v-if="accountText?.length">
                  <el-tag class="text" v-for="(item, index) in accountText" :key="index">{{
                    item
                  }}</el-tag>
                </template>
                <template #suffix>
                  <span class="el-icon el-select__caret el-select__icon"></span>
                </template>
              </el-input>
            </template>

            <template #content>
              <div class="content-list">
                <div class="list">
                  <el-checkbox-group class="content" v-model="form.jobContactSynergyIds" :max="3">
                    <el-checkbox
                      class="checked"
                      v-for="item in (list as any)"
                      :label="item.id"
                      :key="item.id"
                      :disabled="item.companyMemberType === '0'"
                    >
                      <div
                        class="title-name"
                        :class="[item.companyMemberType === '0' ? 'disabled' : '']"
                      >
                        <span class="tag-box" v-if="item.companyMemberType === '0'">
                          <span class="tag parent">主</span>
                        </span>

                        <span class="tag-box" v-else>
                          <span class="tag son">子</span>
                        </span>
                        <el-popover placement="top-start" :content="item.contactText">
                          <template #reference>
                            <span class="contact-name">{{ item.contactText }}</span>
                          </template>
                        </el-popover>
                        <div class="email" v-if="item.companyMemberType === '0'">
                          <span v-if="item.email">{{ item.email }}</span>
                          <span v-if="item.mobile">
                            {{ item.mobile }}
                          </span>
                        </div>
                        <div class="email" v-else>
                          <span v-if="item.email">{{ item.email }}</span>
                        </div>
                      </div>
                    </el-checkbox>
                  </el-checkbox-group>
                  <div class="button">
                    <div class="point">主账号/职位发布账号默认可全权管理当前职位</div>
                    <el-button @click="reset">重置</el-button>
                    <el-button type="primary" @click="confirm">确定</el-button>
                  </div>
                </div>
              </div>
            </template>
          </el-tooltip>
          <div class="account-disabled" v-if="accountDisabled" @click="handleAccountClick">
            请选择协同子账号，共同管理职位&简历
          </div>
        </div>
        <el-button
          class="ml-5"
          type="primary"
          v-if="identifyShow"
          @click="handleIdentify"
          :disabled="accountDisabled"
          >识别</el-button
        >
      </div>
    </el-form-item>
    <el-form-item label="职位联系人" prop="jobContactId" :rules="contactRules">
      <el-select
        style="width: 380px"
        v-model="form.jobContactId"
        popper-class="contact-select contact-select-job-contact"
      >
        <template #prefix v-if="form.jobContactId">
          <span class="tag-box" v-if="isMainAccount">
            <span class="tag parent">主</span>
          </span>

          <span class="tag-box" v-else>
            <span class="tag son">子</span>
          </span>
        </template>
        <el-option
          v-for="item in (contactList as any)"
          :key="item.id"
          :value="item.id"
          :label="item.contact"
          class="checked"
        >
          <div class="title-name">
            <span class="tag-box" v-if="item.companyMemberType === '0'">
              <span class="tag parent">主</span>
            </span>

            <span class="tag-box" v-else>
              <span class="tag son">子</span>
            </span>
            <span class="contact-name">{{ item.contactText }}</span>
            <div class="email" v-if="item.companyMemberType === '0'">
              <span v-if="item.email">{{ item.email }}</span>
              <span v-if="item.mobile">
                {{ item.mobile }}
              </span>
            </div>
            <div class="email" v-else>
              <span v-if="item.email">{{ item.email }}</span>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, defineComponent, nextTick, reactive, ref, toRefs, watch } from 'vue'
import { searchJobAccount, identifyMemberAccount } from '/@/api/job'

export default defineComponent({
  name: 'jobCooperate',

  props: {
    companyId: {
      required: true
    },

    hasAccount: {
      type: Boolean,
      required: false
    },

    modelValue: {
      type: Object,
      default: () => {}
    },

    email: {
      type: String
    },

    identifyShow: {
      type: Boolean,
      default: false
    }
  },

  components: {},

  setup(props, { emit }) {
    const contentRef = ref()

    const contactRules = ref([{ required: true, message: '请选择职位联系人', trigger: 'blur' }])

    const state = reactive({
      query: '',

      form: {
        jobContactSynergyIds: computed({
          get() {
            return props.modelValue.jobContactSynergyIds
          },
          set(val: string[]) {
            emit('update:modelValue', { ...props.modelValue, jobContactSynergyIds: val })
          }
        }),
        jobContactId: computed({
          get() {
            return props.modelValue.jobContactId
          },
          set(val: string) {
            emit('update:modelValue', { ...props.modelValue, jobContactId: val })
          }
        })
      },

      list: [],
      integrityList: [],
      accountText: [],
      contactList: [],

      accountPlaceholder: computed(() =>
        state.accountText?.length ? '' : '请选择协同子账号，共同管理职位&简历'
      ),

      accountDisabled: computed(() => props.companyId === '' || !props.hasAccount),
      isFocus: computed(() => contentRef.value?.isFocusInsideContent()),
      checkList: computed(() => [...state.form.jobContactSynergyIds, state.mainAccountId]),
      isMainAccount: computed(() => state.form.jobContactId === state.mainAccountId),

      mainAccountId: '',
      contactMemberType: ''
    })

    const getCheckContactList = () => {
      state.contactList = state.integrityList.filter((item: any) => {
        return state.checkList.find((i: any) => i === item.id)
      })
    }

    const reset = () => {
      state.form.jobContactSynergyIds = []
    }

    const handleSearch = async () => {
      state.list = await searchJobAccount({ companyId: props.companyId, contact: state.query })
      contentRef.value?.onOpen()
    }

    const checkAccountText = () => {
      state.accountText = state.form.jobContactSynergyIds.map((item: any) => {
        return state.integrityList.find((i: any) => i.id === item).contact
      })

      state.query = ''
    }

    const getContactList = async () => {
      if (props.companyId === '' || props.companyId === undefined) return

      const res = await searchJobAccount({ companyId: props.companyId, contact: state.query })
      state.integrityList = res
      state.list = res

      state.mainAccountId = state.list.find((item: any) => item.companyMemberType === '0').id
      state.form.jobContactId =
        state.form.jobContactId !== '' ? state.form.jobContactId : state.mainAccountId

      getCheckContactList()

      checkAccountText()
    }

    const confirm = () => {
      getCheckContactList()

      const mainChecked = state.checkList.findIndex((item: any) => item === state.form.jobContactId)

      if (mainChecked === -1) {
        ElMessageBox.confirm(
          '本次操作，系统将自动变更职位发布账号为职位联系人，是否继续操作？（您可在“职位联系人”设置中手动操作变更）',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            center: true
          }
        )
          .then(() => {
            state.form.jobContactId = state.mainAccountId
            state.contactMemberType = '0'
            if (state.form.jobContactSynergyIds.length === 0) {
              checkAccountText()
              return
            }
            checkAccountText()
            handleSearch()
          })
          .catch(() => {})
        return
      }
      checkAccountText()
      handleSearch()

      setTimeout(() => {
        contentRef.value?.onClose()
      }, 300)
    }

    watch(
      () => props.companyId,
      (val) => {
        if (val !== '') {
          getContactList()
        }
      }
    )

    //  此处需要监听焦点是否在el-tooltip-content，判断类型是否为boolean，若为undefined则是离开焦点
    watch(
      () => state.isFocus,
      (val) => {
        if (typeof val === 'undefined') {
          //  对比jobContactSynergyIds,若不一致则返回原来的值
          if (state.form.jobContactSynergyIds?.length !== state.accountText.length) {
            state.form.jobContactSynergyIds = state.accountText.map((item: any) => {
              return state.list.find((i: any) => i.contact === item).id
            })
          }
        }
      }
    )

    const handleAccountClick = () => {
      if (!props.hasAccount) {
        ElMessage.warning('请先添加子账号！')
      }

      if (props.companyId === '') {
        ElMessage.warning('请先选择单位！')
      }
    }

    const handleIdentify = async () => {
      if (!props.email) {
        ElMessage.warning('请先填写邮箱')
        return
      }

      const { identify, msg, contactIdentify } = <any>await identifyMemberAccount({
        applyAddress: props.email,
        companyId: props.companyId
      })

      state.form.jobContactSynergyIds = identify
      state.form.jobContactId = contactIdentify

      state.accountText = identify.map((item: any) => {
        return state.integrityList.find((i: any) => i.id === item).contact
      })

      const checked = [state.mainAccountId, ...identify]

      state.contactList = state.integrityList.filter((item: any) => {
        return checked.find((i: any) => i === item.id)
      })

      if (msg) {
        ElMessage.success(msg)
      }
    }

    nextTick(() => {
      getContactList()
    })

    return {
      ...toRefs(state),
      contactRules,
      contentRef,
      confirm,
      handleSearch,
      handleAccountClick,
      reset,
      handleIdentify
    } as any
  }
})
</script>

<style lang="scss" scoped>
@mixin ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.synergy {
  .check {
    display: flex;
    align-items: center;

    .name {
      width: 380px;

      .text {
        margin-right: 5px;
        padding: 5px;

        :deep(.el-tag__content) {
          max-width: 70px;
          @include ellipsis;
        }
      }
    }
  }

  .content-box {
    position: relative;
    .account-disabled {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      color: var(--el-disabled-text-color);
      padding: 1px 11px;
      background-color: var(--el-disabled-bg-color);
      border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
      border: 1px solid var(--el-border-radius-base);
      cursor: not-allowed;
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    }
  }
}
</style>

<style lang="scss">
@mixin ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
// 特殊处理
.contact-select-job-contact {
  .checked {
    display: flex;
    align-items: flex-start;
    height: auto;
    margin-bottom: 5px;
  }

  .title-name {
    font-size: 16px;
    max-width: 420px;
    @include ellipsis;

    .tag {
      padding: 3px;
      margin-right: 5px;
      border-radius: 4px;
    }
  }

  .email {
    color: var(--font-color-basic);
    font-size: 14px;
    line-height: 1.5;
    max-width: 420px;
    @include ellipsis;

    span {
      margin-right: 20px;
    }
  }
}
</style>

<style lang="scss">
@mixin ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.account-popper {
  .content-list {
    width: 450px;
    max-height: 300px;
    overflow: auto;
    padding-top: 20px;
    line-height: 1;
    background-color: var(--color-white);

    .content {
      position: relative;

      margin-bottom: 5px;

      .checked {
        display: flex;
        align-items: flex-start;
        min-height: 50px;
        margin-bottom: 10px;
      }

      .title-name {
        font-size: 16px;
        max-width: 420px;
        @include ellipsis;

        &.disabled {
          .contact-name,
          .email {
            opacity: 0.6;
          }
        }
      }

      .email {
        font-size: 14px;
        margin-top: 10px;
        max-width: 420px;
        @include ellipsis;
        span {
          margin-right: 20px;
        }
      }
    }

    .button {
      display: flex;
      align-items: center;
      position: sticky;
      width: 100%;
      bottom: 0;
      padding: 10px;
      z-index: 1;
      font-size: 14px;
      background-color: #fff;

      .el-button {
        width: 52px;
        height: 30px;
      }

      .point {
        flex: 1;
        display: flex;
        align-items: center;
        &::before {
          content: '';
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
    }
  }

  .tag {
    display: inline-block;
    padding: 3px;
    margin-right: 5px;
    border-radius: 4px;
  }

  .el-input__prefix {
    .tag {
      display: inline;
      width: 16px;
      height: 16px;
    }
  }
}
</style>
