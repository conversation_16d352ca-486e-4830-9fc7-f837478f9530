<template>
  <div class="image-tool">
    <el-upload
      class="image-uploader"
      drag
      :auto-upload="false"
      :on-change="handleImageChange"
      accept="image/*"
      :show-file-list="false"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖拽图片到此处或 <em>点击上传</em></div>
    </el-upload>

    <div v-if="currentImage" class="image-preview">
      <div class="preview-container">
        <div class="preview-item">
          <div class="preview-title">原始图片</div>
          <el-image
            :src="currentImage.url"
            fit="contain"
            :preview-src-list="previewImages"
            :preview-teleported="true"
            :initial-index="0"
          >
            <template #viewer="{ hide }">
              <div class="custom-preview-actions">
                <div class="preview-info">
                  <span class="preview-title">{{ getPreviewTitle }}</span>
                  <span class="preview-size">{{ getPreviewSize }}</span>
                </div>
                <div class="preview-actions">
                  <el-button-group>
                    <el-button
                      :type="currentPreviewIndex === 0 ? 'primary' : ''"
                      @click="switchPreviewImage(0)"
                      :disabled="!processedImage"
                    >
                      原始图片
                    </el-button>
                    <el-button
                      :type="currentPreviewIndex === 1 ? 'primary' : ''"
                      @click="switchPreviewImage(1)"
                      :disabled="!processedImage"
                    >
                      处理后图片
                    </el-button>
                  </el-button-group>
                  <el-button
                    v-if="processedImage && currentPreviewIndex === 1"
                    type="success"
                    @click="downloadProcessedImage"
                  >
                    下载处理后图片
                  </el-button>
                </div>
              </div>
            </template>
          </el-image>
          <div class="preview-size">{{ formatSize(currentImage.size) }}</div>
        </div>

        <div class="preview-item" v-if="processedImage">
          <div class="preview-title">处理后图片</div>
          <el-image
            :src="processedImage.dataUrl"
            fit="contain"
            :preview-src-list="previewImages"
            :preview-teleported="true"
            :initial-index="1"
          >
            <template #viewer="{ hide }">
              <div class="custom-preview-actions">
                <div class="preview-info">
                  <span class="preview-title">{{ getPreviewTitle }}</span>
                  <span class="preview-size">{{ getPreviewSize }}</span>
                </div>
                <div class="preview-actions">
                  <el-button-group>
                    <el-button
                      :type="currentPreviewIndex === 0 ? 'primary' : ''"
                      @click="switchPreviewImage(0)"
                      :disabled="!processedImage"
                    >
                      原始图片
                    </el-button>
                    <el-button
                      :type="currentPreviewIndex === 1 ? 'primary' : ''"
                      @click="switchPreviewImage(1)"
                      :disabled="!processedImage"
                    >
                      处理后图片
                    </el-button>
                  </el-button-group>
                  <el-button
                    v-if="processedImage && currentPreviewIndex === 1"
                    type="success"
                    @click="downloadProcessedImage"
                  >
                    下载处理后图片
                  </el-button>
                </div>
              </div>
            </template>
          </el-image>
          <div class="preview-size">
            {{ formatSize(processedSize) }}
            <span class="size-diff">({{ calculateSizeReduction }})</span>
          </div>
        </div>
      </div>

      <div class="process-controls">
        <div class="control-group">
          <div class="group-title">压缩设置</div>
          <el-form :model="compressOptions" label-width="80px" size="small">
            <el-form-item label="质量">
              <el-slider
                v-model="compressOptions.quality"
                :min="1"
                :max="100"
                :format-tooltip="formatQuality"
                @change="handleQualityChange"
              />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="imageUrl">
                <template #append>
                  <el-button-group>
                    <el-button @click="copyImageUrl">复制</el-button>
                    <el-button @click="insertImageUrl">插入公告</el-button>
                  </el-button-group>
                </template>
              </el-input>
            </el-form-item>

            <!-- <el-form-item label="最大宽度"> -->
            <!-- <el-input-number v-model="compressOptions.maxWidth" :min="100" :step="100" /> -->
            <!-- </el-form-item> -->
          </el-form>
          <el-button type="primary" @click="uploadProcessedImage">生成链接</el-button>
          <el-button type="success" @click="downloadProcessedImage">下载到电脑</el-button>
        </div>

        <!-- <div class="control-group">
          <div class="group-title">格式转换</div>
          <el-form :model="convertOptions" label-width="80px" size="small">
            <el-form-item label="目标格式">
              <el-select v-model="convertOptions.format">
                <el-option label="JPEG" value="jpeg" />
                <el-option label="PNG" value="png" />
                <el-option label="WEBP" value="webp" />
              </el-select>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="convertImage">转换预览</el-button>
        </div> -->
      </div>

      <!-- <div class="upload-section" v-if="processedImage"> -->
      <!-- <el-button type="success" @click="uploadImage">上传图片</el-button> -->
      <!-- </div> -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { uploadBase64Img } from '/@/api/upload'

export default defineComponent({
  name: 'ImageTool',
  setup() {
    const currentImage = ref<any>(null)
    const processedImage = ref<any>(null)
    const processedSize = ref<number>(0)
    const imageUrl = ref('')
    const currentPreviewIndex = ref(0)

    const compressOptions = ref({
      quality: 80,
      maxWidth: 1920
    })

    const convertOptions = ref({
      format: 'jpeg'
    })

    const calculateSizeReduction = computed(() => {
      if (!currentImage.value || !processedSize.value) return ''
      const reduction =
        ((currentImage.value.size - processedSize.value) / currentImage.value.size) * 100
      return `-${reduction.toFixed(1)}%`
    })

    const formatSize = (bytes: number) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
    }

    const handleImageChange = (file: any) => {
      currentImage.value = {
        file: file.raw,
        url: URL.createObjectURL(file.raw),
        size: file.raw.size
      }
      processedImage.value = null
      processedSize.value = 0
      imageUrl.value = ''

      // 直接调用压缩
      compressImage()
    }

    const formatQuality = (val: number) => {
      return `${val}%`
    }

    const compressImage = async () => {
      if (!currentImage.value) return

      const img = new Image()
      img.src = currentImage.value.url

      await new Promise((resolve) => {
        img.onload = resolve
      })

      const canvas = document.createElement('canvas')
      let width = img.width
      let height = img.height

      if (width > compressOptions.value.maxWidth) {
        const ratio = compressOptions.value.maxWidth / width
        width = compressOptions.value.maxWidth
        height = height * ratio
      }

      canvas.width = width
      canvas.height = height
      const ctx = canvas.getContext('2d')
      ctx?.drawImage(img, 0, 0, width, height)

      const quality = compressOptions.value.quality / 100
      const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)

      // 计算压缩后的大小
      const base64str = compressedDataUrl.split(',')[1]
      const decoded = atob(base64str)
      processedSize.value = decoded.length

      processedImage.value = {
        dataUrl: compressedDataUrl,
        type: 'image/jpeg'
      }
    }

    const handleQualityChange = () => {
      compressImage()
    }

    const convertImage = async () => {
      if (!currentImage.value) return

      const img = new Image()
      img.src = currentImage.value.url

      await new Promise((resolve) => {
        img.onload = resolve
      })

      const canvas = document.createElement('canvas')
      canvas.width = img.width
      canvas.height = img.height
      const ctx = canvas.getContext('2d')
      ctx?.drawImage(img, 0, 0)

      const mimeType = `image/${convertOptions.value.format}`
      const convertedDataUrl = canvas.toDataURL(mimeType, 1)

      // 计算转换后的大小
      const base64str = convertedDataUrl.split(',')[1]
      const decoded = atob(base64str)
      processedSize.value = decoded.length

      processedImage.value = {
        dataUrl: convertedDataUrl,
        type: mimeType
      }
    }

    const copyUrl = () => {
      // 检查内容是否存在
      if (!imageUrl.value) {
        ElMessage.warning('请先生成链接')
        return
      }
      navigator.clipboard.writeText(imageUrl.value)
      ElMessage.success('链接已复制')
    }

    // 清理图片资源
    const cleanupImage = () => {
      if (currentImage.value?.url) {
        URL.revokeObjectURL(currentImage.value.url)
      }
      currentImage.value = null
      processedImage.value = null
      processedSize.value = 0
      imageUrl.value = ''
    }

    const previewImages = computed(() => {
      const images = [currentImage.value?.url]
      if (processedImage.value) {
        images.push(processedImage.value.dataUrl)
      }
      return images
    })

    const switchPreviewImage = (index: number) => {
      currentPreviewIndex.value = index
      // 触发 el-image 内置预览器切换图片
      const previewWrapper = document.querySelector('.el-image-viewer__wrapper')
      if (previewWrapper) {
        const img = previewWrapper.querySelector('.el-image-viewer__img') as HTMLImageElement
        if (img) {
          img.src = previewImages.value[index]
        }
      }
    }

    const getPreviewTitle = computed(() => {
      return currentPreviewIndex.value === 0 ? '原始图片' : '处理后图片'
    })

    const getPreviewSize = computed(() => {
      if (currentPreviewIndex.value === 0) {
        return formatSize(currentImage.value?.size || 0)
      }
      return `${formatSize(processedSize.value)} (${calculateSizeReduction.value})`
    })

    const downloadProcessedImage = () => {
      if (!processedImage.value) return

      const link = document.createElement('a')
      link.href = processedImage.value.dataUrl
      link.download = `processed_${Date.now()}.${convertOptions.value.format || 'jpg'}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const uploadProcessedImage = () => {
      if (!processedImage.value) return

      //  uploadBase64Img
      uploadBase64Img(processedImage.value.dataUrl)
        .then((res) => {
          imageUrl.value = res.fullUrl
          ElMessage.success('图片上传成功')
        })
        .catch((err) => {
          ElMessage.error('上传失败')
        })
    }

    const copyImageUrl = () => {
      navigator.clipboard.writeText(imageUrl.value)
      ElMessage.success('链接已复制')
    }

    const insertImageUrl = () => {
      if (!imageUrl.value) {
        ElMessage.warning('请先生成链接')
        return
      }
      const fileFullPath = `<p><img src="${imageUrl.value}" style="max-width:100%;" contenteditable="false"></p>`

      // 粘贴到当前的鼠标选中位置
      document.execCommand('insertHTML', false, fileFullPath)

      ElMessage.success('图片已插入到富文本编辑器')
    }

    onUnmounted(() => {
      cleanupImage()
    })

    return {
      currentImage,
      processedImage,
      processedSize,
      imageUrl,
      compressOptions,
      convertOptions,
      calculateSizeReduction,
      handleImageChange,
      formatQuality,
      formatSize,
      compressImage,
      convertImage,
      copyUrl,
      cleanupImage,
      currentPreviewIndex,
      previewImages,
      switchPreviewImage,
      getPreviewTitle,
      getPreviewSize,
      downloadProcessedImage,
      uploadProcessedImage,
      copyImageUrl,
      insertImageUrl,
      handleQualityChange
    }
  }
})
</script>

<style scoped>
.image-tool {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.info-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.size-diff {
  color: var(--el-color-success);
}

.process-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
}

.group-title {
  font-weight: bold;
  margin-bottom: 12px;
}

.upload-section {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.result {
  margin-top: 16px;
}

.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border: 1px solid #e4e7ed;
  padding: 12px;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  text-align: center;
}

.preview-size {
  text-align: center;
  color: #666;
}

.el-image {
  height: 200px;
  width: 100%;
  background-color: #f5f7fa;
}

.custom-preview-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 自定义预览器样式 */
:deep(.el-image-viewer__wrapper) {
  background-color: rgba(0, 0, 0, 0.9);
}

:deep(.el-image-viewer__btn) {
  color: #fff;
}

:deep(.el-image-viewer__actions) {
  opacity: 1;
  background: rgba(0, 0, 0, 0.7);
}

.image-uploader {
  width: 100%; /* 设置固定宽度 */
  margin: 0 auto;
  height: 100px;
}

.image-uploader :deep(.el-upload) {
  width: 100%;
}

.image-uploader :deep(.el-upload-dragger) {
  width: 100%;
  padding: 10px; /* 减小内边距 */
}

.image-uploader :deep(.el-icon--upload) {
  margin: 8px 0; /* 减小图标的上下间距 */
  height: 50px;
}

.image-uploader :deep(.el-upload__text) {
  font-size: 14px; /* 稍微减小字体大小 */
}
</style>
