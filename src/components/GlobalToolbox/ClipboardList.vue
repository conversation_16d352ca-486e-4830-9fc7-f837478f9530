<template>
  <div class="clipboard-list">
    <template v-if="items.length">
      <div
        v-for="item in items"
        :key="item.timestamp"
        class="clipboard-item"
        @dblclick="pasteContent(item)"
      >
        <div class="item-content">
          <el-tooltip
            v-if="item.type === 'text'"
            :content="item.content"
            placement="top"
            :show-after="500"
            :hide-after="0"
          >
            <div class="text-content">{{ item.content }}</div>
          </el-tooltip>
          <el-image
            v-else
            :src="item.content"
            fit="cover"
            class="item-image"
            :preview-src-list="[item.content]"
          />
        </div>
        <div class="item-actions">
          <el-button type="text" @click="copyContent(item)">复制</el-button>
          <el-button type="text" @click="pasteContent(item)">粘贴</el-button>
          <el-button type="text" @click="removeItem(item)">删除</el-button>
        </div>
      </div>
    </template>
    <el-empty v-else description="暂无记录" />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { ElMessage } from 'element-plus'
import { useStore } from '/@/store'
import { ClipboardItem } from '/@/store/modules/clipboard'

export default defineComponent({
  name: 'ClipboardList',
  props: {
    items: {
      type: Array as PropType<ClipboardItem[]>,
      required: true
    }
  },
  setup() {
    const store = useStore()

    const copyContent = async (item: ClipboardItem) => {
      try {
        await navigator.clipboard.writeText(item.content)
        ElMessage.success('已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }

    const pasteContent = async (item: ClipboardItem) => {
      try {
        // 先将内容复制到剪贴板
        await navigator.clipboard.writeText(item.content)

        // 获取当前焦点元素
        const activeElement = document.activeElement as HTMLElement
        if (activeElement) {
          if (
            activeElement instanceof HTMLInputElement ||
            activeElement instanceof HTMLTextAreaElement
          ) {
            // 对于输入框和文本域，我们使用 setRangeText 方法
            const start = activeElement.selectionStart || 0
            const end = activeElement.selectionEnd || 0
            activeElement.setRangeText(item.content, start, end, 'end')

            // 触发 input 事件以确保值的更新
            activeElement.dispatchEvent(new Event('input', { bubbles: true }))
            activeElement.dispatchEvent(new Event('change', { bubbles: true }))
          } else if (activeElement.isContentEditable) {
            // 对于富文本编辑器等可编辑元素
            const selection = window.getSelection()
            const range = selection?.getRangeAt(0)
            if (range) {
              range.deleteContents()
              range.insertNode(document.createTextNode(item.content))
            }
          } else {
            // 对于其他元素，尝试使用 execCommand
            document.execCommand('insertText', false, item.content)
          }

          // 保持焦点
          activeElement.focus()
          ElMessage.success('已粘贴内容')
        } else {
          // 如果没有焦点元素，提示用户
          ElMessage.warning('请先点击要粘贴的位置')
        }
      } catch (error) {
        console.error('Paste failed:', error)
        ElMessage({
          message: '粘贴失败，请按 Ctrl+V 手动粘贴',
          type: 'warning'
        })
      }
    }

    const removeItem = (item: ClipboardItem) => {
      if (item.type === 'image') {
        const images = document.querySelectorAll(`img[src="${item.content}"]`)
        images.forEach((img) => {
          img.src = ''
        })
      }
      store.commit('clipboard/removeItem', item.timestamp)
    }

    return {
      copyContent,
      pasteContent,
      removeItem
    }
  }
})
</script>

<style scoped>
.clipboard-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.clipboard-item {
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer; /* 添加指针样式提示可点击 */
  transition: background-color 0.2s;
}

.clipboard-item:hover {
  background-color: #f5f7fa;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.text-content {
  font-size: 14px;
  line-height: 1.4;
  max-height: 2.8em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.item-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
</style>
