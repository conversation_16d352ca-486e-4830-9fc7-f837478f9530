<template>
  <div
    class="global-toolbox"
    :class="{
      expanded: isExpanded,
      'stick-left': stickSide === 'left',
      'stick-right': stickSide === 'right'
    }"
    :style="getPosition"
    v-show="visible"
  >
    <div
      class="toolbox-trigger"
      @mousedown="handleTriggerDrag"
      @click="handleTriggerClick"
      v-show="!isExpanded"
    >
      <el-icon><Tools /></el-icon>
    </div>

    <div v-show="isExpanded" class="toolbox-container">
      <div class="toolbox-header" @mousedown="handleDragStart">
        <span class="title">工具集</span>
        <div class="controls">
          <el-button type="text" @click="toggleExpand">
            <el-icon><Fold /></el-icon>
          </el-button>
          <el-button type="text" @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="toolbox-content">
        <el-tabs v-model="activeTab" class="toolbox-tabs">
          <el-tab-pane label="图片工具" name="image">
            <keep-alive>
              <ImageTool />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane label="剪贴板" name="clipboard">
            <keep-alive>
              <ClipboardTool />
            </keep-alive>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { Tools, Fold, Close } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import ClipboardTool from './ClipboardTool.vue'
import ImageTool from './ImageTool.vue'

export default defineComponent({
  name: 'GlobalToolbox',
  components: {
    ClipboardTool,
    ImageTool,
    Tools,
    Fold,
    Close
  },
  setup() {
    const visible = ref(true)
    const isExpanded = ref(false)
    const activeTab = ref('image')
    const position = reactive({
      x: window.innerWidth - 60,
      y: window.innerHeight - 60
    })
    const stickSide = ref<'left' | 'right' | null>('right')
    let dragStartTime = 0

    // 保存原始位置
    let lastPosition = {
      x: position.x,
      y: position.y,
      stickSide: stickSide.value
    }

    // 计算实际位置
    const getPosition = computed(() => {
      if (stickSide.value === 'left') {
        return { left: '0px', top: position.y + 'px' }
      } else if (stickSide.value === 'right') {
        return { right: '0px', top: position.y + 'px' }
      }
      return { left: position.x + 'px', top: position.y + 'px' }
    })

    // 拖拽相关
    let isDragging = false
    let startX = 0
    let startY = 0
    let initialX = 0
    let initialY = 0

    const handleTriggerDrag = (e: MouseEvent) => {
      dragStartTime = Date.now()
      isDragging = true
      startX = e.clientX
      startY = e.clientY
      initialX = position.x
      initialY = position.y

      document.addEventListener('mousemove', handleDragMove)
      document.addEventListener('mouseup', handleDragEnd)
    }

    const handleTriggerClick = (e: MouseEvent) => {
      // 如果拖动时间小于 200ms，认为是点击
      if (Date.now() - dragStartTime < 200) {
        toggleExpand()
      }
    }

    const handleDragStart = (e: MouseEvent) => {
      if (e.target instanceof HTMLElement && e.target.closest('.controls')) {
        return
      }
      isDragging = true
      startX = e.clientX
      startY = e.clientY
      initialX = position.x
      initialY = position.y

      document.addEventListener('mousemove', handleDragMove)
      document.addEventListener('mouseup', handleDragEnd)
    }

    const handleDragMove = (e: MouseEvent) => {
      if (!isDragging) return

      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      position.x = Math.max(
        0,
        Math.min(window.innerWidth - (isExpanded.value ? 600 : 40), initialX + deltaX)
      )
      position.y = Math.max(
        0,
        Math.min(window.innerHeight - (isExpanded.value ? 800 : 40), initialY + deltaY)
      )

      // 检查是否需要吸附
      const stickThreshold = 20
      if (position.x < stickThreshold) {
        stickSide.value = 'left'
      } else if (position.x > window.innerWidth - (isExpanded.value ? 600 : 40) - stickThreshold) {
        stickSide.value = 'right'
      } else {
        stickSide.value = null
      }
    }

    const handleDragEnd = () => {
      isDragging = false
      document.removeEventListener('mousemove', handleDragMove)
      document.removeEventListener('mouseup', handleDragEnd)
    }

    const handleClose = async () => {
      try {
        await ElMessageBox.confirm('关闭后将不再显示此工具，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        visible.value = false
      } catch {
        // 用户取消关闭
      }
    }

    const toggleExpand = () => {
      if (isExpanded.value) {
        // 收起时恢复原始位置
        position.x = lastPosition.x
        position.y = lastPosition.y
        stickSide.value = lastPosition.stickSide
      } else {
        // 展开前保存当前位置
        lastPosition = {
          x: position.x,
          y: position.y,
          stickSide: stickSide.value
        }

        // 检查展开后是否会超出屏幕
        if (position.x + 600 > window.innerWidth) {
          position.x = window.innerWidth - 600
        }
        if (position.y + 800 > window.innerHeight) {
          position.y = window.innerHeight - 800
        }
      }
      isExpanded.value = !isExpanded.value
    }

    return {
      visible,
      isExpanded,
      activeTab,
      position,
      stickSide,
      getPosition,
      handleTriggerDrag,
      handleTriggerClick,
      handleDragStart,
      handleClose,
      toggleExpand
    }
  }
})
</script>

<style scoped>
.global-toolbox {
  position: fixed;
  z-index: 2000;
  user-select: none;
  transition: all 0.3s ease;
}

.toolbox-trigger {
  width: 40px;
  height: 40px;
  background: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  opacity: 0.6;
  position: relative;
}

.toolbox-trigger:hover {
  transform: scale(1.1);
  opacity: 1;
}

.stick-left .toolbox-trigger,
.stick-right .toolbox-trigger {
  border-radius: 0 50% 50% 0;
}

.stick-right .toolbox-trigger {
  border-radius: 50% 0 0 50%;
}

.toolbox-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 600px;
  height: 800px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.toolbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(245, 247, 250, 0.9);
  border-radius: 8px 8px 0 0;
  cursor: move;
}

.toolbox-content {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.controls {
  display: flex;
  gap: 4px;
}

.expanded {
  transition: all 0.3s;
}

.toolbox-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbox-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
  padding-right: 8px;
}

/* 美化滚动条 */
.toolbox-tabs :deep(.el-tabs__content)::-webkit-scrollbar {
  width: 6px;
}

.toolbox-tabs :deep(.el-tabs__content)::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

.toolbox-tabs :deep(.el-tabs__content)::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}
</style>
