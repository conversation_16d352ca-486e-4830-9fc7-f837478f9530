<template>
  <div class="clipboard-tool">
    <div class="clipboard-controls">
      <el-input v-model="searchText" placeholder="搜索剪贴内容" prefix-icon="Search" clearable />
      <el-button type="primary" @click="clearAll">清空记录</el-button>
    </div>

    <div class="clipboard-list">
      <template v-if="filteredItems.length">
        <div
          v-for="item in filteredItems"
          :key="item.timestamp"
          class="clipboard-item"
          @dblclick="pasteContent(item)"
        >
          <div class="item-content">
            <el-tooltip :content="item.content" placement="top" :show-after="500" :hide-after="0">
              <div class="text-content">{{ item.content }}</div>
            </el-tooltip>
          </div>
          <div class="item-actions">
            <div class="action-buttons">
              <el-button type="text" @click="copyContent(item)">复制</el-button>
              <el-button type="text" @click="pasteContent(item)">粘贴</el-button>
              <el-button type="text" @click="removeItem(item)">删除</el-button>
            </div>
          </div>
        </div>
      </template>
      <el-empty v-else description="暂无记录" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from '/@/store'
import { ElMessage } from 'element-plus'
import { ClipboardItem } from '/@/store/modules/clipboard'

export default defineComponent({
  name: 'ClipboardTool',
  setup() {
    const store = useStore()
    const searchText = ref('')

    const filteredItems = computed(() => {
      return store.state.clipboard.items.filter((item) =>
        item.content.toLowerCase().includes(searchText.value.toLowerCase())
      )
    })

    const clearAll = () => {
      store.commit('clipboard/clearAll')
    }

    const copyContent = async (item: ClipboardItem) => {
      try {
        await navigator.clipboard.writeText(item.content)
        ElMessage.success('已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }

    const pasteContent = async (item: ClipboardItem) => {
      try {
        await navigator.clipboard.writeText(item.content)
        const activeElement = document.activeElement as HTMLElement
        if (activeElement) {
          if (
            activeElement instanceof HTMLInputElement ||
            activeElement instanceof HTMLTextAreaElement
          ) {
            const start = activeElement.selectionStart || 0
            const end = activeElement.selectionEnd || 0
            activeElement.setRangeText(item.content, start, end, 'end')
            activeElement.dispatchEvent(new Event('input', { bubbles: true }))
            activeElement.dispatchEvent(new Event('change', { bubbles: true }))
          } else if (activeElement.isContentEditable) {
            const selection = window.getSelection()
            const range = selection?.getRangeAt(0)
            if (range) {
              range.deleteContents()
              range.insertNode(document.createTextNode(item.content))
            }
          } else {
            document.execCommand('insertText', false, item.content)
          }
          activeElement.focus()
          ElMessage.success('已粘贴内容')
        } else {
          ElMessage.warning('请先点击要粘贴的位置')
        }
      } catch (error) {
        console.error('Paste failed:', error)
        ElMessage({
          message: '粘贴失败，请按 Ctrl+V 手动粘贴',
          type: 'warning'
        })
      }
    }

    const removeItem = (item: ClipboardItem) => {
      store.commit('clipboard/removeItem', item.timestamp)
    }

    return {
      searchText,
      filteredItems,
      clearAll,
      copyContent,
      pasteContent,
      removeItem
    }
  }
})
</script>

<style scoped>
.clipboard-tool {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.clipboard-controls {
  display: flex;
  gap: 8px;
}

.clipboard-list {
  height: 600px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.clipboard-item {
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clipboard-item:hover {
  background-color: #f5f7fa;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.text-content {
  font-size: 14px;
  line-height: 1.4;
  max-height: 2.8em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.item-actions {
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  padding: 4px 8px;
  height: 28px;
}
</style>
