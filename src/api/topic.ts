import request from '/@/utils/request'

/**
 * 话题列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getTopicList(params: object) {
  return request({
    url: '/topic/get-topic-list',
    method: 'get',
    params
  })
}

/**
 * 删除话题
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteTopic(params: object) {
  return request({
    url: '/topic/delete-topic',
    method: 'post',
    data: params
  })
}

/**
 * 添加话题
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addTopic(params: object) {
  return request({
    url: '/topic/add',
    method: 'post',
    data: params
  })
}

/**
 * 话题详情
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getTopicDetails(params: object) {
  return request({
    url: '/topic/get-topic-detail',
    method: 'get',
    params
  })
}
