import request from '/@/utils/request'

/**
 * 获取职位类型
 */
export function getJobTypeList() {
  return request({
    url: '/category/get-job-list'
  })
}

/**
 *添加/修改职位类型
 */
export function addJobType(params: Object) {
  return request({
    url: '/category/add-job',
    method: 'post',
    data: params
  })
}

/**
 * 获取简历库配置
 */
export function getResumeLibrary() {
  return request({
    url: '/system-config/get-resume-library',
    method: 'get'
  })
}

/**
 * 修改简历库相关配置
 */
export function setResumeLibrary(params: Object) {
  return request({
    url: '/system-config/set-resume-library',
    method: 'post',
    data: params
  })
}

/**
 * 获取运营配置
 */
export function getOperationConfig(params: Object) {
  return request({
    url: '/system-config/load-operation-setting',
    method: 'get',
    params
  })
}

export function setOperationConfig(params: Object) {
  return request({
    url: '/system-config/set-operation-setting',
    method: 'post',
    data: params
  })
}

// 获取配置列表
export const getConfigList = (params: any) => {
  return request({
    url: '/system-config/load-list',
    method: 'get',
    params
  })
}

// 新增配置
export const addConfig = (data: any) => {
  return request({
    url: '/system-config/add',
    method: 'post',
    data
  })
}

// 获取bi配置
export const getBi = () => {
  return request({
    url: '/system-config/get-bi',
    method: 'get'
  })
}

// 删除七牛图片
export const deleteQiniuImage = (params: any) => {
  return request({
    url: '/system-config/delete-qiniu-image',
    method: 'post',
    data: params
  })
}

// 重新上传七牛图片 ReUploadQiniuImage
export const reUploadQiniuImage = (params: any) => {
  return request({
    url: '/system-config/re-upload-qiniu-image',
    method: 'post',
    data: params
  })
}
