import request from '/@/utils/request'

export function getCompanyGroupList(params: Object) {
  return request({
    url: '/company-group/index',
    method: 'get',
    params
  })
}

export function exportCompanyGroup(params: Object) {
  return request({
    url: '/company-group/export',
    method: 'get',
    params
  })
}

export function createCompanyGroup(params: Object) {
  return request({
    url: '/company-group/add',
    method: 'post',
    data: params
  })
}

export function editCompanyGroup(params: Object) {
  return request({
    url: '/company-group/edit',
    method: 'post',
    data: params
  })
}

export function getGroupEdit(params: Object) {
  return request({
    url: '/company-group/edit-init',
    method: 'get',
    params
  })
}

export function deleteGroup(params: Object) {
  return request({
    url: '/company-group/delete',
    method: 'post',
    data: params
  })
}

export function companyEditGroup(params: Object) {
  return request({
    url: '/company/edit-group',
    method: 'post',
    data: params
  })
}
