import request from '/@/utils/request'

/**
 * 审核状态列表
 * @returns 返回接口数据
 */
export function getStatusList() {
  return request({
    url: '/news/get-status-list',
    method: 'get'
  })
}

/**
 * 所属栏目列表
 * @returns 返回接口数据
 */
export function getHomeColumnList() {
  return request({
    url: '/news/get-home-column-list',
    method: 'get'
  })
}

/**
 * 文档属性列表
 * @returns 返回接口数据
 */
export function getAttributeNewsList() {
  return request({
    url: '/news/get-attribute-news-list',
    method: 'get'
  })
}

/**
 * 高才海外相关属性
 * @returns 返回接口数据
 */
export function getAbroadAttributeNewsList() {
  return request({
    url: '/news/get-abroad-attribute-news-list',
    method: 'get'
  })
}

/**
 * 资讯列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getNewsList(params: object) {
  return request({
    url: '/news/get-news-list',
    method: 'get',
    params
  })
}

/**
 * 审核资讯
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function auditNews(params: object) {
  return request({
    url: '/news/audit-news',
    method: 'post',
    data: params
  })
}

/**
 * 删除资讯
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteNews(params: object) {
  return request({
    url: '/news/delete-news',
    method: 'post',
    data: params
  })
}

/**
 * 批量编辑属性
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function newsChageAttribute(params: object) {
  return request({
    url: '/news/change-attribute',
    method: 'post',
    data: params
  })
}

/**
 * 批量复制
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function newsCopy(params: object) {
  return request({
    url: '/news/copy-to-column',
    method: 'post',
    data: params
  })
}

/**
 * 移动文档
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function newsCarryToColumn(params: object) {
  return request({
    url: '/news/carry-to-column',
    method: 'post',
    data: params
  })
}

/**
 * 发布/编辑资讯
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function newsAdd(params: object) {
  return request({
    url: '/news/add',
    method: 'post',
    data: params
  })
}

/**
 * 发布/编辑资讯
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getNewsDetails(params: object) {
  return request({
    url: '/news/get-news-details',
    method: 'get',
    params
  })
}
