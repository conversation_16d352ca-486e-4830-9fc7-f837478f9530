import request from '/@/utils/request'

/**
 * 列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementIndex(params: object) {
  return request({
    url: '/agreement-config/index',
    params
  })
}

/**
 * 添加
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementAdd(params: object) {
  return request({
    url: '/agreement-config/add',
    method: 'post',
    data: params
  })
}


/**
 * 编辑
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementEdit(params: object) {
  return request({
    url: '/agreement-config/edit',
    method: 'post',
    data: params
  })
}

/**
 * 编辑初始化
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementEditInit(params: object) {
  return request({
    url: '/agreement-config/edit-init',
    params
  })
}

/**
 * 选项
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementFilter(params: object) {
  return request({
    url: '/agreement-config/filter',
    params
  })
}

/**
 * 删除状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function agreementStatus(params: object) {
  return request({
    url: '/agreement-config/status',
    method: 'post',
    data:params
  })
}

