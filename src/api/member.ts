import request from '/@/utils/request'

/**
 * 日志
 * @returns 返回接口数据
 */
export function getLogList(params: object) {
  return request({
    url: '/member/search-log-list',
    method: 'get',
    params
  })
}
/**
 * 警用账号
 * @returns 返回接口数据
 */
export function lockAccount(id: string) {
  return request({
    url: '/member/lock-account',
    method: 'post',
    data: { id }
  })
}

/**
 * 启用账号
 * @returns 返回接口数据
 */
export function unlockAccount(id: string) {
  return request({
    url: '/member/unlock-account',
    method: 'post',
    data: { id }
  })
}

/**
 * 单位日志筛选器
 * @returns 返回接口数据
 */
export function getLogfilter(params: object) {
  return request({
    url: '/member/search-log-filter',
    params
  })
}

/**
 * 单位日志账号变更日志
 * @returns 返回接口数据
 */
export function getOperationLogList(params: object) {
  return request({
    url: '/member/operation-log-list',
    params
  })
}

/**
 * 会员信息
 * @returns 返回接口数据
 */
export function getVipInfo(params: object) {
  return request({
    url: '/person/get-vip-info',
    method: 'get',
    params
  })
}
