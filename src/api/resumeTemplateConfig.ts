import request from '/@/utils/request'

/**
 * 列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function indexTemplate(params: object) {
  return request({
    url: '/resume-template-config/index',
    params
  })
}

/**
 * 添加
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addTemplate(params: object) {
  return request({
    url: '/resume-template-config/add',
    method: 'post',
    data: params
  })
}


/**
 * 编辑
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function editTemplate(params: object) {
  return request({
    url: '/resume-template-config/edit',
    method: 'post',
    data: params
  })
}

/**
 * 编辑初始化
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function editInitTemplate(params: object) {
  return request({
    url: '/resume-template-config/edit-init',
    params
  })
}

/**
 * 删除状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteStatusTemplate(params: object) {
  return request({
    url: '/resume-template-config/delete-status',
    method: 'post',
    data:params
  })
}

/**
 * 显示状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function showStatusTemplate(params: object) {
  return request({
    url: '/resume-template-config/show-status',
    method: 'post',
    data:params
  })
}
