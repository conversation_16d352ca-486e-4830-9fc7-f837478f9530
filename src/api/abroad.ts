import request from '/@/utils/request'

/**
 * 活动管理列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAbroadActivityList(params: object) {
  return request({
    url: '/hw-activity/get-list',
    method: 'get',
    params
  })
}

/**
 * 活动管理列表查询参数获取
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAbroadParamsList() {
  return request({
    url: '/hw-activity/get-search-params-list'
  })
}

/**
 * 活动管理列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function delActivity(params: object) {
  return request({
    url: '/hw-activity/del-activity',
    method: 'post',
    data: params
  })
}

/**
 * 活动表单参数
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAbroadTypeList() {
  return request({
    url: '/hw-activity/get-series-type-list'
  })
}

/**
 * 保存临时活动场次
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function saveTempData(params: object) {
  return request({
    url: '/hw-activity-session/save-temp-data',
    method: 'post',
    data: params
  })
}

/**
 * 获取显示图片类型列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getShowImgTypeList() {
  return request({
    url: '/hw-activity/get-show-img-type'
  })
}

/**
 * 新增/编辑海外活动
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function saveActiveData(params: object) {
  return request({
    url: '/hw-activity/save-info',
    method: 'post',
    data: params
  })
}

/**
 * 获取海外活动编辑信息
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAbroadActivityInfo(params: object) {
  return request({
    url: '/hw-activity/get-edit-info',
    method: 'get',
    params
  })
}

/**
 * 切换活动上下架状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeGroundingStatus(params: object) {
  return request({
    url: '/hw-activity/change-grounding-status',
    method: 'post',
    data: params
  })
}

/**
 * 设置活动排序
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeActiveSort(params: object) {
  return request({
    url: '/hw-activity/set-sort',
    method: 'post',
    data: params
  })
}

/**
 * 获取查询参数列表
 * @returns 返回接口数据
 */
export function getSpecialActivityParams() {
  return request({
    url: '/hw-activity/get-special-activity-params',
    method: 'get'
  })
}

/**
 * 查询专场列表
 * @returns 返回接口数据
 */
export function getSpecialActivityList(params: object) {
  return request({
    url: '/hw-activity/get-special-activity-list',
    method: 'get',
    params
  })
}

/**
 * 专场详情
 * @returns 返回接口数据
 */
export function getSpecialActivityDetail(params: object) {
  return request({
    url: '/hw-activity/get-special-activity-detail',
    method: 'get',
    params
  })
}

/**
 * 获取活动表单
 * @returns 返回接口数据
 */
export function getFormList(params: object) {
  return request({
    url: '/activity-form/get-form-list',
    method: 'get',
    params
  })
}

/**
 * 获取活动表单-选项
 * @returns 返回接口数据
 */
export function getFormOptionsList(params: object) {
  return request({
    url: '/activity-form-intention-option/get-form-options-list',
    method: 'get',
    params
  })
}

/**
 * 获取可添加的关联活动
 * @returns 返回接口数据
 */
export function getSpecialActivityCanAddActivity(params: object) {
  return request({
    url: '/hw-activity/get-special-activity-can-add-activity',
    method: 'get',
    params
  })
}

/**
 * 新增、编辑更新专场
 * @returns 返回接口数据
 */
export function updateSpecialActivity(data: object) {
  return request({
    url: '/hw-activity/update-special-activity-detail',
    method: 'post',
    data
  })
}

// 获取关联的列表
export function getAssociatedList(params: object) {
  return request({
    url: '/hw-activity/get-associated-list',
    method: 'get',
    params
  })
}

// 获取未关联单位列表
export function getAssociatedCompanyList(params: object) {
  return request({
    url: '/hw-activity/get-associated-company-list',
    method: 'get',
    params
  })
}

// 获取未关联公告列表
export function getAssociatedAnnouncementList(params: object) {
  return request({
    url: '/hw-activity/get-associated-announcement-list',
    method: 'get',
    params
  })
}

// 添加关联单位
export function addAssociatedCompany(data: object) {
  return request({
    url: '/hw-activity/add-associated-company',
    method: 'post',
    data
  })
}

// 添加关联公告
export function addAssociatedAnnouncement(data: object) {
  return request({
    url: '/hw-activity/add-associated-announcement',
    method: 'post',
    data
  })
}

// 设置关联单位的排序
export function setAssociatedSort(data: object) {
  return request({
    url: '/hw-activity/set-associated-sort',
    method: 'post',
    data
  })
}

// 设置关联单位的置顶
export function setAssociatedTop(data: object) {
  return request({
    url: '/hw-activity/set-associated-top',
    method: 'post',
    data
  })
}

// 取消关联单位
export function cancelAssociatedCompany(data: object) {
  return request({
    url: '/hw-activity/cancel-associated-company',
    method: 'post',
    data
  })
}

// 删除推广位置
export function deletePromotion(data: object) {
  return request({
    url: '/hw-activity/delete-promotion',
    method: 'post',
    data
  })
}

// 设置推广位置排序
export function setPromotionSort(data: object) {
  return request({
    url: '/hw-activity/set-promotion-sort',
    method: 'post',
    data
  })
}

// 获取添加、编辑活动选项
export function getAddParamsList(data: object) {
  return request({
    url: '/hw-activity/get-add-params-list',
    method: 'post',
    data
  })
}

// 活动详情链接是公告详情时候自动识别单位信息
export function getAnnouncementDetailCompanyInfo(params: object) {
  return request({
    url: '/hw-activity/get-announcement-detail-company-info',
    method: 'get',
    params
  })
}
