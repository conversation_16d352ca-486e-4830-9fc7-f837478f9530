import request from '/@/utils/request'

export function getList(params: object) {
  return request({
    url: '/daily-announcement-summary/get-list',
    method: 'get',
    params
  })
}

export function getJobDetails(id: string) {
  return request({
    url: '/daily-announcement-summary/get-detail',
    method: 'get',
    params: { id }
  })
}

export function editContent(data: object) {
  return request({
    url: '/daily-announcement-summary/edit-content',
    method: 'post',
    data
  })
}

export function changeRelease(id: string) {
  return request({
    url: '/daily-announcement-summary/change-release',
    method: 'post',
    data: { id }
  })
}

export function submitDelete(id: string) {
  return request({
    url: '/daily-announcement-summary/delete',
    method: 'post',
    data: { id }
  })
}
