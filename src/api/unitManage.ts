import request from '/@/utils/request'

/**
 * 单位状态的禁用
 * @returns 返回接口数据
 */
export function unitIsAvailable(params: object) {
  return request({
    url: '/member/lock-account',
    method: 'post',
    data: params
  })
}
/**
 * 单位状态的启用
 * @returns 返回接口数据
 */
export function unitEnable(params: object) {
  return request({
    url: '/member/unlock-account',
    method: 'post',
    data: params
  })
}
/**
 * 获取单位列表
 * @returns 返回接口数据
 */
export function getUnitList() {
  return request({
    url: '/company/get-company-attr-data',
    method: 'get'
  })
}
/**
 * 修改隐藏状态
 * @returns 返回接口数据
 */
export function updateHideState(params: Object) {
  return request({
    url: '/company/update-hide-status',
    method: 'post',
    data: params
  })
}

/**
 * 新增免费单位接口
 * @returns 返回接口数据
 */
export function addUnit(params: object) {
  return request({
    url: '/company-info-auth/free-create',
    method: 'post',
    data: params
  })
}

/**
 * 单位日志
 * @returns 返回接口数据
 */
export function getLogList(params: object) {
  return request({
    url: '/member/search-log-list',
    method: 'get',
    params
  })
}

/**
 * 合作申请列表
 * @returns 返回接口数据
 */
export function getCooperationList(params: object) {
  return request({
    url: '/company-cooperation-apply/get-list',
    method: 'get',
    params
  })
}
/**
 * 合作申请详情
 * @returns 返回接口数据
 */
export function getCooperationDetail(id: string) {
  return request({
    url: '/company-cooperation-apply/get-detail',
    method: 'get',
    params: { id }
  })
}

/**
 * 导出申请列表的excel
 * @returns 返回接口数据
 */
export function exportCooperationList(params: object) {
  return request({
    url: '/company-cooperation-apply/export-excel',
    method: 'get',
    params
  })
}

/**
 * 新增合作单位标注
 * @returns 返回接口数据
 */
export function addCooperationRemark(params: object) {
  return request({
    url: '/company-cooperation-apply/add-remark',
    method: 'post',
    data: params
  })
}

/**
 * 新增合作单位标注
 * @returns 返回接口数据
 */
export function addCooperationCompany(params: object) {
  return request({
    url: '/company-info-auth/create',
    method: 'post',
    data: params
  })
}

/**
 * 设置处理状态
 * @returns 返回接口数据
 */
export function setCooperationHandle(id: string) {
  return request({
    url: '/company-cooperation-apply/set-handle',
    method: 'post',
    data: { id }
  })
}

/**
 *获取列表
 * @export
 * @param {object} params
 * @returns
 */
export function getList(params: object) {
  return request({
    url: '/company/search-list',
    method: 'get',
    params
  })
}

/**
 * 获取单位详情
 * @returns 返回接口数据
 */
export function getComanyDetails(params: object) {
  return request({
    url: '/company/detail',
    method: 'get',
    params
  })
}

/**
 * 获取编辑单位的回显
 * @returns 返回接口数据
 */
export function getEditDeatils(params: object) {
  return request({
    url: '/company/edit',
    method: 'get',
    params
  })
}

/**
 * 合作单位的编辑
 * @returns 返回接口数据
 */
export function postEdit(params: object) {
  return request({
    url: '/company/info-save',
    method: 'post',
    data: params
  })
}
/**
 * 免费单位的编辑
 * @returns 返回接口数据
 */
export function postWaitCooperEdit(params: object) {
  return request({
    url: '/company/free-info-save',
    method: 'post',
    data: params
  })
}

/**
 * 注册查询接口
 * @returns 返回接口数据
 */
export function registeredSearch(params: object) {
  return request({
    url: '/member/register-search-list',
    method: 'get',
    params
  })
}
/**
 * 免费单位查询接口
 * @returns 返回接口数据
 */
export function getWaitCooperationList(params: object) {
  return request({
    url: '/company/free-search-list',
    method: 'get',
    params
  })
}
/**
 * 免费单位新增接口
 * @returns 返回接口数据
 */
export function addWaitCooperation(params: object) {
  return request({
    url: '/company-info-auth/free-create',
    method: 'post',
    data: params
  })
}
/**
 * 免费单位详情
 * @returns 返回接口数据
 */
export function getWaitCooperationDetails(params: object) {
  return request({
    url: '/company/free-detail',
    method: 'get',
    params
  })
}

/**
 * 查询入网审核接口
 * @returns 返回接口数据
 */
export function getAuditList(params: object) {
  return request({
    url: '/company-info-auth/audit-list',
    method: 'get',
    params
  })
}

/**
 * 入网审核查看详情
 * @returns 返回接口数据
 */
export function getAuditDetails(params: object) {
  return request({
    url: '/company-info-auth/audit-detail',
    method: 'get',
    params
  })
}
/**
 * 免费单位编辑回显接口
 * @returns 返回接口数据
 */
export function getWaitCooperationEdit(params: object) {
  return request({
    url: '/company/free-edit',
    method: 'get',
    params
  })
}

/**
 * 初审通过接口
 * @returns 返回接口数据
 */
export function auditPass(params: object) {
  return request({
    url: '/company-info-auth/audit-handle-trial-pass',
    method: 'post',
    data: params
  })
}

/**
 * 终审通过/拒绝接口
 * @returns 返回接口数据
 */
export function finalAudit(params: object) {
  return request({
    url: '/company-info-auth/audit-handle-final-judgment',
    method: 'post',
    data: params
  })
}
/**
 * 初审拒绝接口
 * @returns 返回接口数据
 */
export function rejectTrial(params: object) {
  return request({
    url: '/company-info-auth/audit-handle-trial-refuse',
    method: 'post',
    data: params
  })
}

/**
 * 终审业务员提交
 * @returns 返回接口数据
 */
export function postHandleSalesman(params: object) {
  return request({
    url: '/company-info-auth/audit-handle-salesman',
    method: 'post',
    data: params
  })
}
/**
 * 面试管理获取数据接口
 * @returns 返回接口数据
 */
export function getInterviewDetails(params: object) {
  return request({
    url: '/company/interview-list',
    method: 'get',
    params
  })
}
/**
 * 获取简历来源
 * @returns 返回接口数据
 */
export function getSourceList() {
  return request({
    url: '/company/get-source-list',
    method: 'get'
  })
}
/**
 * 查看面试详情
 * @returns 返回接口数据
 */
export function checkInterviewDetails(params: object) {
  return request({
    url: '/company/interview-detail',
    params
  })
}
/**
 * 批量导入非合作单位接口
 * @returns 返回接口数据
 */
export function postWaitCooperation(params: object) {
  return request({
    url: '/company-info-auth/batch-import',
    method: 'post',
    data: params
  })
}

// 获取所有的业务员列表
export function getAllSale() {
  return request({
    url: '/config/get-all-sale'
  })
}

/**
 * 审核详情编辑
 * @returns 返回接口数据
 */
export function getAuditDetailEdit(params: object) {
  return request({
    url: '/company-info-auth/audit-detail-edit',
    params
  })
}

/**
 * 审核详情编辑提交
 * @returns 返回接口数据
 */
export function auditDetailSave(params: object) {
  return request({
    url: '/company-info-auth/audit-detail-save',
    method: 'post',
    data: params
  })
}

/**
 * 审核详情编辑提交
 * @returns 返回接口数据
 */
export function childUnitDel(params: object) {
  return request({
    url: '/company/child-unit-delete',
    method: 'post',
    data: params
  })
}

/**
 * 上传base64
 * @returns 返回接口数据
 */
export function uploadBase64(base64: string) {
  return request({
    url: '/upload/base64',
    method: 'post',
    data: {
      keyWords: 'company_head_banner',
      base64
    }
  })
}

/**
 * 单位详情修改单位性质
 * @returns 返回接口数据
 */
export function editAccountNature(params: Object) {
  return request({
    url: '/company/detail-edit-account-nature',
    method: 'post',
    data: params
  })
}

/**
 * 获取单位当前套餐信息
 * @returns 返回接口数据
 */
export function getEndPackageList(params: Object) {
  return request({
    url: '/company/get-company-moment-info',
    method: 'get',
    params
  })
}

/**
 * 禁止当前套餐
 * @returns 返回接口数据
 */
export function EndPackageList(params: Object) {
  return request({
    url: '/company/expiration-company-package-now',
    method: 'post',
    data: params
  })
}

/**
 * 单位特色标签配置
 * @returns 返回接口数据
 */
export function companyTag(params: Object) {
  return request({
    url: '/company/edit-company-featured-tag',
    method: 'post',
    data: params
  })
}

/**
 * 单位特色标签新增
 * @returns 返回接口数据
 */
export function addCompanyTag(params: Object) {
  return request({
    url: '/company/add-featured-tag',
    method: 'post',
    data: params
  })
}
