import request from '/@/utils/request'

/**
 * 获取栏目列表
 * @returns 返回接口数据
 */
export function getColumnList(params: Object) {
  return request({
    url: '/home-column/get-list',
    method: 'get',
    params
  })
}

/**
 * 栏目列表添加/编辑
 * @returns 返回接口数据
 */
export function toUpdate(params: Object) {
  return request({
    url: '/home-column/add',
    method: 'post',
    data: params
  })
}

/**
 * 获取列表所需参数
 * @returns 返回接口数据
 */
export function getParams(params: Object) {
  return request({
    url: '/home-column/get-list-params',
    method: 'get',
    params
  })
}

/**
 * 获取栏目关联字典信息
 * @returns 返回接口数据
 */
export function getDictionary(id: string) {
  return request({
    url: '/home-column/get-dictionary-list',
    method: 'get',
    params: { id }
  })
}

/**
 * 栏目设置字典
 */
export function setDictionary(params: Object) {
  return request({
    url: '/home-column/set-dictionary',
    method: 'post',
    data: params
  })
}
