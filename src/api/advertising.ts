import request from '/@/utils/request'

/**
 * 广告列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getShowcaseList(params: object) {
  return request({
    url: '/showcase/get-showcase-list',
    method: 'get',
    params
  })
}

/**
 * 修改排序
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeShowcaseSort(params: object) {
  return request({
    url: '/showcase/change-showcase-sort',
    method: 'post',
    data: params
  })
}

/**
 * 模糊查找广告位置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getHomePositionList(params: object) {
  return request({
    url: '/showcase/get-home-position-list',
    method: 'get',
    params
  })
}

/**
 * 模糊查找小程序广告位置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getHomeMiniPositionList(params: object) {
  return request({
    url: '/showcase/get-home-mini-position-list',
    method: 'get',
    params
  })
}

/**
 * 新增广告
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addShowcase(params: object) {
  return request({
    url: '/showcase/add-showcase',
    method: 'post',
    data: params
  })
}

/**
 * 获取广告详情
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getShowcaseDetails(params: object) {
  return request({
    url: '/showcase/get-showcase-details',
    method: 'get',
    params
  })
}

/**
 * 编辑广告
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function editShowcase(params: object) {
  return request({
    url: '/showcase/edit-showcase',
    method: 'post',
    data: params
  })
}

/**
 * 模糊查找广告位置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getPositionList(params: object) {
  return request({
    url: '/showcase/get-position-list',
    method: 'get',
    params
  })
}

/**
 * 广告统计（点击量）
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getShowcaseClick(params: object) {
  return request({
    url: '/showcase/get-showcase-click',
    method: 'get',
    params
  })
}

/**
 * 删除广告
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteShowcase(params: object) {
  return request({
    url: '/showcase/delete-showcase',
    method: 'post',
    data: params
  })
}

/**
 * 显示/隐藏广告
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeShowcaseShow(params: object) {
  return request({
    url: '/showcase/change-showcase-show',
    method: 'post',
    data: params
  })
}

/**
 * 广告位列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAdPositionList(params: object) {
  return request({
    url: '/home-position/get-home-position-list',
    method: 'get',
    params
  })
}

/**
 * 新增广告位
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addHomePosition(params: object) {
  return request({
    url: '/home-position/add-home-position',
    method: 'post',
    data: params
  })
}

/**
 * 获取广告位详情
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getHomePositionDetails(params: object) {
  return request({
    url: '/home-position/get-home-position-details',
    method: 'get',
    params
  })
}

/**
 * 修改广告位
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function editHomePosition(params: object) {
  return request({
    url: '/home-position/edit-home-position',
    method: 'post',
    data: params
  })
}

/**
 * 删除广告位
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteHomePosition(params: object) {
  return request({
    url: '/home-position/delete-home-position',
    method: 'post',
    data: params
  })
}

/**
 * 显示/隐藏广告位
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeHomePositionShow(params: object) {
  return request({
    url: '/home-position/change-home-position-show',
    method: 'post',
    data: params
  })
}

/**
 * 修改排序
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeHomePositionSort(params: object) {
  return request({
    url: '/home-position/change-home-position-sort',
    method: 'post',
    data: params
  })
}

/**
 * 获取所属平台(web)
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getPlatform() {
  return request({
    url: '/home-position/get-platform',
    method: 'get'
  })
}

/**
 * 获取所属平台
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAllPlatform() {
  return request({
    url: '/home-position/get-all-platform',
    method: 'get'
  })
}

/**
 * 模糊查询广告位置名称
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getPositionName() {
  return request({
    url: '/home-position/get-name',
    method: 'get'
  })
}

/**
 * 模糊查找小程序广告位置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getHomeAbroadPositionList(params: object) {
  return request({
    url: '/showcase/get-home-abroad-position-list',
    method: 'get',
    params
  })
}

/**
 * 模糊查找博士后小程序广告位置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getHomeBoShiHouPositionList(params: object) {
  return request({
    url: '/showcase/get-home-bo-shi-hou-position-list',
    method: 'get',
    params
  })
}

/**
 * 获取标签关联的所有广告位
 * @returns 返回接口数据
 */
export function getPositionTag() {
  return request({
    url: '/home-position/get-home-position-tag',
    method: 'get'
  })
}

/**
 * 广告位新增标签
 * @returns 返回接口数据
 */
export function addPositionTag(data: any) {
  return request({
    url: '/home-position/add-home-position-tag',
    method: 'post',
    data
  })
}

/**
 * 广告位关联标签
 * @returns 返回接口数据
 */
export function editPositionTag(data: any) {
  return request({
    url: '/home-position/edit-home-position-relation-tag',
    method: 'post',
    data
  })
}

/**
 * 获取关联了广告位的所有标签
 * @returns 返回接口数据
 */
export function getPositionRelationTag() {
  return request({
    url: '/home-position/get-home-position-tag-relation',
    method: 'get'
  })
}
