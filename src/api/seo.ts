import request from '/@/utils/request'

/**
 * 获取seo的一些相关参数
 * @returns 返回接口数据
 */
export function getParams() {
  return request({
    url: '/seo/load-params',
    method: 'get'
  })
}

/**
 * 获取百度站长推送日志
 * @returns 返回接口数据
 */
export function getBaiduZZPushList(params: object) {
  return request({
    url: '/seo/load-baidu-zz-push-list',
    method: 'get',
    params
  })
}

/**
 * 获取百度站长推送日志详情
 * @returns 返回接口数据
 */
export function getBaiduZZPushDetail(id: String) {
  return request({
    url: '/seo/load-baidu-zz-push-detail',
    method: 'get',
    params: { id }
  })
}

/**
 * 获取百度站长推送每日汇总
 * @returns 返回接口数据
 */
export function getBaiduZzPushDaily(params: Object) {
  return request({
    url: '/seo/load-baidu-zz-push-daily',
    method: 'get',
    params
  })
}

/**
 * 百度站长主动推送
 * @returns 返回接口数据
 */
export function baiduZZPush(params: object) {
  return request({
    url: '/seo/baidu-zz-push',
    method: 'post',
    data: params
  })
}

/**
 * 获取xml检索项参数
 * @returns 返回接口数据
 */
export function getSearchParams() {
  return request({
    url: '/seo/search-params'
  })
}

/**
 * 获取xml文件列表
 * @returns 返回接口数据
 */
export function getSeoList(params: Object) {
  return request({
    url: '/seo/xml-file-list',
    params
  })
}

/**
 * 获取xml文件编辑
 * @returns 返回接口数据
 */
export function getSeoEdit(params: Object) {
  return request({
    url: '/seo/xml-file-edit',
    params
  })
}

/**
 *删除xml文件
 * @returns 返回接口数据
 */
export function deleteSeoFile(params: object) {
  return request({
    url: '/seo/xml-file-delete',
    method: 'post',
    data: params
  })
}

/**
 *编辑xml文件提交
 * @returns 返回接口数据
 */
export function seoFileSave(params: object) {
  return request({
    url: '/seo/xml-file-save',
    method: 'post',
    data: params
  })
}

/**
 *删除关键词
 * @returns 返回接口数据
 */
export function delSeoKeywords(params: object) {
  return request({
    url: '/seo-hot-word/delete',
    method: 'post',
    data: params
  })
}

/**
 * 获取关键词列表
 * @returns 返回接口数据
 */
export function getSeoKeywordsList(params: Object) {
  return request({
    url: '/seo-hot-word/list',
    params
  })
}

/**
 * 关键词导入
 * @returns 返回接口数据
 */
export function addKeywords(params: Object) {
  return request({
    url: '/seo-hot-word/add',
    method: 'post',
    data: params
  })
}

/**
 * 编辑关键词
 * @returns 返回接口数据
 */
export function editKeywords(params: Object) {
  return request({
    url: '/seo-hot-word/edit',
    method: 'post',
    data: params
  })
}

export function downloadExcelKeywords(params: Object) {
  return request({
    url: '/seo-hot-word/export',
    params
  })
}

/**
 * 获取获取nginx日志列表
 * @returns 返回接口数据
 */
export function getNginxLogList() {
  return request({
    url: '/seo/get-nginx-log-list'
  })
}

/**
 * 下载nginx日志
 * @returns 返回接口数据
 */

export function downloadNginxLog(params: Object) {
  return request({
    url: '/seo/download-nginx-log',
    method: 'post',
    data: params
  })
}

/**
 * 获取关键词列表
 * @returns 返回接口数据
 */
export function getSeoJobWikiList(params: Object) {
  return request({
    url: '/seo-job-wiki/get-list',
    params
  })
}

// 新增关键词
export function saveSeoJobWiki(params: Object) {
  return request({
    url: '/seo-job-wiki/save-seo-job-wiki',
    method: 'post',
    data: params
  })
}

// 删除关键词
export function delSeoJobWiki(params: Object) {
  return request({
    url: '/seo-job-wiki/del-seo-job-wiki',
    method: 'post',
    data: params
  })
}

// 获取关键字详情
export function getSeoJobWikiDetail(params: Object) {
  return request({
    url: '/seo-job-wiki/get-detail',
    params
  })
}

// 根据上传的EXCEL批量新增关键词
export function batchSaveSeoJobWiki(params: Object) {
  return request({
    url: '/seo-job-wiki/batch-save-seo-job-wiki',
    method: 'post',
    data: params
  })
}
