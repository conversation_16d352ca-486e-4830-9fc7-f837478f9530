import request from '/@/utils/request'

/**
 * 获取机器人列表
 */
export const getRobotList = (params: any) => {
  return request({
    url: '/wx-work-robot-config/get-list',
    method: 'get',
    params
  })
}

/**
 * 添加机器人
 */
export const addRobot = (data: any) => {
  return request({
    url: '/wx-work-robot-config/add',
    method: 'post',
    data
  })
}

/**
 * 删除机器人
 */
export const deleteRobot = (id: string) => {
  return request({
    url: `/wx-work-robot-config/delete`,
    method: 'post',
    data: {
      id
    }
  })
}

/**
 * 获取机器人日志列表
 */
export const getRobotLogList = (params: any) => {
  return request({
    url: '/wx-work-robot-config/log-list',
    method: 'get',
    params
  })
}
