import request from '/@/utils/request'

/**
 * 获取角色列表所需参数
 * @returns 返回接口数据
 */
export function getPositionListParams() {
  return request({
    url: '/permissions/get-position-list-params',
    method: 'get'
  })
}

/**
 * 获取角色列表
 * @returns 返回接口数据
 */
export function getPositionList(params: object) {
  return request({
    url: '/permissions/get-position-list',
    method: 'get',
    params
  })
}

/**
 * 获取账号列表所需参数
 * @returns 返回接口数据
 */
export function getAdminListParams() {
  return request({
    url: '/permissions/get-admin-list-params',
    method: 'get'
  })
}

/**
 * 修改状态
 *
 * @export
 * @returns
 */
export function changeStatus(id: string) {
  return request({
    url: '/permissions/change-status',
    method: 'post',
    data: { id }
  })
}

/**
 * 获取账号列表
 * @returns 返回接口数据
 */
export function getAdminList(params: object) {
  return request({
    url: '/permissions/get-admin-list',
    method: 'get',
    params
  })
}

/**
 * 添加角色
 * @returns 返回接口数据
 */
export function addPosition(params: object) {
  return request({
    url: '/permissions/add-position',
    method: 'post',
    data: params
  })
}
/**
 * 添加账号
 * @returns 返回接口数据
 */
export function addAdmin(params: object) {
  return request({
    url: '/permissions/add-admin',
    method: 'post',
    data: params
  })
}

/**
 * 获取整个菜单列表
 * @returns 返回接口数据
 */
export function getRouteList() {
  return request({
    url: '/permissions/get-menu-list',
    method: 'get'
  })
}

/**
 * 添加路由/菜单
 * @returns 返回接口数据
 */
export function addMenu(params: object) {
  return request({
    url: '/permissions/add-menu',
    method: 'post',
    data: params
  })
}

/**
 * 添加操作
 * @returns 返回接口数据
 */
export function addAction(params: object) {
  return request({
    url: '/permissions/add-action',
    method: 'post',
    data: params
  })
}

/**
 * 设置权限
 * @returns 返回接口数据
 */
export function setPositionMenuList(params: object) {
  return request({
    url: '/permissions/set-position-menu-list',
    method: 'post',
    data: params
  })
}

/**
 * 获取某个角色的权限
 * @returns 返回接口数据
 */
export function getPositionMenuList(id: string) {
  return request({
    url: '/permissions/get-position-menu-list',
    method: 'get',
    params: { id }
  })
}
