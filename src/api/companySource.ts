import request from '/@/utils/request'

/**
 * 单位资源配置列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function indexSource(params: object) {
  return request({
    url: '/company-invite-system-config/index',
    params
  })
}

/**
 * 单位资源配置添加
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addSource(params: object) {
  return request({
    url: '/company-invite-system-config/add',
    method: 'post',
    data: params
  })
}


/**
 * 单位资源配置编辑
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function editSource(params: object) {
  return request({
    url: '/company-invite-system-config/edit',
    method: 'post',
    data: params
  })
}

/**
 * 单位资源配置删除
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function deleteSource(params: object) {
  return request({
    url: '/company-invite-system-config/delete',
    params
  })
}
