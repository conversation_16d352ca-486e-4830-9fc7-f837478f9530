import request from '/@/utils/request'

/**
 * 活动列表
 * @returns 返回接口数据
 */
export function getActivityList(params: Object) {
  return request({
    url: '/activity-form/get-activity-form-list',
    method: 'get',
    params
  })
}

/**
 * 获取活动表单报名意向选项说明
 * @returns 返回接口数据
 */
export function saveActivityFormintentionOption(params: Object) {
  return request({
    url: '/activity-form-intention-option/save-activity-form-intention-option',
    method: 'post',
    data: params
  })
}

/**
 * 获取活动表单报名意向选项类型
 * @returns 返回接口数据
 */
export function getIntentionType() {
  return request({
    url: '/activity-form-intention-option/get-type-list',
    method: 'get'
  })
}

/**
 * 获取活动表单前端显示题目
 * @returns 返回接口数据
 */
export function getShowMessageOption() {
  return request({
    url: '/activity-form-intention-option/get-show-message-list',
    method: 'get'
  })
}

/**
 * 保存表单
 * @returns 返回接口数据
 */
export function saveActivity(params: Object) {
  return request({
    url: '/activity-form/save-activity-form',
    method: 'post',
    data: params
  })
}

/**
 * 编辑表单
 * @returns 返回接口数据
 */
export function editActivity(params: Object) {
  return request({
    url: '/activity-form/edit-activity-form',
    method: 'post',
    data: params
  })
}

/**
 * 获取活动表单详情
 * @returns 返回接口数据
 */
export function getActivityFormInfo(params: Object) {
  return request({
    url: '/activity-form/get-activity-form-info',
    method: 'get',
    params
  })
}

/**
 * 删除表单
 * @returns 返回接口数据
 */
export function deleteActivityFrom(params: Object) {
  return request({
    url: '/activity-form/delete-activity-form',
    method: 'post',
    data: params
  })
}

/**
 * 下载excel
 * @returns 返回接口数据
 */
export function downloadExcel(params: Object) {
  return request({
    url: '/activity-form/export-activity-form-registration-form',
    method: 'get',
    params
  })
}

/**
 * 下载附件文件
 * @returns 返回接口数据
 */
export function downattachment(params: Object) {
  return request({
    url: '/activity-form/export-activity-form-file',
    method: 'get',
    params
  })
}

/**
 * 获取活动的签到选项/场次：
 * @returns 返回接口数据
 */
export function getIntentionOptionList(params: Object) {
  return request({
    url: '/activity-form-intention-option/get-intention-option-list',
    method: 'get',
    params
  })
}

/**
 * 下载签到选项/场次：
 * @returns 返回接口数据
 */
export function downSignExcel(params: Object) {
  return request({
    url: '/activity-form-intention-option/export-activity-form-option-sign',
    method: 'get',
    params
  })
}
