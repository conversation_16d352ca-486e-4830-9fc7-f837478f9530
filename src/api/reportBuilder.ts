import request from '/@/utils/request'

/**
 * 标签页
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function tabsData() {
  return request({
    url: '/statement-report-builder/tabs'
  })
}

/**
 * 筛选项
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function filterData() {
  return request({
    url: '/statement-report-builder/filter'
  })
}

/**
 * 表头
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function configList() {
  return request({
    url: '/statement-report-builder/config'
  })
}

/**
 * 创建报表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function create(params: object) {
  return request({
    url: '/statement-report-builder/create',
    method: 'post',
    data: params
  })
}
