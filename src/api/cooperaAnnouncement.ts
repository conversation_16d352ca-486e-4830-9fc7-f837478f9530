import request from '/@/utils/request'

// 公告审核列表

export function getCooperaAuditList(params: Object) {
  return request({
    url: '/announcement/audit-list',
    params
  })
}

// 公告操作日志

export function getAnnouncementHandleLog(params: Object) {
  return request({
    url: '/announcement/get-announcement-handle-log',
    params
  })
}

// 业务--收到的简历

export function getAnnouncementApplyList(params: Object) {
  return request({
    url: '/announcement/get-announcement-job-apply-list',
    params
  })
}

// 业务--面试邀约
export function getAnnouncementInterviewList(params: Object) {
  return request({
    url: '/announcement/get-announcement-interview-list',
    params
  })
}

// 业务--下载的简历
export function getResumeDownloadLog(params: Object) {
  return request({
    url: '/announcement/get-resume-download-log',
    params
  })
}

// 业务--站外投递

export function getOutsideApplyList(params: Object) {
  return request({
    url: '/announcement/get-outside-apply-list',
    params
  })
}
