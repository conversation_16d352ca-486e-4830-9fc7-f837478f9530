import request from '/@/utils/request'

// 公告回收站列表

export function getAnnouncementRecycleList(params: Object) {
  return request({
    url: '/announcement/recycle-list',
    params
  })
}

// 职位回收站列表

export function getJobRecycleList(params: Object) {
  return request({
    url: '/announcement/recycle-job-list',
    params
  })
}

// 公告操作-还原

export function announcementRecycleReduction(params: Object) {
  return request({
    url: '/announcement/recycle-reduction',
    method: 'post',
    data: params
  })
}

// 职位操作-还原

export function jobRecycleReduction(params: Object) {
  return request({
    url: '/announcement/recycle-job-reduction',
    method: 'post',
    data: params
  })
}

// 公告操作-批量还原

export function announcementRecycleBatchReduction(params: Object) {
  return request({
    url: '/announcement/recycle-batch-reduction',
    method: 'post',
    data: params
  })
}

// 职位操作-批量还原

export function jobRecycleBatchReduction(params: Object) {
  return request({
    url: '/announcement/recycle-job-batch-reduction',
    method: 'post',
    data: params
  })
}

// 广告列表
export function getShowcaseList(params: Object) {
  return request({
    url: '/showcase/get-showcase-list',
    method: 'get',
    params
  })
}

// 恢复广告
export function adRecycleReduction(params: Object) {
  return request({
    url: '/showcase/reload-showcase',
    method: 'post',
    data: params
  })
}
