import request from '/@/utils/request'

export function getList(params: Object) {
  return request({
    url: '/major/get-list',
    method: 'get',
    params
  })
}

export function getAiTextList(params: Object) {
  return request({
    url: '/major/get-ai-text-list',
    method: 'get',
    params
  })
}

export function addAiTex(data: Object) {
  return request({
    url: '/major/add-ai-text',
    method: 'post',
    data
  })
}

export function getAiTextDetail(id: string) {
  return request({
    url: '/major/get-ai-text-detail',
    method: 'get',
    params: { id }
  })
}

export function aiRecognition(text: string) {
  return request({
    url: 'major/ai-text-recognition',
    method: 'get',
    params: { text }
  })
}

export function getAiLogList(params: Object) {
  return request({
    url: '/major/get-ai-log-list',
    method: 'get',
    params
  })
}

export function editSynonym(data: Object) {
  return request({
    url: '/major/edit-synonym',
    method: 'post',
    data
  })
}

export function deleteAiText(id: number) {
  return request({
    url: '/major/delete-ai-text',
    method: 'post',
    data: { id }
  })
}
