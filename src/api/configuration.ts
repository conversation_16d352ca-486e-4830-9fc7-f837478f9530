import request from '/@/utils/request'

/**
 * 单位归属业务员变更记录表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyAdminChangeLog(params: object) {
  return request({
    url: '/service-configuration/get-company-admin-change-log',
    method: 'get',
    params
  })
}

/**
 * 模糊查找单位信息
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyList(params: object) {
  return request({
    url: '/service-configuration/get-company-list',
    method: 'get',
    params
  })
}

/**
 * 模糊查找业务员
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getAdminList(params: object) {
  return request({
    url: '/service-configuration/get-admin-list',
    method: 'get',
    params
  })
}

/**
 * 单位业务员变更
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function companyAdminChange(params: object) {
  return request({
    url: '/service-configuration/company-admin-change',
    method: 'post',
    data: params
  })
}

/**
 * 单位权益配置列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyPackageConfigLog(params: object) {
  return request({
    url: '/service-configuration/get-company-package-config-log',
    method: 'get',
    params
  })
}

/**
 * 权益包有效期列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getPackageAmountList() {
  return request({
    url: '/service-configuration/get-package-amount-list',
    method: 'get'
  })
}

/**
 * 新增单位权益
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyPackageList() {
  return request({
    url: '/service-configuration/get-company-package-config',
    method: 'get'
  })
}

/**
 * 新增单位权益
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function addConfigurationPackage(params: object) {
  return request({
    url: '/service-configuration/create',
    method: 'post',
    data: params
  })
}

/**
 * 获取权益明细-服务项目选项
 * @returns 返回接口数据
 */
export function getServiceOption() {
  return request({
    url: '/service-configuration/get-service-items'
  })
}

/**
 * 获取权益明细-操作类型选项
 * @returns 返回接口数据
 */
export function getHandleTypeOption() {
  return request({
    url: '/service-configuration/get-handle-type-name'
  })
}

/**
 * 权益明细列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyPackageChangeLog(params: object) {
  return request({
    url: '/service-configuration/get-company-package-change-log',
    method: 'get',
    params
  })
}

/**
 * 设置下载点数
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function setResumeDownloadNum(params: Object) {
  return request({
    url: '/service-configuration/set-resume-download-num',
    method: 'post',
    data: params
  })
}

/**
 * 单位修改日志列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyDeliveryChangeLogList(params: object) {
  return request({
    url: '/company-delivery-change-log/list',
    params
  })
}

/**
 * 修改单位投递类型生成单位投递类型修改日志
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeCompanyDelivery(params: object) {
  return request({
    url: '/company-delivery-change-log/add',
    method: 'post',
    data: params
  })
}

/**
 * 获取筛选项配置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyDeliverySearch() {
  return request({
    url: '/company-delivery-change-log/search'
  })
}

/**
 * 获取置顶配置列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getJobTopConfigList(params: object) {
  return request({
    url: '/job-top-config/get-list',
    params
  })
}

/**
 * 模糊查找职位信息列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getSearchJobInfoList(params: object) {
  return request({
    url: '/job/search-job-info-list',
    params
  })
}

/**
 * 保存置顶配置信息
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function saveJobTopConfig(params: object) {
  return request({
    url: '/job-top-config/save',
    method: 'post',
    data: params
  })
}

/**
 * 获取职位置顶配置
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getJobTopDetail(params: object) {
  return request({
    url: 'job-top-config/get-detail',
    params
  })
}

/**
 * 修改排序
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function jobTopConfigChangeSort(params: object) {
  return request({
    url: 'job-top-config/change-sort',
    method: 'post',
    data: params
  })
}

/**
 * 暂停置顶/重启置顶
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function jobTopConfigChangeStatus(params: object) {
  return request({
    url: 'job-top-config/change-top-status',
    method: 'post',
    data: params
  })
}

/**
 * 移除记录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function jobTopConfigRemove(params: object) {
  return request({
    url: 'job-top-config/remove',
    method: 'post',
    data: params
  })
}

/**
 * 投递邀约查询列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getInviteList(params: object) {
  return request({
    url: '/delivery-invite/get-list',
    params
  })
}

/**
 * 投递邀约查询列表带出
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getInviteListExport(params: object) {
  return request({
    url: '/delivery-invite/get-list-export',
    params
  })
}

/**
 * 求职者--订单列表
 * @param params
 * @returns
 */
export function getPersonOrderList(params: object) {
  return request({ url: '/order/person-list', params })
}

/**
 * 求职者--订单列表参数
 * @param params
 * @returns
 */
export function getPersonOrderListParams() {
  return request({ url: '/order/person-list-params' })
}

/**
 * 求职者--订单详情
 * @param {string} id
 * @returns
 */
export function getPersonOrderDetail(id: string) {
  return request({ url: '/order/person-detail', params: { id } })
}

/**
 * 求职者--订单详情备注
 * @param {object} data
 * @returns
 */
export function setPersonOrderDetailNote(data: object) {
  return request({ url: '/order/detail-remark', method: 'post', data })
}

/**
 * 求职者--订单列表导出
 * @param params
 * @returns
 */
export function getPersonOrderListFile(params: object) {
  return request({ url: '/order/person-export', params })
}

/**
 * 求职者-权益筛选项
 * @returns 返回接口数据
 */
export function getPersonEquityFilter() {
  return request({
    url: '/equity/filter'
  })
}

/**
 * 求职者--权益列表
 * @param params
 * @returns
 */
export function getPersonEquityList(params: object) {
  return request({ url: '/equity/index', params })
}

/**
 * 求职者--权益列表导出
 * @param params
 * @returns
 */
export function getPersonEquityExport(params: object) {
  return request({ url: '/equity/export', params })
}

/**
 * 配置权益列表
 * @returns 返回接口数据
 */
export function getOtherPackageList() {
  return request({
    url: '/service-configuration/get-other-package-list'
  })
}

/**
 * 直聊记录列表
 * @returns 返回接口数据
 */
export function getChatRoomList(params: object) {
  return request({
    url: '/chat-room/get-chat-room-list',
    params
  })
}

/**
 * 直聊记录列表
 * @returns 返回接口数据
 */
export function getChatLastMessage(params: object) {
  return request({
    url: '/chat-room/get-last-message',
    params
  })
}

/**
 * 获取埋点所有action列表
 * @returns 返回接口数据
 */
export function getAllActionList() {
  return request({
    url: '/pay-transform-buried-point-log/get-all-action-list'
  })
}

/**
 * 导出支付转化埋点日志
 * @returns 返回接口数据
 */
export function exportLogList(params: object) {
  return request({
    url: '/pay-transform-buried-point-log/export-log-list',
    method: 'post',
    data: params
  })
}

/**
 * 人才套餐list筛选项
 * @returns 返回接口数据
 */
export function getSetMealParamsList() {
  return request({
    url: '/resume-equity-admin-setting/filter'
  })
}

/**
 * 人才套餐list
 * @returns 返回接口数据
 */
export function getSetMealList(params: object) {
  return request({
    url: '/resume-equity-admin-setting/list',
    params
  })
}

/**
 * 人才套餐导出下载
 * @returns 返回接口数据
 */
export function getSetMealListFile(params: object) {
  return request({
    url: '/resume-equity-admin-setting/export',
    params
  })
}

/**
 * 检查手机号
 * @returns 返回接口数据
 */
export function checkMobile(params: object) {
  return request({
    url: '/resume-equity-admin-setting/check',
    method: 'post',
    data: params
  })
}

/**
 * 人才套餐数据删除
 * @returns 返回接口数据
 */
export function delMealSetting(params: object) {
  return request({
    url: '/resume-equity-admin-setting/delete',
    params
  })
}

/**
 * 获取套餐配置详情
 * @returns 返回接口数据
 */
export function getMealDetail(params: object) {
  return request({
    url: '/resume-equity-admin-setting/init',
    params
  })
}

/**
 * 查看套餐编辑
 * @returns 返回接口数据
 */
export function mealViewEdit(params: object) {
  return request({
    url: '/resume-equity-admin-setting/view-edit',
    method: 'post',
    data: params
  })
}

/**
 * 配置审核
 * @returns 返回接口数据
 */
export function mealAudit(params: object) {
  return request({
    url: '/resume-equity-admin-setting/setting-audit',
    method: 'post',
    data: params
  })
}

/**
 * 添加配置
 * @returns 返回接口数据
 */
export function addMeal(params: object) {
  return request({
    url: '/resume-equity-admin-setting/add',
    method: 'post',
    data: params
  })
}

/**
 * 添加配置
 * @returns 返回接口数据
 */
export function editMeal(params: object) {
  return request({
    url: '/resume-equity-admin-setting/edit',
    method: 'post',
    data: params
  })
}

/**
 * 退款审核提交
 * @returns 返回接口数据
 */
export function submitRefund(params: object) {
  return request({
    url: '/resume-equity-admin-setting/refund-submit',
    method: 'post',
    data: params
  })
}

/**
 * 获取退款信息
 * @returns 返回接口数据
 */
export function getRefundMessage(params: object) {
  return request({
    url: '/resume-equity-admin-setting/refund-init',
    params
  })
}

/**
 * 退款审核
 * @returns 返回接口数据
 */
export function auditRefund(params: object) {
  return request({
    url: '/resume-equity-admin-setting/refund-audit',
    method: 'post',
    data: params
  })
}

/**
 * 获取投递限制列表
 */
export function getLimitlList(params: object) {
  return request({
    url: '/job-apply-limit/get-list',
    params
  })
}

/**
 * 获取参数
 */
export function getParams(params: object) {
  return request({
    url: '/job-apply-limit/get-params',
    params
  })
}

/**
 * 添加职位限制配置
 */
export function jobApplyLimitadd(params: object) {
  return request({
    url: '/job-apply-limit/add',
    method: 'post',
    data: params
  })
}

/**
 * 编辑职位限制配置
 */
export function jobApplyLimitEdit(params: object) {
  return request({
    url: '/job-apply-limit/edit',
    method: 'post',
    data: params
  })
}

/**
 * 获取单个详情
 */
export function getDetail(params: object) {
  return request({
    url: '/job-apply-limit/get-detail',
    params
  })
}

/**
 * 删除配置
 */
export function jobApplyLimitDel(params: object) {
  return request({
    url: '/job-apply-limit/delete',
    method: 'post',
    data: params
  })
}