import request from '/@/utils/request'

/**
 * 获取下载任务列表
 * @returns 返回接口数据
 */
export function getList(params: object) {
  return request({
    url: '/download-task/load-list',
    method: 'get',
    params
  })
}

/**
 * 获取下载url
 * @param id
 */
export function getUrl(id: number) {
  return request({
    url: '/download-task/get-url',
    method: 'get',
    params: { id }
  })
}

export function cancelDownloadTask(id: number) {
  return request({
    url: '/download-task/close',
    method: 'get',
    params: { id }
  })
}
