import request from '/@/utils/request'

/**
 * 列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageIndex(params: object) {
  return request({
    url: '/resume-equity-package-setting/index',
    params
  })
}

/**
 * 添加
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageAdd(params: object) {
  return request({
    url: '/resume-equity-package-setting/add',
    method: 'post',
    data: params
  })
}


/**
 * 编辑
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageEdit(params: object) {
  return request({
    url: '/resume-equity-package-setting/edit',
    method: 'post',
    data: params
  })
}

/**
 * 编辑初始化
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageEditInit(params: object) {
  return request({
    url: '/resume-equity-package-setting/edit-init',
    params
  })
}

/**
 * 删除状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageStatus(params: object) {
  return request({
    url: '/resume-equity-package-setting/status',
    method: 'post',
    data:params
  })
}

/**
 * 过滤
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function equityPackageFilter(params: object) {
  return request({
    url: '/resume-equity-package-setting/filter',
    params
  })
}

