import request from '/@/utils/request'

/**
 * 列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function friendLinkIndex(params: object) {
  return request({
    url: '/friend-link-config/index',
    params
  })
}

/**
 * 添加
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function friendLinkAdd(params: object) {
  return request({
    url: '/friend-link-config/add',
    method: 'post',
    data: params
  })
}


/**
 * 编辑
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function friendLinkEdit(params: object) {
  return request({
    url: '/friend-link-config/edit',
    method: 'post',
    data: params
  })
}

/**
 * 编辑初始化
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function friendLinkEditInit(params: object) {
  return request({
    url: '/friend-link-config/edit-init',
    params
  })
}

/**
 * 删除状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function friendLinkStatus(params: object) {
  return request({
    url: '/friend-link-config/status',
    method: 'post',
    data:params
  })
}

