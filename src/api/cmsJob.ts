import request from '/@/utils/request'

/**
 * 职位列表
 * @returns 返回接口数据
 */
export function getJobList(params: object) {
  return request({
    url: '/job/get-agent-job-list',
    method: 'get',
    params
  })
}

/**
 *删除职位
 * @returns 返回接口数据
 */
export function deleteJob(params: Object) {
  return request({
    url: '/job/delete-agent-job',
    method: 'post',
    data: params
  })
}

/**
 *投递列表
 * @returns 返回接口数据
 */
export function getJobApplyList(params: Object) {
  return request({
    url: '/job/job-apply-list',
    method: 'get',
    params
  })
}

/**
 *获取报名方式
 * @returns 返回接口数据
 */
export function getJobSignUpList() {
  return request({
    url: '/job/get-sign-up-list',
    method: 'get'
  })
}

/**
 *模糊查找职位列表
 * @returns 返回接口数据
 */
export function getSearchJobList(params: Object) {
  return request({
    url: '/job/search-job-list',
    method: 'get',
    params
  })
}

/**
 *单位检索
 * @returns 返回接口数据
 */
export function getCompanyList(params: Object) {
  return request({
    url: '/job/get-company-list',
    method: 'get',
    params
  })
}

/**
 *发布/保存新职位
 * @returns 返回接口数据
 */
export function jobCreate(params: Object) {
  return request({
    url: '/job/create',
    method: 'post',
    data: params
  })
}

/**
 *批量新增职位
 * @returns 返回接口数据
 */
export function jobBatchImport(params: Object) {
  return request({
    url: '/job/job-batch-import',
    method: 'post',
    data: params
  })
}

/**
 * 职位编制
 * @returns 返回接口数据
 */
export function getJobEstablishmentList() {
  return request({
    url: '/config/get-systematic-list'
  })
}
