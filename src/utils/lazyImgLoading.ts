import { nextTick } from 'vue'

/**
 * 图片懒加载
 * @param el dom 目标元素
 * @param arr 列表数据
 * @description data-xxx 属性用于存储页面或应用程序的私有自定义数据
 */
export const lazyImgLoading = (el: any, arr: any) => {
  const io = new IntersectionObserver((res) => {
    res.forEach((v: any) => {
      if (v.isIntersecting) {
        const { img, key } = v.target.dataset
        v.target.src = img
        v.target.onload = () => {
          io.unobserve(v.target)
          arr[key].loading = false
        }
      }
    })
  })
  nextTick(() => {
    document.querySelectorAll(el).forEach((img) => io.observe(img))
  })
}
