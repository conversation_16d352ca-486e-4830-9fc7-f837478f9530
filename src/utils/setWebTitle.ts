import { nextTick } from 'vue'
import router from '/@/router/index'
import { store } from '/@/store/index'

/**
 * 设置浏览器标题国际化
 */
export function useTitle() {
  return () => {
    nextTick(() => {
      let webTitle: unknown | string = '高校人才网'
      const { globalTitle } = store.state.themeConfig.themeConfig

      if (router.currentRoute.value.path === '/login') {
        webTitle = router.currentRoute.value.meta.title
      }

      document.title = `${webTitle} - ${globalTitle}` || globalTitle
    })
  }
}
