import axios from 'axios'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Local } from '/@/utils/storage'
import qs from 'qs'

// 配置新建一个 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL as any,
  timeout: 500000
})

let loading = <any>null

// 返回的数据格式是any
declare module 'axios' {
  interface AxiosInstance {
    (config: AxiosRequestConfig): Promise<any>
  }
}
// 添加请求拦截器
service.interceptors.request.use(
  (config) => {
    // 对数据进行一些数据,让后端yii可以接收到
    if (config.method === 'get') {
      config.paramsSerializer = (params) => {
        return qs.stringify(params)
      }
    } else {
      loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      config.data = qs.stringify(config.data)
    }
    // 在发送请求之前做些什么 token
    if (Local.get('token')) {
      config.headers.Authorization = `${Local.get('token')}`
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    if (loading) {
      try {
        loading.close()
      } catch (err) {
        //
      }
    }
    return Promise.reject(error)
  }
)

// 添加响应拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    const res = response.data
    if (loading) {
      try {
        loading.close()
      } catch (error) {
        //
      }
    }
    if (res.result === 1) {
      // 正常
      if (res.msg !== '') {
        ElMessage.success(res.msg)
      }
      return res.data
    }
    if (res.code === 403) {
      Local.clear() // 清除浏览器全部临时缓存
      window.location.href = '/' // 去登录页
      ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
        .then(() => {})
        .catch(() => {})
      return true
    }
    // 异常
    if (res.msg !== '') {
      ElMessage.error(res.msg)
    }

    if (res.data) {
      return Promise.reject(res.data)
    }
    return Promise.reject(res.msg)
  },
  (error) => {
    if (loading) {
      try {
        loading.close()
      } catch (err) {
        //
      }
    }
    // 对响应错误做点什么
    if (error.message.indexOf('timeout') !== -1) {
      ElMessage.error('网络超时')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接错误')
    } else if (error.response.data) ElMessage.error(error.response.statusText)
    else ElMessage.error('接口路径找不到')
    return Promise.reject(error)
  }
)

// 导出 axios 实例
export default service
