import { getMenu } from '../api/admin'
import { setAddRoute, setFilterMenuAndCacheTagsViewRoutes } from '/@/router/index'
import { dynamicRoutes } from '/@/router/route'
import { store } from '/@/store/index.ts'
import { NextLoading } from '/@/utils/loading'
import { Local } from '/@/utils/storage'

const layouModules: any = import.meta.glob('../layout/routerView/*.{vue,tsx}')
const viewsModules: any = import.meta.glob('../views/**/*.{vue,tsx}')
/**
 * 获取目录下的 .vue、.tsx 全部文件
 * @method import.meta.glob
 * @link 参考：https://cn.vitejs.dev/guide/features.html#json
 */
const dynamicViewsModules: Record<string, Function> = {
  ...layouModules,
  ...viewsModules
}

/**
 * 后端控制路由：初始化方法，防止刷新时路由丢失
 * @method  NextLoading 界面 loading 动画开始执行
 * @method store.dispatch('userInfos/setUserInfos') 触发初始化用户信息
 * @method store.dispatch('requestOldRoutes/setBackEndControlRoutes') 存储接口原始路由（未处理component），根据需求选择使用
 * @method setAddRoute 添加动态路由
 * @method setFilterMenuAndCacheTagsViewRoutes 设置递归过滤有权限的路由到 vuex routesList 中（已处理成多级嵌套路由）及缓存多级嵌套数组处理后的一维数组
 */
export async function initBackEndControlRoutes() {
  // 界面 loading 动画开始执行
  if (window.nextLoading === undefined) NextLoading.start()
  // 无 token 停止执行下一步
  if (!Local.get('token')) return false
  // 触发初始化用户信息
  store.dispatch('userInfos/setUserInfos')
  // 获取路由菜单数据
  const res = await getBackEndControlRoutes()
  // 处理路由（component），替换 dynamicRoutes（/@/router/route）第一个顶级 children 的路由
  dynamicRoutes[0].children = await backEndComponent(res)
  // 添加动态路由
  await setAddRoute()
  // 设置递归过滤有权限的路由到 vuex routesList 中（已处理成多级嵌套路由）及缓存多级嵌套数组处理后的一维数组
  setFilterMenuAndCacheTagsViewRoutes()
}

/**
 * 请求后端路由菜单接口
 * @description isRequestRoutes 为 true，则开启后端控制路由
 * @returns 返回后端路由菜单数据
 */
export async function getBackEndControlRoutes() {
  const r = await getMenu()
  const action = {}
  const res = r.map((item: any) => {
    if (item.action && item.action.length > 0) {
      action[item.route] = item.action
    }
    return item.route
  })

  // 存储接口原始路由（未处理component），根据需求选择使用
  store.dispatch('requestOldRoutes/setBackEndControlRoutes', res)
  store.dispatch('requestOldRoutes/setBackEndControlRoutesAction', action)

  return getFilterList(dynamicRoutes, res)
}

/*
写一个方法返回正确的数组
1.拿到后端返回来的数据
2.跟路由那边对比一下是否name相等，返回数组
*/
/**
 * 把路由传进来，遍历路由，新增一个最终返回的数组a，一个开关，遍历
 * 路由子集，新增子集数组b,先判断后端传来的参数有没有子集的内容，
 * 如果有的话，把对应的子集路由放到数组b中（判断是否有首页的路由，有的话放到数组a中），
 * 得到子集的路由列表，再判断后端传来的值有没有包括路由本身或者子集列表中已经有值，
 * 如果有的话，替换子集的路由列表，并放到返回的数组a中为了避免重复赋值，使用break关键字跳出循环
 */
export async function getFilterList(dynamicRoutes: any, res: any) {
  // 设置一个数组放完整数组
  const routerPathList = []
  // 遍历路由
  for (let i = 0; i < dynamicRoutes.length; i++) {
    // 如果有children的话并且有长度
    if (dynamicRoutes[i].children && dynamicRoutes[i].children.length > 0) {
      // 新增一个数组替换掉原来的children
      const replaceChildren = []
      // 开关，当showParentMenu等于1时显示父级菜单
      let showParentMenu = 0
      // 再遍历children这个数组
      for (let v = 0; v < dynamicRoutes[i].children.length; v++) {
        // routeName是children的名字
        const routeName = dynamicRoutes[i].children[v].name
        // 后端回来的数据作比较
        for (let k = 0; k < res.length; k++) {
          if (routeName === res[k] && routeName === 'home') {
            routerPathList.push(dynamicRoutes[i].children[v])
          }
          // 如果相等的话就加进去
          else if (routeName === res[k]) {
            replaceChildren.push(dynamicRoutes[i].children[v])
            showParentMenu = 1
          }
        }
      }
      // 如果没有children的话直接对比name，有的话加进去大数组
      for (let index = 0; index < res.length; index++) {
        if (dynamicRoutes[i].name === res[index] || showParentMenu === 1) {
          dynamicRoutes[i].children = replaceChildren
          // delete dynamicRoutes[i].children
          routerPathList.push(dynamicRoutes[i])
          break
        }
      }
    }
  }
  return routerPathList
}

/**
 * 重新请求后端路由菜单接口
 * @description 用于菜单管理界面刷新菜单（未进行测试）
 * @description 路径：/src/views/system/menu/component/addMenu.vue
 */
export function setBackEndControlRefreshRoutes() {
  getBackEndControlRoutes()
}

/**
 * 后端路由 component 转换
 * @param routes 后端返回的路由表数组
 * @returns 返回处理成函数后的 component
 */
export function backEndComponent(routes: any) {
  if (!routes) return
  return routes.map((item: any) => {
    if (item.component) item.component = dynamicImport(dynamicViewsModules, item)
    item.children && backEndComponent(item.children)
    return item
  })
}

/**
 * 后端路由 component 转换函数
 * @param dynamicViewsModules 获取目录下的 .vue、.tsx 全部文件
 * @param component 当前要处理项 component
 * @returns 返回处理成函数后的 component
 */
export function dynamicImport(dynamicViewsModules: Record<string, Function>, route: any) {
  const keys = Object.keys(dynamicViewsModules)
  const matchKeys = keys.filter((key) => {
    return key.indexOf(route.path) > -1
  })
  if (matchKeys?.length === 1) {
    const matchKey = matchKeys[0]
    return dynamicViewsModules[matchKey]
  }
  if (matchKeys?.length > 1) {
    return route.component
  }
  return route.component
}
