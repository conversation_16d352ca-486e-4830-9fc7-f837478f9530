import { RouteRecordRaw } from 'vue-router'

export const announcementRoutes: Array<RouteRecordRaw> = [
  {
    path: '/announcement',
    name: 'announcement',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '公告管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/announcement.svg'
    },
    children: [
      {
        path: '/announcement/list',
        name: 'announcementList',
        component: () => import('/@/views/announcement/list.vue'),
        meta: {
          title: '公告查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/announcement/auditList',
        name: 'announcementAuditList',
        component: () => import('/@/views/announcement/auditList.vue'),
        meta: {
          title: '公告审核',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/announcement/business',
        name: 'announcementBusiness',
        component: () => import('/@/views/announcement/business.vue'),
        meta: {
          title: '公告查询-业务',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
