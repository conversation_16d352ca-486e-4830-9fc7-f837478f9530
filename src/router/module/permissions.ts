import { RouteRecordRaw } from 'vue-router'

export const permissionsRoutes: Array<RouteRecordRaw> = [
  {
    path: '/permissions',
    name: 'permissions',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '权限管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/permissions.svg'
    },
    children: [
      {
        path: '/position/list',
        name: 'positionList',
        component: () => import('/@/views/permissions/positionList.vue'),
        meta: {
          title: '角色管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/route/list',
        name: 'routeList',
        component: () => import('/@/views/permissions/routeList.vue'),
        meta: {
          title: '路由管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/admin/list',
        name: 'adminList',
        component: () => import('/@/views/permissions/adminList.vue'),
        meta: {
          title: '账户管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
