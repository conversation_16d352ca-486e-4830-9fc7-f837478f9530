import { RouteRecordRaw } from 'vue-router'

export const seoRoutes: Array<RouteRecordRaw> = [
  {
    path: '/seo',
    name: 'seo',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: 'seo管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/announcement.svg'
    },
    children: [
      {
        path: '/seo/baiduZZList',
        name: 'seoBaiduZZList',
        component: () => import('/@/views/seo/baiduZZ/list/index.vue'),
        meta: {
          title: '链接推送',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/seo/seoSitemapList',
        name: 'seoSitemapList',
        component: () => import('/@/views/seo/sitemap/list/index.vue'),
        meta: {
          title: 'sitemap文件',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/seo/keyword',
        name: 'keyword',
        component: () => import('/@/views/seo/keyword/index.vue'),
        meta: {
          title: '关键词管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/nginxLog/list',
        name: 'nginxLogList',
        component: () => import('/@/views/seo/nginxLog/list/index.vue'),
        meta: {
          title: 'nginx日志',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
