import { RouteRecordRaw } from 'vue-router'

export const abroadRoutes: Array<RouteRecordRaw> = [
  {
    path: '/abroad',
    name: 'abroad',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '高才海外',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/abroad.svg'
    },
    children: [
      {
        path: '/abroad/activityList',
        name: 'abroadActivity',
        component: () => import('/@/views/abroad/activity/index.vue'),
        meta: {
          title: '活动管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/active/add',
        name: 'abroadActiveAdd',
        component: () => import('../../views/abroad/activity/add.vue'),
        meta: {
          title: '新增活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/active/edit/:id',
        name: 'abroadActiveEdit',
        component: () => import('../../views/abroad/activity/add.vue'),
        meta: {
          title: '编辑活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/advertisingList',
        name: 'abroadAdvertising',
        component: () => import('/@/views/abroad/advertising/index.vue'),
        meta: {
          title: '高才海外广告管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/specialActivity',
        name: 'specialActivity',
        component: () => import('/@/views/abroad/specialActivity/list.vue'),
        meta: {
          title: '专场管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/specialActivity/add',
        name: 'specialActivityAdd',
        component: () => import('/@/views/abroad/specialActivity/add.vue'),
        meta: {
          title: '新增专场活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/specialActivity/edit/:id',
        name: 'specialActivityEdit',
        component: () => import('/@/views/abroad/specialActivity/add.vue'),
        meta: {
          title: '编辑专场活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
