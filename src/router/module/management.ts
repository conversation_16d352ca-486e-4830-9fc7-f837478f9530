import { RouteRecordRaw } from 'vue-router'

export const managementRoutes: Array<RouteRecordRaw> = [
  {
    path: '/management',
    name: 'management',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '用户运营',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/person.svg'
    },
    children: [
      {
        path: '/management/companyGroup',
        name: 'companyGroup',
        component: () => import('/@/views/management/companyGroup/list.vue'),
        meta: {
          title: '单位群组',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // 验证码
      {
        path: '/management/verificationCode',
        name: 'verificationCode',
        component: () => import('/@/views/management/verificationCode/list.vue'),
        meta: {
          title: '验证码',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
