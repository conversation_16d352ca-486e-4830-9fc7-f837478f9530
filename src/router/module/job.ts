import { RouteRecordRaw } from 'vue-router'

export const jobRoutes: Array<RouteRecordRaw> = [
  {
    path: '/job',
    name: 'job',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/job/query',
    meta: {
      title: '职位管理',
      isLink: '',
      isHide: false,
      isKeepAlive: false,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/job.svg'
    },
    children: [
      {
        path: '/job/query',
        name: 'jobQuery',
        component: () => import('/@/views/job/query/index.vue'),
        meta: {
          title: '职位查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/job/queryV2',
        name: 'jobQueryV2',
        component: () => import('/@/views/job/queryV2/index.vue'),
        meta: {
          title: '会员查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/job/query/business/:id',
        name: 'jobQueryBusiness',
        component: () => import('/@/views/job/query/business.vue'),
        meta: {
          title: '职位查询-业务',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/job/audit',
        name: 'jobAudit',
        component: () => import('/@/views/job/audit/index.vue'),
        meta: {
          title: '职位审核',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/job/audit/detail/:id',
        name: 'jobAuditDetail',
        component: () => import('/@/views/job/audit/detail.vue'),
        meta: {
          title: '审核详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/job/edit/:id',
        name: 'jobEdit',
        component: () => import('/@/views/job/edit/index.vue'),
        meta: {
          title: '编辑职位',
          isLink: '',
          isHide: true,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
