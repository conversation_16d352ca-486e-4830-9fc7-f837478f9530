import { RouteRecordRaw } from 'vue-router'

export const testRoutes: Array<RouteRecordRaw> = [
  {
    path: '/demo',
    name: 'demo',
    component: () => import('/@/views/demo/demo-index/index.vue'),
    meta: {
      title: '开发示例',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/cms.svg'
    },
    children: [
      {
        path: '/demo/index-demo1',
        name: 'indexDemo1',
        component: () => import('/@/views/demo/demo-index/index.vue'),
        meta: {
          title: '列表示例1',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  },
  {
    path: '/demoList1',
    name: 'demoList',
    component: () => import('/@/views/demo/demo1/list.vue'),
    meta: {
      title: '内容管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/person.svg'
    }
  },

  {
    path: '/column',
    name: 'column',
    component: () => import('/@/views/cms/column/list.vue'),
    meta: {
      title: '栏目管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: ''
    }
  },

  {
    path: '/unitManage',
    name: 'unitManage',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '列表示例1',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: ''
    }
  }
]
