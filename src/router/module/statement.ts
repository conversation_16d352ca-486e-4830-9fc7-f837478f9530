// 报表管理
import { RouteRecordRaw } from 'vue-router'

export const statementRoutes: Array<RouteRecordRaw> = [
  {
    path: '/statement',
    name: 'statement',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/statement',
    meta: {
      title: '报表管理',
      isLink: '',
      isHide: false,
      isKeepAlive: false,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/configuration.svg'
    },
    children: [
      {
        path: '/statement/reportBuilder',
        name: 'statementReportBuilder',
        component: () => import('/@/views/statement/reportBuilder/index.vue'),
        meta: {
          title: '报表生成器',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // bi报表
      {
        path: '/statement/biReport',
        name: 'statementBiReport',
        component: () => import('/@/views/statement/biReport/index.vue'),
        meta: {
          title: 'bi报表',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: true,
          icon: ''
        }
      }
    ]
  }
]
