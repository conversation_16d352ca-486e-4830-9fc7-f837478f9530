import { RouteRecordRaw } from 'vue-router'

export const memberRoutes: Array<RouteRecordRaw> = [
  {
    path: '/member',
    name: 'member',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/member/logList',
    meta: {
      title: '会员管理',
      isLink: '',
      isHide: true,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/person.svg'
    },
    children: [
      {
        path: '/member/logList',
        name: 'memberLogList',
        component: () => import('/@/views/member/logList.vue'),
        meta: {
          title: '会员日志',
          isLink: '',
          isHide: true,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/member/vipInfo',
        name: 'memberVipInfo',
        component: () => import('/@/views/member/vipInfo.vue'),
        meta: {
          title: '会员信息',
          isLink: '',
          isHide: true,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
