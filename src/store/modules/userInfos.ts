import { Module } from 'vuex'
import { Local } from '/@/utils/storage.ts'
// 此处加上 `.ts` 后缀报错，具体原因不详
import { UserInfosState, RootStateTypes } from '/@/store/interface/index'

const userInfosModule: Module<UserInfosState, RootStateTypes> = {
  namespaced: true,
  state: {
    userInfos: Local.get('userInfo') || {}
  },
  mutations: {
    // 设置用户信息
    getUserInfos(state: any, data: object) {
      state.userInfos = data
      Local.set('userInfo', data)
    }
  },
  actions: {
    // 设置用户信息
    async setUserInfos({ commit }, data: object = Local.get('userInfo') || {}) {
      commit('getUserInfos', data)
    }
  }
}

export default userInfosModule
