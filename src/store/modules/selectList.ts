import { Module } from 'vuex'
// 此处加上 `.ts` 后缀报错，具体原因不详
import { SelectListState, RootStateTypes } from '/@/store/interface/index'

const selectListModule: Module<SelectListState, RootStateTypes> = {
  namespaced: true,
  state: {
    selectList: {
      majorList: [], // 学科专业
      majorSecondList: [], // 学科专业(二级)
      categoryJobList: [], // 职位类型
      adroadExperienceList: [], // 海外经历
      ageList: [], // 年龄要求
      educationList: [], // 学历要求
      educationSearchList: [], // 学历要求
      expectSalaryList: [], // 期望月薪/薪资要求
      experienceList: [], // 工作经验/工作年限
      levelTitleList: [], // 职称(一级)
      levelTitleMultistageList: [], // 职称(多级)
      memberSourceList: [], // 注册来源
      nativePlaceList: [], // 户籍/国籍
      personStatusList: [], // 求职状态
      politicalList: [], // 政治面貌
      regionSecondList: [], // 地区(二级)
      regionFullList: [], // 地区(三级)
      tradeList: [], // 行业类别
      wageList: [], // 福利待遇
      workExperienceList: [], // 工作经验/工作年限
      wordNatureList: [], // 工作性质
      arriveDateList: [], // 到岗时间
      adPlatFormList: [], // 广告所属平台
      adPositionList: [], // 广告位置
      adMiniPositionList: [], // 广告位置
      adAbroadPositionList: [], // 高才海外广告位置
      adBoShiHouPositionList: [], // 高才博士后广告位置
      jobApplyPlatform: [], // 投递端口
      isMiniappList: [], // 是否小程序
      establishmentTypeList: [], // 编制类型
      companyTypeList: [], // 单位类型
      companyNatureList: [] // 单位性质
    }
  },
  mutations: {
    // 设置
    getSelectList(state: any, data: Array<object>) {
      state.selectList = data
    }
  },
  actions: {
    // 设置
    async setSelectList({ commit }, data: any) {
      commit('getSelectList', data)
    }
  }
}

export default selectListModule
