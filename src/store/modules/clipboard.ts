import { Module } from 'vuex'
import { StateInterface } from '../interface'

export interface ClipboardItem {
  type: 'text'
  content: string
  timestamp: number
}

export interface ClipboardState {
  items: ClipboardItem[]
}

// 从本地存储加载数据
const loadFromStorage = (): ClipboardItem[] => {
  try {
    const stored = localStorage.getItem('clipboard-history')
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Failed to load clipboard history:', error)
    return []
  }
}

// 保存到本地存储
const saveToStorage = (items: ClipboardItem[]) => {
  try {
    localStorage.setItem('clipboard-history', JSON.stringify(items))
  } catch (error) {
    console.error('Failed to save clipboard history:', error)
  }
}

const clipboardModule: Module<ClipboardState, StateInterface> = {
  namespaced: true,
  state: {
    items: loadFromStorage() // 初始化时加载本地存储的数据
  },
  mutations: {
    addItem(state, item: ClipboardItem) {
      // 只检查最新一条记录是否重复
      const latestItem = state.items[0]
      if (!latestItem || latestItem.content !== item.content) {
        state.items.unshift(item)
        // 限制最大记录数
        if (state.items.length > 50) {
          state.items.pop()
        }
        saveToStorage(state.items)
      }
    },
    clearAll(state) {
      state.items = []
      saveToStorage(state.items)
    },
    removeItem(state, timestamp: number) {
      state.items = state.items.filter((item) => item.timestamp !== timestamp)
      saveToStorage(state.items)
    }
  },
  // 添加 actions 用于处理复制事件
  actions: {
    async handleCopy({ commit }) {
      try {
        const text = await navigator.clipboard.readText()
        if (text) {
          commit('addItem', {
            type: 'text',
            content: text,
            timestamp: Date.now()
          })
        }
      } catch (error) {
        console.error('Failed to read clipboard:', error)
      }
    }
  }
}

export default clipboardModule
