import { Module } from 'vuex'
// 此处加上 `.ts` 后缀报错，具体原因不详
import { AppGlobalState, RootStateTypes } from '/@/store/interface/index'

const AppGlobalModule: Module<AppGlobalState, RootStateTypes> = {
  namespaced: true,
  state: {
    showLoading: false
  },
  mutations: {
    getLoading: (state: any, type: Boolean) => {
      state.showLoading = type
    }
  },
  actions: {
    // 设置路由缓存（name字段）
    setLoading({ commit }, type: Boolean) {
      commit('getLoading', type)
    }
  }
}

export default AppGlobalModule
