// 接口类型声明

// 布局配置
export interface ThemeConfigState {
  themeConfig: {
    isDrawer: boolean
    primary: string
    success: string
    info: string
    warning: string
    danger: string
    topBar: string
    menuBar: string
    columnsMenuBar: string
    topBarColor: string
    menuBarColor: string
    columnsMenuBarColor: string
    isTopBarColorGradual: boolean
    isMenuBarColorGradual: boolean
    isColumnsMenuBarColorGradual: boolean
    isMenuBarColorHighlight: boolean
    isCollapse: boolean
    isUniqueOpened: boolean
    isFixedHeader: boolean
    isFixedHeaderChange: boolean
    isClassicSplitMenu: boolean
    isLockScreen: boolean
    lockScreenTime: number
    isShowLogo: boolean
    isShowLogoChange: boolean
    isBreadcrumb: boolean
    isTagsview: boolean
    isBreadcrumbIcon: boolean
    isTagsviewIcon: boolean
    isCacheTagsView: boolean
    isSortableTagsView: boolean
    isShareTagsView: boolean
    isFooter: boolean
    isGrayscale: boolean
    isInvert: boolean
    isWartermark: boolean
    wartermarkText: string
    tagsStyle: string
    animation: string
    columnsAsideStyle: string
    columnsAsideLayout: string
    layout: string
    isRequestRoutes: boolean
    globalTitle: string
    globalViceTitle: string
    globalComponentSize: string
  }
}

// 路由列表
export interface RoutesListState {
  routesList: Array<object>
}

// 路由缓存列表
export interface KeepAliveNamesState {
  keepAliveNames: Array<string>
}

// TagsView 路由列表
export interface TagsViewRoutesState {
  tagsViewRoutes: Array<object>
  isTagsViewCurrenFull: Boolean
}

// 用户信息
export interface UserInfosState {
  userInfos: object
}

// 后端返回原始路由(未处理时)
export interface RequestOldRoutesState {
  requestOldRoutes: Array<object>
  requestOldRoutesAction: Object
}

// 整个app的一些全局信息(现在只放loading)
export interface AppGlobalState {
  showLoading: Boolean
}

// 剪贴板
export interface ClipboardState {
  clipboard: Array<object>
}

// 整个app的baseSelect的暂存
export interface SelectListState {
  selectList: {
    majorList: Array<object> // 学科专业
    majorSecondList: Array<object> // 学科专业(二级)
    categoryJobList: Array<object> // 职位类型
    adroadExperienceList: Array<object> // 海外经历
    ageList: Array<object> // 年龄要求
    educationList: Array<object> // 学历要求
    educationSearchList: Array<object> // 学历筛选
    expectSalaryList: Array<object> // 期望月薪/薪资要求
    experienceList: Array<object> // 工作经验/工作年限
    levelTitleList: Array<object> // 职称
    levelTitleMultistageList: Array<object> // 职称
    memberSourceList: Array<object> // 注册来源
    nativePlaceList: Array<object> // 户籍/国籍
    personStatusList: Array<object> // 求职状态
    politicalList: Array<object> // 政治面貌
    regionSecondList: Array<object> // 地区(二级)
    regionFullList: Array<object> // 地区(三级级)
    tradeList: Array<object> // 行业类别
    wageList: Array<object> // 福利待遇
    workExperienceList: Array<object> // 工作经验/工作年限
    wordNatureList: Array<object> // 工作性质
    arriveDateList: Array<object> // 到岗时间
    adPlatFormList: Array<object> // 广告所属平台
    adMiniPositionList: Array<object> // 广告位置
    adPositionList: Array<object> // 小程序广告位置
    adAbroadPositionList: Array<object> // 高才海外广告位置
    adBoShiHouPositionList: Array<object> // 高才博士后广告位置
    jobApplyPlatform: Array<object> // 投递端口
    isMiniappList: Array<object> // 是否小程序
    establishmentTypeList: Array<object> // 编制类型
    companyTypeList: Array<object> // 单位类型
    companyNatureList: Array<object> // 单位性质
  }
}

// 主接口(顶级类型声明)
export interface RootStateTypes {
  themeConfig: ThemeConfigState
  routesList: RoutesListState
  selectList: SelectListState
  keepAliveNames: KeepAliveNamesState
  tagsViewRoutes: TagsViewRoutesState
  userInfos: UserInfosState
  requestOldRoutes: RequestOldRoutesState
  appGlobal: AppGlobalState
  clipboard: ClipboardState
}
