import { Injection<PERSON><PERSON> } from 'vue'
import { createStore, useStore as baseUseStore, Store } from 'vuex'
import { RootStateTypes } from '/@/store/interface/index'

// Vite supports importing multiple modules from the file system using the special import.meta.glob function
// see https://cn.vitejs.dev/guide/features.html#glob-import
const modulesFiles = import.meta.glob('./modules/*.ts', { eager: true })
const pathList: string[] = Object.keys(modulesFiles)

const modules = pathList.reduce((previous: { [x: string]: any }, current: string) => {
  const moduleName = current.replace(/^\.\/modules\/(.*)\.\w+$/, '$1')
  const value: any = modulesFiles[current]
  const result = { ...previous }

  result[moduleName] = value.default
  return result
}, {})

export const key: InjectionKey<Store<RootStateTypes>> = Symbol('')

export const store = createStore<RootStateTypes>({
  modules: {
    ...modules
  }
})

export function useStore() {
  return baseUseStore(key)
}
