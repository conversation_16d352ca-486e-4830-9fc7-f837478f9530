<template>
  <el-menu
    router
    :default-active="defaultActive"
    background-color="transparent"
    active-text-color="#fff"
    :collapse="setIsCollapse"
    :unique-opened="getThemeConfig.isUniqueOpened"
    :collapse-transition="false"
    class="menu"
  >
    <template v-for="val in menuLists">
      <el-sub-menu :index="val.path" v-if="val.children && val.children.length > 0" :key="val.path">
        <template #title>
          <i class="icon" :style="{ backgroundImage: 'url(' + getImgSrc(val.meta.icon) + ')' }"></i>
          <span>{{ val.meta.title }}</span>
        </template>
        <SubItem :chil="val.children" />
      </el-sub-menu>
      <el-menu-item :index="val.path" :key="val.path" v-else>
        <i class="icon" :style="{ backgroundImage: 'url(' + getImgSrc(val.meta.icon) + ')' }"></i>
        <template #title v-if="!val.meta.isLink || (val.meta.isLink && val.meta.isIframe)">
          <span>{{ val.meta.title }}</span>
        </template>
        <template #title v-else>
          <a :href="val.meta.isLink" target="_blank" rel="opener">{{ val.meta.title }}</a></template
        >
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script lang="ts">
import { toRefs, reactive, computed, defineComponent, getCurrentInstance } from 'vue'
import { useRoute, onBeforeRouteUpdate } from 'vue-router'
import { useStore } from '/@/store/index'
import SubItem from '/@/layout/navMenu/subItem.vue'

export default defineComponent({
  name: 'navMenuVertical',
  components: { SubItem },
  props: {
    menuList: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const ImgModule = import.meta.glob('../../assets/icons/*', { eager: true })

    const { proxy } = getCurrentInstance() as any
    const store = useStore()
    const route = useRoute()
    const state = reactive({
      // 修复：https://gitee.com/lyt-top/vue-next-admin/issues/I3YX6G
      defaultActive: route.meta.isDynamic ? route.meta.isDynamicPath : route.path
    })
    // 获取父级菜单数据
    const menuLists = computed(() => {
      return props.menuList
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 设置菜单的收起/展开
    const setIsCollapse = computed(() => {
      return document.body.clientWidth < 1000 ? false : getThemeConfig.value.isCollapse
    })
    // 路由更新时
    onBeforeRouteUpdate((to) => {
      // 修复：https://gitee.com/lyt-top/vue-next-admin/issues/I3YX6G
      state.defaultActive = to.meta.isDynamic ? to.meta.isDynamicPath : to.path
      proxy.mittBus.emit('onMenuClick')
      const { clientWidth } = document.body
      if (clientWidth < 1000) getThemeConfig.value.isCollapse = false
    })
    const getImgSrc = (src: string) => {
      const key = src.replace(/^(\/src\/)/, '../../')
      return ImgModule[key].default
    }
    return {
      menuLists,
      getThemeConfig,
      setIsCollapse,
      getImgSrc,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.menu {
  .icon {
    display: inline-block;
    height: 18px;
    width: 18px;
    margin-right: 16px;
    background-size: contain;
  }
  .el-menu-item {
    font-size: 15px;
    opacity: 0.8;
    padding-left: 30px !important;
    color: #fff !important;
    * {
      vertical-align: middle;
    }
    &:hover {
      opacity: 1;
    }
    &.is-active {
      opacity: 1;
      background-color: var(--color-primary) !important;
    }
  }
}
:deep() {
  .el-sub-menu {
    .el-sub-menu__title {
      opacity: 0.8;
      font-size: 15px;
      padding-left: 30px !important;
      color: #fff !important;
      &:hover {
        opacity: 1;
        i {
          color: inherit;
        }
      }
    }
    .el-menu-item {
      padding-left: 65px !important;
    }
  }
}

// 收起状态
.el-menu.el-menu--collapse {
  .icon {
    margin-right: 0;
  }
  :deep() {
    .el-sub-menu__title {
      padding-left: 20px !important;
      & > span {
        height: 0;
        width: 0;
        overflow: hidden;
      }
      i.el-icon {
        display: none !important;
      }
    }
  }
}
</style>
