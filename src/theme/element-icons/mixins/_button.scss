@use 'sass:map';

@use '../common/var' as *;

@mixin button-plain($color, $type) {
  color: $color;
  background-color: map.get($--colors, $type, 'light-9');
  border-color: map.get($--colors, $type, 'light-6');

  &:hover,
  &:focus {
    background: $color;
    border-color: $color;
    color: var(--el-color-white);
  }

  &:active {
    background: var(--el-button-active-background-color);
    border-color: var(--el-button-active-border-color);
    color: var(--el-color-white);
    outline: none;
  }

  &.is-disabled {
    &,
    &:hover,
    &:focus,
    &:active {
      color: map.get($--colors, $type, 'light-4');
      background-color: map.get($--colors, $type, 'light-9');
      border-color: map.get($--colors, $type, 'light-8');
    }
  }
}

@mixin button-variant($type) {
  &:hover,
  &:focus {
    background: var(--el-button-hover-color);
    border-color: var(--el-button-hover-color);
    color: var(--el-button-font-color);
  }

  &:active {
    background: var(--el-button-active-background-color);
    border-color: var(--el-button-active-border-color);
    color: var(--el-button-active-font-color);
    outline: none;
  }

  &.is-active {
    background: var(--el-button-active-background-color);
    border-color: var(--el-button-active-border-color);
    color: var(--el-button-active-font-color);
  }

  &.is-disabled {
    &,
    &:hover,
    &:focus,
    &:active {
      color: $--color-white;
      background-color: map.get($--colors, $type, 'light-5');
      border-color: map.get($--colors, $type, 'light-5');
    }
  }

  &.is-plain {
    @include button-plain(var(--el-button-background-color), $type);
  }
}

@mixin button-size($padding-vertical, $padding-horizontal, $font-size, $border-radius) {
  padding: $padding-vertical $padding-horizontal;
  font-size: $font-size;
  border-radius: $border-radius;

  &.is-round {
    padding: $padding-vertical $padding-horizontal;
  }
}
