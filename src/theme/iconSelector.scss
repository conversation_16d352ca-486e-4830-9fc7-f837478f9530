/* Popover 弹出框(图标选择器)
------------------------------- */
.icon-selector-popper {
  padding: 0 !important;
  .icon-selector-warp {
    height: 260px;
    overflow: hidden;
    .icon-selector-warp-title {
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
    }
    .icon-selector-warp-row {
      height: 230px;
      overflow: hidden;
      border-top: 1px solid #ebeef5;
      .el-row {
        padding: 15px;
      }
      .el-scrollbar__bar.is-horizontal {
        display: none;
      }
      .icon-selector-warp-item {
        display: flex;
        border: 1px solid #ebeef5;
        padding: 5px;
        border-radius: 5px;
        margin-bottom: 10px;
        .icon-selector-warp-item-value {
          i {
            font-size: 20px;
            color: #606266;
          }
        }
        &:hover {
          cursor: pointer;
          background-color: var(--color-primary-light-9);
          border: 1px solid var(--color-primary-light-6);
          .icon-selector-warp-item-value {
            i {
              color: var(--color-primary);
            }
          }
        }
      }
      .icon-selector-active {
        background-color: var(--color-primary-light-9);
        border: 1px solid var(--color-primary-light-6);
        .icon-selector-warp-item-value {
          i {
            color: var(--color-primary);
          }
        }
      }
    }
  }
}
