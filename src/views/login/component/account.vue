<template>
  <el-form class="login-content-form" ref="accountLoginForm" :rules="rule" :model="ruleForm">
    <el-form-item prop="account">
      <el-input
        type="primary"
        link
        placeholder="用户名"
        v-model="ruleForm.username"
        @keyup.enter="onLogin"
        clearable
        autocomplete="off"
      >
        <template #prefix>
          <i class="el-icon-user"></i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        :type="isShowPassword ? 'text' : 'password'"
        placeholder="密码"
        v-model="ruleForm.password"
        autocomplete="off"
        @keyup.enter="onLogin"
      >
        <template #prefix>
          <i class="el-icon-lock"></i>
        </template>

        <template #suffix>
          <i
            class="iconfont el-input__icon login-content-password"
            :class="isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
            @click="isShowPassword = !isShowPassword"
          ></i>
        </template>
      </el-input>
    </el-form-item>
    <!-- <el-form-item prop="code">
            <el-row :gutter="15">
                <el-col :span="16">
                    <el-input
                        type="primary" link
                        maxlength="4"
                        placeholder="请输入验证码"
                        prefix-icon="el-icon-position"
                        v-model="ruleForm.code"
                        clearable
                        autocomplete="off"
                    ></el-input>
                </el-col>
                <el-col :span="8">
                    <div class="login-content-code">
                        <span class="login-content-code-img">1234</span>
                    </div>
                </el-col>
            </el-row>
        </el-form-item>-->
    <el-form-item>
      <el-button
        type="primary"
        class="login-content-submit"
        round
        @click="onLogin"
        :loading="loading.login"
        :disabled="!canSubmit"
      >
        <span>登录</span>
      </el-button>
    </el-form-item>
    <el-form-item class="login-animation-five">
      <el-button type="primary" link size="small" @click="wxWorkdLogin">企业微信登录</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, computed, getCurrentInstance, onUnmounted } from 'vue'
import { accountLogin, checkWxWorkLogin, getWxWorkLoginConfig } from '/@/api/admin'
import { ElNotification } from 'element-plus'

export default defineComponent({
  name: 'login',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const state = reactive({
      isShowPassword: false,
      ruleForm: {
        username: '',
        password: ''
      },
      rule: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      loading: {
        login: false
      }
    })

    const canSubmit = computed(() => {
      return state.ruleForm.username !== '' && state.ruleForm.password !== ''
    })

    if (process.env.NODE_ENV === 'development') {
      state.ruleForm.username = 'admin'
      state.ruleForm.password = '123456'
    }

    // 表单验证
    const formRulesValidate = () => {
      return new Promise((resolve) => {
        proxy.$refs.accountLoginForm.validate((valid: any) => {
          if (valid) resolve(valid)
        })
      })
    }

    // 登录
    const onLogin = async () => {
      await formRulesValidate()
      try {
        state.loading.login = true
        const rs = await accountLogin(state.ruleForm)
        // 关闭 loading
        state.loading.login = false
        proxy.mittBus.emit('loginSuccess', rs)
        // 弹出一个长时间警告
        // ElNotification({
        //   title: '警告',
        //   message: '账号密码登录功能即将在1月13号下线,请替换成企业微信扫码登录',
        //   type: 'warning',
        //   duration: 0
        // })
      } catch (error) {
        state.loading.login = false
      }
    }

    let time: any

    const wxWorkdLogin = async () => {
      const rs = await getWxWorkLoginConfig()
      const { appid } = rs
      // 打开登录页面
      const { agentid } = rs
      const { state } = rs
      const { redirectUri } = rs
      const iWidth = 600 // 弹出窗口的宽度;
      const iHeight = 600 // 弹出窗口的高度;

      const iTop = (window.screen.height - 30 - iHeight) / 2 // 获得窗口的垂直位置;
      const iLeft = (window.screen.width - 10 - iWidth) / 2 // 获得窗口的水平位置;

      // 打开登录页面
      window.open(
        `https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=${appid}&agentid=${agentid}&redirect_uri=${redirectUri}&state=${state}`,
        'newwindow',
        `height=${iWidth}, width=${iHeight}, top=${iTop},left=${iLeft},toolbar =no, menubar=no, scrollbars=no, resizable=no, location=no, status=no`
      )
      // 开启轮询6
      if (time) clearInterval(time)

      time = setInterval(() => {
        checkWxWorkLogin({ state }).then((rs) => {
          if (rs.token) {
            clearInterval(time)
            proxy.mittBus.emit('loginSuccess', rs)
          }
        })
      }, 300) // time 毫秒之后执行
    }

    onUnmounted(() => {
      // 关闭轮询
      clearInterval(time)
    })

    return {
      onLogin,
      canSubmit,
      wxWorkdLogin,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.login-content-form {
  margin-top: 20px;
  .login-content-password {
    display: inline-block;
    width: 25px;
    cursor: pointer;
    &:hover {
      color: #909399;
    }
  }
  .login-content-code {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .login-content-code-img {
      width: 100%;
      height: 40px;
      line-height: 40px;
      background-color: #ffffff;
      border: 1px solid rgb(220, 223, 230);
      color: #333;
      font-size: 16px;
      font-weight: 700;
      letter-spacing: 5px;
      text-indent: 5px;
      text-align: center;
      cursor: pointer;
      transition: all ease 0.2s;
      border-radius: 4px;
      user-select: none;
      &:hover {
        border-color: #c0c4cc;
        transition: all ease 0.2s;
      }
    }
  }
  .login-content-submit {
    width: 100%;
    letter-spacing: 2px;
    font-weight: 300;
    margin-top: 15px;
  }
}
</style>
