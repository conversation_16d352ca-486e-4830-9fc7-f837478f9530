<template>
  <div class="login-container">
    <div class="login-logo">
      <span>{{ getThemeConfig.globalViceTitle }}</span>
    </div>
    <div class="login-content">
      <div class="login-content-main">
        <h4 class="login-content-title">{{ getThemeConfig.globalTitle }}</h4>
        <div>
          <el-tabs v-model="tabsActiveName" @tab-click="onTabsClick">
            <Account v-show="isTabPaneShow" />
          </el-tabs>
        </div>
      </div>
    </div>
    <div class="login-copyright">
      <div class="mb5 login-copyright-company">版权所有：广州高才信息科技有限公司</div>
      <!-- <div class="login-copyright-msg">备案号等等</div> -->
    </div>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, computed, getCurrentInstance } from 'vue'
import Account from '/@/views/login/component/account.vue'
import { ElMessage } from 'element-plus'
import { initBackEndControlRoutes } from '/@/router/backEnd'
import { useStore } from '/@/store/index'
import { useRoute, useRouter } from 'vue-router'
import { Session, Local } from '/@/utils/storage'

export default {
  name: 'login',
  components: { Account },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      tabsActiveName: 'account',
      isTabPaneShow: true,
      isScan: false
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 切换密码、手机登录
    const onTabsClick = () => {
      state.isTabPaneShow = !state.isTabPaneShow
    }

    // 登录成功后的跳转
    const afterLogininSuccess = () => {
      // 登录成功，跳到转首页
      // 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
      // 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
      if (route.query?.redirect) {
        router.push({
          path: <string>route.query?.redirect,
          query:
            Object.keys(<Object>route.query?.params).length > 0
              ? JSON.parse(<string>route.query?.params)
              : ''
        })
      } else {
        router.push('/')
      }
      // 登录成功提示
      setTimeout(() => {
        const loginText = '登录成功'
        ElMessage.success(`${loginText}`)
        // 修复防止退出登录再进入界面时，需要刷新样式才生效的问题，初始化布局样式等(登录的时候触发，目前方案)
        proxy.mittBus.emit('onLoginClick')
        proxy.mittBus.off('loginSuccess')
      }, 300)
    }

    proxy.mittBus.on('loginSuccess', async (rs: any) => {
      let defaultAuthPageList: Array<string> = []
      let defaultAuthBtnList: Array<string> = []
      // admin 页面权限标识，对应路由 meta.auth，用于控制路由的显示/隐藏
      const adminAuthPageList: Array<string> = ['admin']
      // admin 按钮权限标识
      const adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.link']
      // 不同用户模拟不同的用户权限
      defaultAuthPageList = adminAuthPageList
      defaultAuthBtnList = adminAuthBtnList
      // 用户信息模拟数据
      const userInfos = {
        account: rs.username || rs.name,
        photo: rs.photo,
        status: rs.status,
        email: rs.email,
        mobile: rs.mobile,
        time: new Date().getTime(),
        authPageList: defaultAuthPageList,
        authBtnList: defaultAuthBtnList
      }
      // 存储 token 到浏览器缓存
      Local.set('token', rs.token)
      // 存储用户信息到浏览器缓存
      Local.set('userInfo', userInfos)
      // 1、请注意执行顺序(存储用户信息到vuex)
      store.dispatch('userInfos/setUserInfos', userInfos)
      // 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
      // 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
      await initBackEndControlRoutes()
      // 执行完 initBackEndControlRoutes，再执行 afterLogininSuccess
      afterLogininSuccess()
    })

    return {
      onTabsClick,
      getThemeConfig,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.login-container {
  width: 100%;
  height: 100%;
  background: url('/@/assets/images/login-bg.png') no-repeat;
  background-size: 100% 100%;
  .login-logo {
    position: absolute;
    top: 30px;
    left: 50%;
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--color-primary);
    letter-spacing: 2px;
    width: 90%;
    transform: translateX(-50%);
  }
  .login-content {
    width: 500px;
    padding: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translate3d(0, 0, 0);
    background-color: rgba(255, 255, 255, 0.99);
    border: 5px solid var(--color-primary-light-8);
    border-radius: 4px;
    transition: height 0.2s linear;
    height: 480px;
    overflow: hidden;
    z-index: 1;
    .login-content-main {
      margin: 0 auto;
      width: 80%;
      .login-content-title {
        color: #333;
        font-weight: 500;
        font-size: 22px;
        text-align: center;
        letter-spacing: 4px;
        margin: 15px 0 30px;
        white-space: nowrap;
      }
    }
    .login-content-main-sacn {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 50px;
      overflow: hidden;
      cursor: pointer;
      opacity: 0.7;
      transition: all ease 0.3s;
      &::before {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-bottom: 50px solid #ffffff;
        border-right: 50px solid transparent;
        z-index: 2;
        top: 0;
        right: 0;
      }
      &:hover {
        opacity: 1;
        transition: all ease 0.3s;
        color: var(--color-primary);
      }
      i {
        content: '';
        width: 48px;
        height: 50px;
        position: absolute;
        top: 0px;
        right: 0px;
        font-size: 47px;
        z-index: 1;
      }
    }
  }
  .login-content-mobile {
    height: 418px;
  }
  .login-copyright {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 30px;
    text-align: center;
    color: white;
    font-size: 12px;
    opacity: 0.8;
    .login-copyright-company {
      white-space: nowrap;
    }
    .login-copyright-msg {
      @extend .login-copyright-company;
    }
  }
}
</style>
