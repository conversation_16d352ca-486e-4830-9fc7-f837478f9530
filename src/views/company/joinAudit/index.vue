<template>
  <div class="join-audit">
    <div class="box" v-if="!isAdd">
      <el-card>
        <el-form ref="form" :model="formData" label-width="80px">
          <el-row>
            <el-col :span="4">
              <el-form-item label="单位名称" prop="fullName">
                <el-input
                  class="block"
                  v-model="formData.fullName"
                  placeholder="请填写单位名称"
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="单位类型" prop="type">
                <el-select
                  v-model="formData.type"
                  placeholder="不限"
                  class="block"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in unitList.companyTypeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="单位性质" prop="nature">
                <el-select
                  v-model="formData.nature"
                  placeholder="不限"
                  class="block"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in unitList.companyNatureList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="所在地区" prop="area">
                <Region v-model="formData.area" filterable multiple collapseTags></Region>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="所属行业" prop="industryId">
                <Industry v-model="formData.industryId" multiple fliter></Industry>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="创建时间" prop="addTimeFrom">
                <DatePickerRange
                  v-model:start="formData.addTimeFrom"
                  v-model:end="formData.addTimeTo"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="手机号码" prop="mobile">
                <el-input
                  v-model="formData.mobile"
                  placeholder="请填写手机号码"
                  class="block"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="审核状态" prop="auditStatus">
                <el-select v-model="formData.auditStatus" placeholder="不限" class="block">
                  <el-option
                    v-for="item in unitList.companyAuditStatusList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="入网来源" prop="sourceType">
                <el-select v-model="formData.sourceType" placeholder="不限" class="block">
                  <el-option
                    v-for="item in unitList.companySourceTypeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="创建人" prop="createName">
                <el-input
                  v-model="formData.createName"
                  placeholder="请填写创建人账号"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="注册邮箱" prop="email" label-width="80px">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="申请时间" prop="applyFrom">
                <DatePickerRange
                  v-model:start="formData.applyTimeFrom"
                  v-model:end="formData.applyTimeTo"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-button type="primary" @click="searchBtn">搜索</el-button>
        <el-button type="default" @click="resetForm">重置</el-button>
        <el-button type="default" @click="downloadExcel({ ...formData, export: 1 })"
          >下载</el-button
        >

        <div class="unitCount">
          共计：<span class="danger">{{ pagination.total }}</span
          >所单位； 待审核：<span class="danger">{{ stayExamineCount }}</span
          >所单位； 已驳回：<span class="danger">{{ rejectExamineCount }}</span
          >所单位；
        </div>
        <div class="showTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            style="width: 100%"
            align="center"
            @sort-change="handleSortable"
          >
            <el-table-column prop="fullName" label="单位名称" width="180" align="center" />
            <el-table-column prop="contact" label="联系人" align="center" />
            <el-table-column prop="cMobile" label="联系电话" align="center" width="120" />
            <el-table-column prop="cEmail" label="联系邮箱" width="120" align="center" />
            <el-table-column prop="department" label="所在部门" align="center" />
            <el-table-column prop="sourceTypeTxt" label="入网来源" align="center" />
            <el-table-column prop="createName" label="创建人" align="center" />
            <el-table-column prop="typeTxt" label="单位类型" align="center" />
            <el-table-column prop="natureTxt" label="单位性质" align="center" width="120px" />
            <el-table-column prop="address" label="所在地" width="120" align="center" />
            <el-table-column
              prop="sortAddTime"
              label="创建时间"
              sortable="custom"
              width="120"
              align="center"
            >
              <template #default="{ row }">
                <span>{{ row.addTime }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="sortApplyTime"
              label="申请时间"
              sortable="custom"
              width="120"
              align="center"
            >
              <template #default="{ row }">
                <span>{{ row.applyTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="auditStatusTxt" label="审核状态" width="120" align="center" />
            <el-table-column label="操作" align="center" width="120px" fixed="right">
              <template #default="{ row }">
                <el-row :gutter="10">
                  <el-col
                    :span="12"
                    v-if="
                      row.auditStatus === '7' ||
                      row.auditStatus === '-8' ||
                      row.auditStatus === '1' ||
                      row.auditStatus === '-9' ||
                      row.auditStatus === '6'
                    "
                  >
                    <el-button type="primary" class="opbutton" @click="checkDetails(row)">
                      查看</el-button
                    >
                  </el-col>
                  <el-col :span="12" v-else>
                    <el-button type="success" class="opbutton" @click="audit(row)"> 审核</el-button>
                  </el-col>
                  <el-col :span="12" v-if="row.sourceType === '2' && row.auditStatus === '-9'">
                    <el-button class="opbutton" @click="toEdit(row)">重新提交</el-button>
                  </el-col>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column prop="mEmail" label="注册邮箱" align="center" />
          </el-table>
        </div>
        <div class="paging">
          <Paging :total="pagination.total" @change="change"></Paging>
        </div>
      </el-card>
    </div>
    <router-view></router-view>
  </div>
</template>

<script lang="ts">
import { onMounted, reactive, ref, toRefs, watch, defineComponent } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { useRoute, useRouter } from 'vue-router'
import Region from '/@/components/base/select/region.vue'
import { getAuditList, getUnitList } from '/@/api/unitManage'
import Industry from '/@/components/base/industry.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

export default defineComponent({
  name: 'joinAudit',
  components: { Paging, Region, Industry, DatePickerRange },
  setup() {
    const state = reactive({
      formData: {
        fullName: '',
        type: '', // 单位类型
        nature: '', // 单位性质
        area: [], // 所在地区
        industryId: [], // 所属行业
        addTimeFrom: '', // 创建时间
        addTimeTo: '', // 创建时间
        applyTimeFrom: '', // 申请时间
        applyTimeTo: '', // 申请时间
        mobile: '', // 手机号码
        auditStatus: '', // 审核状态
        sourceType: '', // 入网来源
        createName: '', // 创建人
        email: '',
        // 排序页码
        page: 1,
        pageSize: 20,
        sortAddTime: ''
      },
      isAdd: false,
      unitList: {} as any,
      tableData: [],
      // 分页
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      rejectExamineCount: '',
      stayExamineCount: '',
      downloaUrl: '',
      loading: false
    })
    const router = useRouter()
    const route = useRoute()
    // 拿数据方法
    const getData = async () => {
      state.loading = true
      const {
        formData: { industryId, area },
        formData
      } = state
      const postData = {
        ...formData,
        industryId: industryId.length ? industryId.join() : '',
        area: area.length ? area.join() : ''
      }
      const res = await getAuditList(postData)
      state.tableData = res.list
      state.pagination = res.pages
      state.stayExamineCount = res.amount.stayExamineCount
      state.rejectExamineCount = res.amount.rejectExamineCount
      state.loading = false
    }
    onMounted(async () => {
      getData()
      state.unitList = await getUnitList()
    })
    const form = ref()
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      state.formData.addTimeTo = ''
      state.formData.industryId = []
      state.formData.area = []
      getData()
    }
    // 跳去详情页
    const checkDetails = (data: any) => {
      router.push({
        name: 'joinAuditDetails',
        params: { id: data.companyId, auditStatus: data.auditStatus }
      })
    }
    watch(
      () => route.name,
      (n) => {
        if (n === 'joinAuditDetails') {
          state.isAdd = true
        } else {
          state.isAdd = false
        }
      },
      { immediate: true }
    )
    // 审核按钮
    const audit = (data: any) => {
      router.push({
        name: 'joinAuditDetails',
        params: { id: data.companyId, auditStatus: data.auditStatus, sourceType: data.sourceType }
      })
    }
    // 搜索
    const searchBtn = async () => {
      state.loading = true
      const res = await getAuditList(state.formData)
      state.tableData = res.list
      state.pagination = res.pages
      state.stayExamineCount = res.amount.stayExamineCount
      state.rejectExamineCount = res.amount.rejectExamineCount
      state.loading = false
    }
    // 分页
    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getData()
    }
    // 下载
    const downloadExcel = async (data: any) => {
      const { excelUrl } = await getAuditList(data)
      state.downloaUrl = excelUrl
      window.location.href = excelUrl
    }
    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortAddTime')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getData()
    }
    const toEdit = (item: any) => {
      console.log('🚀 ~ file: index.vue ~ line 321 ~ toEdit ~ item', item)
      if (item.companyId) {
        router.push(`/company/add/${item.companyId}`)
      }
    }
    return {
      ...toRefs(state),
      resetForm,
      form,
      checkDetails,
      audit,
      searchBtn,
      change,
      downloadExcel,
      handleSortable,
      toEdit
    }
  }
})
</script>

<style lang="scss" scoped>
.unitCount {
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.paging {
  margin-top: 30px;
}
.opbutton {
  padding: 6px;
}
.el-table td.el-table__cell div {
  display: flex;
  justify-content: space-around;
}
:deep() {
  .el-table {
    .cell {
      padding-left: 0px;
    }
  }
}
</style>
