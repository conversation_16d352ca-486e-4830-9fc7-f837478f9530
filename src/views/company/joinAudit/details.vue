<template>
  <div class="details-box">
    <div class="title">详情</div>
    <el-card>
      <el-descriptions title="基本信息" border :column="3">
        <el-descriptions-item label="单位名称：" width="120px">{{
          basicsInfo.fullName
        }}</el-descriptions-item>
        <el-descriptions-item label="单位性质：" width="120px">{{
          basicsInfo.natureTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="所属行业：" width="120px">{{
          basicsInfo.industryTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="注册手机号：" width="120px">{{
          basicsInfo.mMobile
        }}</el-descriptions-item>
        <el-descriptions-item label="注册邮箱：" width="120px">{{
          basicsInfo.mEmail
        }}</el-descriptions-item>
        <el-descriptions-item label="账号性质：" width="120px">{{
          basicsInfo.accountNatureTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="投递配置：" width="120px">{{
          basicsInfo.deliveryTypeTxt
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="联系方式" :column="4" border>
        <el-descriptions-item label="联系人：" width="120px">{{
          basicsInfo.contact
        }}</el-descriptions-item>
        <el-descriptions-item label="所在部门：" width="120px">{{
          basicsInfo.department
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：" width="120px">{{
          basicsInfo.cMobile
        }}</el-descriptions-item>
        <el-descriptions-item label="固定电话：" width="120px">{{
          basicsInfo.telephone
        }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱：">{{ basicsInfo.cEmail }}</el-descriptions-item>
        <el-descriptions-item label="联系地址：">{{ basicsInfo.address }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="资质信息" :column="4">
        <el-descriptions-item label="单位资质证件：">
          <el-button type="primary" link @click="licensePreview"
            >预览</el-button
          ></el-descriptions-item
        >
        <el-descriptions-item label="经办人身份证明：">
          <el-button type="primary" link @click="personPreview"
            >预览</el-button
          ></el-descriptions-item
        >
      </el-descriptions>
      <div class="history" v-if="show === '7' || show === '-8' || show === '1' || show === '-9'">
        <div class="his-title">审核处理历史</div>
        <div class="his-table">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="actionTxt" label="操作" width="200" />
            <el-table-column prop="addTime" label="时间" width="200" />
            <el-table-column prop="auditStatusTxt" label="审核状态" />
            <el-table-column prop="reason" label="处理意见" />
          </el-table>
        </div>
      </div>
      <div class="opinion" v-else>
        <div class="op-title">审核处理意见</div>
        <el-input
          v-model="form.reason"
          :rows="2"
          type="textarea"
          placeholder="审核拒绝须明确拒绝原因"
        />
        <div class="button-box">
          <!--
            1 -> 自主申请：终审通过、终审拒绝
            2 -> 自主申请：初审通过、初审拒绝、终审通过
            3 -> 运营添加：终审通过、终审拒绝
          -->
          <template v-if="showButton === 2">
            <el-button type="primary" size="small" @click="dialogFormVisible = true">
              初审通过
            </el-button>

            <el-button type="primary" size="small" @click="trialReject">初审拒绝</el-button>
          </template>

          <el-button type="primary" size="small" @click="finalPass">终审通过</el-button>

          <el-button
            v-if="showButton === 1 || showButton === 3"
            type="primary"
            size="small"
            @click="finalReject"
          >
            终审拒绝
          </el-button>
        </div>
      </div>
    </el-card>
    <div class="mask">
      <!-- 外层的遮罩 -->
      <div class="mask-cover" v-if="licenseShow" @click="closeByMask"></div>
      <!-- 内容区 -->
      <div class="mask-content" v-if="licenseShow">
        <!-- 插槽，放置要插入到遮罩里的内容 -->
        <slot name="default">
          <img class="preImg" :src="basicsInfo.licensePath" alt="" />
        </slot>
      </div>
    </div>
  </div>
  <div class="mask">
    <!-- 外层的遮罩 -->
    <div class="mask-cover" v-if="personShow" @click="closeByMask"></div>
    <!-- 内容区 -->
    <div class="mask-content" v-if="personShow">
      <!-- 插槽，放置要插入到遮罩里的内容 -->
      <slot name="default">
        <img class="preImg" :src="basicsInfo.personInfoPath" alt="" />
      </slot>
    </div>
  </div>
  <!-- 初审通过弹框 -->
  <el-dialog v-model="dialogFormVisible" title="经办人资质处理意见">
    <el-form :model="form">
      <el-form-item>
        <el-radio v-model="form.isAgent" label="2">经办人信息为空</el-radio>
        <el-radio v-model="form.isAgent" label="1">经办人信息不为空</el-radio>
      </el-form-item>
      <el-form-item>
        <el-input
          v-show="form.isAgent == 1"
          v-model="form.agent"
          :rows="2"
          type="textarea"
          placeholder="请填写经办人资料拒绝原因"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="trialPass">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 终审通过弹窗 -->
  <el-dialog v-model="finalAuditDialog" title="请选择所属业务员">
    <el-select v-model="form.adminId" filterable placeholder="请选择">
      <el-option v-for="item in adminList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="finalAuditDialog = false">取消</el-button>
        <el-button type="primary" @click="finalAuditPass">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { computed, defineComponent, getCurrentInstance, reactive, ref, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { auditPass, finalAudit, getAllSale, getAuditDetails, rejectTrial } from '/@/api/unitManage'

export default defineComponent({
  name: 'joinAuditDetails',
  components: {},
  setup() {
    const route = useRoute()
    const { proxy } = getCurrentInstance() as any

    const sourceType = ref()
    const licenseShow = ref(false)
    const personShow = ref(false)

    const state = reactive({
      show: ref(route.params.auditStatus),
      basicsInfo: {},
      textarea: '',
      dialogFormVisible: false,
      finalAuditDialog: false,
      tableData: [],
      form: {
        isAgent: '',
        agent: '',
        reason: '',
        id: route.params.id,
        auditStatus: '',
        adminId: ''
      },
      adminList: [],
      passAudit: computed(
        () =>
          state.tableData[0]?.auditStatus === '7' ||
          state.tableData[0]?.auditStatus === '6' ||
          state.tableData[0]?.auditStatus === '-9'
      ),
      sourceStatus: computed(() => sourceType.value === '1'), // 自主申请
      /*
       1 -> 自主申请：终审通过、终审拒绝
       2 -> 自主申请：初审通过、初审拒绝、终审通过
       3 -> 运营添加：终审通过、终审拒绝
      */
      showButton: computed(() => {
        const { passAudit, sourceStatus } = state
        if (sourceStatus) {
          if (passAudit) {
            return 1
          }
          return 2
        }
        return 3
      })
    })

    // 数据回显
    const getDetail = async () => {
      const res = await getAuditDetails({ id: route.params.id })
      state.basicsInfo = res.basicsInfo
      state.show = res.basicsInfo.status
      state.tableData = res.auditRecordList
      sourceType.value = res.basicsInfo.sourceType
      state.adminList = await getAllSale()
    }
    getDetail()
    // 预览
    const licensePreview = () => {
      licenseShow.value = true
    }
    const personPreview = () => {
      personShow.value = true
    }
    // 关闭预览
    const closeByMask = () => {
      licenseShow.value = false
      personShow.value = false
    }
    // 初审通过
    const trialPass = async () => {
      if (state.form.isAgent === '1') {
        state.form.auditStatus = '6'
      } else {
        state.form.auditStatus = '7'
      }
      await auditPass({ ...state.form, auditStatus: state.form.auditStatus })
      state.dialogFormVisible = false
      proxy.mittBus.emit('closeCurrentViewTag')
    }
    // 终审通过
    const finalPass = async () => {
      // 弹出选择框
      if (sourceType.value === '1') {
        state.finalAuditDialog = true
        // const auditData = { id: state.form.id, auditStatus: 1, reason: state.form.reason }
        // const res = await finalAudit(auditData)
        // state.adminList = res.adminList
      } else {
        await finalAudit({ ...state.form, auditStatus: '1' })
        proxy.mittBus.emit('closeCurrentViewTag')
      }
    }
    // 初审拒绝
    const trialReject = async () => {
      if (state.form.reason === '') {
        ElMessage({
          message: '审核拒绝必须填写拒绝原因',
          type: 'warning'
        })
        return
      }
      await rejectTrial({ ...state.form, auditStatus: '-8' })
      proxy.mittBus.emit('closeCurrentViewTag')
    }
    // 终审拒绝
    const finalReject = async () => {
      if (state.form.reason === '') {
        ElMessage({
          message: '审核拒绝必须填写拒绝原因',
          type: 'warning'
        })
        return
      }
      await finalAudit({ ...state.form, auditStatus: '-9' })
      proxy.mittBus.emit('closeCurrentViewTag')
    }
    // 终审业务员
    const finalAuditPass = async () => {
      await finalAudit({ ...state.form, auditStatus: '1' })
      state.finalAuditDialog = false
      proxy.mittBus.emit('closeCurrentViewTag')
    }

    return {
      ...toRefs(state),
      licensePreview,
      closeByMask,
      licenseShow,
      personShow,
      trialPass,
      finalPass,
      finalAuditPass,
      finalReject,
      sourceType,
      trialReject,
      personPreview
    }
  }
})
</script>

<style lang="scss" scoped>
.title {
  border-left: 2px solid #196bf9;
  text-indent: 1em;
  margin-bottom: 15px;
}
.op-title {
  margin-bottom: 10px;
}
.button-box {
  margin-top: 10px;
}
.mask {
  position: relative;
  color: #2e2c2d;
  font-size: 16px;
}
//遮罩，设置背景层，z-index值要足够大确保能覆盖，高度 宽度设置满 做到全屏遮罩
.mask-cover {
  background: rgba($color: #000000, $alpha: 0.5);
  position: fixed;
  z-index: 9999;
  // 设置top、left、宽高保证全屏遮罩
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

//内容层，z-index要大于遮罩层，确保内容在遮罩上显示
.mask-content {
  position: fixed;
  top: 30%;
  height: 70%;
  z-index: 10000;
  .preImg {
    width: 200px;
    height: 200px;
  }
}
</style>
