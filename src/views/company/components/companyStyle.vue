<template>
  <div v-if="!type || type == 'banner'" class="banner">
    <div class="select">
      <el-upload
        accept="image/jpg,image/png,image/jpeg"
        action="/upload/base64"
        :auto-upload="false"
        :on-change="handleBannerChange"
        :show-file-list="showFileList"
        :data="{ uploadType: 'banner' }"
      >
        <el-button :size="triggerSize" type="primary">选择文件</el-button>
      </el-upload>
      <span class="tips">仅支持jpg、png、jpeg格式；图片尺寸1920px*370px。</span>
    </div>
    <div v-show="baseHeadBannerUrl" class="banner-img">
      <img :src="baseHeadBannerUrl" alt="" />
      <span @click="handleRemoveBannerSrc" class="remove-img-btn"></span>
    </div>
  </div>
  <div v-if="!type || type == 'style'" class="style">
    <div class="select">
      <el-upload
        ref="styleUploadRef"
        accept="image/jpg,image/png,image/jpeg"
        :disabled="styleDisabled"
        action="/upload/send-image"
        :data="{ companyId }"
        :on-exceed="handleExceed"
        :before-upload="handleStyleBeforeUpload"
        :on-success="handleStyleSuccess"
        :show-file-list="showFileList"
        :multiple="multiple"
        :file-list="fileList"
        :limit="styleMaxLimit"
      >
        <el-button
          :class="{ 'is-disabled': styleDisabled }"
          :size="triggerSize"
          type="primary"
          @click="handleDisabled"
          >选择文件</el-button
        >
      </el-upload>
      <span class="tips"
        >最多6张，仅支持jpg、png、jpeg格式；图片尺寸280 px* 140 px；大小不超过{{
          styleMaxSize
        }}M</span
      >
    </div>
    <div class="content">
      <div v-for="(item, index) in styleSrcArray" :key="index" class="list">
        <span @click="handleRemoveStyleSrc(index)" class="remove-img-btn"></span>
        <img :src="item.url" alt="" />
      </div>
    </div>
  </div>

  <el-dialog
    title="图片剪裁"
    append-to-body
    v-model="visible"
    width="auto"
    top="8vh"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="cropper-main">
      <div class="cropper-warp">
        <div class="cropper">
          <img :src="bannerBase64" class="cropper-img" />
        </div>
        <div class="cropper-preview">
          <div class="title">预览</div>
          <div class="company-bg-preview"></div>
        </div>
      </div>

      <div class="cropper-mobile">
        <div class="cropper-mobile-img">
          <span>移动端裁剪</span>
          <img :src="bannerBase64" class="cropper-img-mobile" />
        </div>
        <div class="cropper-mobile-preview">
          <div class="title">移动端预览</div>
          <div class="preview-mobile"></div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts">
import { watch, computed, defineComponent, nextTick, reactive, toRefs } from 'vue'
import { useRoute } from 'vue-router'

import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'

import { uploadBase64 } from '/@/api/unitManage'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'companyStyle',
  props: {
    // banner 单位主页背景，style 风采
    type: {
      type: String,
      default: () => ''
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    showFileList: {
      type: Boolean,
      default: () => false
    },
    // M为单位
    bannerMaxSize: {
      type: Number,
      default: () => 2
    },
    // M为单位
    styleMaxSize: {
      type: Number,
      default: () => 1
    },
    bannerSrc: {
      type: String,
      default: () => ''
    },
    styleSrc: {
      type: Array,
      default: () => []
    },
    styleMaxLimit: {
      type: Number,
      default: () => 6
    },
    triggerSize: {
      type: String,
      default: () => 'small'
    },
    bannerMobileSrc: {
      type: String,
      default: () => ''
    }
  },
  emits: ['update:bannerSrc', 'update:styleSrc', 'update:bannerMobileSrc'],
  setup(props: any, { emit }) {
    const route = useRoute()

    const state = reactive({
      companyId: route.query.id,
      visible: false,
      bannerBase64: '',
      mobileBnnnerBase64: '',
      submitLoading: false,
      fileList: <any>[],
      isFirstRun: true
    })

    const baseHeadBannerUrl = computed({
      get() {
        return props.bannerSrc
      },
      set(value) {
        emit('update:bannerSrc', value)
      }
    })

    const baseMobileHeadBannerUrl = computed({
      get() {
        return props.bannerMobileSrc
      },
      set(value) {
        emit('update:bannerMobileSrc', value)
      }
    })

    const styleSrcArray = computed<any>({
      get() {
        const { styleSrc } = props
        return styleSrc || []
      },
      set(value) {
        emit('update:styleSrc', value)
      }
    })

    const styleDisabled = computed(() => {
      const { length } = styleSrcArray.value
      return length >= props.styleMaxLimit
    })

    watch(
      () => props.styleSrc,
      (newStyleSrc) => {
        if (state.isFirstRun) {
          state.fileList = [...newStyleSrc]
          state.isFirstRun = false
        }
      }
    )

    // 关闭弹窗
    const closeDialog = () => {
      state.visible = false
    }

    // 取消
    const handleClose = () => {
      closeDialog()
    }

    const handleSubmit = () => {
      state.submitLoading = true
      uploadBase64(state.bannerBase64)
        .then((resp) => {
          baseHeadBannerUrl.value = resp.fullUrl
          state.submitLoading = false
          state.visible = false
        })
        .catch(() => {
          state.submitLoading = false
          ElMessage.error('上传出错')
        })
      uploadBase64(state.mobileBnnnerBase64)
        .then((resp: any) => {
          baseMobileHeadBannerUrl.value = resp.fullUrl
          state.submitLoading = false
          state.visible = false
        })
        .catch(() => {
          state.submitLoading = false
          ElMessage.error('上传出错')
        })
    }

    // 初始化cropperjs图片裁剪
    const initCropper = () => {
      const letImg: any = document.querySelector('.cropper-img')
      const mbImg: any = document.querySelector('.cropper-img-mobile')

      const cropper = new Cropper(letImg, {
        viewMode: 2,
        center: true,
        dragMode: 'move',
        initialAspectRatio: 5.19,
        aspectRatio: 5.19,
        autoCropArea: 0.85,
        zoomOnWheel: false,
        preview: '.company-bg-preview',
        ready: () => {
          state.bannerBase64 = cropper.getCroppedCanvas().toDataURL('image/jpeg')
        },
        cropend: () => {
          state.bannerBase64 = cropper.getCroppedCanvas().toDataURL('image/jpeg')
        }
      })

      const cropperMobile = new Cropper(mbImg, {
        viewMode: 2,
        center: true,
        dragMode: 'move',
        initialAspectRatio: 750 / 220,
        aspectRatio: 750 / 220,
        autoCropArea: 0.85,
        zoomOnWheel: false,
        preview: '.preview-mobile',
        ready: () => {
          state.mobileBnnnerBase64 = cropperMobile.getCroppedCanvas().toDataURL('image/jpeg')
        },
        cropend: () => {
          state.mobileBnnnerBase64 = cropperMobile.getCroppedCanvas().toDataURL('image/jpeg')
        }
      })
    }

    const handleBannerChange = (file) => {
      if (file.status === 'ready') {
        const reader = new FileReader()
        reader.readAsDataURL(file.raw)
        reader.onload = (e) => {
          const { target } = <any>e
          state.visible = true
          state.bannerBase64 = <string>target.result
          nextTick(() => {
            initCropper()
          })
        }
      }
    }

    // 超过最大选择数时的处理函数
    const handleExceed = () => {
      const { styleMaxLimit } = props
      ElMessage.error(`最多上传${styleMaxLimit}张单位风采`)
    }

    // 图片压缩函数
    const compressImage = (file) => {
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = (e: any) => {
          const img = new Image()
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx: any = canvas.getContext('2d')
            const maxWidth = 1024 // 最大宽度
            const maxHeight = 1024 // 最大高度
            let { width } = img
            let { height } = img

            // 根据最大宽度和最大高度计算图片的压缩比例
            if (width > height) {
              if (width > maxWidth) {
                height *= maxWidth / width
                width = maxWidth
              }
            } else if (height > maxHeight) {
              width *= maxHeight / height
              height = maxHeight
            }

            // 绘制到 canvas 上
            canvas.width = width
            canvas.height = height
            ctx.drawImage(img, 0, 0, width, height)

            // 将 canvas 转换为 data URL
            canvas.toBlob(
              (blob: any) => {
                const compressedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now()
                })
                resolve(compressedFile)
              },
              file.type,
              0.7 // 压缩质量，0.7 表示 70% 质量
            )
          }
          img.src = e.target.result
        }
        reader.readAsDataURL(file) // 将图片读取为 Data URL
      })
    }

    // 上传压缩后的文件
    const uploadFile = (file) => {
      // 将文件添加到 fileList
      const formData = new FormData()
      formData.append('file', file)
      // 调用上传接口
      // 假设你的上传接口是 POST 请求
      fetch('/upload/send-image', {
        method: 'POST',
        body: formData
      })
        .then((response) => response.json())
        .then((data) => {
          const { id, fullUrl: url } = data.data
          styleSrcArray.value = [...styleSrcArray.value, { id, url }]
        })
        .catch(() => {
          ElMessage.error('图片上传失败')
        })
    }

    const handleStyleBeforeUpload = (file) => {
      const { size } = file
      const { styleMaxSize } = props
      if (size / 1024 / 1024 > styleMaxSize) {
        // 压缩图片
        compressImage(file).then((compressedFile) => {
          // 将压缩后的文件替换原始文件进行上传
          uploadFile(compressedFile)
        })
        return false // 阻止原始文件上传
      }
      return true
    }

    const handleStyleSuccess = (resp, file, fileList) => {
      const {
        data: { id, fullUrl: url }
      } = resp
      styleSrcArray.value = [...styleSrcArray.value, { id, url }]

      state.fileList = fileList
    }

    const handleRemoveBannerSrc = () => {
      baseHeadBannerUrl.value = ''
      baseMobileHeadBannerUrl.value = ''
    }

    const handleRemoveStyleSrc = (index) => {
      const data = <any>styleSrcArray.value
      data.splice(index, 1)
      styleSrcArray.value = [...data]
      state.fileList = [...data]
    }

    const handleDisabled = () => {
      if (styleDisabled.value) {
        ElMessage.error(`最多上传${props.styleMaxLimit}张单位风采`)
      }
    }

    return {
      handleBannerChange,
      handleSubmit,
      handleClose,
      handleRemoveBannerSrc,
      handleRemoveStyleSrc,
      uploadFile,
      handleExceed,
      compressImage,
      handleStyleBeforeUpload,
      handleStyleSuccess,
      styleDisabled,
      baseHeadBannerUrl,
      baseMobileHeadBannerUrl,
      styleSrcArray,
      handleDisabled,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.select {
  display: flex;
  .tips {
    color: rgba($color: #333, $alpha: 0.8);
    margin-left: 8px;
    display: flex;
    align-items: center;
    &::before {
      content: '*';
      color: #fe1637;
    }
  }
}

.remove-img-btn {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba($color: #333, $alpha: 0.6);
  right: 8px;
  top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 2px;
    border-radius: 2px;
    background: #fff;
    transform: rotate(45deg);
  }
  &::after {
    transform: rotate(-45deg);
  }
}

.cropper-main {
  display: flex;
  align-items: center;
}

.banner {
  .banner-img {
    width: 480px;
    height: 100px;
    position: relative;
    margin-top: 20px;
    img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }
}

.style {
  .content {
    display: flex;
    flex-wrap: wrap;
    .list {
      margin-top: 20px;
      width: 160px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      margin-right: 16px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
.banner-img-mobile {
  margin-left: 20px;
  width: 400px;
  height: 80px;
  position: relative;
  margin-top: 20px;
  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.cropper-warp {
  .cropper {
    height: 350px;
    border: var(--el-border-base);
    background: var(--color-whites);
    overflow: hidden;
    background-repeat: no-repeat;
    cursor: move;
    border-radius: var(--el-border-radius-base);
    .cropper-img {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
  .cropper-preview {
    .title {
      padding: 20px 0 15px;
    }
    .company-bg-preview {
      width: 600px;
      height: 115px;
      overflow: hidden;
    }
  }
}
.cropper-mobile {
  margin-left: 20px;
  .cropper-mobile-img {
    height: 250px;
    border: var(--el-border-base);
    background: var(--color-whites);
    overflow: hidden;
    background-repeat: no-repeat;
    cursor: move;
    border-radius: var(--el-border-radius-base);
    .cropper-img-mobile {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
}
.cropper-mobile-preview {
  .title {
    padding: 20px 0 15px;
  }
  .preview-mobile {
    width: 300px;
    height: 115px;
    overflow: hidden;
  }
}
</style>
