<template>
  <el-dialog v-model="configAccountVisible" title="子账号设置" @close="close">
    <h3>当前单位：{{ companyInfo.fullName }}（{{ companyInfo.packageTypeTxt }}）</h3>
    <el-form class="form" ref="form" label-width="145px" :model="formData" :rules="rules">
      <el-form-item label="子账号总量：" prop="totalNumber">
        <div class="flex">
          <div><el-input v-model="formData.totalNumber" /></div>
          <div class="ml-5">个（已创建：{{ configInfo.used }}个）</div>
        </div>
      </el-form-item>

      <el-form-item label="VIP权限授权总量：" prop="vipTotalNumber">
        <div class="flex">
          <div><el-input v-model="formData.vipTotalNumber" :disabled="!isSenior" /></div>
          <div class="ml-5">个（已授权：{{ configInfo.vipUsed }}个）</div>
        </div>
      </el-form-item>
    </el-form>

    <div class="button-grounp">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs, watch } from 'vue'
import { saveAccountConfig } from '/@/api/account'

export default defineComponent({
  name: 'configAccount',

  components: {},

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },

  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      configAccountVisible: computed({
        get: () => props.visible,
        set: (val) => {
          emit('update:visible', val)
        }
      }),

      companyInfo: computed(() => props.data.companyInfo),
      configInfo: computed(() => props.data.configInfo),
      isSenior: computed(() => state.companyInfo?.packageType === '2'),

      formData: {
        id: computed(() => state.configInfo?.id),
        totalNumber: 0,
        vipTotalNumber: 0
      }
    })

    // 监听配置信息变化
    watch(
      () => state.configInfo,
      (val) => {
        if (val) {
          state.formData.totalNumber = val.total
          state.formData.vipTotalNumber = val.vipTotal
        }
      },
      { immediate: true }
    )

    const totalNumberValidate = (value, rules, callback) => {
      const { totalNumber } = state.formData
      const { used } = state.configInfo
      const valid = /^[0-9]\d*$/.test(totalNumber)

      if (!valid) {
        callback(new Error('请输入整数'))
      }

      if (Number(totalNumber) < used) {
        callback(new Error('子账号总量不能低于已创建子账号数量'))
      }

      callback()
    }

    const vipTotalNumberValidate = (value, rules, callback) => {
      const { vipTotalNumber } = state.formData
      const { vipUsed } = state.configInfo
      const valid = /^[0-9]\d*$/.test(vipTotalNumber)

      if (!valid) {
        callback(new Error('请输入整数'))
      }

      if (Number(vipTotalNumber) < vipUsed) {
        callback(new Error('VIP权限授权总量不能低于已创建VIP权限授权数量'))
      }

      callback()
    }

    const rules = ref({
      totalNumber: [
        { required: true, message: '请输入子账号总量', trigger: 'blur' },
        { validator: totalNumberValidate, trigger: ['blur', 'change'] }
      ],

      vipTotalNumber: [
        { required: true, message: '请输入VIP权限授权总量', trigger: 'blur' },
        { validator: vipTotalNumberValidate, trigger: ['blur', 'change'] }
      ]
    })

    const close = () => {
      form.value.resetFields()
      emit('update:modelValue', false)
    }

    const submit = () => {
      form.value.validate(async (valid) => {
        if (valid) {
          await saveAccountConfig(state.formData)
          close()
          emit('refresh')
        } else {
          return false
        }
        return valid
      })
    }

    return { ...toRefs(state), rules, close, form, submit } as any
  }
})
</script>

<style lang="scss" scoped>
.form {
  margin: 20px 0;
}

.button-grounp {
  width: 200px;
  margin: 10px auto;
}
</style>
