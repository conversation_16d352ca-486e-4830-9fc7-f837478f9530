<template>
  <div class="table">
    <el-table :data="tableData" style="width: 100%" align="left">
      <el-table-column prop="addTime" label="时间" width="180" />
      <el-table-column prop="content" label="操作" width="120" />
      <el-table-column prop="ip" label="IP" width="180" />
      <el-table-column prop="ascriptionIp" label="IP归属地" />
      <el-table-column prop="platform" label="来源" />
    </el-table>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs } from 'vue'

export default {
  props: {
    tableValue: {
      type: Array,
      default() {
        return []
      }
    }
  },
  setup(props) {
    const state = reactive({
      tableData: props.tableValue
    })
    return { ...toRefs(state) }
  }
}
</script>

<style></style>
