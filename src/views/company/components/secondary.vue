<template>
  <div>
    <el-row>
      <el-form-item
        label="院系名称"
        :prop="`childColleges.${index}.name`"
        :rules="{
          required: true,
          message: '请输入院系名称',
          trigger: 'blur'
        }"
      >
        <el-input type="primary" link placeholder="请输入院系名称" v-model="data.name"></el-input>
      </el-form-item>
      <el-form-item
        label="联系人"
        :prop="`childColleges.${index}.contact`"
        :rules="{
          required: true,
          message: '请输入联系人',
          trigger: 'blur'
        }"
      >
        <el-input type="primary" link placeholder="请输入联系人" v-model="data.contact"></el-input>
      </el-form-item>
    </el-row>
    <el-row>
      <el-form-item label="固定电话">
        <el-input
          type="primary"
          link
          placeholder="请输入固定电话"
          v-model="data.telephone"
        ></el-input>
      </el-form-item>
      <el-form-item label="传真">
        <el-input type="primary" link placeholder="请输入传真" v-model="data.fax"></el-input>
      </el-form-item>
    </el-row>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'secondary',

  components: {},

  props: {
    modelValue: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      data: computed({
        get() {
          return props.modelValue
        },

        set(val) {
          emit('update:modelValue', val)
        }
      })
    })

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped>
.el-form-item--medium {
  margin-right: 30px;
}
</style>
