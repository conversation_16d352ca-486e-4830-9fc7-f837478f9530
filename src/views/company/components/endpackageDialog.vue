<template>
  <div>
    <el-dialog
      v-model="visible"
      title="终止套餐"
      width="520px"
      @close="handleClose"
      center
      align-center
    >
      <div class="mt-10 flexs">
        <div class="right-center">名字：</div>
        <div>{{ companyInfo.companyName }}</div>
      </div>
      <div class="mt-10 flexs">
        <div class="right-center">当下套餐类型：</div>
        <div>{{ companyInfo.packageTypeTxt }}</div>
      </div>
      <div class="mt-10 flexs">
        <div class="right-center">原失效时间：</div>
        <div>{{ companyInfo.expireTime }}</div>
      </div>
      <div class="mt-10 flex">
        <div class="right-center">剩余套餐明细：</div>
        <div>
          职位发布条数：{{ companyInfo.jobAmount }}<br />
          职位可刷新条数：{{ companyInfo.jobRefreshAmount }}<br />
          公告发布条数：{{ companyInfo.announcementRefreshAmount }}<br />
          公告可刷新条数：{{ companyInfo.announcementAmount }}<br />
          剩余下载点数：{{ companyInfo.resumeDownloadAmount }}
        </div>
      </div>
      <div class="mt-10 flexs">
        <div class="right-center">待审核公告条数：</div>
        <div>{{ companyInfo.companyAnnouncementAmount }}</div>
      </div>
      <div class="mt-10 flexs">
        <div class="right-center">待审核职位条数：</div>
        <div>{{ companyInfo.companyJobAmount }}</div>
      </div>
      <div class="flex center mt-20">
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ElMessageBox } from 'element-plus'
import { defineComponent, reactive, toRefs } from 'vue'
import { getEndPackageList, EndPackageList } from '/@/api/unitManage'

export default defineComponent({
  // name: '',
  emits: ['update'],
  setup(props, { emit }) {
    const state = reactive({
      companyInfo: <any>{},
      visible: false,
      postData: {}
    })

    const handleClose = () => {
      state.visible = false
    }

    const submit = () => {
      ElMessageBox.confirm('执行后将不可撤回，确定要执行？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await EndPackageList(state.postData)
          handleClose()
          emit('update')
        })
        .catch(() => {})
    }

    const open = async (data: any) => {
      state.postData = { memberId: data }
      state.companyInfo = await getEndPackageList(state.postData)
      state.visible = true
    }

    return {
      ...toRefs(state),
      handleClose,
      open,
      submit,
      getEndPackageList
    }
  }
})
</script>

<style lang="scss" scoped>
.right-center {
  width: 120px;
  text-align: right;
}
.flexs {
  display: flex;
  align-items: center;
}
</style>
