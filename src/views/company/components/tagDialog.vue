<template>
  <div>
    <el-dialog v-model="setTagDialog" title="贴标签" width="30%" @close="close">
      <el-select v-model="featuredTagIds" clearable multiple>
        <el-option
          v-for="item in featuredTagList"
          :key="item.k"
          :label="item.v"
          :value="item.k"
        ></el-option>
      </el-select>

      <div class="flex mt-20">
        <el-link :underline="false" type="primary" class="mr-20" @click="handleAddDialog"
          >新建标签</el-link
        >
        <div class="flex-1">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="addTagDialog" title="新建特色标签" width="20%">
      <el-form ref="addFormRef" :model="form" :rules="rules">
        <el-form-item prop="tag">
          <el-input maxlength="20" show-word-limit type="text" v-model="form.tag"></el-input>
        </el-form-item>
      </el-form>
      <div class="flex jc-center mt-20">
        <el-button @click="closeAdd">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getCompanyFeaturedTagList } from '/@/api/config'
import { companyTag, addCompanyTag } from '/@/api/unitManage'

const emit = defineEmits(['update', 'tags'])

const setTagDialog = ref(false)

const addTagDialog = ref(false)

const featuredTagIds = ref([])

const form = ref({
  tag: ''
})

const rules = ref({
  tag: [{ required: true, message: '请输入标签名称', trigger: 'blur' }]
})

const addFormRef = ref()

const companyId = ref('')

const featuredTagList = ref([]) as any

const getParamsList = async () => {
  featuredTagList.value = await getCompanyFeaturedTagList()
}

getParamsList()

const open = (id, ids) => {
  companyId.value = id
  if (ids) {
    featuredTagIds.value = ids?.split(',')
  } else {
    featuredTagIds.value = []
  }
  setTagDialog.value = true
}

const close = () => {
  setTagDialog.value = false
}

const handleAddDialog = () => {
  addTagDialog.value = true
}

const closeAdd = () => {
  addFormRef.value.resetFields()
  addTagDialog.value = false
}

const handleAddSubmit = () => {
  addFormRef.value.validate(async (validate: boolean) => {
    if (validate) {
      await addCompanyTag({ tag: form.value.tag })
      emit('tags')
      getParamsList()
      closeAdd()
    }
  })
}

const handleSubmit = async () => {
  await companyTag({ companyId: companyId.value, featuredTagIds: featuredTagIds.value.join() })
  emit('update')
  close()
}

defineExpose({ open })
</script>

<style lang="scss" scoped></style>
