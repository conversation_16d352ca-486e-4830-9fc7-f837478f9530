<template>
  <div class="box">
    <el-form class="form" :model="formData" ref="form">
      <div class="form-content">
        <div class="form-item">
          <span class="label">单位名称:</span>
          <el-input
            v-model="formData.fullName"
            placeholder="请填写单位名称"
            clearable
            @keyup.enter="$emit('search', formData)"
          ></el-input>
        </div>

        <div class="form-item">
          <span class="label">单位性质:</span>
          <el-select v-model="formData.nature" placeholder="不限" filterable clearable>
            <el-option
              v-for="item in unitList.companyNatureList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <span class="label">单位类型:</span>
          <el-select v-model="formData.type" placeholder="不限" clearable filterable>
            <el-option
              v-for="item in unitList.companyTypeList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <span class="label">所在地区:</span>
          <Region v-model="formData.area" :filterable="true" :multiple="true" collapseTags></Region>
        </div>

        <div class="form-item">
          <span class="label">所属行业:</span>
          <Industry v-model="formData.industryId" :multiple="true" :fliter="true"></Industry>
        </div>

        <div class="form-item">
          <span class="label">手机号码:</span>
          <el-input
            v-model="formData.mobile"
            placeholder="请填写手机号码"
            clearable
            @keyup.enter="$emit('search', formData)"
          ></el-input>
        </div>

        <div class="form-item">
          <span class="label">创建时间:</span>
          <DatePickerRange v-model:start="formData.addTimeFrom" v-model:end="formData.addTimeTo" />
        </div>

        <div class="form-item" v-if="hideState">
          <span class="label">隐藏状态:</span>
          <el-select v-model="formData.isHide" placeholder="全部" clearable filterable>
            <el-option
              v-for="item in unitList.hideStatusList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <span class="label">是否小程序:</span>
          <IsMiniapp v-model="formData.isMiniapp" />
        </div>

        <div class="form-item">
          <span class="label">是否高才海外:</span>
          <el-select v-model="formData.isAbroad" filterable clearable>
            <el-option
              v-for="item in isAbroadList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <span class="label">单位群组:</span>
          <CompanyGroup v-model="formData.groupIds" :data="paramsList" />
        </div>

        <div class="form-item" v-if="!hideState">
          <span class="label">注册邮箱:</span>
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱"
            filterable
            clearable
          ></el-input>
        </div>
      </div>

      <div class="form-footer">
        <el-form-item>
          <el-button type="primary" @click="$emit('search', formData)">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="$emit('downloadFile', { ...formData, export: 1 })">下载</el-button>
          <el-button @click="$emit('downloadAllFile')">下载全部</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue'
import Region from '../../../components/base/select/region.vue'
import { getUnitList } from '/@/api/unitManage'
import Industry from '/@/components/base/industry.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import { getIsAbroad } from '/@/api/config'
import CompanyGroup from '/@/components/base/select/companyGroup.vue'

export default {
  name: 'baseTable',
  components: { Region, Industry, DatePickerRange, IsMiniapp, CompanyGroup },
  emits: ['tableData', 'download', 'reset', 'search', 'downloadFile', 'downloadAllFile'],
  props: {
    // 是否需要隐藏状态选择
    hideState: {
      type: Boolean,
      default: false
    },
    paramsList: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      formData: {
        fullName: '',
        groupIds: '',
        nature: '',
        type: '',
        area: [],
        industryId: [],
        mobile: '',
        addTimeFrom: '',
        addTimeTo: '',
        export: '',
        email: '',
        isAbroad: '',
        // 隐藏状态
        isHide: '',
        // 排序页码
        page: 1,
        pageSize: 20,
        isMiniapp: ''
      },
      isAbroadList: [],
      isMiniapp: '',
      groupIds: '',
      unitList: {
        companyNatureList: [],
        companyTypeList: [],
        hideStatusList: []
      }
    })
    const form = ref()
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      state.formData.addTimeFrom = ''
      state.formData.addTimeTo = ''
      emit('reset')
    }

    // 获取后台数据类型
    onMounted(async () => {
      state.unitList = await getUnitList()
      state.isAbroadList = await getIsAbroad()
    })
    // 搜索按键通知父组件
    const handleData = () => {
      emit('tableData', state)
    }
    // 下载按键通知父组件
    const handleDownload = () => {
      emit('download', state)
    }

    return { ...toRefs(state), form, handleData, resetForm, handleDownload }
  }
}
</script>

<style lang="scss" scoped>
.form {
  background: #fff;
  padding: 0px;

  .form-content {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;

    .form-item {
      width: 25%;
      padding: 0 10px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;

      .label {
        width: 90px;
        text-align: right;
        margin-right: 8px;
      }

      :deep(.el-input),
      :deep(.el-select) {
        flex: 1;
      }
    }
  }

  .form-footer {
    margin-top: 10px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-left: 10px;
    }
  }
}

:deep(.el-input__prefix-inner) {
  align-items: center;
}
</style>
