<template>
  <div class="main">
    <div class="main-form">
      <el-form :model="formData" class="form" label-width="90px" :rules="rules" ref="form">
        <el-form-item label="所属单位">{{ accountInfo.companyName }}</el-form-item>
        <el-form-item label="账号类型">
          <div class="account">
            <div>{{ accountInfo.companyMemberTypeText }}</div>
            <div v-if="!isMainAccount">如需变更子账号权限，请前往【账号查询】列表中设置</div>
          </div>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" />
        </el-form-item>
        <el-form-item label="姓名" prop="contact">
          <el-input v-model="formData.contact" />
        </el-form-item>
        <el-form-item label="所在部门" prop="department">
          <el-input v-model="formData.department" />
        </el-form-item>

        <div v-if="!isMainAccount">
          <el-form-item label="绑定邮箱" prop="email" :rules="email">
            <el-input v-model="formData.email" />
          </el-form-item>

          <el-form-item label="绑定手机号">
            <el-input v-model="formData.mobile" />
          </el-form-item>
        </div>

        <div class="senior" v-else>
          <el-form-item label="绑定信息" prop="mobile" :rules="mobile">
            <div class="flex">
              <el-checkbox
                class="mr-10"
                label="绑定手机号"
                v-model="checkMobile"
                true-label="1"
                false-label="2"
              ></el-checkbox>
              <el-input
                placeholder="请输入手机号"
                :disabled="mobileDisabled"
                v-model="formData.mobile"
              ></el-input>
            </div>
          </el-form-item>

          <el-form-item prop="email" :rules="email">
            <div class="flex">
              <el-checkbox
                class="mr-10"
                label="绑定邮箱"
                v-model="checkEmail"
                true-label="1"
                false-label="2"
              ></el-checkbox>
              <el-input
                v-model="formData.email"
                :disabled="emailDisabled"
                placeholder="请输入邮箱"
              ></el-input>
            </div>
          </el-form-item>
          <div class="error" v-if="showValidate">请选择绑定信息</div>
        </div>

        <el-form-item label="绑定微信">{{ accountInfo.wxBindText }} </el-form-item>
      </el-form>
      <div class="upload">
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          action="/company-account/upload-avatar"
          :on-success="handleSuccess"
          v-if="accountInfo.avatarShow === ''"
        >
          <div class="avatar-uploader-icon el-icon-plus"></div>
        </el-upload>
        <div class="avatar" v-else>
          <img :src="accountInfo.avatarShow" alt="" />
        </div>
        <div class="text">个人头像</div>
      </div>
    </div>

    <el-button type="primary" @click="submit">确定</el-button>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { getAccountSetting, editAccount } from '/@/api/account'
import { verifyEmail, verifyPhone } from '/@/utils/toolsValidate'

export default defineComponent({
  name: 'accountSetting',

  components: {},

  setup() {
    const route = useRoute()
    const id = route.params.id as string

    const state = reactive({
      formData: {
        id: computed(() => route.params.id),
        username: '',
        contact: '',
        department: '',
        email: '',
        mobile: '',
        avatar: ''
      },
      checkMobile: '',
      checkEmail: '',
      accountInfo: {},
      emailDisabled: computed(() => state.checkEmail !== '1'),
      mobileDisabled: computed(() => state.checkMobile !== '1'),
      isMainAccount: computed(() => state.accountInfo.companyMemberType === 0),
      showValidate: computed(() => state.checkMobile === '2' && state.checkEmail === '2')
    })

    const form = ref()

    const rules = ref({
      username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
      contact: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      department: [{ required: true, message: '请输入所在部门', trigger: 'blur' }]
    })

    const validateEmail = (rule, value, callback) => {
      const { email } = state.formData
      const { checkEmail } = state

      if (checkEmail === '1' && !email) {
        callback(new Error('请输入邮箱'))
      } else if (checkEmail === '1' && !verifyEmail(email)) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }

    const validateMobile = (rule, value, callback) => {
      const { mobile } = state.formData
      const { checkMobile } = state

      if (checkMobile === '1' && !mobile) {
        callback(new Error('请输入手机号'))
      } else if (checkMobile === '1' && !verifyPhone(mobile)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    const email = ref({ required: true, validator: validateEmail, trigger: ['change', 'blur'] })
    const mobile = ref({ required: true, validator: validateMobile, trigger: ['change', 'blur'] })

    const getDetail = async () => {
      state.accountInfo = await getAccountSetting({ id })
      Object.keys(state.formData).forEach((key) => {
        if (state.accountInfo[key]) {
          state.formData[key] = state.accountInfo[key]
        }
      })
      state.checkMobile = state.accountInfo.mobile ? '1' : '2'
      state.checkEmail = state.accountInfo.email ? '1' : '2'
    }

    const handleSuccess = (res: any) => {
      const { url, fullUrl } = res.data
      state.accountInfo.avatarShow = fullUrl
      state.formData.avatar = url
    }

    const submit = () => {
      form.value.validate(async (valide: boolean) => {
        if (valide && !state.showValidate) {
          await editAccount(state.formData)
        }
      })
    }

    getDetail()

    return { ...toRefs(state), form, rules, handleSuccess, submit, email, mobile } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: white;
  padding: 20px;
  text-align: center;

  .main-form {
    display: flex;

    .upload {
      margin-left: 100px;
    }

    .text {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }

    .avatar-uploader {
      width: 150px;
      height: 150px;
      border: 1px dashed #ccc;

      :deep(.el-upload) {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
      }
    }

    .avatar {
      width: 150px;
      height: 150px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .senior {
    position: relative;
  }
  .error {
    position: absolute;
    color: #fe676a;
    font-size: 12px;
    bottom: -15px;
    left: 20px;
  }

  .account {
    text-align: left;
  }

  .form {
    width: 600px;
  }
}
</style>
