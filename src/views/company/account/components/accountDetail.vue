<template>
  <el-dialog title="账号详情" v-model="visible" width="50%">
    <h3>基本信息</h3>
    <div class="top">
      <table class="info" border="1">
        <tr>
          <td class="table-title">用户名</td>
          <td>{{ info.username }}</td>

          <td class="table-title">联系人姓名</td>
          <td>{{ info.contact }}</td>
        </tr>
        <tr>
          <td class="table-title">所属单位</td>
          <td>{{ info.companyName }}</td>

          <td class="table-title">所在部门</td>
          <td>{{ info.department }}</td>
        </tr>
        <tr>
          <td class="table-title">注册手机号</td>
          <td>{{ info.mobile }}</td>

          <td class="table-title">注册邮箱</td>
          <td>{{ info.email }}</td>
        </tr>
        <tr>
          <td class="table-title">绑定微信</td>
          <td>{{ info.wxBindText }}</td>
        </tr>
      </table>

      <div class="avater">
        <img :src="info.avatar" />
        <div>个人头像</div>
      </div>
    </div>

    <h3>账号信息</h3>
    <table class="info" border="1">
      <tr>
        <td class="table-title">账号信息</td>
        <td>{{ info.companyMemberTypeText }}</td>

        <td class="table-title">账号权限</td>
        <td>
          <div>{{ info.memberRuleText }}</div>
          <div v-if="info.companyVipExpire">（{{ info.companyVipExpire }}）</div>
        </td>
      </tr>
      <tr>
        <td class="table-title">创建时间</td>
        <td>{{ info.addTime }}</td>

        <td class="table-title">账号状态</td>
        <td>{{ info.statusText }}</td>
      </tr>
    </table>

    <div class="flex ai-center">
      <h3>微信通知管理</h3>
      <div class="ml-10">开启后，单位端可以在微信公众号接收到以下消息提示</div>
    </div>

    <div class="flex ai-center mt-20">
      <h3>简历消息投递</h3>

      <el-switch
        class="ml-10"
        :disabled="isSwitchDisable"
        @change="deliveryChange"
        v-model="delivery"
      />
    </div>

    <!--    <div class="flex ai-center">-->
    <!--      <h3>直聊消息提醒</h3>-->
    <!--      <el-switch-->
    <!--        class="ml-10"-->
    <!--        v-model="info.isChat"-->
    <!--        active-value="1"-->
    <!--        inactive-value="2"-->
    <!--        @click="handleChatClick"-->
    <!--      ></el-switch>-->
    <!--    </div>-->
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { editAccountMessageConfig, getAccountDetail, editChatConfig } from '/@/api/account'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'accountDetail',

  setup() {
    const state = reactive({
      visible: false,
      info: <any>{},
      delivery: false,
      isSwitchDisable: true
    })

    const open = async (id: string) => {
      state.visible = true
      state.info = await getAccountDetail({ id })
      state.delivery = state.info.deliveryMessage == 1
      state.isSwitchDisable = state.info.isWxBind != 1
    }

    const deliveryChange = (value) => {
      const params = {
        delivery_message: 1,
        message_id: state.info.messageId
      }

      if (!value) {
        params.delivery_message = 2
      }

      editAccountMessageConfig(params).then(() => {
        ElMessage.success('操作成功')
      })
    }

    const handleChatClick = async () => {
      const postData = { memberId: state.info.memberId, isChat: state.info.isChat }
      editChatConfig(postData)
    }

    return { ...toRefs(state), open, deliveryChange, handleChatClick }
  }
})
</script>

<style lang="scss" scoped>
.top {
  display: flex;
}

.avater {
  margin-left: 50px;
  text-align: center;

  img {
    width: 150px;
    height: 150px;
  }
}
.info {
  margin: 20px 0;
  color: #606266;
  border-collapse: collapse;
  border-color: rgba(#909399, 0.3);

  td {
    padding: 10px;
    width: 250px;
  }

  .table-title {
    background-color: #f3f6f9;
    width: 120px;
  }
}
</style>
