<template>
  <el-dialog title="设置账号" v-model="visible" class="setting">
    <div>请为子账号选择账号权限</div>
    <div class="check">
      <el-radio-group v-model="postData.memberRule">
        <el-radio :label="1">普通权限</el-radio>
        <el-radio :label="2" :disabled="!isDisable" class="vip">
          <div>vip权限</div>
          <div class="tips">{{ notice }}</div>
        </el-radio>
      </el-radio-group>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { getAccountEditStatus, saveAccountPermission } from '/@/api/account'

export default defineComponent({
  name: 'setAccountDialog',

  components: {},

  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      notice: '',
      postData: {
        id: '',
        memberRule: 1
      },
      companyMemberRule: 1,
      isDisable: Boolean
    })

    const open = async (id: string) => {
      state.visible = true
      const { vipNotice, companyMemberRule, isDisable } = await getAccountEditStatus({ id })
      state.postData.memberRule = companyMemberRule
      state.companyMemberRule = companyMemberRule
      state.isDisable = isDisable
      state.notice = vipNotice
      state.postData.id = id
    }

    const submit = async () => {
      await saveAccountPermission(state.postData)
      state.visible = false

      emit('update')
    }

    return { ...toRefs(state), open, submit } as any
  }
})
</script>

<style lang="scss" scoped>
.setting {
  .check {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .tips {
      color: #f56c6c;
      margin-left: 10px;
    }
  }

  .vip {
    :deep(.el-radio__label) {
      display: flex;
    }
  }

  .dialog-footer {
    width: 200px;
    margin: 20px auto 0;
  }
}
</style>
