<template>
  <div class="main">
    <el-tabs v-model="active" @tab-change="getList">
      <el-tab-pane label="操作日志" name="operate">
        <el-table :data="list" border>
          <el-table-column prop="addTime" label="时间" />
          <el-table-column prop="content" label="操作" />
          <el-table-column prop="ip" label="IP" />
          <el-table-column prop="ascriptionIp" label="IP归属地" />
          <el-table-column prop="platform" label="来源" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="登录日志" name="login">
        <el-table :data="list" border>
          <el-table-column prop="addTime" label="时间" />
          <el-table-column prop="ip" label="IP" />
          <el-table-column prop="ascriptionIp" label="IP归属地" />
          <el-table-column prop="platform" label="来源" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="账号状态/权限变更记录" name="account">
        <el-table :data="list" border>
          <el-table-column prop="addTime" label="时间" />
          <el-table-column align="center" prop="operationContent" label="操作">
            <template #default="{ row }">
              <div>
                <div>{{ row.operationContent }}</div>
                <div>（ 操作类型：{{ row.typeText }}）</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="operationPort" label="操作端口" />
          <el-table-column prop="operationPersonName" label="操作人" />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <Paging class="mt-20" :total="pages.total" @change="handlePageChange" />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'
import { getLogList } from '/@/api/member'
import { useRoute } from 'vue-router'
import Paging from '/@/components/base/paging.vue'
import { getAccountLog } from '/@/api/account'

export default defineComponent({
  name: 'accountLog',

  components: { Paging },

  setup() {
    const route = useRoute()
    const id = route.params.id as string
    const state = reactive({
      active: 'operate',
      list: [],

      pages: {
        page: 1,
        pageSize: 20,
        total: 0
      },

      postData: {
        id: computed(() => id),
        isLogTab: computed(() => (state.active === 'operate' ? 0 : 1)),
        page: computed(() => state.pages.page),
        pageSize: computed(() => state.pages.pageSize),
        isCompany: '2'
      }
    })

    const getList = async () => {
      const api = state.active === 'account' ? getAccountLog : getLogList
      const accountData = { id, page: state.pages.page, pageSize: state.pages.pageSize }
      const postData = state.active === 'account' ? accountData : state.postData

      const { list, pages } = await api(postData)
      state.list = list
      state.pages = pages
    }

    const handlePageChange = (data: any) => {
      const { page, limit } = data
      state.pages.page = page
      state.pages.pageSize = limit

      getList()
    }

    getList()

    return { ...toRefs(state), getList, handlePageChange } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  padding: 20px;
  background-color: white;
}
</style>
