<template>
  <div class="main">
    <el-form class="form" :model="form" label-width="110px" :rules="rules" ref="formRef">
      <div class="flex">
        <el-form-item label="选择单位：" prop="companyId">
          <el-select
            style="width: 100%"
            filterable
            remote
            v-model="form.companyId"
            :remote-method="remoteMethod"
            @change="handleCompanyChange"
            placeholder="请选择单位"
          >
            <el-option
              v-for="item in companyList"
              :key="item.companyId"
              :value="item.companyId"
              :label="item.companyName"
            ></el-option>
          </el-select>
        </el-form-item>
        <div>{{ packageTypeText }}</div>
      </div>
      <div class="tips" v-if="showRule">
        当前单位还可以创建{{ available }}个子账号（已创建/总量：{{ used }}/{{ total }}）
      </div>
      <div class="danger" v-else>{{ notice }}</div>

      <el-form-item prop="contact" label="姓名：">
        <el-input v-model="form.contact" placeholder="请填写姓名" />
      </el-form-item>

      <el-form-item prop="department" label="所在部门：">
        <el-input v-model="form.department" placeholder="请填写所在部门" />
      </el-form-item>

      <el-form-item prop="email" label="绑定邮箱：">
        <el-input v-model="form.email" placeholder="请填写邮箱地址" />
      </el-form-item>

      <el-form-item prop="mobile" label="绑定手机号：">
        <el-input v-model="form.mobile" placeholder="请填写手机号码" />
      </el-form-item>

      <el-form-item prop="memberRule" label="子账号授权：">
        <el-radio-group v-model="form.memberRule">
          <el-radio label="1">普通权限</el-radio>
          <el-radio label="2" :disabled="!showTips">
            <div class="flex ai-center">
              <div>VIP权限</div>
              <div class="tips check" v-if="showTips">
                剩余{{ vipAvailable }}个可授权资源，目前已授权{{ vipUsed }}个，到期时间
                {{ filterList[0]?.companyVipExpire }}
              </div>
              <div class="danger check" v-else>{{ tips }}</div>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <el-button class="button" type="primary" @click="handleSubmit">确定</el-button>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs } from 'vue'
import { getCompanyAccountList, companyAccountAdd } from '/@/api/account'

export default defineComponent({
  name: 'addAccount',

  components: {},

  setup() {
    const state = reactive({
      form: {
        companyId: '',
        contact: '',
        department: '',
        email: '',
        mobile: '',
        memberRule: '1'
      },

      rules: {
        companyId: [{ required: true, message: '请选择单位', trigger: 'change' }],
        contact: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        department: [{ required: true, message: '请输入所在部门', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        mobile: [
          { trigger: 'change' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: ['blur', 'change']
          }
        ],
        memberRule: [{ required: true, trigger: 'change', message: '请选择子账号授权' }]
      },

      notice: '',
      tips: '',
      packageType: '',
      packageTypeText: '',
      companyList: [],
      filterList: [],
      available: 0,
      total: 0,
      used: 0,
      vipAvailable: 0,
      vipUsed: 0,
      showRule: computed(() => Number(state.total) - Number(state.used) > 0),
      showTips: computed(() => state.packageType === '2' && Number(state.vipAvailable) > 0)
    })

    const formRef = ref()

    const remoteMethod = async (companyName: string) => {
      state.companyList = await getCompanyAccountList({ companyName })
    }

    const handleShowTips = () => {
      const { packageType, vipAvailable, vipTotal } = state.filterList[0]

      if (packageType === '2') {
        if (vipAvailable === '0') {
          state.tips = `VIP授权资源已达数量上限(${vipTotal}个)。若要变更权限，请重新分配或追加VIP授权资源数量。`
        }
        if (vipTotal === '0') {
          state.tips = '当前单位无VIP授权资源'
        }
      } else {
        state.tips = '必须有生效中的单位会员套餐才能配置'
      }
    }

    const handleCompanyChange = (val: string) => {
      const infoList = state.companyList.filter((item: any) => item.companyId === val)
      state.filterList = infoList
      state.available = infoList[0]?.available
      state.total = infoList[0]?.total
      state.used = infoList[0]?.used
      state.vipAvailable = infoList[0]?.vipAvailable
      state.vipUsed = infoList[0]?.vipUsed
      state.packageType = infoList[0]?.packageType
      state.packageTypeText = infoList[0]?.packageTypeText

      if (state.total === '0') {
        state.notice = '当前单位无子账号配置权限'
        return
      }

      if (state.available === '0') {
        state.notice = `当前单位创建的子账号数量已达上限（已创建/总量：${infoList[0].used}/${infoList[0].total}）`
      }

      handleShowTips()
    }

    remoteMethod('')

    const handleSubmit = () => {
      formRef.value.validate(async (valid: boolean) => {
        if (valid) {
          await companyAccountAdd(state.form)
          formRef.value.resetFields()
          remoteMethod('')
          handleCompanyChange('')
          state.tips = ''
        }
      })
    }

    return {
      ...toRefs(state),
      formRef,
      handleSubmit,
      remoteMethod,
      handleCompanyChange
    } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: white;

  .form {
    padding: 20px;
    width: 500px;
  }

  .button {
    margin-left: 130px;
    margin-bottom: 20px;
  }

  .tips {
    margin-top: -10px;
    margin-left: 110px;
  }

  .danger {
    color: #f56c6c;
    margin-left: 110px;
  }

  .check {
    margin-left: 10px;
  }
}
</style>
