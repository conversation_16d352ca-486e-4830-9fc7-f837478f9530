<template>
  <div class="main">
    <div class="title">会员日志</div>
    <el-card>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="操作日志" name="first">
          <div class="search">
            <el-input
              v-model="data.keywords"
              placeholder="请输入关键词"
              class="input-with-select"
              style="width: 500px"
              clearable
            >
              <template #append>
                <el-button icon="el-icon-search" @click="searchBtn"></el-button>
              </template>
            </el-input>
          </div>
          <div class="show-table">
            <el-table :data="tableData" style="width: 100%" align="left">
              <el-table-column prop="addTime" label="时间" width="180" />
              <el-table-column prop="content" label="操作" width="120" />
              <el-table-column prop="ip" label="IP" width="180" />
              <el-table-column prop="ascriptionIp" label="IP归属地" />
              <el-table-column prop="platform" label="来源" />
            </el-table>
          </div>
          <!-- 分页组件 -->
          <div class="paging">
            <Paging :total="data.total" @change="changePage"></Paging>
          </div>
        </el-tab-pane>
        <el-tab-pane label="登陆日志" name="second" v-if="isShow">
          <div class="show-table">
            <el-table :data="logData" style="width: 100%" align="left">
              <el-table-column prop="addTime" label="时间" width="180" />
              <el-table-column prop="ip" label="IP" width="180" />
              <el-table-column prop="ascriptionIp" label="IP归属地" />
              <el-table-column prop="platform" label="来源" />
            </el-table>
          </div>
          <!-- 分页组件 -->
          <div class="paging">
            <Paging :total="data.total" @change="changePage"></Paging>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { useRoute } from 'vue-router'
import { getLogList } from '/@/api/member'

export default {
  name: 'memberLog',
  components: { Paging },
  setup() {
    const route = useRoute()
    const state = reactive({
      activeName: 'first',
      logSearch: '',
      // 路由参数
      data: { id: route.query.id, isLogTab: 0, pageSize: '', page: '', total: 0, keywords: '' },
      tableData: [],
      logData: []
    })
    const isShow = ref(route.query.isShow)
    // 搜索按钮
    const searchBtn = async () => {
      const res = await getLogList(state.data)
      state.data.total = res.pages.total
      if (state.activeName === 'first') {
        state.tableData = res.list
      } else {
        state.logData = res.list
      }
    }
    // 切换tab
    const handleClick = async (tab: any) => {
      state.data.isLogTab = tab.index
      searchBtn()
    }
    const changePage = (data: any) => {
      state.data.page = data.page
      state.data.pageSize = data.limit
      searchBtn()
    }
    onMounted(() => {
      searchBtn()
    })

    return { ...toRefs(state), handleClick, searchBtn, changePage, isShow }
  }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  .title {
    height: 30px;
    background-color: #f0f0f0;
    font-size: 16px;
    font-weight: 700;
    padding-left: 30px;
  }
  .show-table {
    padding-top: 30px;
  }
  .paging {
    margin-top: 90px;
  }
}
</style>
