<template>
  <div class="detail-card">
    <el-card v-loading="isLoading">
      <el-descriptions title="申请信息" :column="4" border>
        <template #extra>
          <el-button type="danger" size="small" @click="openRemark">添加标注</el-button>
          <el-button type="success" size="small" @click="setHandle"
            >设置为{{ isHandle == '1' ? '未' : '已' }}处理</el-button
          >
          <el-button type="primary" size="small" @click="back">返回</el-button>
        </template>
        <el-descriptions-item label="单位名称">{{ name }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ contact }}</el-descriptions-item>
        <el-descriptions-item label="所在部门">{{ department }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ mobile }}</el-descriptions-item>
        <el-descriptions-item label="QQ/微信">{{ txIm }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱">{{ email }}</el-descriptions-item>
        <el-descriptions-item label="招聘时长" :span="2">{{ recruitment }}</el-descriptions-item>
        <el-descriptions-item label="招聘岗位" :span="4">{{ jobName }}</el-descriptions-item>
        <el-descriptions-item label="学历要求" :span="4">{{ educationTxt }}</el-descriptions-item>
        <el-descriptions-item label="其他说明" :span="4">{{ content }}</el-descriptions-item>
        <el-descriptions-item label="处理流程" :span="4">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in handleList"
              :key="index"
              :timestamp="item.addTime"
            >
              {{ item.content }}
            </el-timeline-item>
          </el-timeline></el-descriptions-item
        >
      </el-descriptions>
    </el-card>
  </div>
</template>

<script lang="ts">
import { ElMessageBox } from 'element-plus'
import { onMounted, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { addCooperationRemark, getCooperationDetail, setCooperationHandle } from '/@/api/unitManage'

export default {
  name: 'companyCooperationListDetail',
  setup() {
    const state = reactive({
      addTime: '',
      type: '',
      isHandle: '',
      name: '',
      contact: '',
      department: '',
      mobile: '',
      jobName: '',
      education: '',
      educationTxt: '',
      recruitment: '',
      txIm: '',
      email: '',
      content: '',
      handleList: [],
      isLoading: false
    })
    const route = useRoute()
    const router = useRouter()
    const id = route.params.id as string

    const getDetail = () => {
      state.isLoading = true
      getCooperationDetail(id).then((res: any) => {
        state.isLoading = false
        state.addTime = res.addTime
        state.isHandle = res.isHandle
        state.type = res.type
        state.isHandle = res.isHandle
        state.name = res.name
        state.contact = res.contact
        state.department = res.department
        state.mobile = res.mobile
        state.jobName = res.jobName
        state.education = res.education
        state.recruitment = res.recruitment
        state.txIm = res.txIm
        state.email = res.email
        state.content = res.content
        state.handleList = res.handleList
        state.educationTxt = res.educationTxt
      })
    }

    const setHandle = () => {
      const txt = state.isHandle === '1' ? '未' : '已'
      ElMessageBox.confirm(`确定设置为${txt}处理?`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          setCooperationHandle(id)
            .then(() => {
              getDetail()
            })
            .catch(() => {})
        })
        .catch(() => {})
    }

    const openRemark = () => {
      ElMessageBox.prompt('添加标注', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S/,
        inputErrorMessage: '内容不能为空'
      })
        .then(({ value }) => {
          addCooperationRemark({ id, content: value })
            .then(() => {
              getDetail()
            })
            .catch(() => {})
        })
        .catch(() => {})
    }

    onMounted(() => {
      getDetail()
    })

    const back = () => {
      router.push(`/company/cooperationList`)
    }

    return {
      ...toRefs(state),
      openRemark,
      setHandle,
      back
    }
  }
}
</script>

<style lang="scss" scoped></style>
