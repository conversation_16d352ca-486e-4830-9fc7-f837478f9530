<template>
  <div class="main">
    <div class="formbigbox" v-show="!isDetail">
      <div class="formbox">
        <el-form ref="form" :model="formData" label- class="demo-form-inline" :inline="true">
          <el-row>
            <el-col :span="4">
              <el-form-item label="单位名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请填写单位名称"
                  @keyup.enter.native="search"
                  clearable
                  autocomplete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="联系电话" prop="mobile">
                <el-input
                  v-model="formData.mobile"
                  @keyup.enter.native="search"
                  placeholder="请填写联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="处理状态" prop="isHandel">
                <el-select
                  v-model="formData.isHandel"
                  placeholder="不限"
                  style="width: 150px"
                  clearable
                >
                  <el-option
                    v-for="item in isHandelList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="申请类型" prop="type">
                <el-select
                  v-model="formData.type"
                  placeholder="不限"
                  style="width: 150px"
                  clearable
                >
                  <el-option
                    v-for="item in typeList as any"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="申请时间" prop="addTime">
                <el-date-picker
                  class="w100"
                  value-format="YYYY-MM-DD"
                  v-model="formData.addTime"
                  type="daterange"
                  range-separator="到"
                  start-placeholder="开始"
                  end-placeholder="结束"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="default" @click="resetForm">重置</el-button>
                <el-button type="default" @click="excel" :loading="downloadLoading">下载</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="注册邮箱" prop="email" label-width="68px">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱"
                  filterable
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="unitCount">
          共计：<span class="red">{{ pages.total }}</span> 条申请；待处理
          <span class="red">{{ amount.isHandelNo }}</span
          >条；已处理 <span class="red">{{ amount.isHandelYes }}</span
          >条
        </div>
        <div class="showTable">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            align="center"
            v-loading="listLoading"
            @sort-change="handleSortChange"
          >
            <el-table-column prop="name" label="单位名称" />
            <el-table-column prop="contact" label="联系人" />
            <el-table-column prop="department" label="所在部门" />
            <el-table-column prop="mobile" label="联系电话" />
            <el-table-column prop="email" label="联系邮箱" />
            <el-table-column prop="txIm" label="QQ/微信" />
            <el-table-column prop="typeTxt" label="申请类型" />
            <el-table-column prop="addTime" sortable label="申请时间" />
            <el-table-column prop="isHandleTxt" label="处理状态" />
            <el-table-column prop="email" label="注册邮箱" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button
                  v-if="scope.row.isHandle === '0'"
                  type="success"
                  size="small"
                  @click="checkDetails(scope.row.id)"
                >
                  处理</el-button
                >
                <el-button v-else type="primary" size="small" @click="checkDetails(scope.row.id)">
                  查看</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="paging">
          <Paging :total="pages.total" @change="changePage"></Paging>
        </div>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<script lang="ts">
import { reactive, ref, onMounted, toRefs, watch } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { getCompanyCooperationTypeList } from '/@/api/config'
import { exportCooperationList, getCooperationList } from '/@/api/unitManage'
import router from '/@/router'
import { useRoute } from 'vue-router'

export default {
  components: { Paging },
  name: 'companyCooperationList',
  setup() {
    const state = reactive({
      formData: {
        name: '', // 名称
        mobile: '', // 联系方式
        isHandel: '', // 是否已经处理
        type: '', // 申请类型
        addTimeFrom: '', // 创建时间
        addTimeTo: '', // 创建时间
        email: '',
        addTime: ['', ''],
        sortAddTime: '',
        page: 1,
        pageSize: ''
      },
      tableData: [],
      typeList: [],
      isHandelList: [
        { k: '1', v: '是' },
        { k: '0', v: '否' }
      ],
      listLoading: false,
      dialogFormVisible: false,
      downloadLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      isDetail: false,
      amount: {
        isHandelYes: 0,
        isHandelNo: 0
      }
    })

    const route = useRoute()

    const search = () => {
      state.listLoading = true
      state.formData.addTimeFrom = state.formData.addTime[0]
      state.formData.addTimeTo = state.formData.addTime[1]
      getCooperationList(state.formData).then((res: any) => {
        state.tableData = res.list
        state.pages.size = res.pages.size
        state.pages.total = res.pages.total
        state.amount = res.amount
        state.listLoading = false
      })
    }

    // 获取后台数据类型
    onMounted(async () => {
      if (route.name === 'companyCooperationListDetail') {
        state.isDetail = true
      } else {
        state.isDetail = false
      }
      search()
      const res = await getCompanyCooperationTypeList()
      state.typeList = res
    })

    watch(
      () => route.name,
      (n) => {
        if (n === 'companyCooperationListDetail') {
          state.isDetail = true
        } else {
          state.isDetail = false
        }
      },
      { immediate: true }
    )

    const form = ref()
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      search()
    }

    const checkDetails = (id: any) => {
      router.push(`/company/cooperationList/detail/${id}`)
    }
    const changePage = (r: any) => {
      state.formData.page = r.page
      state.formData.pageSize = r.limit
      search()
    }
    const excel = () => {
      state.downloadLoading = true
      state.formData.addTimeFrom = state.formData.addTime[0]
      state.formData.addTimeTo = state.formData.addTime[1]

      exportCooperationList(state.formData).then((res) => {
        state.downloadLoading = false
        window.location.href = res.downloadUrl
      })
    }

    const handleSortChange = ({ prop, order }) => {
      state.formData.sortAddTime = ''
      if (order === 'ascending') {
        // 顺序
        state.formData.sortAddTime = '0'
      } else if (order === 'descending') {
        // 倒序
        state.formData.sortAddTime = '1'
      }
      search()
    }

    return {
      ...toRefs(state),
      search,
      resetForm,
      checkDetails,
      changePage,
      excel,
      handleSortChange,
      form
    }
  }
}
</script>
<style lang="scss" scoped>
.red {
  color: red;
}
.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;

  .unitCount {
    margin: 20px 0;
    height: 30px;
    line-height: 30px;
    background-color: #edf9ff;
  }
  .paging {
    margin-top: 90px;
  }
  .opbutton {
    padding: 4px;
  }
}
</style>
