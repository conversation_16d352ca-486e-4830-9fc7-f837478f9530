<template>
  <div class="main">
    <p class="unit-info">单位基本信息</p>
    <el-form ref="form" :model="ruleForm" label-width="140px" :rules="rules" label-position="right">
      <el-row>
        <el-col :span="25">
          <el-form-item label="单位名称" prop="baseFullName">
            <el-input type="primary" link v-model="ruleForm.baseFullName" style="width: 198px">{{
              ruleForm.baseFullName
            }}</el-input>
          </el-form-item>
          <el-form-item label="单位简称">
            <el-col :span="20">
              <el-input
                maxlength="50"
                placeholder="请输入单位简称，最多50字"
                clearable
                v-model="ruleForm.baseShortName"
                style="width: 200px"
              ></el-input>
            </el-col>
          </el-form-item>
        </el-col>
        <el-form-item prop="baseLogoUrl" ref="baseLogo">
          <el-upload
            class="avatar-uploader"
            action="upload/company-relevant-image"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            v-model="ruleForm.baseLogoUrl"
            :data="{ uploadType: logo }"
          >
            <img v-if="ruleForm.baseLogoUrl" :src="ruleForm.baseLogoUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <p class="logo" v-if="!ruleForm.baseLogoUrl">*请上传logo或校徽</p>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-form-item label="英文名称">
        <el-col :span="20">
          <el-input
            type="primary"
            link
            placeholder="请输入英文名称"
            v-model="ruleForm.baseEnglishName"
            style="width: 200px"
          ></el-input>
        </el-col>
      </el-form-item>
      <el-row>
        <el-form-item label="单位类型" prop="baseType">
          <el-col :span="20">
            <el-select
              v-model="ruleForm.baseType"
              placeholder="请选择单位类型"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in data.typeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              >
              </el-option
            ></el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="单位性质" prop="baseNature">
          <el-col :span="20">
            <el-select
              v-model="ruleForm.baseNature"
              placeholder="请选择单位性质"
              filterable
              clearable
            >
              <el-option
                v-for="item in data.natureList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              >
              </el-option
            ></el-select>
          </el-col>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="单位规模" prop="baseScale">
          <el-col :span="20">
            <el-select
              v-model="ruleForm.baseScale"
              placeholder="请选择单位规模"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="item in data.scaleList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="所属行业" prop="baseIndustryId">
          <el-col :span="20">
            <Industry v-model="ruleForm.baseIndustryId"></Industry>
          </el-col>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="单位福利">
          <el-col :span="20">
            <el-input
              v-model="welfareText"
              @click="openDialogWelfare"
              readonly
              placeholder="请选择单位福利"
              style="width: 200px"
            >
              ></el-input
            >
          </el-col>
        </el-form-item>
        <el-form-item label="单位官网">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入单位官网"
              v-model="ruleForm.baseWebsite"
              style="width: 160px"
            ></el-input>
          </el-col>
        </el-form-item>
      </el-row>
      <el-form-item label="单位标签">
        <el-col :span="20">
          <el-select
            v-model="ruleForm.baseLabelIds"
            placeholder="请选择单位标签"
            style="width: 538px"
            multiple
          >
            <el-option v-for="item in data.tagList" :key="item.k" :label="item.v" :value="item.k">
            </el-option
          ></el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="单位介绍" prop="baseIntroduce">
        <el-col :span="20">
          <el-input
            v-model="ruleForm.baseIntroduce"
            :rows="2"
            type="textarea"
            style="width: 538px"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="单位主页背景" prop="baseHeadBannerUrl">
        <CompanyStyle
          type="banner"
          v-model:bannerSrc="ruleForm.baseHeadBannerUrl"
          v-model:bannerMobileSrc="ruleForm.baseMobileHeadBannerUrl"
        />
      </el-form-item>
      <el-form-item label="单位风采" prop="baseHeadBannerUrl">
        <CompanyStyle type="style" multiple v-model:styleSrc="ruleForm.styleAtlas" />
      </el-form-item>
      <el-row v-if="isCooperation">
        <el-form-item label="单位资质证明" prop="baseLicensePath">
          <el-col :span="12">
            <el-upload
              action="upload/company-relevant-image"
              :on-success="handleSuccess"
              :before-upload="beforeUpload"
              multiple
              :limit="1"
              v-model="ruleForm.baseLicensePath"
              :data="{ uploadType: license }"
            >
              <el-button size="small" type="primary" class="chose-document">选择文件</el-button>
            </el-upload>
            <a class="preview" :href="ruleForm.baseLicenseFullPath" target="_blank">预览</a>
          </el-col>
        </el-form-item>
        <el-form-item label="经办人身份证明" prop="basePersonInfoPath">
          <el-col :span="12">
            <el-upload
              action="upload/company-relevant-image"
              multiple
              :limit="1"
              :on-success="handleSuccesses"
              :before-upload="beforeUpload"
              v-model="ruleForm.basePersonInfoPath"
              :data="{ uploadType: handler }"
            >
              <el-button size="small" type="primary">选择文件</el-button>
            </el-upload>
            <a class="preview" :href="ruleForm.basePersonInfoFullPath" target="_blank">预览</a>
          </el-col>
        </el-form-item>
      </el-row>

      <div v-if="isCooperation">
        <div class="flex ai-center">
          <p class="unit-info">注册账号信息</p>
          <router-link :to="`/company/account/setting/${ruleForm.memberRecordId}`"
            >修改</router-link
          >
        </div>
        <table class="info" border="1">
          <tr>
            <td class="table-title">账号ID</td>
            <td>{{ ruleForm.memberId }}</td>
            <td class="table-title">用户名</td>
            <td>{{ ruleForm.memberUsername }}</td>

            <td class="table-title">联系人姓名</td>
            <td>{{ ruleForm.memberContact }}</td>

            <td class="table-title">所在部门</td>
            <td>{{ ruleForm.memberDepartment }}</td>
          </tr>
          <tr>
            <td class="table-title">注册手机号</td>
            <td>{{ ruleForm.memberMobile }}</td>

            <td class="table-title">注册邮箱</td>
            <td>{{ ruleForm.memberEmail }}</td>

            <td class="table-title">微信绑定</td>
            <td>{{ ruleForm.memberWxBindTxt }}</td>

            <td class="table-title">已创建子账号数量</td>
            <td>{{ ruleForm.memberUsed }}</td>
          </tr>
        </table>
      </div>

      <p class="unit-info">联系方式</p>
      <el-row>
        <el-form-item label="联系人" prop="contactName">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入联系人"
              v-model="ruleForm.contactName"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-col :span="20">
            <el-checkbox
              v-model="ruleForm.contactNameIsPublic"
              true-label="2"
              false-label="0"
              label="不公开"
            ></el-checkbox>
          </el-col>
        </el-form-item>
        <el-form-item label="所在部门" prop="contactDepartment">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入所在部门"
              v-model="ruleForm.contactDepartment"
            ></el-input>
          </el-col>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="联系电话" prop="contactMobile">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入联系电话"
              v-model="ruleForm.contactMobile"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-col :span="20">
            <el-checkbox disabled label="不公开"></el-checkbox>
          </el-col>
        </el-form-item>
        <el-form-item label="固定电话">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入固定电话"
              v-model="ruleForm.contactTelephone"
            ></el-input>
          </el-col>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入联系邮箱"
              v-model="ruleForm.contactEmail"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-col :span="20">
            <el-checkbox
              v-model="ruleForm.contactEmailIsPublic"
              label="不公开"
              true-label="2"
              false-label="0"
            ></el-checkbox>
          </el-col>
        </el-form-item>
        <el-form-item label="传真">
          <el-col :span="20">
            <el-input
              type="primary"
              link
              placeholder="请输入传真"
              v-model="ruleForm.contactFax"
            ></el-input>
          </el-col>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="单位地址" prop="baseAreaArr">
          <Region v-model="ruleForm.baseAreaArr"></Region>
        </el-form-item>
        <el-form-item prop="addressDetail" class="address-detail" label-width="0">
          <el-input v-model="ruleForm.addressDetail" placeholder="详细地址" style="width: 460px" />
        </el-form-item>
      </el-row>
      <el-form-item>
        <el-col :span="20">
          <el-checkbox
            v-model="ruleForm.isSynContact"
            label="修改联系方式同步到职位"
            true-label="2"
            false-label="0"
          ></el-checkbox>
        </el-col>
      </el-form-item>
      <p class="unit-info">二级院校联系方式</p>
      <p>
        <el-button type="primary" link size="small" class="addSeondbtn" @click="addSeondBtn"
          >+添加二级院校</el-button
        >
      </p>
      <div v-for="(item, index) in ruleForm.childColleges" :key="index" class="secondary">
        <Secondary v-model="ruleForm.childColleges[index]" :index="index"></Secondary>
        <el-form-item>
          <el-button type="danger" @click="deleteSeondBtn(item.id)">删除</el-button>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click="onSubmit(ruleForm)">全部保存</el-button>
      </el-form-item>
    </el-form>
    <DialogWelfare
      title="职位福利"
      ref="dialogWelfare"
      @confirm="handleWelfare"
      :memberId="id"
    ></DialogWelfare>
  </div>
</template>
<script lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getEditDeatils,
  getWaitCooperationEdit,
  postEdit,
  postWaitCooperEdit
} from '/@/api/unitManage'
import { useRoute } from 'vue-router'
import Industry from '/@/components/base/industry.vue'
import DialogWelfare from '/@/components/base/welfare.vue'
import Secondary from './components/secondary.vue'
import Region from '/@/components/base/select/region.vue'
import CompanyStyle from './components/companyStyle.vue'

export default {
  components: { Industry, DialogWelfare, Secondary, Region, CompanyStyle },
  name: 'editCompany',
  setup() {
    const state = <any>reactive({
      banner: 'banner',
      logo: 'logo',
      license: 'license',
      handler: 'handler',
      // 绑定数据
      ruleForm: {
        baseFullName: '',
        baseCompanyId: '',
        baseShortName: '',
        baseEnglishName: '',
        baseType: '',
        baseNature: '',
        baseScale: '',
        baseIndustryId: '',
        baseWebsite: '',
        baseIntroduce: '',
        baseLogoUrl: '',
        baseMobileHeadBannerUrl: '',
        baseLicensePath: '',
        basePersonInfoPath: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        contactTelephone: '',
        contactDepartment: '',
        contactFax: '',
        contactNameIsPublic: '0',
        contactEmailIsPublic: '0',
        childName: '',
        childContact: '',
        childTelephone: '',
        childFax: '',
        addressDetail: '',
        baseLabelIds: '',
        baseWelfareLabelIds: '',
        childColleges: <any>[],
        baseHeadBannerUrl: '',
        styleAtlas: [],
        isSynContact: '0',
        baseAreaArr: []
      },

      // 表单校验
      cooperationRules: {
        baseFullName: [{ required: true, message: '单位名称不能为空', trigger: 'blur' }],
        baseLogoUrl: [{ required: true, message: '请上传logo或校徽', trigger: 'change' }],
        baseType: [{ required: false, message: '请选择单位类型', trigger: 'change' }],
        // baseScale: [{ required: true, message: '请选择单位规模', trigger: 'change' }],
        // baseIndustryId: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
        baseIntroduce: [{ required: true, message: '请填写单位介绍', trigger: 'blur' }],
        contactMobile: [
          { required: false, message: '请填写手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        baseLicensePath: [{ required: true, message: '请上传单位资质证明', trigger: 'change' }],
        basePersonInfoPath: [
          { required: true, message: '请上传经办人身份证明', trigger: 'change' }
        ],
        contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        childContact: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        contactDepartment: [{ required: true, message: '请填写所在部门', trigger: 'blur' }],
        addressDetail: [{ required: true, message: '请输入联系地址', trigger: 'blur' }],
        childName: [{ required: true, message: '请输入院系名称', trigger: 'blur' }],
        baseAreaArr: [{ required: true, message: '请选择地区', trigger: 'change' }]
      },
      waitCooperationRule: {
        baseFullName: [{ required: true, message: '单位名称不能为空', trigger: 'blur' }],
        baseNature: [{ required: true, message: '请选择单位性质', trigger: 'change' }],
        baseType: [{ required: false, message: '请选择单位类型', trigger: 'change' }]
        // baseScale: [{ required: false, message: '请选择单位规模', trigger: 'change' }],
        // baseIndustryId: [{ required: false, message: '请选择所属行业', trigger: 'change' }]
      },
      data: {
        industryList: [],
        natureList: [],
        scaleList: [],
        tagList: [],
        welfareLabelList: [],
        typeList: []
      },
      userlocation: { lng: '', lat: '' },
      contactNameIsPublicCheck: false,
      contactMobileIsPublicCheck: false,
      contactEmailIsPublicCheck: false,
      // 图片地址
      imageUrl: '',
      welfareArray: [],
      welfareText: '',
      id: '',
      rules: {}
    })
    const form = ref()
    const route = useRoute()
    const dialogWelfare = ref()
    const baseLogo = ref()
    // 获取编辑回显
    const { isCooperation } = route.query
    const editDetails = async () => {
      if (isCooperation) {
        const res = await getEditDeatils({ id: route.query.id })
        state.data = { ...res }
        const { baseStyleAtlasList: styleAtlas } = res.companyInfo
        state.ruleForm = {
          ...res.companyInfo,
          styleAtlas
        }
        state.welfareArray = res.companyInfo.baseWelfareTage
        state.welfareText = res.companyInfo.baseWelfareTage.map((item: any) => item.v).join(',')
        state.ruleForm.baseLabelIds = res.companyInfo.baseCompanyTage.map((item: any) => item.k)
        state.ruleForm.childColleges = res.companyInfo.childColleges

        // 两种不同类型的单位的的规则不一样
        state.rules = state.cooperationRules
      } else {
        const data = await getWaitCooperationEdit({ id: route.query.id })
        state.data = data
        const { baseStyleAtlasList: styleAtlas } = data.companyInfo
        state.ruleForm = { ...data.companyInfo, styleAtlas }
        state.welfareArray = data.companyInfo.baseWelfareTage
        state.welfareText = data.companyInfo.baseWelfareTage.map((item: any) => item.v).join(',')
        state.ruleForm.baseLabelIds = data.companyInfo.baseCompanyTage.map((item: any) => item.k)
        state.ruleForm.childColleges = data.companyInfo.childColleges
        // 两种不同类型的单位的的规则不一样
        state.rules = state.waitCooperationRule
      }
      state.id = route.query.id
    }
    const onSubmit = async (val: any) => {
      const styleAtlasIds = val.styleAtlas?.map((item) => item.id).join()
      if (isCooperation) {
        // 先验证
        form.value.validate(async (valid: boolean) => {
          if (valid) {
            await postEdit({
              ...val,
              baseLabelIds: val.baseLabelIds.join(','),
              styleAtlas: styleAtlasIds
            })

            editDetails()
          } else {
            return false
          }
          return valid
        })
      } else {
        form.value.validate(async (valide: boolean) => {
          if (valide) {
            await postWaitCooperEdit({
              ...val,
              baseLabelIds: val.baseLabelIds.join(','),
              styleAtlas: styleAtlasIds,
              shortName: val.baseShortName
            })
          } else {
            return false
          }
          return valide
        })
      }
    }
    onMounted(async () => {
      editDetails()
    })
    // 图片上传
    const handleAvatarSuccess = (res: any, file: any) => {
      state.imageUrl = URL.createObjectURL(file.raw)
      state.ruleForm.baseLogoUrl = res.data.fullUrl
      baseLogo.value.clearValidate()
    }
    const beforeAvatarUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传头像图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }
    const beforeUpload = (file: any) => {
      const isJPG =
        file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isJPG) {
        ElMessage.error('上传文件只能是 JPG/PNG/JPEG 格式!')
      }
      if (!isLt10M) {
        ElMessage.error('上传文件大小不能超过10M')
      }
      return isJPG && isLt10M
    }
    const handleSuccess = (response: any) => {
      state.ruleForm.baseLicensePath = response.data.url
    }
    const handleSuccesses = (response: any) => {
      state.ruleForm.basePersonInfoPath = response.data.url
    }
    // 点击添加二级院校
    const addSeondBtn = () => {
      state.ruleForm.childColleges.push({
        name: '',
        contact: '',
        telephone: '',
        fax: ''
      })
    }

    const deleteSeondBtn = (id: string) => {
      state.ruleForm.childColleges = state.ruleForm.childColleges.filter(
        (item: any) => item.id !== id
      )
    }

    // 单位福利
    const openDialogWelfare = () => {
      dialogWelfare.value.openDialog(state.welfareArray)
    }
    const handleWelfare = (welfare: any) => {
      state.welfareArray = welfare
      state.welfareText = welfare.map((item: any) => item.v).join('，')
      state.ruleForm.baseWelfareLabelIds = welfare.map((item: any) => item.k).join(',')
    }

    return {
      onSubmit,
      ...toRefs(state),
      handleAvatarSuccess,
      beforeAvatarUpload,
      form,
      handleSuccess,
      handleSuccesses,
      addSeondBtn,
      deleteSeondBtn,
      isCooperation,
      dialogWelfare,
      handleWelfare,
      openDialogWelfare,
      beforeUpload,
      baseLogo
    } as any
  }
}
</script>
<style lang="scss" scoped>
#mapContainer {
  width: 100%;
  height: 400px;
}
.main {
  background-color: #fff;
  padding: 15px;
  .head-banner {
    display: flex;
    .banner {
      color: #196bf9;
      margin: 0 10px;
      font-size: 12px;
    }
  }

  // 特殊处理
  :deep(.el-textarea__inner) {
    min-height: 360px !important;
  }

  .unit-info {
    border-left: 2px solid #196bf9;
    text-indent: 1em;
    margin: 15px;
  }

  .info {
    margin: 20px;
    color: #606266;
    border-collapse: collapse;
    border-color: rgba(#909399, 0.3);

    td {
      padding: 10px;
    }

    .table-title {
      background-color: #f3f6f9;
    }
  }

  .address-detail {
    margin-left: 20px;
  }
  :deep(.el-form-item__content) {
    align-items: flex-start;
  }
  .avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
    border: 1px dashed #d9d9d9;
  }
  .avatar {
    width: 150px;
    height: 150px;
    display: block;
  }
  .logo {
    color: #2c93fa;
  }
  .addSeondbtn {
    margin-left: 700px;
    margin-top: -45px;
  }
  .secondary {
    padding: 10px 30px;
  }
  .el-col-12 {
    display: flex;

    .preview {
      flex: 1 0 auto;
      margin-left: 10px;
      text-decoration: none;
      color: var(--el-color-primary);
    }
  }
}
</style>
