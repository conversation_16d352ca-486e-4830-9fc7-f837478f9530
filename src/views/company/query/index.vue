<template>
  <div class="main">
    <div class="formbigbox">
      <div class="formbox">
        <el-form ref="form" :model="formData" label- class="demo-form-inline" :inline="true">
          <el-row>
            <el-col :span="4">
              <el-form-item label="单位检索" prop="companySearch">
                <el-input
                  v-model="formData.companySearch"
                  placeholder="请填写单位名称或者ID"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="单位性质" prop="nature">
                <el-select v-model="formData.nature" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in unitList.companyNatureList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="单位类型" prop="type">
                <el-select v-model="formData.type" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in unitList.companyTypeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="所在地区" prop="area">
                <Region
                  v-model="formData.area"
                  :filterable="true"
                  :multiple="true"
                  collapseTags
                ></Region>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="所属行业" prop="industryId">
                <Industry v-model="formData.industryId" multiple></Industry>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="创建时间" prop="addTimeFrom">
                <DatePickerRange
                  v-model:start="formData.addTimeFrom"
                  v-model:end="formData.addTimeTo"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="4">
              <el-form-item label="手机号码" prop="mobile">
                <el-input
                  v-model="formData.mobile"
                  placeholder="请填写手机号码"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="入网来源" prop="sourceType">
                <el-select v-model="formData.sourceType" placeholder="不限" clearable>
                  <el-option
                    v-for="item in unitList.companySourceTypeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="会员类型" prop="packageType">
                <el-select v-model="formData.packageType" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in unitList.companyPackageList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="单位状态" prop="status">
                <el-select v-model="formData.status" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in unitList.companyAccountsList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="业务员" prop="salesman" label-width="68px">
                <el-input
                  v-model="formData.salesman"
                  placeholder="请输入业务员姓名或账号"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="注册邮箱" prop="email" label-width="68px">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="4">
              <el-form-item label="创建人" prop="createUserName" label-width="68px">
                <el-input
                  v-model="formData.createUserName"
                  placeholder="请输入创建人账号"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="隐藏状态" prop="isHide">
                <el-select v-model="formData.isHide" placeholder="全部" filterable clearable>
                  <el-option
                    v-for="item in unitList.hideStatusList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="投递配置" prop="deliveryType" label-width="68px">
                <el-select v-model="formData.deliveryType" filterable clearable>
                  <el-option
                    v-for="item in unitList.deliveryType"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="账号性质" prop="accountNature" label-width="68px">
                <el-select v-model="formData.accountNature" filterable clearable>
                  <el-option
                    v-for="item in unitList.accountNature"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="是否小程序" prop="isMiniapp" label-width="85px">
                <IsMiniapp v-model="formData.isMiniapp" />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="是否高才海外" prop="isAbroad" label-width="100px">
                <el-select v-model="formData.isAbroad" filterable clearable>
                  <el-option
                    v-for="item in isAbroadList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="4">
              <el-form-item label="单位群组" prop="groupIds">
                <companyGroup v-model="formData.groupIds" :data="companyGroupList" />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="单位特色标签" prop="featuredTagIds" label-width="100px">
                <el-select
                  class="select"
                  v-model="formData.featuredTagIds"
                  clearable
                  multiple
                  filterable
                  collapse-tags
                >
                  <el-option
                    v-for="item in unitList.featuredTagList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="getData">搜索</el-button>
            <el-button type="default" @click="resetForm">重置</el-button>
            <el-button
              type="default"
              v-loading="downloadLoading"
              @click="downloadExcel({ ...formData, export: 1 })"
              >下载</el-button
            >
            <el-button type="default" @click="downloadExcelAll">全部下载</el-button>

            <el-upload
              ref="groupUploadRef"
              action="/company/batch-edit-group"
              :limit="1"
              class="ml-12"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              :on-error="handleUploadError"
            >
              <el-button type="primary" v-if="groupPrivilege">批量更新群组</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="addUnitBtn">+新增单位</el-button>
        <div class="unitCount">
          <div>
            共计：<span class="danger">{{ pagination.total }}</span
            >所单位
          </div>
          <el-link :underline="false" type="primary" size="small" @click="handleOpenCustomColumn"
            >选择列</el-link
          >
        </div>
        <div class="showTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            style="width: 100%"
            align="center"
            @sort-change="handleSortable"
          >
            <template v-for="(item, index) in customColumns">
              <el-table-column
                v-if="item.select && item.k === 1"
                :key="index"
                :label="item.v"
                prop="uid"
                width="100"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 2"
                :key="index"
                :label="item.v"
                prop="fullName"
                width="180"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 3"
                :key="index"
                :label="item.v"
                prop="contact"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 4"
                :key="index"
                :label="item.v"
                prop="mobile"
                align="center"
                width="120"
              />
              <el-table-column
                v-if="item.select && item.k === 5"
                :key="index"
                :label="item.v"
                prop="packageNameTxt"
                width="120"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 6"
                :key="index"
                :label="item.v"
                prop="memberId"
                width="120"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 7"
                :key="index"
                prop="sortAddTime"
                align="center"
                header-align="center"
                sortable="custom"
                :label="item.v"
                width="120"
              >
                <template #default="{ row }">
                  <span>{{ row.addTime }}</span>
                </template>
              </el-table-column>

              <el-table-column
                v-if="item.select && item.k === 8"
                :key="index"
                prop="subTotal"
                align="center"
                header-align="center"
                :label="item.v"
                width="120"
              >
                <template #default="{ row }">
                  <span class="point color-primary" @click="toPathAccount(row.uid)">{{
                    `${row.subUsed}/${row.subTotal}`
                  }}</span>
                </template>
              </el-table-column>

              <el-table-column
                v-if="item.select && item.k === 9"
                :key="index"
                :label="item.v"
                prop="address"
                align="center"
                width="120"
              />
              <el-table-column
                v-if="item.select && item.k === 10"
                :key="index"
                :label="item.v"
                prop="statusCompany"
                align="center"
                fixed="right"
                width="240px"
              >
                <template #default="scope">
                  <el-row>
                    <el-col :span="8">
                      <el-button size="small" type="primary" @click="checkDetails(scope.row.id)">
                        查看</el-button
                      >
                    </el-col>
                    <el-col :span="8">
                      <el-button size="small" type="success" @click="editUnitBtn(scope.row)">
                        编辑</el-button
                      >
                    </el-col>
                    <el-col :span="8">
                      <el-button size="small" type="warning" @click="memberLogBtn(scope.row)">
                        日志</el-button
                      >
                    </el-col>
                    <el-col :span="8">
                      <el-button
                        size="small"
                        type="danger"
                        v-if="scope.row.statusMember === '1'"
                        @click="disableUnit({ id: scope.row.memberId })"
                      >
                        禁用</el-button
                      >
                      <el-button
                        size="small"
                        type="danger"
                        @click="openUnit({ id: scope.row.memberId })"
                        v-else
                      >
                        可用</el-button
                      >
                    </el-col>

                    <el-col :span="8">
                      <el-button
                        size="small"
                        type="info"
                        @click="changeHideStateUnitBtn(scope.row.companyId)"
                      >
                        {{ hideButtonText(scope.row.isHide) }}</el-button
                      >
                    </el-col>

                    <el-col :span="8">
                      <el-button
                        size="small"
                        type="danger"
                        v-if="endPackagelist.includes('endPackage')"
                        @click="openInfoDialog(scope.row.memberId)"
                        >终止套餐
                      </el-button>
                    </el-col>

                    <el-col :span="8">
                      <el-button size="small" type="primary" @click="handlSetAccount(scope.row.id)"
                        >子账号配置
                      </el-button>
                    </el-col>

                    <el-col :span="8">
                      <el-button
                        size="small"
                        type="warning"
                        @click="handleOpenTag(scope.row.companyId, scope.row.featuredTagIds)"
                        >贴标签
                      </el-button>
                    </el-col>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column
                v-if="item.select && item.k === 11"
                :key="index"
                :label="item.v"
                prop="natureTxt"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 12"
                :key="index"
                :label="item.v"
                prop="typeTxt"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 13"
                :key="index"
                :label="item.v"
                prop="industryTxt"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 14"
                :key="index"
                :label="item.v"
                prop="statusMemberTxt"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 15"
                :key="index"
                :label="item.v"
                prop="createUserName"
                align="center"
              />
              <el-table-column
                v-if="item.select && item.k === 16"
                :key="index"
                :label="item.v"
                prop="sourceTypeName"
                align="center"
              />
              <el-table-column
                prop="name"
                align="center"
                v-if="item.select && item.k === 17"
                :key="index"
                :label="item.v"
              />
              <el-table-column
                prop="mEmail"
                align="center"
                v-if="item.select && item.k === 18"
                :key="index"
                :label="item.v"
              />
              <el-table-column
                prop="isMiniappTxt"
                align="center"
                v-if="item.select && item.k === 20"
                :key="index"
                :label="item.v"
              >
                <template #default="{ row }">
                  <isMiniappChange
                    :value="row.isMiniapp"
                    type="company"
                    :id="row.id"
                  ></isMiniappChange>
                </template>
              </el-table-column>

              <el-table-column
                prop="groupIds"
                align="center"
                v-if="item.select && item.k === 21"
                :key="index"
                :label="item.v"
              >
                <template #default="{ row }">
                  <companyGroup
                    v-if="groupPrivilege"
                    v-model="row.groupIds"
                    :data="companyGroupList"
                    @change="handleGroupChange(row.groupIds, row.companyId)"
                  />
                  <span v-else>{{ row.groupNames }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="isAbroadTxt"
                align="center"
                v-if="item.select && item.k === 22"
                :key="index"
                :label="item.v"
              >
                <template #default="{ row }">
                  <el-select
                    v-model="row.isAbroad"
                    @change="handleAbroadChange(row.id, row.isAbroad)"
                  >
                    <el-option
                      v-for="item in isAbroadList"
                      :key="item.k"
                      :label="item.v"
                      :value="item.k"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column
                prop="featuredTags"
                align="center"
                v-if="item.select && item.k === 23"
                :key="index"
                :label="item.v"
              />
            </template>
          </el-table>
        </div>
        <div class="paging">
          <Paging :total="pagination.total" @change="change"></Paging>
        </div>
      </div>
    </div>
    <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns"></CustomColumnDialog>
    <EndpackageDialog ref="endpackageDialog" @update="getData" />
    <ConfigAccount v-model="configAccountVisible" :data="accountInfo" @refresh="getData" />
    <TagDialog ref="tagDialog" @update="getData" @tags="getParams" />
  </div>
</template>

<script lang="ts">
import { reactive, ref, onMounted, toRefs, defineComponent, computed } from 'vue'
import {
  unitIsAvailable,
  getUnitList,
  getList,
  unitEnable,
  updateHideState
} from '/@/api/unitManage'
import { getAccountConfig } from '/@/api/account'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import Region from '/@select/region.vue'
import Paging from '/@/components/base/paging.vue'
import Industry from '/@/components/base/industry.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog.vue'
import { changeIsAbroad, getIsAbroad, getTableStagingField, getCompanyGroup } from '/@/api/config'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import EndpackageDialog from '../components/endpackageDialog.vue'
import { useStore } from '/@/store'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'
import ConfigAccount from '../components/configAccount.vue'
import companyGroup from '/@select/companyGroup.vue'
import { companyEditGroup } from '/@/api/companyGroup'
import TagDialog from '../components/tagDialog.vue'

export default defineComponent({
  name: 'queryComany',
  components: {
    Region,
    Paging,
    Industry,
    CustomColumnDialog,
    DatePickerRange,
    EndpackageDialog,
    IsMiniapp,
    isMiniappChange,
    ConfigAccount,
    companyGroup,
    TagDialog
  },
  setup() {
    const state = reactive({
      // 表单数据
      formData: <any>{
        companySearch: '', // 单位检索
        nature: '', // 单位性质
        type: '', // 单位类型
        area: [], // 所在地区
        industryId: [], // 所属行业
        addTimeFrom: '', // 创建时间
        addTimeTo: '', // 创建时间
        mobile: '', // 手机号码
        sourceType: '', // 入网来源
        packageType: '', // 会员类型
        status: '', // 单位状态
        salesman: '', // 业务员姓名
        createUserName: '', // 创建人
        isHide: '', // 隐藏状态 2正常显示 1隐藏
        deliveryType: '',
        accountNature: '',
        email: '',
        isAbroad: '', // 是否海外
        groupIds: '',
        featuredTagIds: [],
        sortAddTime: 1,
        // 排序页码
        page: 1,
        pageSize: 20
      },
      // 单位列表
      unitList: <any>{},
      dialogFormVisible: false,
      configAccountVisible: false,
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      downloaUrl: '',
      accountInfo: {},
      loading: false,
      downloadLoading: false,
      endPackagelist: <any>[],
      isAbroadList: [],
      companyGroupList: []
    })

    const store = useStore()
    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )
    state.endPackagelist = requestOldRoutesAction.value.queryComany

    const endpackageDialog = ref()
    const customColumnDialog = ref()
    const groupUploadRef = ref()
    const tagDialog = ref()

    // 接收路由参数 groupId
    const { groupId } = useRoute().query
    // 赋值给群组
    if (groupId) {
      state.formData.groupIds = groupId
    }

    // 是否拥有群组分配权限
    const groupPrivilege = state.endPackagelist?.includes('updateCompanyGroup')

    const customColumns = <any>ref([
      {
        k: 1,
        v: '单位ID',
        name: 'uid',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '单位名称',
        name: 'fullName',
        select: true,
        default: true
      },

      {
        k: 3,
        v: '联系人姓名',
        name: 'contact',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '联系电话',
        name: 'mobile',
        select: true,
        default: true
      },
      {
        k: 5,
        v: '会员类型',
        name: 'packageNameTxt',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '注册账号ID',
        name: 'memberId',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '创建时间',
        name: 'addTime',
        select: true,
        default: true
      },

      {
        k: 8,
        v: '子账号数量(已创建/总量)',
        name: 'subTotal',
        select: true,
        default: true
      },

      {
        k: 9,
        v: '单位地址',
        name: 'address',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '操作',
        name: 'statusCompany',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '单位性质',
        name: 'natureTxt',
        select: false,
        default: false
      },
      {
        k: 12,
        v: '单位类型',
        name: 'typeTxt',
        select: false,
        default: false
      },
      {
        k: 13,
        v: '所属行业',
        name: 'industryTxt',
        select: false,
        default: false
      },
      {
        k: 14,
        v: '单位状态',
        name: 'statusMemberTxt',
        select: false,
        default: false
      },
      {
        k: 15,
        v: '创建人',
        name: 'createUserName',
        select: false,
        default: false
      },
      {
        k: 16,
        v: '入网来源',
        name: 'sourceTypeName',
        select: false,
        default: false
      },
      {
        k: 17,
        v: '业务员',
        name: 'name',
        select: false,
        default: false
      },
      {
        k: 18,
        v: '注册邮箱',
        name: 'mEmail',
        select: false,
        default: false
      },

      {
        k: 20,
        v: '是否小程序',
        name: 'isMiniappTxt',
        select: false,
        default: false
      },

      {
        k: 21,
        v: '单位群组',
        name: 'groupIds',
        select: false,
        default: false
      },
      {
        k: 22,
        v: '是否高才海外',
        name: 'isAbroadTxt',
        select: false,
        default: false
      },
      {
        k: 23,
        v: '单位特色标签',
        name: 'featuredTags',
        select: false,
        default: false
      }
    ])

    // 修改分组的权限key
    // const editGroup = 'company:editGroup'

    const getParams = async () => {
      state.unitList = await getUnitList()
    }

    const getData = async () => {
      state.loading = true
      const {
        formData: { industryId, area, featuredTagIds },
        formData
      } = state

      const postData = {
        ...formData,
        industryId: industryId.length ? (<any>industryId).join() : '',
        area: area.length ? (<any>area).join() : '',
        featuredTagIds: featuredTagIds.length ? (<any>featuredTagIds).join() : ''
      }

      const { list, pages } = await getList(postData)
      state.tableData = list
      state.pagination = pages
      state.loading = false

      getParams()
    }

    // 获取后台数据类型
    onMounted(async () => {
      state.companyGroupList = await getCompanyGroup()

      getTableStagingField('companyQuery').then((resp: any) => {
        if (!resp.value) return

        const value = resp.value.split(',')
        customColumns.value = customColumns.value.map((item: any) => {
          return {
            ...item,
            select: value.includes(item.name)
          }
        })
      })
      // 获取单位属性数据
      getData()
      state.isAbroadList = await getIsAbroad()
    })

    const form = ref()
    // 重置
    const resetForm = () => {
      form.value.resetFields()

      state.formData.addTimeTo = ''
      state.formData.groupIds = ''
      state.formData.lastActiveTimeTo = ''
      state.formData.lastLoginTimeTo = ''
      state.formData.industryId = state.formData.industryId.length ? '' : ''
      state.formData.area = state.formData.area.length ? '' : ''
      state.formData.featuredTagIds = state.formData.featuredTagIds.length ? '' : ''

      getData()
    }

    const router = useRouter()
    const addUnitBtn = async () => {
      router.push({
        name: 'addCompany'
      })
    }
    // 编辑单位按钮
    const editUnitBtn = (data: any) => {
      // 路由跳转
      router.push({
        path: '/company/edit',
        query: { id: data.id, isCooperation: 1 }
      })
    }
    // 跳去会员日志
    const memberLogBtn = (data: any) => {
      // 路由跳转
      router.push({
        path: '/member/logList',
        query: { id: data.memberId, isShow: 1 }
      })
    }
    // 跳去单位详情
    const checkDetails = (data: any) => {
      // 路由跳转
      router.push({
        path: '/company/details',
        query: { id: data }
      })
    }
    // 打开弹窗
    const disableUnit = (val: object) => {
      ElMessageBox.confirm(
        '确定要禁用该单位吗？禁用后该单位下的所有账号均不可登录单位后台。',
        '提示'
      ).then(async () => {
        await unitIsAvailable(val)
        getData()
      })
    }

    // 禁用状态
    const openUnit = (val: Object) => {
      ElMessageBox.confirm(
        '确定要启用该单位吗？启用后该单位下的所有账号均可登录单位后台。',
        '提示'
      ).then(async () => {
        await unitEnable(val)
        getData()
      })
    }

    // 隐藏展示按钮
    const changeHideStateUnitBtn = async (id: string) => {
      await updateHideState({ id })
      getData()
    }
    // 隐藏按钮文字
    const hideButtonText = (val) => {
      if (val === '2') {
        return '隐藏'
      }
      return '展示'
    }
    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getData()
    }
    // 下载
    const downloadExcel = (data: object) => {
      // 出loading
      state.loading = true
      state.downloadLoading = true
      getList(data)
        .then((res) => {
          state.downloaUrl = res.excelUrl
          state.loading = false
          state.downloadLoading = false
        })
        .catch(() => {
          state.loading = false
          state.downloadLoading = false
        })
    }

    // 下载
    const downloadExcelAll = () => {
      getList({ export: 1, all: 1 })
    }

    // 选择列
    const handleOpenCustomColumn = () => {
      customColumnDialog.value.open('companyQuery')
    }
    // 所属行业
    const handleChange = (data: any) => {
      state.formData.industryId = data
    }

    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortAddTime')
      Reflect.deleteProperty(state.formData, 'sortLastLoginTime')

      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getData()
    }

    // 终止套餐
    const openInfoDialog = (data: any) => {
      endpackageDialog.value.open(data)
    }

    const handlSetAccount = async (id: string) => {
      state.accountInfo = await getAccountConfig({ id })
      state.configAccountVisible = true
    }

    const toPathAccount = (id: string) => {
      router.push({
        path: '/company/account',
        query: { id }
      })
    }

    const handleAbroadChange = (id: string, value: string) => {
      changeIsAbroad({ id, type: 'company', value })
    }
    const handleGroupChange = async (groupIds: string, companyId: string) => {
      // 如果没有权限

      await companyEditGroup({ groupIds, companyId })
    }

    const handleUploadSuccess = (resp) => {
      const { data, msg } = resp
      if (!data) {
        state.loading = false
        ElMessage.warning(msg)
      } else {
        ElMessage.success(msg)
        // 重新加载列表
        getData()
      }

      groupUploadRef.value.clearFiles()
    }

    const beforeUpload = () => {
      // 出loading
      state.loading = true
    }

    const handleUploadError = () => {
      ElMessage.error('上传失败')
      state.loading = false
    }

    const handleOpenTag = (id, ids) => {
      tagDialog.value.open(id, ids)
    }

    return {
      ...toRefs(state),
      groupUploadRef,
      resetForm,
      addUnitBtn,
      form,
      disableUnit,
      editUnitBtn,
      memberLogBtn,
      checkDetails,
      change,
      downloadExcel,
      handleChange,
      openUnit,
      customColumnDialog,
      customColumns,
      endpackageDialog,
      handleOpenCustomColumn,
      handleSortable,
      changeHideStateUnitBtn,
      hideButtonText,
      downloadExcelAll,
      openInfoDialog,
      getData,
      handlSetAccount,
      toPathAccount,
      handleAbroadChange,
      handleGroupChange,
      handleUploadSuccess,
      groupPrivilege,
      getParams,
      beforeUpload,
      handleOpenTag,
      handleUploadError,
      tagDialog
    }
  }
})
</script>
<style lang="scss" scoped>
.el-form--inline {
  .el-form-item {
    display: flex;
    :deep() {
      .el-input,
      .el-cascader,
      .el-select,
      .el-autocomplete {
        display: block;
        width: 100% !important;
      }
    }
  }
}

.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  .buttomBox {
    margin-left: 110px;
  }
  .unitCount {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    margin: 20px 0;
    height: 30px;
    line-height: 30px;
    background-color: #edf9ff;
    .danger {
      color: #d9041a;
      font-weight: bold;
    }
  }
  .paging {
    margin-top: 90px;
  }
}
</style>
