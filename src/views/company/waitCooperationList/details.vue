<template>
  <div class="main">
    <div>免费单位详情</div>
    <el-card>
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="4" border>
        <el-descriptions-item label="单位名称：">{{
          detailsList.basicsInfo.fullName
        }}</el-descriptions-item>
        <el-descriptions-item label="英文名称：">{{
          detailsList.basicsInfo.englishName
        }}</el-descriptions-item>
        <el-descriptions-item label="logo预览：">
          <el-button type="primary" link @click="previewLogo">预览</el-button>
        </el-descriptions-item>
        <el-descriptions-item label="单位性质：">{{
          detailsList.basicsInfo.natureTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="单位规模：">{{
          detailsList.basicsInfo.scaleTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="所属行业：">{{
          detailsList.basicsInfo.industryTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="单位官网：">{{
          detailsList.basicsInfo.website
        }}</el-descriptions-item>
        <el-descriptions-item label="单位标签：">{{
          detailsList.basicsInfo.labelName
        }}</el-descriptions-item>
        <el-descriptions-item label="单位主页背景">
          <el-button
            v-if="detailsList.basicsInfo.headBannerUrl"
            @click="handleImagePreview(detailsList.basicsInfo.headBannerUrl)"
            type="primary"
            link
            >预览</el-button
          >
        </el-descriptions-item>
        <el-descriptions-item label="单位风采">
          <el-button
            v-if="detailsList.basicsInfo.styleAtlasList.length"
            @click="handleImagePreview(detailsList.basicsInfo.styleAtlasList)"
            type="primary"
            link
            >预览</el-button
          >
        </el-descriptions-item>
        <el-descriptions-item label="单位类型：">{{
          detailsList.basicsInfo.typeTxt
        }}</el-descriptions-item>
        <el-descriptions-item label="单位福利：" :span="4">{{
          detailsList.basicsInfo.welfareName
        }}</el-descriptions-item>
        <el-descriptions-item label="单位介绍：">
          <div>{{ detailsList.basicsInfo.introduce }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="单位简称：">
          <div>{{ detailsList.basicsInfo.shortName }}</div>
        </el-descriptions-item>
      </el-descriptions>
      <!--  联系方式-->
      <el-descriptions title="联系方式" :column="4" border>
        <el-descriptions-item label="联系人：">{{
          detailsList.contactInfo.name
        }}</el-descriptions-item>
        <el-descriptions-item label="所在部门：">{{
          detailsList.contactInfo.department
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：">{{
          detailsList.contactInfo.mobile
        }}</el-descriptions-item>
        <el-descriptions-item label="固定电话："
          >{{ detailsList.contactInfo.telephone }}
        </el-descriptions-item>
        <el-descriptions-item label="传真：">{{
          detailsList.contactInfo.fax
        }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱：">{{
          detailsList.contactInfo.email
        }}</el-descriptions-item>
        <el-descriptions-item label="微信绑定：">{{
          detailsList.contactInfo.weixin ? '已绑定' : '未绑定'
        }}</el-descriptions-item>
        <el-descriptions-item label="联系地址：">{{
          detailsList.contactInfo.address
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="二级院校" :column="4" border>
        <div v-for="(item, index) in detailsList.childCompanyInfo" :key="index">
          <el-descriptions-item label="院系名称：">{{ item.childName }}</el-descriptions-item>
          <el-descriptions-item label="联系人：">{{ item.childContact }}</el-descriptions-item>
          <el-descriptions-item label="固定电话：">{{ item.childTelephone }}</el-descriptions-item>
          <el-descriptions-item label="传真：">{{ item.childFax }}</el-descriptions-item>
        </div>
      </el-descriptions>
    </el-card>
    <div class="mask">
      <!-- 外层的遮罩 -->
      <div class="mask-cover" v-if="isShow" @click="closeByMask"></div>
      <!-- 内容区 -->
      <div class="mask-content" v-if="isShow">
        <!-- 插槽，放置要插入到遮罩里的内容 -->
        <slot name="default">
          <img class="preImg" :src="detailsList.basicsInfo.logoUrl" alt="" />
        </slot>
      </div>
    </div>
    <ImagePreview hideOnClickModal ref="imagePreview" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getWaitCooperationDetails } from '/@/api/unitManage'

import ImagePreview from '/@/components/business/imagePreview.vue'

export default defineComponent({
  name: 'waitCooperationDetails',

  components: { ImagePreview },

  setup() {
    const detailsList = <any>ref({
      basicsInfo: {
        styleAtlasList: []
      },
      adminInfo: {},
      contactInfo: {},
      childCompanyInfo: {},
      packageInfo: {}
    })
    // 遮罩显示
    const isShow = ref(false)
    const imagePreview = ref()
    const route = useRoute()
    const { id } = route.params
    const getDetail = async () => {
      const res = await getWaitCooperationDetails({ id })
      detailsList.value = { ...res }
    }
    // logo预览方法
    const previewLogo = () => {
      isShow.value = true
    }
    // 关闭预览
    const closeByMask = () => {
      isShow.value = false
    }
    onMounted(() => {
      getDetail()
    })

    const handleImagePreview = (data) => {
      imagePreview.value.open(data)
    }

    return { detailsList, previewLogo, isShow, closeByMask, imagePreview, handleImagePreview }
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  --el-card-padding: 15px;
}
.mask {
  position: relative;
  color: #2e2c2d;
  font-size: 16px;
}
//遮罩，设置背景层，z-index值要足够大确保能覆盖，高度 宽度设置满 做到全屏遮罩
.mask-cover {
  background: rgba($color: #000000, $alpha: 0.5);
  position: fixed;
  z-index: 9999;
  // 设置top、left、宽高保证全屏遮罩
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

//内容层，z-index要大于遮罩层，确保内容在遮罩上显示
.mask-content {
  position: fixed;
  top: 30%;
  height: 70%;
  z-index: 10000;
  .preImg {
    width: 200px;
    height: 200px;
  }
}
</style>
