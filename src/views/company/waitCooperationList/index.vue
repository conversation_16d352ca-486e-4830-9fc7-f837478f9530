<template>
  <div class="box">
    <el-card>
      <BaseTable
        @search="handleSearch"
        @downloadFile="handleDownload"
        @downloadAllFile="downloadExcelAll"
        @reset="resetForm"
        :hideState="true"
        :paramsList="companyGroupList"
      ></BaseTable>
      <el-button type="primary" @click="addWaitCoope" class="addBtn"> +新增单位</el-button>
      <div class="unitCount">
        共计：<span class="danger">{{ pagination.total }}</span
        >所单位
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        align="center"
        @sort-change="handleSortable"
      >
        <el-table-column prop="uid" label="UID" align="center" />
        <el-table-column prop="fullName" label="单位名称" align="center" />
        <el-table-column prop="contact" label="联系人" align="center" />
        <el-table-column prop="department" label="所在部门" align="center" />
        <el-table-column prop="mobile" label="联系电话" align="center" />
        <el-table-column prop="telephone" label="固定电话" align="center" />
        <el-table-column prop="email" label="邮箱" align="center" />
        <el-table-column prop="natureTxt" label="单位性质" align="center" />
        <el-table-column prop="typeTxt" label="单位类型" align="center" />
        <el-table-column prop="industryTxt" label="所属行业" align="center" />
        <el-table-column prop="isMiniappTxt" label="是否小程序" align="center">
          <template #default="{ row }">
            <!-- <el-button type="primary">{{ row.isMiniappTxt }}</el-button> -->
            <isMiniappChange :value="row.isMiniapp" type="company" :id="row.id"></isMiniappChange>
          </template>
        </el-table-column>
        <el-table-column prop="isAbroadTxt" label="是否高才海外" align="center">
          <template #default="{ row }">
            <el-select v-model="row.isAbroad" @change="handleAbroadChange(row.id, row.isAbroad)">
              <el-option
                v-for="item in isAbroadList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="sortAddTime"
          label="创建时间"
          sortable="custom"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ row.addTime }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="groupIds" label="单位群组" align="center">
          <template #default="{ row }">
            <companyGroup
              v-if="groupPrivilege"
              v-model="row.groupIds"
              :data="companyGroupList"
              @change="handleGroupChange(row.groupIds, row.id)"
            />
            <span v-else>{{ row.groupNames }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="所在地" align="center" />
        <el-table-column label="操作" align="center" width="150px">
          <template #default="scope">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-button
                  type="primary"
                  size="small"
                  class="opbutton"
                  @click="checkDetails(scope.row.id)"
                >
                  查看</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  type="success"
                  size="small"
                  class="opbutton"
                  @click="editUnitBtn(scope.row)"
                >
                  编辑</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  type="info"
                  size="small"
                  class="opbutton"
                  @click="changeHideStateUnitBtn(scope.row.id)"
                >
                  {{ hideButtonText(scope.row.isHide) }}
                </el-button>
              </el-col>
              <!-- <el-col :span="8">
                <el-button
                  type="warning"
                  size="small"
                  class="opbutton"
                  @click="memberLogBtn(scope.row)"
                >
                  日志</el-button
                >
              </el-col> -->
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <Paging :total="pagination.total" @change="change"></Paging>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
import { computed, onMounted, reactive, toRefs } from 'vue'
import BaseTable from '/@/views/company/components/baseTable.vue'
import Paging from '/@/components/base/paging.vue'
import { useRouter } from 'vue-router'
import { getWaitCooperationList, updateHideState } from '/@/api/unitManage'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'
import { changeIsAbroad, getIsAbroad, getCompanyGroup } from '/@/api/config'
import { companyEditGroup } from '/@/api/companyGroup'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import CompanyGroup from '/@select/companyGroup.vue'
import { store } from '/@/store/index.ts'

export default {
  name: 'waitCooperationList',
  components: { BaseTable, Paging, IsMiniapp, isMiniappChange, CompanyGroup },
  setup() {
    const state = reactive({
      form: <any>{ sortAddTime: 1, industryId: '', area: '' },
      tableData: [],
      isAbroadList: [],

      // 分页信息
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      downloaUrl: '',
      loading: false,
      companyGroupList: []
    })
    const router = useRouter()
    // 拿数据方法
    const getData = async () => {
      const {
        form: { industryId, area },
        form
      } = state
      const postData = {
        ...form,
        industryId: industryId.length ? (<any>industryId).join() : '',
        area: area.length ? (<any>area).join() : ''
      }
      state.loading = true
      const { list, pages } = await getWaitCooperationList(postData)
      state.isAbroadList = await getIsAbroad()

      state.tableData = list
      state.pagination = pages
      state.loading = false

      state.companyGroupList = await getCompanyGroup()
    }
    onMounted(() => {
      getData()
    })
    const addWaitCoope = () => {
      router.push('/company/waitCooperationList/add')
    }
    // 发请求获取数据，搜索
    const handleSearch = async (val: any) => {
      const { list, pages } = await getWaitCooperationList(val)
      state.tableData = list
      state.pagination = pages
      state.form = val
    }

    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )
    const endPackagelist = requestOldRoutesAction.value.queryComany

    // 是否拥有群组分配权限
    const groupPrivilege = endPackagelist?.includes('updateCompanyGroup')

    // 下载
    const handleDownload = async (val: any) => {
      const { excelUrl } = await getWaitCooperationList(val)
      state.downloaUrl = excelUrl
      window.location.href = excelUrl
    }

    const downloadExcelAll = () => {
      // 出loading
      getWaitCooperationList({ export: 1, all: 1 })
    }

    const resetForm = () => {
      getData()
    }
    // 显示或隐藏按钮
    const changeHideStateUnitBtn = async (id: string) => {
      await updateHideState({ id })
      getData()
    }
    // 隐藏按钮文字
    const hideButtonText = (val) => {
      if (val === '2') {
        return '隐藏'
      }
      return '展示'
    }
    // 分页
    const change = (data: any) => {
      state.form.page = data.page
      state.form.pageSize = data.limit
      getData()
    }
    // 跳去会员日志
    const memberLogBtn = (data: any) => {
      // 路由跳转
      router.push({
        path: '/member/logList',
        query: { id: data.memberId }
      })
    }
    // 跳去查看详情
    const checkDetails = (id: any) => {
      router.push(`/company/waitCooperationList/details/${id}`)
    }
    // 编辑页面
    const editUnitBtn = (data: any) => {
      // 路由跳转
      router.push({
        path: '/company/edit',
        query: { id: data.id }
      })
    }
    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.form, 'sortAddTime')
      if (order === 'ascending') {
        // 正序
        state.form[prop] = 2
      } else if (order === 'descending') {
        state.form[prop] = 1
      }
      getData()
    }

    const handleAbroadChange = (id: string, value: string) => {
      changeIsAbroad({ id, type: 'company', value })
    }
    const handleGroupChange = async (groupIds: string, companyId: string) => {
      await companyEditGroup({ groupIds, companyId })
    }
    return {
      ...toRefs(state),
      addWaitCoope,
      handleSearch,
      change,
      handleDownload,
      checkDetails,
      memberLogBtn,
      editUnitBtn,
      resetForm,
      handleSortable,
      changeHideStateUnitBtn,
      hideButtonText,
      downloadExcelAll,
      handleAbroadChange,
      handleGroupChange,
      groupPrivilege
    }
  }
}
</script>
<style lang="scss" scoped>
.button {
  margin-bottom: 15px;
}
.unitCount {
  margin: 10px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
    margin-right: 5px;
  }
}
.paging {
  margin-top: 30px;
}
.opbutton {
  padding: 8px;
}
.addBtn {
  margin-top: 5px;
}
</style>
