<template>
  <div class="box">
    <el-card>
      <el-form :model="form" :rules="rules" ref="formData" label-position="right">
        <el-form-item label="单位类型" prop="isCooperation">
          <el-select
            v-model="form.isCooperation"
            placeholder="请选择合作类型"
            style="width: 720px"
            @change="changeCooper"
          >
            <el-option label="非合作单位" :value="0"></el-option>
          </el-select>
          <el-button type="primary" class="uploadBtn" @click="status = !status"
            >{{ status ? '单个' : '批量' }}新增</el-button
          >
        </el-form-item>
        <div v-if="status === false">
          <el-form-item label="单位名称" prop="fullName" style="width: 800px">
            <el-input
              v-model="form.fullName"
              autocomplete="off"
              placeholder="请填写单位全称；若属于二级单位或者课题组，请具体到二级单位或者课题组"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="单位简称" prop="shortName">
            <el-input
              style="width: 300px"
              v-model="form.shortName"
              maxlength="50"
              placeholder="请输入单位简称，最多50字"
              clearable
            ></el-input>
          </el-form-item>
          <el-row class="region">
            <el-form-item label="单位类型" prop="type">
              <el-select
                v-model="form.type"
                clearable
                placeholder="请选择单位类型"
                style="width: 300px"
              >
                <el-option
                  v-for="item in unitList.companyTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="单位性质" prop="nature" class="nature">
              <el-select
                v-model="form.nature"
                placeholder="请选择单位性质"
                class="contact"
                style="width: 300px"
              >
                <el-option
                  v-for="item in unitList.companyNatureList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-form-item label="所属行业" class="margin" prop="industryId">
            <Industry v-model="form.industryId"></Industry>
          </el-form-item>

          <el-form-item class="unit-box">
            <el-row class="company-address">
              <el-form-item label="单位地址" prop="area">
                <Region v-model="form.area"></Region>
              </el-form-item>
              <el-form-item prop="address" class="address">
                <el-input
                  v-model="form.address"
                  placeholder="详细地址"
                  style="width: 502px"
                  clearable
                />
              </el-form-item>
            </el-row>
          </el-form-item>

          <el-row>
            <el-form-item label="联系人" class="margin" prop="contact">
              <el-input
                v-model="form.contact"
                autocomplete="off"
                placeholder="请填写联系人姓名"
                class="contact"
                style="width: 300px"
              ></el-input>
            </el-form-item>
            <el-form-item label="所在部门" class="margin" prop="department">
              <el-input
                v-model="form.department"
                autocomplete="off"
                placeholder="请填写所在部门"
                clearable
                style="width: 322px"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-form-item label="联系电话" style="width: 790px" class="margin" prop="mobile">
            <el-input
              v-model="form.mobile"
              autocomplete="off"
              placeholder="请填写联系电话"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系邮箱" style="width: 790px" class="margin" prop="email">
            <el-input
              v-model="form.email"
              autocomplete="off"
              placeholder="请填写联系邮箱"
            ></el-input>
          </el-form-item>
          <el-form-item label="固定电话" style="width: 790px" class="margin" prop="telephone">
            <el-input
              v-model="form.telephone"
              autocomplete="off"
              placeholder="请填写单位固定电话"
            ></el-input>
          </el-form-item>
        </div>
        <div v-else>
          <el-form-item label="上传文件" prop="excelPath" class="uploadFile">
            <el-row>
              <el-upload
                class="upload-demo"
                action="company/upload-excel"
                :limit="1"
                :on-success="fileSucces"
                auto-upload
              >
                <el-button size="small" type="primary" class="chose-document">选择文件</el-button>
              </el-upload>
              <p class="upload" @click="downloadFile">下载导入模板</p>
            </el-row>
          </el-form-item>
        </div>
      </el-form>
      <div class="button">
        <span class="dialog-footer">
          <el-button @click="resetForm('formData')">重置</el-button>
          <el-button type="primary" @click="submitBtn(form)">提交</el-button>
        </span>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue'
import { addWaitCooperation, getUnitList, postWaitCooperation } from '/@/api/unitManage'
import { ElMessage } from 'element-plus'
import Region from '../../../components/base/select/region.vue'
import Industry from '/@/components/base/industry.vue'

export default {
  name: 'addWaitCooperation',
  components: { Region, Industry },
  setup() {
    const state = reactive({
      form: {
        isCooperation: 0, // 单位类型
        fullName: '', // 单位名称
        shortName: '', // 单位简称
        nature: '', // 单位类型
        type: '', // 单位性质
        industryId: '', // 所属行业
        area: '', // 所在地区
        address: '', // 详细地址
        contact: '', // 联系人
        department: '', // 所在部门
        mobile: '', // 联系电话
        email: '', // 邮箱
        telephone: '', // 固定电话
        excelPath: ''
      },
      rules: {
        isCooperation: [{ required: true, message: '请选择合作单位', trigger: 'change' }],
        fullName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        type: [{ required: false, message: '请选择单位类型', trigger: 'change' }],
        nature: [{ required: true, message: '请选择单位性质', trigger: 'change' }]
      }
    })
    const unitList = ref({})
    // 批量上传
    const status = ref(false)
    const formData = ref()
    onMounted(async () => {
      unitList.value = await getUnitList()
    })
    // 重置
    const resetForm = () => {
      formData.value.resetFields()
      state.form.nature = ''
      setTimeout(() => {
        formData.value.clearValidate()
      }, 0)
    }
    // 文件上传成功回调
    const fileSucces = (response: any) => {
      state.form.excelPath = response.data.url
    }
    // 下载导入模版
    const downloadFile = () => {
      window.location.href =
        '//test.gcjob.ideaboat.cn/uploads/template/company_unit_no_template.xlsx'
    }
    // 提交按钮
    const submitBtn = async (val: any) => {
      if (status.value === false) {
        formData.value.validate(async (valid: any) => {
          if (valid) {
            await addWaitCooperation(val)
            resetForm()
          } else {
            return false
          }
          return val
        })
      } else {
        await postWaitCooperation({ filePath: state.form.excelPath })
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
      }
    }
    return {
      ...toRefs(state),
      unitList,
      status,
      resetForm,
      formData,
      submitBtn,
      downloadFile,
      fileSucces
    }
  }
}
</script>
<style lang="scss" scoped>
.uploadBtn {
  margin-left: 20px;
}
.contact {
  margin-left: 15px;
}
.department {
  margin-left: 25px;
}
.telephone {
  margin-left: 31px;
}
.margin {
  margin-top: 10px;
  margin-left: 10px;
}

.unit-box {
  margin-left: 10px;
}
.unit-address {
  margin-left: -20px;
}
.address {
  margin-left: 25px;
}
.button {
  text-align: center;
  margin-top: 20px;
}
.upload {
  cursor: pointer;
  color: #2c93fa;
  margin-left: 15px;
}
.nature {
  margin-left: 20px;
}
.uploadFile {
  margin-left: 10px;
}
</style>
