<template>
  <div class="main">
    <el-card>
      <div class="formbox">
        <el-form ref="form" :model="formData" class="demo-form-inline" :inline="true">
          <el-row>
            <el-col :span="4">
              <el-form-item label="职位检索" prop="jobNameNum">
                <el-input
                  v-model="formData.jobNameNum"
                  placeholder="请输入职位名称或者编号"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="公告检索" prop="announcementNameNum">
                <el-input
                  v-model="formData.announcementNameNum"
                  placeholder="请输入公告名称或编号"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="人才检索" prop="resumeNameNum">
                <el-input
                  v-model="formData.resumeNameNum"
                  placeholder="请输入人才姓名或编号"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="单位检索" prop="companyNameNum">
                <el-input
                  v-model="formData.companyNameNum"
                  placeholder="请输入单位名称或编号"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="面试时间" prop="interviewTime">
                <DatePickerRange
                  v-model:start="formData.interviewTimeFrom"
                  v-model:end="formData.interviewTimeTo"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="面试状态" prop="interviewStatus">
                <el-select
                  v-model="formData.interviewStatus"
                  placeholder="不限"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in unitList.interviewStatus"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="联系电话" prop="telephone">
                <el-input
                  v-model="formData.telephone"
                  placeholder="请输入面试官联系电话"
                  clearable
                  @keyup.enter="searchBtn"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="简历来源" prop="source">
                <el-select v-model="formData.source" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in resumeSourceOptions"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-form-item>
              <el-button type="primary" @click="searchBtn">搜索</el-button>
              <el-button type="default" @click="resetForm('form')">重置</el-button>
              <el-button type="default" @click="downloadExcel({ ...formData, export: 1 })"
                >下载</el-button
              >
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div class="unitCount">
        共计<span class="danger">{{ pagination.total }}</span
        >条面试，共<span class="danger">{{ amount.companyCount }}</span
        >个单位 ； 未进行：<span class="danger">{{ amount.tobagoCount }}</span
        >条； 已结束：<span class="danger">{{ amount.endedCount }}</span
        >条
      </div>
      <div class="showTable">
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
          align="center"
          @sort-change="handleSortable"
        >
          <el-table-column prop="presonUid" label="人才编号" width="120" align="center" />
          <el-table-column prop="resumeName" label="人才姓名" align="center" />
          <el-table-column prop="baseInfo" label="基本信息" width="180" align="center" />
          <el-table-column prop="jobName" label="职位信息" align="center" />
          <el-table-column prop="jobUid" label="职位编号" align="center" />
          <el-table-column prop="title" label="公告名称" width="150" align="center">
            <template #default="{ row }">
              <router-link
                class="bg-primary td-none"
                :to="`/cms/announcementDetail/${row.announcementId}/${row.status}`"
                >{{ row.title }}</router-link
              >
            </template>
          </el-table-column>
          <el-table-column prop="noticeUid" label="公告编号" align="center" />
          <el-table-column prop="fullName" label="单位名称" align="center" width="150">
            <template #default="{ row }">
              <el-button type="primary" link @click="companyDetails(row.companyId)">{{
                row.fullName
              }}</el-button></template
            >
          </el-table-column>
          <el-table-column prop="companyUid" label="单位编号" align="center" />
          <el-table-column
            prop="sortInterviewTime"
            label="面试时间"
            sortable="custom"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ row.interviewTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="interviewStatusTxt" label="面试状态" align="center" />
          <el-table-column prop="sourceTxt" label="简历来源" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                class="opbutton"
                @click="checkDetails(scope.row.interviewId)"
              >
                查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="paging">
        <Paging :total="pagination.total" @change="change"></Paging>
      </div>
    </el-card>
    <InterviewInfo v-model="dialogVisible" :details="details"></InterviewInfo>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, toRaw, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import {
  getInterviewDetails,
  getList,
  getUnitList,
  checkInterviewDetails,
  getSourceList
} from '/@/api/unitManage'
import InterviewInfo from '/@/components/base/interviewInfo.vue'

import Paging from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

export default defineComponent({
  name: 'interview',
  components: { Paging, InterviewInfo, DatePickerRange },
  setup() {
    const state = reactive({
      formData: {
        jobNameNum: '',
        announcementNameNum: '',
        resumeNameNum: '',
        companyNameNum: '',
        interviewTimeFrom: '',
        interviewTimeTo: '',
        interviewStatus: '',
        telephone: '',
        source: '',
        // 排序页码
        page: 1,
        pageSize: 20,
        sortInterviewTime: 1
      },
      amount: {},
      unitList: {},
      tableData: [],
      // 分页信息
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      details: {},
      downloaUrl: '',
      dialogVisible: false,
      applyId: '',
      loading: false,
      resumeSourceOptions: []
    })
    const form = ref()
    const router = useRouter()
    const getData = async () => {
      state.loading = true
      const { list, pages, amount } = await getInterviewDetails(state.formData)
      state.tableData = list
      state.pagination = pages
      state.amount = amount
      state.unitList = await getUnitList()
      state.loading = false
    }

    const getOptions = async () => {
      state.resumeSourceOptions = await getSourceList()
    }

    onMounted(() => {
      getData()
      getOptions()
    })
    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getData()
    }
    // 搜索按钮
    const searchBtn = async () => {
      const { list } = await getInterviewDetails(state.formData)
      state.tableData = list
      change(list)
    }
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      state.formData.interviewTimeFrom = ''
      state.formData.interviewTimeto = ''
      getData()
    }

    // 下载
    const downloadExcel = async (data: object) => {
      const res = await getList(data)
      state.downloaUrl = res.excelUrl
      window.location.href = res.excelUrl
    }
    // 查看详情
    const checkDetails = async (data: any) => {
      state.details = await checkInterviewDetails({ interviewId: data })
      state.dialogVisible = true
    }
    // 打开单位详情页面
    const companyDetails = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }
    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortInterviewTime')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getData()
    }
    return {
      ...toRefs(state),
      searchBtn,
      form,
      resetForm,
      change,
      downloadExcel,
      checkDetails,
      companyDetails,
      handleSortable
    }
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-input__prefix-inner) {
  align-items: center;
}
.unitCount {
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.opbutton {
  padding: 4px;
}
.paging {
  margin-top: 30px;
}
.inter-title {
  margin: 10px;
  font-size: 18px;
  font-weight: 700;
}
.box {
  margin-top: 20px;
  margin-left: 10px;
  div {
    margin: 10px 0;
    color: #747474;
  }
}
.status {
  margin-left: 10px;
  color: #5dae23;
  font-size: 12px;
}
</style>
