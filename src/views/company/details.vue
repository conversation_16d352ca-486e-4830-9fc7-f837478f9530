<template>
  <div class="main">
    <div class="title">单位详情</div>
    <div class="detail-card">
      <el-card>
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="4" border>
          <el-descriptions-item label="单位名称：">{{
            detailsList.basicsInfo.fullName
          }}</el-descriptions-item>
          <el-descriptions-item label="英文名称：">{{
            detailsList.basicsInfo.englishName
          }}</el-descriptions-item>
          <el-descriptions-item label="logo预览：">
            <el-button type="primary" link @click="previewLogo">预览</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="单位性质：">{{
            detailsList.basicsInfo.natureTxt
          }}</el-descriptions-item>
          <el-descriptions-item label="单位规模：">{{
            detailsList.basicsInfo.scaleTxt
          }}</el-descriptions-item>
          <el-descriptions-item label="所属行业：">{{
            detailsList.basicsInfo.industryTxt
          }}</el-descriptions-item>
          <el-descriptions-item label="单位官网：">{{
            detailsList.basicsInfo.website
          }}</el-descriptions-item>
          <el-descriptions-item label="单位标签：">{{
            detailsList.basicsInfo.labelName
          }}</el-descriptions-item>
          <el-descriptions-item label="单位类型：">{{
            detailsList.basicsInfo.typeTxt
          }}</el-descriptions-item>
          <el-descriptions-item label="单位主页背景">
            <el-button
              v-if="detailsList.basicsInfo.headBannerUrl"
              @click="handleImagePreview(detailsList.basicsInfo.headBannerUrl)"
              type="primary"
              link
              >预览</el-button
            >
          </el-descriptions-item>
          <el-descriptions-item label="单位风采">
            <el-button
              v-if="detailsList.basicsInfo.styleAtlasList.length"
              @click="handleImagePreview(detailsList.basicsInfo.styleAtlasList)"
              type="primary"
              link
              >预览</el-button
            >
          </el-descriptions-item>
          <el-descriptions-item label="投递配置"
            >{{ detailsList.basicsInfo.deliveryTypeTxt }}
          </el-descriptions-item>
          <el-descriptions-item label="账号性质">
            <div class="flex">
              <div v-if="accountShow">
                {{ detailsList.basicsInfo.accountNatureTxt }}
              </div>
              <el-button type="text" v-if="accountShow" @click="showAccount">修改</el-button>
              <div v-if="submitShow">
                <el-select v-model="form.accountNature">
                  <el-option
                    v-for="item in accountNature"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </div>
              <el-button type="text" v-if="submitShow" @click="submit">提交</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="单位福利：" :span="4">{{
            detailsList.basicsInfo.welfareName
          }}</el-descriptions-item>
          <el-descriptions-item label="单位介绍：" :span="4">
            <div>{{ detailsList.basicsInfo.introduce }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="注册账号信息" :column="4" border>
          <el-descriptions-item label="账号ID：">{{
            detailsList.basicsInfo.memberId
          }}</el-descriptions-item>
          <el-descriptions-item label="用户名：">{{
            detailsList.basicsInfo.username
          }}</el-descriptions-item>
          <el-descriptions-item label="联系人姓名：">{{
            detailsList.basicsInfo.memberContact
          }}</el-descriptions-item>
          <el-descriptions-item label="所在部门：">{{
            detailsList.basicsInfo.memberDepartment
          }}</el-descriptions-item>
          <el-descriptions-item label="注册手机号：">{{
            detailsList.basicsInfo.memberMobile
          }}</el-descriptions-item>
          <el-descriptions-item label="注册邮箱：">{{
            detailsList.basicsInfo.memberEmail
          }}</el-descriptions-item>
          <el-descriptions-item label="微信绑定：">{{
            detailsList.basicsInfo.memberWxBindTxt
          }}</el-descriptions-item>
          <el-descriptions-item class-name="flex" label="已创建子账号数量：">
            <div>
              {{ detailsList.basicsInfo.memberUsed }}
            </div>
            <el-link
              :underline="false"
              type="primary"
              @click="toPathAccount(detailsList.adminInfo?.encryptCompanyId)"
              >查看</el-link
            >
          </el-descriptions-item>
        </el-descriptions>

        <!--  联系方式-->
        <el-descriptions title="联系方式" :column="4" border>
          <el-descriptions-item label="联系人：">{{
            detailsList.contactInfo.name
          }}</el-descriptions-item>
          <el-descriptions-item label="所在部门：">{{
            detailsList.contactInfo.department
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话：">{{
            detailsList.contactInfo.mobile
          }}</el-descriptions-item>
          <el-descriptions-item label="固定电话："
            >{{ detailsList.contactInfo.telephone }}
          </el-descriptions-item>
          <el-descriptions-item label="传真：">{{
            detailsList.contactInfo.fax
          }}</el-descriptions-item>
          <el-descriptions-item label="联系邮箱：">{{
            detailsList.contactInfo.email
          }}</el-descriptions-item>
          <el-descriptions-item label="联系地址：">{{
            detailsList.contactInfo.address
          }}</el-descriptions-item>
        </el-descriptions>
        <!-- 二级院校 -->
        <el-descriptions title="二级院校" :column="4" border>
          <div v-for="(item, index) in detailsList.childCompanyInfo" :key="index">
            <el-descriptions-item label="院系名称：">{{ item.childName }}</el-descriptions-item>
            <el-descriptions-item label="联系人：">{{ item.childContact }}</el-descriptions-item>
            <el-descriptions-item label="固定电话：">{{
              item.childTelephone
            }}</el-descriptions-item>
            <el-descriptions-item label="传真：">{{ item.childFax }}</el-descriptions-item>
          </div>
        </el-descriptions>
        <!-- 资质信息 -->
        <el-descriptions title="资质信息" :column="2" border>
          <el-descriptions-item label="资质证件：">
            <el-button type="primary" link @click="previewLicense">预览</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="经办人身份证明：">
            <el-button type="primary" link @click="previewPerson">预览</el-button>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="所属业务员：" border>
          <el-descriptions-item :span="4"
            >{{ detailsList.adminInfo.salesman }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 会员信息 -->
        <el-descriptions title="会员信息" border :column="4">
          <el-descriptions-item label="会员类型："
            >{{ detailsList.packageInfo.packageName }}
          </el-descriptions-item>
          <el-descriptions-item label="人才库权限："
            >{{ detailsList.packageInfo.isResume }}
          </el-descriptions-item>
          <el-descriptions-item label="开通时间："
            >{{ detailsList.packageInfo.openTime }}
          </el-descriptions-item>
          <el-descriptions-item label="到期时间："
            >{{ detailsList.packageInfo.expireTime }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余职位发布次数："
            >{{ detailsList.packageInfo.jobReleaseNum }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余公告发布次数："
            >{{ detailsList.packageInfo.announcementReleaseNum }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余职位刷新次数："
            >{{ detailsList.packageInfo.jobRefreshNum }} </el-descriptions-item
          ><el-descriptions-item label="总/剩余公告刷新次数："
            >{{ detailsList.packageInfo.announcementRefreshNum }}
          </el-descriptions-item>
          <el-descriptions-item label="简历下载："
            >{{ detailsList.packageInfo.resumeDownloadReleaseNum }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余子账号数量："
            >{{ detailsList.packageInfo.accountRestNum }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余VIP授权数量（子账号）："
            >{{ detailsList.packageInfo.accountVipRestNum }}
          </el-descriptions-item>
          <el-descriptions-item label="总/剩余直聊点数"
            >{{ detailsList.packageInfo.chatNum }}
          </el-descriptions-item>
          <el-descriptions-item label="短信条数"
            >{{ detailsList.packageInfo.smsAmount }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
    <div class="mask">
      <!-- 外层的遮罩 -->
      <div class="mask-cover" v-if="logoShow" @click="closeByMask"></div>
      <!-- 内容区 -->
      <div class="mask-content" v-if="logoShow">
        <!-- 插槽，放置要插入到遮罩里的内容 -->
        <slot name="default">
          <img class="preImg" :src="detailsList.basicsInfo.logoUrl" alt="" />
        </slot>
      </div>
    </div>
    <div class="mask">
      <!-- 资质证件 -->
      <div class="mask-cover" v-if="licenseShow" @click="closeByMask"></div>
      <!-- 内容区 -->
      <div class="mask-content" v-if="licenseShow">
        <!-- 插槽，放置要插入到遮罩里的内容 -->
        <slot name="default">
          <img class="preImg" :src="detailsList.qualificationsInfo.licensePath" alt="" />
        </slot>
      </div>
    </div>
    <div class="mask">
      <!-- 资质证件 -->
      <div class="mask-cover" v-if="personShow" @click="closeByMask"></div>
      <!-- 内容区 -->
      <div class="mask-content" v-if="personShow">
        <!-- 插槽，放置要插入到遮罩里的内容 -->
        <slot name="default">
          <img class="preImg" :src="detailsList.qualificationsInfo.personInfoPath" alt="" />
        </slot>
      </div>
    </div>

    <ImagePreview hideOnClickModal ref="imagePreview" />
  </div>
</template>

<script lang="ts">
import { computed, onMounted, reactive, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { editAccountNature, getComanyDetails } from '/@/api/unitManage'

import ImagePreview from '/@/components/business/imagePreview.vue'

export default {
  name: 'companyDetails',
  components: { ImagePreview },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const detailsList = <any>ref({
      basicsInfo: <any>{
        styleAtlasList: []
      },

      adminInfo: {},
      contactInfo: {},
      childCompanyInfo: {},
      packageInfo: {},
      qualificationsInfo: {}
    })
    const state = reactive({
      form: {
        id: computed(() => route.query.id),
        accountNature: ''
      },
      accountShow: true,
      submitShow: false,
      accountNature: []
    })

    // 遮罩显示
    const logoShow = ref(false)
    const licenseShow = ref(false)
    const personShow = ref(false)

    const imagePreview = ref()
    onMounted(async () => {
      const res = await getComanyDetails(route.query)
      detailsList.value = { ...res }
      state.accountNature = res.accountNature
      state.form.accountNature = res.basicsInfo.accountNature
    })
    // logo预览方法
    const previewLogo = () => {
      logoShow.value = true
    }
    const previewLicense = () => {
      licenseShow.value = true
    }
    const previewPerson = () => {
      personShow.value = true
    }
    // 关闭预览
    const closeByMask = () => {
      logoShow.value = false
      licenseShow.value = false
      personShow.value = false
    }
    const showAccount = () => {
      state.accountShow = !state.accountShow
      state.submitShow = true
    }
    const submit = async () => {
      const { accountNatureTxt } = await editAccountNature(state.form)
      detailsList.value.basicsInfo.accountNatureTxt = accountNatureTxt
      state.submitShow = false
      state.accountShow = true
    }

    const toPathAccount = (id: any) => {
      router.push({
        path: '/company/account',
        query: { id }
      })
    }

    const handleImagePreview = (data) => {
      imagePreview.value.open(data)
    }

    return {
      ...toRefs(state),
      logoShow,
      licenseShow,
      personShow,
      detailsList,
      previewLogo,
      closeByMask,
      previewLicense,
      previewPerson,
      imagePreview,
      handleImagePreview,
      showAccount,
      submit,
      toPathAccount
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  // position: relative;
  :deep(.el-card) {
    --el-card-padding: 15px;
  }
  .title {
    height: 30px;
    background-color: #f0f0f0;
    font-size: 16px;
    font-weight: 700;
    padding-left: 20px;
  }
  .sub {
    margin-left: 30px;
  }
  .my-img {
    width: 100px;
    height: 100px;
    display: none;
  }

  .mask {
    position: relative;
    color: #2e2c2d;
    font-size: 16px;
  }
  //遮罩，设置背景层，z-index值要足够大确保能覆盖，高度 宽度设置满 做到全屏遮罩
  .mask-cover {
    background: rgba($color: #000000, $alpha: 0.5);
    position: fixed;
    z-index: 9999;
    // 设置top、left、宽高保证全屏遮罩
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }

  //内容层，z-index要大于遮罩层，确保内容在遮罩上显示
  .mask-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;

    img {
      width: 800px;
      height: 800px;
      object-fit: contain;
    }
  }
  .el-descriptions {
    margin-bottom: 20px;
  }
}
</style>
