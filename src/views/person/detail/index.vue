<template>
  <div class="resume">
    <div class="container flex" v-loading="loading">
      <div class="left" id="print-resume">
        <div class="px-20 py-10 top jc-between opacity-70 ai-center">
          <div class="flex-1">
            <div class="mb-5">最近应聘职位名称：{{ lastApplyJobName }}</div>
            <div>
              最近投递时间：{{ lastApplyJobTime }} &nbsp;&nbsp;&nbsp; 最近一次更新简历时间：{{
                lastUpdateResumeTime
              }}
            </div>
          </div>
          <div>
            <a class="point" @click="download">下载</a>
            <a class="ml-20" href="javascript:;" v-print="'#print-resume'">打印</a>
          </div>
        </div>
        <div class="flex px-20 py-10 personal">
          <div>
            <el-avatar shape="square" :size="80" fit="cover" :src="userInfo.avatar"></el-avatar>
            <div class="fs-12 ta-center">ID:{{ userInfo.resumeId }}</div>
          </div>
          <div class="flex-1 ml-20 column">
            <div class="jc-between">
              <div class="flex flex-1">
                <div class="name fs-16 fw-bold">
                  {{ userInfo.name }}
                  <span v-if="userInfo.identityText" class="identity-type">{{
                    userInfo.identityText
                  }}</span>
                </div>
                <!-- <span
                  v-for="(item, index) in userInfo.updateResumeInfo?.tag"
                  :key="index"
                  class="mx-10"
                  >{{ item }}</span
                > -->
              </div>
            </div>
            <div>
              <span>{{ userInfo.age }}岁</span>
              <span class="mx-8">{{ userInfo.identityExperience }}</span>
              <span class="mx-8">{{ userInfo.educationName }}</span>
              <span class="mx-8">{{ userInfo.schoolName }}</span>
              <span
                v-if="userInfo.fullMobile"
                class="mx-8 cursor-pointer color-primary"
                @click="showMobile = !showMobile"
                >{{ showMobile ? userInfo.fullMobile : '点击查看' }}</span
              >
              <span class="mx-8 cursor-pointer color-primary" @click="showEmail = !showEmail">{{
                showEmail ? userInfo.email : '点击查看'
              }}</span>
            </div>
            <div>
              <span v-if="isExist(userInfo.genderTxt)" class="mr-10">{{ userInfo.genderTxt }}</span>
              <span v-if="isExist(userInfo.marriageTxt)" class="mr-10">{{
                userInfo.marriageTxt
              }}</span>
              <span v-if="isExist(userInfo.politicalStatusName)" class="mr-10">{{
                userInfo.politicalStatusName
              }}</span>
              <span v-if="isExist(userInfo.nationTxt)" class="mr-10">{{ userInfo.nationTxt }}</span>
              <span v-if="isExist(userInfo.titleName)" class="mr-10">{{ userInfo.titleName }}</span>
              <span v-if="isExist(userInfo.householdRegisterText)" class="mr-10"
                >户籍/国籍：{{ userInfo.householdRegisterText }}</span
              >
              <span v-if="isExist(userInfo.residenceTxt)" class="mr-10"
                >现居住地：{{ userInfo.residenceTxt }}</span
              >
              <span v-if="isExist(userInfo.nativePlaceAreaTxt)" class="mr-10"
                >籍贯：{{ userInfo.nativePlaceAreaTxt }}</span
              >
            </div>
          </div>
        </div>
        <div class="py-20 pl-20 pr-10 info">
          <el-tabs v-model="activeName">
            <el-tab-pane label="在线简历" name="onlineResume">
              <div class="main-info">
                <div v-if="isExist(userInfo.advantage)" class="list">
                  <div class="arrow-title">
                    <span class="title">个 人 优 势</span>
                  </div>
                  <div class="intention advantage">{{ userInfo.advantage }}</div>
                </div>

                <div class="list intention-wrapper">
                  <div class="arrow-title">
                    <span class="title">求 职 意 向</span>
                  </div>

                  <el-row class="intention-title">
                    <el-col :span="5">意向职位</el-col>
                    <el-col :span="7">意向城市</el-col>
                    <el-col :span="4">期望月薪</el-col>
                    <el-col :span="5">工作性质</el-col>
                  </el-row>

                  <el-row
                    class="intention-list"
                    v-for="(item, index) in intentionList"
                    :key="index"
                  >
                    <el-col :span="5">{{ item.jobCategoryName }}</el-col>
                    <el-col :span="7">{{ item.areaName }}</el-col>
                    <el-col :span="4">{{ item.wageName }}</el-col>
                    <el-col :span="5">{{ item.natureName }}</el-col>
                  </el-row>

                  <el-row class="intention-status">
                    <el-col :span="10">求职状态：{{ userInfo.workStatusName }}</el-col>
                    <el-col :span="14">到岗时间：{{ userInfo.arriveDateTypeName }}</el-col>
                  </el-row>
                </div>

                <div class="list education-wrapper">
                  <div class="arrow-title">
                    <span class="title">教 育 经 历</span>
                  </div>
                  <el-row class="intention" v-for="(item, index) in educationList" :key="index">
                    <el-col :span="6">
                      {{ item.studyBeginDate }}-{{ item.studyEndDate }}
                      <span class="mark" v-if="item.isOverseasStudy == 1">海外</span>
                      <span class="mark" v-if="item.isProjectSchool == 1">985/211</span>
                    </el-col>
                    <el-col class="show-complete" :span="4"
                      >{{ item.school }}{{ `${item.isRecruitment == 1 ? '(统招)' : ''}` }}</el-col
                    >
                    <el-col class="show-complete" :span="3">{{ item.college }}</el-col>
                    <el-col class="show-complete" :span="4">{{ item.majorName }}</el-col>
                    <el-col :span="4">{{ item.educationName }}</el-col>
                    <el-col :span="3">{{ item.mentor ? '导师：' + item.mentor : '' }}</el-col>
                  </el-row>
                </div>

                <div v-if="isExist(researchDirection)" class="list research-wrapper">
                  <div class="arrow-title">
                    <span class="title">研 究 方 向</span>
                  </div>
                  <el-row class="intention">
                    <el-col>
                      <div class="description">{{ researchDirection }}</div>
                    </el-col>
                  </el-row>
                </div>

                <div v-if="isExist(workList)" class="list work-wrapper">
                  <div class="arrow-title">
                    <span class="title">工作/实习/研究经历</span>
                  </div>
                  <div class="intention" v-for="(item, index) in workList" :key="index">
                    <el-row>
                      <el-col :span="6"
                        >{{ item.jobBeginDate }}-{{ isCurrentDate(item.jobEndDate) }}</el-col
                      >
                      <el-col :span="8"
                        >{{ item.company }}
                        <span v-if="item.isOverseas == 1" class="mark">海外经历</span>
                        <span v-if="item.isPostdoc == 1" class="mark">博士后经历</span>
                        <span v-if="item.isPractice == 1" class="mark">实习</span>
                      </el-col>
                      <el-col :span="5">{{ item.jobName }}</el-col>
                      <el-col :span="5">{{ item.department }}</el-col>
                    </el-row>
                    <div class="description">{{ item.jobContent }}</div>
                  </div>
                </div>

                <div v-if="isExist(projectList)" class="list projec-wrapper">
                  <div class="arrow-title">
                    <span class="title">项 目 经 历</span>
                  </div>

                  <div class="intention" v-for="(item, index) in projectList" :key="index">
                    <el-row>
                      <el-col :span="5"
                        >{{ item.beginDate }}-{{ isCurrentDate(item.endDate) }}</el-col
                      >
                      <el-col :span="4">{{ item.company }}</el-col>
                      <el-col :span="4"
                        >{{ item.name }}
                        {{ `${item.isClose == 1 ? '(已结项)' : '(未结项)'}` }}
                      </el-col>
                      <el-col :span="4">{{ item.categoryName }}</el-col>
                      <el-col :span="4">{{ item.role }}</el-col>
                    </el-row>
                    <div class="description">项目描述：{{ item.description }}</div>
                  </div>
                </div>

                <div
                  v-if="isExist(pageList) || isExist(patentList) || isExist(bookList)"
                  class="list"
                >
                  <div class="arrow-title">
                    <span class="title">学 术 成 果</span>
                  </div>
                  <template v-if="isExist(pageList)">
                    <div class="small-title">学术论文</div>
                    <div class="intention" v-for="(item, index) in pageList" :key="index">
                      <el-row>
                        <el-col :span="4">{{ item.publishDate }}</el-col>
                        <el-col :span="4">{{ item.title }}</el-col>
                        <el-col :span="4">{{ item.serialNumber }}</el-col>
                        <el-col :span="4">{{ item.recordSituation }}</el-col>
                        <el-col :span="4">{{ item.positionText }}</el-col>
                        <el-col :span="4">{{ item.impactFactor }}</el-col>
                      </el-row>
                      <div class="description">论文描述：{{ item.description }}</div>
                    </div>
                  </template>

                  <template v-if="isExist(patentList)">
                    <div class="small-title">学术专利</div>
                    <div class="intention" v-for="(item, index) in patentList" :key="index">
                      <el-row>
                        <el-col :span="4">{{ item.authorizationDate }}</el-col>
                        <el-col :span="4">{{ item.name }}</el-col>
                        <el-col :span="4">{{ item.number }}</el-col>
                        <el-col :span="4">{{ item.positionText }}</el-col>
                        <el-col :span="4">{{ item.finishStatus }}</el-col>
                      </el-row>
                      <div class="description">专利描述：{{ item.description }}</div>
                    </div>
                  </template>

                  <template v-if="isExist(bookList)">
                    <div class="small-title">学术专著</div>
                    <el-row class="intention" v-for="(item, index) in bookList" :key="index">
                      <el-col :span="6">{{ item.publishDate }}</el-col>
                      <el-col :span="6">{{ item.name }}</el-col>
                      <el-col :span="6">{{ item.words }}</el-col>
                      <el-col :span="6">{{ item.publishAmount }}</el-col>
                    </el-row>
                  </template>
                </div>

                <div v-if="isExist(rewardList) || isExist(otherRewardList)" class="list">
                  <div class="arrow-title">
                    <span class="title">荣 誉 奖 励</span>
                  </div>

                  <template v-if="isExist(rewardList)">
                    <div class="small-title">学术奖励</div>
                    <el-row class="intention" v-for="(item, index) in rewardList" :key="index">
                      <el-col :span="6">{{ item.obtainDate }}</el-col>
                      <el-col :span="6">{{ item.name }}</el-col>
                      <el-col :span="6">{{ item.level }}</el-col>
                      <el-col :span="6">{{ item.role }}</el-col>
                    </el-row>
                  </template>

                  <template v-if="isExist(otherRewardList)">
                    <div class="small-title">其他奖励</div>
                    <el-row class="intention" v-for="(item, index) in otherRewardList" :key="index">
                      <el-col :span="6">{{ item.obtainDate }}</el-col>
                      <el-col :span="6">{{ item.name }}</el-col>
                      <el-col :span="6">{{ item.level }}</el-col>
                      <el-col :span="6">{{ item.role }}</el-col>
                    </el-row>
                  </template>
                </div>

                <div
                  v-if="isExist(certificateList) || isExist(skillList) || isExist(otherSkillList)"
                  class="list"
                >
                  <div class="arrow-title">
                    <span class="title">技 能 特 长</span>
                  </div>

                  <template v-if="isExist(certificateList)">
                    <div class="small-title">资质证书</div>
                    <el-row
                      class="intention"
                      v-for="(item, index) in certificateList"
                      :key="index"
                      :gutter="5"
                    >
                      <el-col :span="4">{{ item.obtainDate }}</el-col>
                      <el-col :span="8">{{ item.certificateName }}</el-col>
                      <el-col :span="12">{{ item.score }}</el-col>
                    </el-row>
                  </template>

                  <template v-if="isExist(skillList)">
                    <div class="small-title">技能/语言</div>
                    <el-row class="intention" v-for="(item, index) in skillList" :key="index">
                      <el-col :span="6">{{ item.skillName }}</el-col>
                      <el-col :span="6">{{ item.degreeTypeName }}</el-col>
                    </el-row>
                  </template>

                  <template v-if="isExist(otherSkillList)">
                    <div class="small-title">其他技能</div>
                    <div class="intention" v-for="(item, index) in otherSkillList" :key="index">
                      <el-row :gutter="5">
                        <el-col :span="5">{{ item.name }}</el-col>
                        <el-col :span="19">{{ item.degreeTypeName }}</el-col>
                      </el-row>
                      <div class="description">技能说明：{{ item.description }}</div>
                    </div>
                  </template>
                </div>

                <div v-if="isExist(addInfoList)" class="list">
                  <div class="arrow-title">
                    <span class="title">附 加 信 息</span>
                  </div>
                  <div class="intention" v-for="(item, index) in addInfoList" :key="index">
                    <el-row>
                      <el-col :span="24">
                        {{ item.themeName || item.themeIdName }}
                      </el-col>
                    </el-row>
                    <div class="description">主题描述：{{ item.content }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="附件简历" name="attachment">
              <el-table :data="attachmentList" style="width: 100%">
                <el-table-column prop="addTime" label="上传时间" width="180" />
                <el-table-column prop="fileName" label="简历名称" width="180" />
                <el-table-column
                  align="center"
                  header-align="center"
                  label="操作"
                  show-overflow-tooltip
                  ><template #default="{ row }">
                    <a :href="row.url">下载</a>
                    <a class="ml-15" @click="handlePreview(row)" href="javascript:;">预览</a>
                  </template></el-table-column
                >
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="单位下载记录" name="companyDown">
              <CompanyDownload :id="memberId" />
            </el-tab-pane>
            <el-tab-pane label="内部下载记录" name="insideDown">
              <InsideDownload :id="memberId" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="right px-26 py-18">
        <div class="setting">
          <div class="setting-title">求职设置</div>
          <p class="status">
            简历隐藏状态
            <el-switch
              :before-change="handelChangeResumeShowStatus"
              v-model="resumeSettingInfo.isHideResume"
              active-value="1"
              inactive-value="2"
              :disabled="!personList.includes('editResumeOpen')"
            />
          </p>
          <p class="status">
            简历代投状态<el-switch
              :before-change="handelChangeResumeProxyDeliverStatus"
              v-model="resumeSettingInfo.isProxyDeliver"
              active-value="1"
              inactive-value="2"
              :disabled="!personList.includes('editResumeOpen')"
            />
          </p>
          <p class="status">
            匿名投递
            <el-switch
              :before-change="handelChangeResumeAnonymousStatus"
              v-model="resumeSettingInfo.isAnonymous"
              active-value="1"
              inactive-value="2"
              :disabled="!personList.includes('editResumeOpen')"
            />
          </p>
          <p>屏蔽单位 已屏蔽{{ resumeSettingInfo.shieldCompanyAmount }}家</p>
        </div>
        <div class="schedu">
          <div class="setting-title">求职日程</div>
          <div class="calendar">
            <el-calendar ref="calendar" v-model="currentDate">
              <template #header="{ date }">
                <span class="date">{{ date }}</span>
                <el-button-group>
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="selectDate('prev-month')"
                    class="prev-month"
                  >
                    <i class="el-icon-arrow-left"></i
                  ></el-button>
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="selectDate('next-month')"
                    class="next-month"
                    ><i class="el-icon-arrow-right"></i
                  ></el-button>
                </el-button-group>
              </template>
              <template #dateCell="{ data }">
                <div :class="`date ${data.isSelected ? 'is-selected' : ''}`">
                  {{ handleDateToArray(data.day) }}
                  {{ data.day.split('-').slice(2).join('') }}
                  <span :class="`${hasSchedule(data.day)}`"></span>
                </div>
              </template>
            </el-calendar>
            <!-- 日历详情 -->
            <div
              class="schedule-details"
              v-for="(item, index) in currentScheduleArray"
              :key="index"
            >
              <span class="point">•</span>
              <span class="time">
                <div>{{ item?.timeRange && item?.timeRange.slice(0, 7) }}</div>
                <div class="end">{{ item?.timeRange && item?.timeRange.slice(10, 17) }}</div>
              </span>
              <span class="interview">{{ item?.title }}</span>
            </div>
          </div>
        </div>
        <div class="subscribe" v-if="subscribeInfo.subscribeType">
          <div class="setting-title">职位订阅</div>
          <div class="calendar">
            <el-row class="subscribe-item">
              <el-row>意向职位：</el-row>
              <el-row class="subscribe-item-content">{{ subscribeInfo.jobCategoryText }}</el-row>
            </el-row>
            <el-row class="subscribe-item">
              <el-row>意向城市：</el-row>
              <el-row class="subscribe-item-content">{{ subscribeInfo.areaText }}</el-row>
            </el-row>
            <el-row class="subscribe-item">
              <el-row>学历要求：</el-row>
              <el-row class="subscribe-item-content">{{ subscribeInfo.educationText }}</el-row>
            </el-row>
            <el-row class="subscribe-item">
              <el-row>推送渠道：</el-row>
              <el-row class="subscribe-item-content">{{ subscribeInfo.subscribeType }}</el-row>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <el-dialog top="8vh" v-model="previewOption.visible" destroy-on-close="true" title="预览">
      <ResumeAttachmentPreview
        v-if="previewOption.visible"
        v-model:token="previewOption.token"
        v-model:resumeId="previewOption.resumeId"
        @close="previewOption.visible = false"
      ></ResumeAttachmentPreview>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, ref, watch, toRefs, onMounted, defineComponent, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import {
  changeResumeAnonymousStatus,
  changeResumeShowStatus,
  changeResumeProxyDeliverStatus,
  getResumeInfo,
  getPersonResumeAttachmentList
} from '/@/api/person.ts'
import print from 'vue3-print-nb'
import { AnyARecord } from 'dns'
import CompanyDownload from '../components/companyDownload.vue'
import InsideDownload from '../components/insideDownload.vue'
import ResumeAttachmentPreview from '/@/components/resumeAttachmentPreview/index.vue'

import { getScheduleList } from '/@/api/person'
import 'dayjs/locale/zh-cn.js'
import { formatDate } from '/@/utils/formatTime'
import { useStore } from '/@/store'

export default defineComponent({
  components: { CompanyDownload, InsideDownload, ResumeAttachmentPreview },
  name: 'resumeDetail',
  directives: {
    print
  },
  setup() {
    const dialogInvite = ref()
    const state = reactive({
      activeName: 'onlineResume',
      memberId: '',
      loading: false,
      log: [],
      userInfo: {
        jobName: '',
        applyTime: '',
        refreshTime: '',
        avatar: '',
        name: '',
        age: '',
        workExperience: '',
        educationName: '',
        schoolName: '',
        fullMobile: '',
        email: '',
        workStatusName: '',
        arriveDateTypeName: '',
        resumeId: ''
      } as any,
      intentionList: [],
      educationList: [] as any,
      pageList: [],
      patentList: [],
      bookList: [],
      rewardList: [],
      otherRewardList: [],
      subscribeInfo: {
        areaText: '',
        educationText: '',
        jobCategoryText: '',
        subscribeType: ''
      },
      resumeSettingInfo: {
        shieldCompanyAmount: 0,
        isHideResume: 0,
        isAnonymous: 0,
        isProxyDeliver: 0
      },
      workList: [] as any,
      // 技能特长 -> 资质证书
      certificateList: <any>[],
      // 技能特长 -> 技能/语言
      skillList: <any>[],
      // 技能特长 -> 其他技能
      otherSkillList: <any>[],
      projectList: [],
      showMobile: false,
      showEmail: false,
      researchDirection: '',
      compantTableData: [],
      lastApplyJobTime: '',
      lastUpdateResumeTime: '',
      lastApplyJobName: '',
      dateArray: <any>[],
      currentDate: new Date(),
      // 当前日历面板日程
      scheduleArray: <any>[],
      // 选择当日的所有日程
      currentScheduleArray: [],
      scheduleFormData: {
        dateStart: '',
        dateEnd: ''
      },

      personList: [],
      attachmentList: [],
      addInfoList: [],

      previewOption: {
        visible: false,
        token: '',
        resumeId: ''
      }
    })

    const route = useRoute()
    const store = useStore()
    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )
    state.personList = requestOldRoutesAction.value.personDetail

    const calendar = ref()

    // 获取信息
    const getInfo = async () => {
      await getResumeInfo({ memberId: state.memberId }).then((resp: any) => {
        state.userInfo = resp.userInfo
        state.lastUpdateResumeTime = resp.lastUpdateResumeTime
        state.lastApplyJobName = resp.lastApplyJobName
        state.lastApplyJobTime = resp.lastApplyJobTime
        state.intentionList = resp.intentionList
        state.educationList = resp.educationList
        state.workList = resp.workList
        state.certificateList = resp.certificateList
        state.skillList = resp.skillList
        state.otherSkillList = resp.otherSkillList
        state.resumeSettingInfo = resp.resumeSettingInfo
        state.subscribeInfo = resp.subscribeInfo
        state.researchDirection = resp.researchDirection
        state.projectList = resp.projectList
        state.pageList = resp.pageList
        state.patentList = resp.patentList
        state.bookList = resp.bookList
        state.rewardList = resp.rewardList
        state.otherRewardList = resp.otherRewardList
        state.addInfoList = resp.addInfoList
      })
    }

    // 获取日程信息
    const getSchedule = async () => {
      state.scheduleArray = await getScheduleList({
        memberId: state.memberId,
        ...state.scheduleFormData
      })
    }

    nextTick(() => {
      const dateStart = state.dateArray[0]
      const { length } = state.dateArray
      const dateEnd = state.dateArray[length - 1]
      state.scheduleFormData.dateStart = dateStart
      state.scheduleFormData.dateEnd = dateEnd
      getSchedule()
    })

    const handleDateToArray = (day: string) => {
      state.dateArray.push(day)
    }

    const selectDate = (val: string) => {
      state.dateArray = []
      calendar.value.selectDate(val)
      nextTick(() => {
        const dateStart = state.dateArray[0]
        const { length } = state.dateArray
        const dateEnd = state.dateArray[length - 1]
        state.scheduleFormData.dateStart = dateStart
        state.scheduleFormData.dateEnd = dateEnd
        getSchedule()
      })
    }

    const handleCurrentSchedule = (date: any) => {
      const day = formatDate(new Date(date), 'YYYY-mm-dd')
      const scheduleArray = state.scheduleArray.filter((item: any) => {
        return item.date === day
      })
      if (!scheduleArray.length) {
        state.currentScheduleArray = []
        return
      }
      const { schedule } = <any>scheduleArray[0]
      state.currentScheduleArray = schedule || []
    }

    watch(
      () => state.currentDate,
      (date) => {
        handleCurrentSchedule(date)
      }
    )

    // 获取附件简历列表
    const getAttachment = () => {
      getPersonResumeAttachmentList({ memberId: state.memberId }).then((resp: any) => {
        state.attachmentList = resp.map((item: any) => {
          return {
            ...item,
            url: `/person/resume-attachment-download?resumeId=${item.resumeId}&token=${item.token}`
          }
        })
      })
    }

    const download = () => {
      window.location.href = `/resume/download?resumeId=${state.userInfo.resumeId}`
    }

    onMounted(async () => {
      state.memberId = String(route.params.id)
      state.activeName = <string>route.query.type ? <string>route.query.type : 'onlineResume'
      state.loading = true
      await getInfo()
      await getAttachment()
      state.loading = false
    })

    const handleInvite = (data: any) => {
      dialogInvite.value.openDialog(data)
    }

    const handelChangeResumeShowStatus = async () => {
      // 等待成功后
      await changeResumeShowStatus(state.memberId)
      return Promise.resolve(true)
    }
    const handelChangeResumeAnonymousStatus = () => {
      changeResumeAnonymousStatus(state.memberId)
      return Promise.resolve(true)
    }
    const handelChangeResumeProxyDeliverStatus = () => {
      changeResumeProxyDeliverStatus(state.memberId)
      return Promise.resolve(true)
    }

    const hasSchedule = (date: string) => {
      const current = state.scheduleArray.filter((item: any) => {
        return item.date === date
      })
      if (!current.length) return ''
      const isPassed = new Date(date).getTime() + 24 * 60 * 60 * 1000 < new Date().getTime()
      return isPassed ? 'passed' : 'future'
    }

    const isExist = (val: string | any[]): boolean => {
      if (Array.isArray(val)) {
        return val.length > 0
      }

      return val !== ''
    }

    const isCurrentDate = (date: string) => {
      return /000-00/.test(date) ? '至今' : date
    }

    const handlePreview = ({ resumeId, token }) => {
      state.previewOption.visible = true
      state.previewOption.resumeId = resumeId
      state.previewOption.token = token
    }

    return {
      handleInvite,
      selectDate,
      calendar,
      handelChangeResumeShowStatus,
      handelChangeResumeAnonymousStatus,
      handelChangeResumeProxyDeliverStatus,
      download,
      hasSchedule,
      handleDateToArray,
      isExist,
      isCurrentDate,
      handlePreview,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
@mixin mark {
  .mark {
    background-color: var(--color-primary);
    color: #fff;
    border-radius: 4px;
    padding: 0 6px;
    font-size: 13px;

    & + .mark {
      margin-left: 5px;
    }
  }
}

.subscribe-item {
  margin-top: 5px;
  margin-bottom: 10px;
}

.subscribe-item-content {
  width: 210px;
}

.resume {
  background-color: #f2f2f2;
  min-height: 100vh;
  width: 100%;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
  .container {
    min-width: 1200px;
    max-width: 1400px;
    margin: auto;
    background-color: #fff;
    .left {
      width: 75%;
      .el-col-6:not(.show-complete) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .el-col-6.show-complete {
        word-break: break-all;
      }
      .el-col-8 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .column {
        justify-content: space-around;
      }
      .top {
        border-bottom: 1px solid #f2f2f2;
      }
      .info {
        border-top: 10px solid #f2f2f2;
        :deep(#tab-companyDown) {
          margin-left: 550px;
        }
        .small-title {
          line-height: 20px;
          margin-top: 15px;
          padding: 10px;
          background: #fafafc;
          color: var(--color-primary);
        }
        .arrow-title {
          color: #fff;
          border-bottom: 1px solid var(--color-primary);
          margin: 20px 0;
          .title {
            display: inline-block;
            padding: 0px 20px;
            line-height: 24px;
            position: relative;
            background-color: var(--color-primary);
            &::before {
              content: '';
              display: block;
              border-width: 12px;
              border-style: solid;
              border-color: transparent transparent transparent var(--color-primary);
              position: absolute;
              left: 100%;
              top: 0;
            }
          }
        }
        .intention {
          padding: 15px;
          color: #666;
          .description {
            white-space: pre-wrap;
            margin-top: 10px;
          }
        }
        .advantage {
          word-break: break-all;
        }
      }
      .attachment-list {
        padding: 15px 10px;
        border-bottom: 1px solid #f2f2f2;
        a {
          color: #555;
          font-size: 15px;
          &:hover {
            color: var(--color-primary);
          }
        }
      }

      .intention-wrapper {
        color: #666;

        .intention-title {
          color: #555;
          padding: 5px 15px;
          border-radius: 2px;
          background-color: #f6f6f6;
        }

        .intention-list {
          padding: 15px;
          border-bottom: 1px solid #f2f2f2;
        }

        .intention-status {
          padding: 15px 15px 0;
        }
      }

      .education-wrapper {
        .el-col {
          margin: 0px !important;

          @include mark;
        }
      }

      .research-wrapper {
        pre {
          white-space: pre-line;
        }
      }

      .work-wrapper {
        @include mark;
      }
    }
    .right {
      width: 25%;
      border-left: 10px solid #f2f2f2;
      .setting-title {
        padding: 10px 0;
        margin: 10px 0;
        font-size: 16px;
        border-bottom: 1px solid var(--color-primary);
      }
      .status {
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
      }
      .el-calendar {
        .el-calendar__header {
          align-items: center;
          border-bottom: none;
          padding-bottom: 0;
          .date {
            font-size: 16px;
            font-weight: bold;
            opacity: 0.8;
          }
        }
        .el-calendar__body {
          padding: 12px 20px;
          font-size: 14px;
        }
        :deep() {
          .el-calendar-table {
            border-bottom: 1px solid rgba(102, 102, 102, 0.06);
            .el-calendar-table__row {
              .el-calendar-day {
                width: 30px;
                height: auto;
                border-radius: 50%;
                padding: 6px 0;
                text-align: center;
                .date {
                  position: relative;

                  span {
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    position: absolute;
                    bottom: -3px;
                    left: 12px;
                    &.future {
                      background: #ec634d;
                    }
                    &.passed {
                      background: #bbb;
                    }
                  }
                }
              }
              .is-today {
                color: var(--color-primary);
              }
              td {
                border-color: transparent;
                background-color: transparent;
              }
              .is-selected {
                .el-calendar-day {
                  background-color: var(--color-primary);
                  color: #fff;
                }
              }
            }
          }
        }
      }
      .schedule-details {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0 30px;
        font-size: 14px;
        .point {
          color: var(--color-primary);
          font-size: 20px;
        }
        .time {
          margin: 0 10px;
          font-size: 14px;
          div {
            margin: 5px 0;
          }
          .end {
            opacity: 0.4;
          }
        }
        .interview {
          font-weight: bold;
          padding: 0 10px;
          border-left: 1px solid rgba(102, 102, 102, 0.4);
        }
      }
      .steps {
        border-left: 2px dashed #f2f2f2;
        padding-left: 15px;
        .list {
          div {
            padding: 3px 0;
          }
          .time {
            position: relative;
            line-height: 1;
            color: #666;
            &::after {
              position: absolute;
              width: 6px;
              height: 6px;
              border-radius: 50%;
              line-height: 1;
              content: '';
              display: block;
              left: -21px;
              background-color: #fff;
              border: 2px solid var(--color-primary);
            }
          }
          .description {
            padding-bottom: 15px;
          }
        }
      }
    }

    .identity-type {
      font-size: 14px;
      padding: 2px 7px;
      border: 1px solid var(--color-primary);
      color: var(--color-primary);
      border-radius: 4px;
      margin-left: 5px;
    }
  }
}
</style>
