<template>
  <div class="box">
    <el-tabs type="card" v-model="active" @tab-change="handleTabChange">
      <el-tab-pane label="站内投递" name="1">
        <el-form
          ref="onSiteApply"
          :model="onSiteApplyData"
          class="mt-10"
          size="small"
          :inline="true"
        >
          <el-form-item prop="keyword">
            <el-input
              v-model="onSiteApplyData.keyword"
              placeholder="请输入搜索内容"
              filterable
              clearable
            >
              <template #prepend>
                <el-select
                  v-model="onSiteApplyData.keywordType"
                  placeholder="类型"
                  style="width: 100px"
                >
                  <el-option
                    v-for="item in filterSelectOption"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="startApplyTime">
            <DatePickerRange
              v-model:start="onSiteApplyData.startApplyTime"
              v-model:end="onSiteApplyData.endApplyTime"
              size="small"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="onSiteApplyData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item prop="platform">
            <JobApplyPlatform
              v-model="onSiteApplyData.platform"
              placeholder="选择投递端口"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="searchOnSiteApplyList">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="onSiteJobList" border size="small" v-loading="loading">
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位信息"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('job', row.jobId)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.jobName }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="announcementName"
            align="center"
            header-align="center"
            label="公告信息"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="companyName"
            align="center"
            header-align="center"
            label="单位信息"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('company', row.companyId)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.companyName }}</el-link
              >
            </template></el-table-column
          >
          <el-table-column
            prop="resumeAttachmentName"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('attachment', id)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.resumeAttachmentName }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deliveryWayTxt"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="platformText"
            align="center"
            header-align="center"
            label="投递端口"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="applyStatusTxt"
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="!row.interviewInfo">{{ row.applyStatusTxt }}</span>
              <el-popover v-else placement="left" :width="420" trigger="hover">
                <template #reference>
                  <span class="color-primary cursor-pointer">{{ row.applyStatusTxt }}</span>
                </template>
                <div class="px-30">
                  <div class="py-8 fw-bold fs-15">面试信息</div>
                  <div class="py-8">面试职位：{{ row.interviewInfo.jobName }}</div>
                  <div class="py-8">面试时间：{{ row.interviewInfo.interviewTime }}</div>
                  <div class="py-8">面试地址：{{ row.interviewInfo.address }}</div>
                  <div class="py-8">联 系 人：{{ row.interviewInfo.contact }}</div>
                  <div class="py-8">联系电话：{{ row.interviewInfo.telephone }}</div>
                  <div v-if="row.interviewInfo.content" class="py-8">
                    备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：{{ row.interviewInfo.content }}
                  </div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="onSiteJobPaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="站外投递" name="2">
        <el-form
          ref="offSiteApply"
          :model="offSiteApplyData"
          class="mt-10"
          size="small"
          :inline="true"
        >
          <el-form-item prop="keyword">
            <el-input
              v-model="offSiteApplyData.keyword"
              placeholder="请输入搜索内容"
              filterable
              clearable
            >
              <template #prepend>
                <el-select
                  v-model="offSiteApplyData.keywordType"
                  placeholder="选择"
                  style="width: 100px"
                >
                  <el-option
                    v-for="item in filterSelectOption"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="startApplyTime">
            <DatePickerRange
              v-model:start="offSiteApplyData.startApplyTime"
              v-model:end="offSiteApplyData.endApplyTime"
              size="small"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="offSiteApplyData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="2"
            />
          </el-form-item>
          <el-form-item prop="platform">
            <JobApplyPlatform v-model="offSiteApplyData.platform" placeholder="选择投递端口" />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="searchOffSiteApplyList">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="offSiteJobList" border size="small" v-loading="loading">
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位信息"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('job', row.jobId)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.jobName }}</el-link
              >
            </template></el-table-column
          >
          <el-table-column
            prop="announcementName"
            align="center"
            header-align="center"
            label="公告信息"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="companyName"
            align="center"
            header-align="center"
            label="单位信息"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('company', row.companyId)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.companyName }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="open('attachment', id)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.resumeAttachmentName }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="applyDate"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deliveryWayTxt"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="platformText"
            align="center"
            header-align="center"
            label="投递端口"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="sourceText"
            align="center"
            header-align="center"
            label="职位来源"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="applyStatusTxt"
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
          ></el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="offSitePaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="面试邀约" name="3">
        <el-form ref="interview" :model="interviewData" class="mt-10" size="small" :inline="true">
          <el-form-item prop="keyword">
            <el-input
              v-model="interviewData.keyword"
              placeholder="请输入搜索内容"
              filterable
              clearable
            >
              <template #prepend>
                <el-select
                  v-model="interviewData.keywordType"
                  placeholder="选择"
                  style="width: 100px"
                >
                  <el-option
                    v-for="item in filterSelectOption"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="startInterviewTime">
            <DatePickerRange
              v-model:start="interviewData.invitationTimeStart"
              v-model:end="interviewData.invitationTimeEnd"
              size="small"
            />
          </el-form-item>
          <el-form-item prop="interviewStatus">
            <el-select
              v-model="interviewData.interviewStatus"
              placeholder="面试进度"
              filterable
              clearable
            >
              <el-option value="1" label="未开始"></el-option>
              <el-option value="2" label="已结束"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="searchInterviewList">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="interviewList" border size="small" v-loading="loading">
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位信息"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="announcementName"
            align="center"
            header-align="center"
            label="公告信息"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="companyName"
            align="center"
            header-align="center"
            label="单位信息"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewTime"
            align="center"
            header-align="center"
            label="面试时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewStatusTxt"
            align="center"
            header-align="center"
            label="面试进度"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="面试详情"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleInterviewDetail(row)">
                查看</el-button
              >
            </template>
          </el-table-column>

          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="interviewPaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
    </el-tabs>
    <InterviewInfo
      v-model="InterviewInfoDialogVisible"
      :details="InterviewInfoDetails"
    ></InterviewInfo>

    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, toRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import ResumeSource from '/@select/resumeSource.vue'
import Pagination from '/@/components/base/paging.vue'
import { getOffSiteApplyList, getOnSiteApplyList } from '/@/api/person'
import { getInvitationList } from '/@/api/job'
import InterviewInfo from '/@/components/base/interviewInfo.vue'
import { checkInterviewDetails } from '/@/api/unitManage'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'
import JobApplyPlatform from '/@select/jobApplyPlatform.vue'

export default {
  name: 'personJobApplyDetail',
  components: {
    DatePickerRange,
    ResumeSource,
    Pagination,
    InterviewInfo,
    JobDetailDialog,
    JobApplyPlatform
  },
  props: {
    data: {}
  },
  emits: ['confirm'],
  setup() {
    const jobDetailDialog = ref()

    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      loading: false,
      active: '1',
      id: '',
      filterSelectOption: [
        { k: '1', v: '职位ID' },
        { k: '2', v: '职位名称' },
        { k: '3', v: '公告ID' },
        { k: '4', v: '公告名称' },
        { k: '5', v: '单位ID' },
        { k: '6', v: '单位名称' }
      ],
      onSiteApplyData: {
        memberId: '',
        keywordType: '1',
        keyword: '',
        startApplyTime: '',
        endApplyTime: '',
        deliveryWay: '',
        platform: '',
        page: 1,
        limit: 20,
        resumeAttachmentId: '' // 附件简历id
      },
      offSiteApplyData: {
        memberId: '',
        keywordType: '1',
        keyword: '',
        startApplyTime: '',
        endApplyTime: '',
        deliveryWay: '', // 投递方式
        platform: '', // 投递端口
        page: 1,
        limit: 20
      },
      interviewData: {
        resumeMemberId: '',
        keywordType: '1',
        keyword: '',
        invitationTimeStart: '',
        invitationTimeEnd: '',
        interviewStatus: '', // 面试进度
        page: 1,
        limit: 20
      },
      pagination: {
        total: 0
      },
      InterviewInfoDialogVisible: false,
      InterviewInfoDetails: {},
      // 站内投递列表
      onSiteJobList: [],
      offSiteJobList: [],
      interviewList: []
    })

    const searchOnSiteApplyList = () => {
      state.onSiteApplyData.memberId = state.id
      state.loading = true
      const data = toRaw(state.onSiteApplyData)
      getOnSiteApplyList(data).then((res) => {
        state.onSiteJobList = res.list
        state.pagination.total = res.page.count
        state.loading = false
      })
    }

    const searchOffSiteApplyList = () => {
      state.offSiteApplyData.memberId = state.id
      state.loading = true
      const data = toRaw(state.offSiteApplyData)
      getOffSiteApplyList(data).then((res) => {
        state.offSiteJobList = res.list
        state.pagination.total = res.page.count
        state.loading = false
      })
    }

    const searchInterviewList = () => {
      state.interviewData.resumeMemberId = state.id
      state.loading = true
      const data = toRaw(state.interviewData)
      getInvitationList(data).then((res) => {
        state.interviewList = res.list
        state.pagination.total = res.page.total
        state.loading = false
      })
    }

    const handleTabChange = () => {
      switch (state.active) {
        case '1':
          searchOnSiteApplyList()
          break
        case '2':
          searchOffSiteApplyList()
          break
        case '3':
          searchInterviewList()
          break
        default:
          break
      }
    }

    onMounted(() => {
      const { id } = route.params
      const { tag } = route.query
      if (!id) {
        router.back()
      }
      if (tag) {
        switch (tag) {
          case '1':
            state.active = '1'
            break
          case '2':
            state.active = '2'
            break
          case '3':
            state.active = '3'
            break
          default:
            state.active = '1'
            break
        }
      }
      if (id) {
        state.id = String(id)
        handleTabChange()
      }
    })

    const onSiteApply = ref()
    const offSiteApply = ref()
    const interview = ref()
    const handleClose = () => {
      onSiteApply.value.resetFields()
      offSiteApply.value.resetFields()
      interview.value.resetFields()
    }

    const onSiteJobPaginationChange = (data: any) => {
      state.onSiteApplyData.limit = data.limit
      state.onSiteApplyData.page = data.page
      searchOnSiteApplyList()
    }
    const offSitePaginationChange = (data: any) => {
      state.offSiteApplyData.limit = data.limit
      state.offSiteApplyData.page = data.page
      searchOffSiteApplyList()
    }
    const interviewPaginationChange = (data: any) => {
      state.interviewData.limit = data.limit
      state.interviewData.page = data.page
      searchInterviewList()
    }

    const handleInterviewDetail = (row: any) => {
      checkInterviewDetails({ interviewId: row.interviewId }).then((res) => {
        state.InterviewInfoDetails = res
        state.InterviewInfoDialogVisible = true
      })
    }
    const open = (type: string, id) => {
      switch (type) {
        case 'job':
          jobDetailDialog.value.open(id)
          break
        case 'company':
          router.push({
            path: '/company/details',
            query: { id }
          })
          break
        case 'attachment':
          // eslint-disable-next-line no-case-declarations
          const routeUrl = <any>router.resolve({
            path: `/person/detail/${id}`,
            query: { type: 'attachment' }
          })
          window.open(routeUrl.href, '_blank')
          break
        default:
          break
      }
    }

    return {
      jobDetailDialog,
      onSiteApply,
      offSiteApply,
      interview,
      searchOnSiteApplyList,
      searchOffSiteApplyList,
      searchInterviewList,
      handleClose,
      handleTabChange,
      onSiteJobPaginationChange,
      offSitePaginationChange,
      interviewPaginationChange,
      handleInterviewDetail,
      open,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.invite-amount {
  font-size: 10px;
  color: #02a7f0;
  background-color: #f2f2f2;
  align-self: center;
  padding: 1px 8px;
  border-radius: 2px;
  margin-left: 10px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
  }
}
</style>
