<template>
  <div class="talent-dialog">
    <el-dialog v-model="visible" @close="resetRef">
      <el-tabs v-model="tabName" type="border-card" @tab-click="fetchData">
        <el-tab-pane name="collect" label="人才库收藏">
          <el-form :model="collectData" ref="collectRef">
            <el-row>
              <el-col :span="11">
                <el-form-item label="单位检索" prop="companyKeyword">
                  <el-input
                    v-model="collectData.companyKeyword"
                    placeholder="请填写单位名称或ID"
                    clearable
                    @keyup.enter="searchColllectList"
                  >
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11" :offset="2">
                <el-form-item label="收藏时间" prop="duration">
                  <DatePickerRange v-model="collectData.duration" @change="searchColllectList" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <el-tab-pane name="download" label="人才库下载">
          <el-form :model="downloadData" ref="downloadRef">
            <el-row>
              <el-col :span="11">
                <el-form-item label="单位检索" prop="companyKeyword">
                  <el-input
                    v-model="downloadData.companyKeyword"
                    placeholder="请填写单位名称或ID"
                    clearable
                    @keyup.enter="searchDownloadList"
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11" :offset="2">
                <el-form-item label="下载时间" prop="duration">
                  <DatePickerRange v-model="downloadData.duration" @change="searchDownloadList" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <el-table :data="getList" border v-loading="loading">
          <el-table-column prop="name" label="单位信息" align="center"> </el-table-column>
          <el-table-column prop="addTime" label="收藏时间" align="center"></el-table-column>
        </el-table>

        <Pagination
          v-if="pagination.count > 0"
          :total="pagination.count"
          :page="getData.currentPage"
          :page-size="pagination.limit"
          :layout="'total, prev, pager, next, jumper'"
          @change="currentChange"
        />
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, computed } from 'vue'
import { getResumeLibraryCollectList, getResumeLibraryDownloadList } from '/@/api/person'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'

export default defineComponent({
  name: 'TalentDialog',

  components: { DatePickerRange, Pagination },

  setup() {
    const collectRef = ref()
    const downloadRef = ref()

    const state = reactive({
      visible: false,
      tabName: 'collect',

      loading: true,

      resumeId: 0,

      collectData: {
        companyKeyword: '',
        duration: [],
        currentPage: 1
      },

      downloadData: {
        companyKeyword: '',
        duration: [],
        currentPage: 1
      },

      collectList: [],
      downloadList: [],

      pagination: {
        count: 0,
        limit: 0
      },

      isCollect: computed(() => state.tabName === 'collect'),
      getData: computed(() => (state.isCollect ? state.collectData : state.downloadData)),
      getList: computed(() => (state.isCollect ? state.collectList : state.downloadList))
    })

    const searchColllectList = async () => {
      state.loading = true

      const { companyKeyword, duration, currentPage } = state.collectData
      const [startTime, endTime] = duration || []

      const data = {
        resumeId: state.resumeId,
        page: currentPage,
        companyKeyword,
        startTime,
        endTime
      }

      const {
        list,
        page: { limit, count }
      } = await getResumeLibraryCollectList(data)

      state.collectList = list
      state.pagination = { limit, count }

      state.loading = false
    }

    const searchDownloadList = async () => {
      state.loading = true

      const { companyKeyword, duration, currentPage } = state.downloadData
      const [startTime, endTime] = duration || []

      const data = {
        resumeId: state.resumeId,
        page: currentPage,
        companyKeyword,
        startTime,
        endTime
      }

      const {
        list,
        page: { limit, count }
      } = await getResumeLibraryDownloadList(data)

      state.downloadList = list
      state.pagination = { limit, count }

      state.loading = false
    }

    const fetchData = () => {
      if (state.isCollect) {
        searchColllectList()
      } else {
        searchDownloadList()
      }
    }

    const currentChange = (data: any) => {
      const { page } = data

      if (state.isCollect) {
        state.collectData.currentPage = page
        searchColllectList()
      } else {
        state.downloadData.currentPage = page
        searchDownloadList()
      }
    }

    const show = (id: number, type: 'collect' | 'download') => {
      state.visible = true
      state.tabName = type
      state.resumeId = id

      fetchData()
    }

    const resetRef = () => {
      collectRef.value.resetFields()
      downloadRef.value.resetFields()

      state.collectData.currentPage = 1
      state.downloadData.currentPage = 1
    }

    return {
      collectRef,
      downloadRef,
      searchColllectList,
      searchDownloadList,
      fetchData,
      show,
      currentChange,
      resetRef,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.talent-dialog {
  :deep(.el-dialog__body) {
    margin-top: 20px;
  }
}

.el-table {
  margin-bottom: 18px;
}

.jc-end {
  justify-content: center;
}
</style>
