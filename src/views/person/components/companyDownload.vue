<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="6">
        <el-input v-model="form.companyKeyword" placeholder="请输入单位编号/名称"></el-input>
      </el-col>
      <el-col :span="10">
        <DatePickerRange
          v-model:start="form.startDownloadTime"
          v-model:end="form.endDownloadTime"
        />
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="getList" class="search">搜索</el-button>
      </el-col>
      <el-col :span="4">共计下载{{ page.total }}次</el-col>
    </el-row>
    <el-table :data="tableData" style="width: 100%" class="table">
      <el-table-column prop="id" label="编号" />
      <el-table-column prop="companyName" label="单位名称" />
      <el-table-column prop="companyId" label="单位编号" />
      <el-table-column prop="downloadTime" label="下载时间" />
      <el-table-column prop="" label="下载类型">
        <template #default="{ row }">
          {{ row.type == 'resume' ? '在线简历' : '附件简历' }}
        </template>
      </el-table-column>
      <el-table-column prop="" label="附件简历名称">
        <template #default="{ row }">
          {{ row.type == 'resumeAttachment' ? row.resumeAttachmentName : '' }}
        </template>
      </el-table-column>
    </el-table>
    <Paging v-if="tableData.length" :total="page.total" @change="handleChange" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import { getCompanyDownloadList } from '/@/api/person'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

export default defineComponent({
  components: { DatePickerRange, Paging },
  name: 'companyDownload',

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  setup(prop) {
    const state = reactive({
      form: {
        companyKeyword: '',
        startDownloadTime: '',
        endDownloadTime: '',
        page: 1,
        limit: 20
      },
      tableData: [],

      page: {
        size: 0,
        total: 0
      }
    })

    const getList = async () => {
      if (prop.id) {
        const { list, page } = await getCompanyDownloadList({
          memberId: prop.id,
          ...state.form
        })
        state.tableData = list
        state.page.total = page.count
      }
    }

    watch(
      () => prop.id,
      async () => {
        getList()
      },
      { deep: true, immediate: true }
    )
    const handleChange = (val: any) => {
      state.form.page = val.page
      state.form.limit = val.limit
      getList()
    }
    return { ...toRefs(state), handleChange, getList }
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-row) {
  align-items: center;
  margin-top: 15px;
}
.table {
  margin: 20px 0;
}
.search {
  margin-left: 10px;
}
</style>
