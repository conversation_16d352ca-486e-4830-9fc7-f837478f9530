<template>
  <el-dialog v-model="visible" title="自定义显示列(最多12项)" width="750px">
    <div class="content">
      <div class="mb-20">选择显示列</div>
      <div class="wrap jc-center">
        <el-tag
          v-for="item in list"
          :class="{ checked: item.select }"
          :key="item.k"
          :disable-transitions="false"
          @click="handleTagClick(item)"
          >{{ item.v }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button size="small" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="small" @click="handleClose">取消</el-button>
        <el-link class="fs-12 ml-10" type="primary" :underline="false" @click="handleDefault"
          >恢复默认项</el-link
        >
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs, toRaw } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'customColumnDialog',
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    min: {
      type: Number,
      default() {
        return 1
      }
    },
    max: {
      type: Number,
      default() {
        return 12
      }
    }
  },
  emits: ['confirm'],
  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      defaultList: [],
      list: JSON.parse(JSON.stringify((props as any).data))
    })

    onMounted(() => {
      state.defaultList = JSON.parse(JSON.stringify((props as any).data)).map((item: any) => {
        return {
          k: item.k,
          v: item.v,
          select: !!item.default
        }
      })
    })

    const open = () => {
      state.visible = true
    }

    const checkedLength = (flag: Boolean) => {
      const { length } = state.list.filter((item: any) => item.select)
      if (length === 1 && flag === true) {
        ElMessage.error('最少一项')
        return false
      }
      if (length > 12 && flag === false) {
        ElMessage.error('最多12项')
        return false
      }

      return true
    }

    const handleTagClick = (item: any) => {
      const { select } = item
      const flag = checkedLength(select)
      if (!flag) return
      // eslint-disable-next-line no-param-reassign
      item.select = !item.select
    }

    const handleClose = () => {
      state.visible = false
    }

    const handleConfirm = () => {
      emit('confirm', JSON.parse(JSON.stringify(toRaw(state.list))))
      handleClose()
    }

    const handleDefault = () => {
      state.list = JSON.parse(JSON.stringify(toRaw(state.defaultList)))
    }

    return {
      open,
      handleConfirm,
      handleClose,
      handleTagClick,
      handleDefault,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin-left: 10px;
  border-radius: 2px;
  min-width: 82px;
  text-align: center;
  font-size: 13px;
  height: 32px;
  line-height: 30px;
  margin-bottom: 10px;
  color: #666;
  border-color: #e1e1e1;
  background-color: transparent;
  cursor: pointer;
  &.checked {
    color: #fff;
    border-color: var(--color-primary);
    background-color: var(--color-primary);
  }
}
</style>
