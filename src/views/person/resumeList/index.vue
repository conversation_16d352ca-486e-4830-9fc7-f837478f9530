<template>
  <div class="main">
    <div class="box">
      <Filter @search="handerSearch" @reset="handleResetField" />
      <div v-loading="tableLoding">
        <div class="jc-between amount">
          <div>
            共计:
            <span class="danger">{{ statisticsData.totalAmount }}</span>
            个附件&nbsp;&nbsp;共投递:
            <span class="danger">{{ statisticsData.totalApplyAmount }}</span>
            次
          </div>
        </div>

        <el-table :data="list" border size="small" align="center" @sort-change="handleSortChange">
          <el-table-column prop="id" label="附件编号" />
          <el-table-column prop="fileName" label="附件名称">
            <template #default="{ row }">
              <el-link
                @click="open(row.memberId)"
                type="primary"
                class="fw-normal fs-12"
                :underline="false"
                >{{ row.fileName }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column prop="uid" label="UID" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="sortOnSiteApplyAmount" label="站内投递" sortable="custom">
            <template #default="{ row }">
              <div>
                {{ row.onSiteApplyAmount }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sortOffSiteApplyAmount" label="站外投递" sortable="custom">
            <template #default="{ row }">
              <div>
                {{ row.offSiteApplyAmount }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sortDownloadAmount" label="简历下载" sortable="custom">
            <template #default="{ row }">
              <div>
                {{ row.resumeDownloadAmount }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sortUploadAmount" label="上传时间" sortable="custom">
            <template #default="{ row }">
              <div>
                {{ row.createTime }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sortLastApplyTime" label="最近使用" sortable="custom">
            <template #default="{ row }">
              <div>
                {{ row.lastApplyJobTime }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="toJobApply(scope.row)"
                >应聘详情</el-button
              >

              <el-button type="primary" link size="small" @click="preview(scope.row)"
                >预览</el-button
              >
              <el-button type="primary" link size="small" @click="download(scope.row)"
                >下载</el-button
              >
              <el-button type="primary" link size="small" @click="identify(scope.row)"
                >识别</el-button
              >

              <el-button
                type="primary"
                link
                size="small"
                @click="changeResumeAttachment(scope.row)"
              >
                {{ scope.row.status == 1 ? '禁用' : '启用' }}
              </el-button></template
            >
          </el-table-column>
        </el-table>
        <div class="paging">
          <Paging :total="pagination.total" @change="changePage"></Paging>
        </div>
      </div>
    </div>
    <el-dialog v-model="resumeAttachmentPreviewVisible" :destroy-on-close="true" title="预览">
      <resumeAttachmentPreview
        v-if="resumeAttachmentPreviewVisible"
        v-model:token="resumeAttachmentPreviewFrom.token"
        v-model:resumeId="resumeAttachmentPreviewFrom.resumeId"
        @close="resumeAttachmentPreviewVisible = false"
      ></resumeAttachmentPreview>
    </el-dialog>
    <el-dialog
      v-model="resumeAttachmentIdentifyVisible"
      :destroy-on-close="true"
      title="识别内容(具体可以看文档,地址在最后)"
    >
      <json-viewer :value="jsonData" :expand-depth="5" copyable boxed sort> </json-viewer>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, toRefs, onMounted, defineComponent } from 'vue'
import Filter from './components/filter.vue'
import {
  changeResumeAttachmentStatus,
  getResumeAttachmentList,
  resumeAttachmentIdentify
} from '/@/api/person'
import Paging from '/@/components/base/paging.vue'
import resumeAttachmentPreview from '/@/components/resumeAttachmentPreview/index.vue'
import router from '/@/router'

export default defineComponent({
  name: 'personList',
  components: { Filter, Paging, resumeAttachmentPreview },
  setup() {
    const state = reactive({
      loading: false,
      resumeAttachmentPreviewVisible: false,
      resumeAttachmentIdentifyVisible: false,
      resumeAttachmentPreviewFrom: {
        resumeId: '',
        token: ''
      },
      formData: <any>{
        page: '',
        pageSize: '',
        sortOnSiteApplyAmount: '', // 站内投递排序
        sortOffSiteApplyAmount: '', // 站外应聘排序
        sortDownloadAmount: '', // 下载次数排序
        sortUploadAmount: '', // 上传时间排序
        sortLastApplyTime: '' // 最近使用时间排序
      },
      // 分页信息
      pagination: {
        total: 0,
        limit: 20,
        page: 1
      },
      list: [],
      showList: [],
      showListColumn: [],
      showStat: false,
      tableLoding: false,
      statisticsData: {
        totalAmount: 0,
        totalApplyAmount: 0
      },
      jsonData: ''
    })

    const search = () => {
      state.tableLoding = true
      getResumeAttachmentList(state.formData).then((r) => {
        state.list = r.list
        state.tableLoding = false
        state.pagination.total = r.page.count * 1
        state.statisticsData.totalAmount = r.page.count * 1
        state.statisticsData.totalApplyAmount = r.totalApplyAmount * 1
      })
    }

    // 改变排序
    const handleSortChange = ({ prop, order }) => {
      state.formData.sortOnSiteApplyAmount = ''
      state.formData.sortOffSiteApplyAmount = ''
      state.formData.sortDownloadAmount = ''
      state.formData.sortUploadAmount = ''
      state.formData.sortLastApplyTime = ''
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 1
      } else if (order === 'descending') {
        state.formData[prop] = 2
      }
      search()
    }
    // 更改页码
    const changePage = (r) => {
      state.formData.page = r.page
      state.formData.pageSize = r.limit
      search()
    }

    const handerSearch = (data) => {
      state.formData = Object.assign(data, state.formData)
      search()
    }
    const handleResetField = () => {
      state.formData = {
        page: '',
        pageSize: '',
        sortOnSiteApplyAmount: '', // 站内投递排序
        sortOffSiteApplyAmount: '', // 站外应聘排序
        sortDownloadAmount: '', // 下载次数排序
        sortUploadAmount: '', // 上传时间排序
        sortLastApplyTime: '' // 最近使用时间排序
      }
      search()
    }

    const toJobApply = (row) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      router.push(`/person/jobApplyDetail/${row.memberId}`)
    }

    const changeResumeAttachment = (row: any) => {
      const txt = row.status === '1' ? '你确定禁用该附件简历' : '你确定启动该附件简历'
      ElMessageBox.confirm(txt, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          changeResumeAttachmentStatus({ id: row.id }).then(() => {
            search()
          })
        })
        .catch(() => {})
    }

    const preview = (row: any) => {
      state.resumeAttachmentPreviewFrom.resumeId = row.resumeId
      state.resumeAttachmentPreviewFrom.token = row.token
      state.resumeAttachmentPreviewVisible = true
      // window.open(`/person/resume-attachment-preview?resumeId=${row.resumeId}&token=${row.token}`)
    }
    const download = (row: any) => {
      window.open(`/person/resume-attachment-download?resumeId=${row.resumeId}&token=${row.token}`)
    }

    const identify = (row: any) => {
      const params = { resumeId: row.resumeId, token: row.token }
      resumeAttachmentIdentify(params).then((r) => {
        // 把内容整个输出到前端
        const jsonString = JSON.stringify(r, null, 3)
        const json = JSON.parse(jsonString)
        state.jsonData = json

        state.resumeAttachmentIdentifyVisible = true
      })
    }

    // 页面初始化
    onMounted(async () => {
      search()
    })

    const open = (id: string | number) => {
      const routeUrl = <any>router.resolve({
        path: `/person/detail/${id}`,
        query: { type: 'attachment' }
      })
      window.open(routeUrl.href, '_blank')
    }

    return {
      handleSortChange,
      changePage,
      preview,
      download,
      handerSearch,
      handleResetField,
      changeResumeAttachment,
      toJobApply,
      identify,
      open,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.title {
  border-left: 2px solid #196bf9;
  text-indent: 1em;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.paging {
  margin-top: 30px;
}
</style>
