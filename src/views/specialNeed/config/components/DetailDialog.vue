<template>
  <el-dialog
    title="配置详情"
    v-model="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="detail-content">
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="detailData.status == 1 ? 'success' : 'danger'">
              {{ detailData.status == 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="配置ID">
            {{ detailData.id }}
          </el-descriptions-item>
          <el-descriptions-item label="配置名称">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="配置类型">
            {{ detailData.typeText }}
          </el-descriptions-item>
          <el-descriptions-item label="目标信息">
            {{ detailData.targetInfo }}
          </el-descriptions-item>
          <el-descriptions-item label="字段名称">
            {{ detailData.fieldText }}
          </el-descriptions-item>
          <el-descriptions-item label="适用平台">
            {{ detailData.platformText }}
          </el-descriptions-item>
          <el-descriptions-item label="生效开始时间" :span="2">
            {{ detailData.startTime || '无限制' }}
          </el-descriptions-item>
          <el-descriptions-item label="生效结束时间" :span="2">
            {{ detailData.endTime || '无限制' }}
          </el-descriptions-item>
          <el-descriptions-item label="字段值" :span="2">
            <div class="field-value">
              {{ detailData.fieldValue }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ detailData.remark || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ detailData.createdByName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ detailData.addTime }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ detailData.updateTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="detail-card" v-if="detailData.type">
        <template #header>
          <span>配置测试</span>
        </template>

        <el-form :model="testForm" label-width="120px" :inline="true">
          <el-form-item label="配置类型">
            <el-input v-model="testForm.type" :value="detailData.type" disabled />
          </el-form-item>
          <el-form-item label="目标ID">
            <el-input v-model="testForm.targetId" :value="detailData.targetId" disabled />
          </el-form-item>
          <el-form-item label="平台">
            <el-select v-model="testForm.platform" placeholder="请选择平台">
              <el-option label="PC端" value="PC" />
              <el-option label="H5端" value="H5" />
              <el-option label="小程序" value="MINI" />
              <el-option label="全平台" value="ALL" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleTest" :loading="testLoading">
              测试配置
            </el-button>
          </el-form-item>
        </el-form>

        <div v-if="testResult" class="test-result">
          <h4>测试结果：</h4>
          <el-alert
            :title="testResult.message"
            :type="testResult.success ? 'success' : 'error'"
            show-icon
            :closable="false"
          />
          <div v-if="testResult.data && testResult.data.length" class="test-data">
            <h5>匹配的配置：</h5>
            <el-table :data="testResult.data" border size="small">
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="配置名称" />
              <el-table-column prop="fieldName" label="字段名称" />
              <el-table-column prop="fieldValue" label="字段值" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { testConfig } from '/@/api/specialNeed'

interface Props {
  visible: boolean
  detailData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  detailData: () => ({})
})

const emit = defineEmits<Emits>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 测试表单
const testForm = reactive({
  type: '',
  targetId: '',
  platform: 'PC'
})

const testResult = ref<any>(null)
const testLoading = ref(false)

// 测试配置
const handleTest = async () => {
  testLoading.value = true
  try {
    const res = await testConfig(testForm)
    testResult.value = {
      success: true,
      message: '测试成功',
      data: res
    }
  } catch (error) {
    console.error('测试配置失败:', error)
    testResult.value = {
      success: false,
      message: '测试失败'
    }
  } finally {
    testLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val && props.detailData) {
      // 设置测试表单默认值
      testForm.type = props.detailData.type
      testForm.targetId = props.detailData.targetId
      testResult.value = null
    }
  },
  { immediate: true }
)

// 监听弹窗关闭
watch(dialogVisible, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})
</script>

<style scoped lang="scss">
.detail-content {
  .detail-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.field-value {
  max-width: 500px;
  word-break: break-all;
  white-space: pre-wrap;
}

.test-result {
  margin-top: 20px;

  h4,
  h5 {
    margin: 10px 0;
    color: #303133;
  }

  .test-data {
    margin-top: 15px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
