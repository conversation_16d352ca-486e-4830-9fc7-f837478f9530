<template>
  <el-dialog
    :title="isEdit ? '编辑限制' : '新增限制'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="限制名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入限制名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位ID" prop="company_id">
            <el-input v-model="formData.companyId" placeholder="请输入单位ID" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="限制类型" prop="limit_type">
            <el-select
              v-model="formData.limitType"
              placeholder="请选择限制类型"
              @change="handleLimitTypeChange"
              style="width: 100%"
            >
              <el-option label="次数限制" value="count" />
              <el-option label="条件限制" value="condition" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 次数限制相关字段 -->
      <template v-if="formData.limitType === 'count'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间限制(天)" prop="timeLimit">
              <el-input-number
                v-model="formData.timeLimit"
                :min="0"
                :max="365"
                placeholder="请输入天数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="次数限制" prop="countLimit">
              <el-input-number
                v-model="formData.countLimit"
                :min="1"
                :max="100"
                placeholder="请输入次数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 条件限制相关字段 -->
      <template v-if="formData.limitType === 'condition'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="条件字段" prop="conditionField">
              <el-select
                v-model="formData.conditionField"
                placeholder="请选择条件字段"
                style="width: 100%"
              >
                <el-option label="海外经历" value="isAbroad" />
                <el-option label="学历水平" value="educationLevel" />
                <el-option label="工作经验" value="workExperience" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="条件值" prop="conditionValue">
              <el-input
                v-model="formData.conditionValue"
                placeholder="请输入条件值"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-form-item label="错误提示信息" prop="errorMessage">
        <el-input
          v-model="formData.errorMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入错误提示信息"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <!-- 规则说明 -->
    <el-card class="rule-card" v-if="formData.limitType">
      <template #header>
        <span>规则说明</span>
      </template>

      <div class="rule-content">
        <div v-if="formData.limitType === 'count'" class="rule-section">
          <h4>次数限制规则：</h4>
          <ul>
            <li>在指定天数内，同一用户最多可以向该单位投递指定次数的简历</li>
            <li>时间窗口为滚动窗口，从用户第一次投递开始计算</li>
            <li>超出限制时，系统将显示设置的错误提示信息</li>
          </ul>
        </div>

        <div v-if="formData.limitType === 'condition'" class="rule-section">
          <h4>条件限制规则：</h4>
          <ul>
            <li>当用户的指定字段值匹配设置的条件值时，禁止投递</li>
            <li>条件检查在每次投递时进行</li>
            <li>不满足条件时，系统将显示设置的错误提示信息</li>
          </ul>
        </div>
      </div>
    </el-card>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getLimitDetail, createLimit, updateLimit } from '/@/api/specialNeed'

interface Props {
  visible: boolean
  editId?: number | string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editId: undefined
})

const emit = defineEmits<Emits>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 是否编辑模式
const isEdit = ref(false)

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  companyId: '',
  limitType: '',
  timeLimit: 0,
  countLimit: 1,
  conditionField: '',
  conditionValue: '',
  errorMessage: '',
  status: 1,
  remark: ''
})

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    name: [
      { required: true, message: '请输入限制名称', trigger: 'blur' },
      { max: 100, message: '限制名称不能超过100个字符', trigger: 'blur' }
    ],
    companyId: [
      { required: true, message: '请输入单位ID', trigger: 'blur' },
      { pattern: /^\d+$/, message: '单位ID必须是数字', trigger: 'blur' }
    ],
    limitType: [{ required: true, message: '请选择限制类型', trigger: 'change' }],
    errorMessage: [
      { required: true, message: '请输入错误提示信息', trigger: 'blur' },
      { max: 255, message: '错误提示信息不能超过255个字符', trigger: 'blur' }
    ]
  }

  // 根据限制类型添加相应的验证规则
  if (formData.limitType === 'count') {
    baseRules.timeLimit = [
      { required: true, message: '请输入时间限制', trigger: 'blur' },
      { type: 'number', min: 0, message: '时间限制必须大于等于0', trigger: 'blur' }
    ]
    baseRules.countLimit = [
      { required: true, message: '请输入次数限制', trigger: 'blur' },
      { type: 'number', min: 1, message: '次数限制必须大于0', trigger: 'blur' }
    ]
  } else if (formData.limitType === 'condition') {
    baseRules.conditionField = [{ required: true, message: '请选择条件字段', trigger: 'change' }]
    baseRules.conditionValue = [
      { required: true, message: '请输入条件值', trigger: 'blur' },
      { max: 100, message: '条件值不能超过100个字符', trigger: 'blur' }
    ]
  }

  return baseRules
})

// 状态
const loading = ref(false)
const saveLoading = ref(false)

// 限制类型变化
const handleLimitTypeChange = (type: string) => {
  // 清空相关字段
  if (type === 'count') {
    formData.conditionField = ''
    formData.conditionValue = ''
  } else if (type === 'condition') {
    formData.timeLimit = 0
    formData.countLimit = 1
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    companyId: '',
    limitType: '',
    timeLimit: 0,
    countLimit: 1,
    conditionField: '',
    conditionValue: '',
    errorMessage: '',
    status: 1,
    remark: ''
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 获取详情（编辑模式）
const getDetail = async () => {
  if (!isEdit.value || !props.editId) return

  loading.value = true
  try {
    const res = await getLimitDetail({ id: props.editId })

    // 填充表单数据
    Object.keys(formData).forEach((key) => {
      if (res[key] !== undefined) {
        formData[key] = res[key]
      }
    })
  } catch (error) {
    console.error('获取限制详情失败:', error)
    ElMessage.error('获取限制详情失败')
  } finally {
    loading.value = false
  }
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()

    saveLoading.value = true

    const data = {
      SpecialNeedApplyLimit: { ...formData }
    }

    if (isEdit.value) {
      await updateLimit({ ...data, id: props.editId })
      ElMessage.success('更新成功')
    } else {
      await createLimit(data)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      isEdit.value = !!props.editId
      if (isEdit.value) {
        getDetail()
      } else {
        resetForm()
      }
    }
  },
  { immediate: true }
)

// 监听弹窗关闭
watch(dialogVisible, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

.rule-card {
  margin-top: 20px;

  .rule-content {
    .rule-section {
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.6;
          color: #606266;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
