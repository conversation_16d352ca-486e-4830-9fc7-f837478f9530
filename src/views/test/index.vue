<template>
  <div>
    <p>{{ checked }}</p>
    <majorLevel2 v-model="checked"></majorLevel2>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import majorLevel2 from '/@select/majorLevel2.vue'

export default defineComponent({
  components: { majorLevel2 },
  setup() {
    const state = reactive({ checked: ['15'] })
    return {
      ...toRefs(state)
    }
  }
})
</script>
<style scoped lang="scss">
.left {
  text-align: right;
  border: 1px solid #ccc;
}
.checkbox {
  min-width: 200px;
}
</style>
