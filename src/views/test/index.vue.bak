<template>
  <div id="pdf-container">
    <!-- <canvas id="pdf-canvas"></canvas> -->
  </div>
</template>

<script lang="ts">
import * as PdfJs from 'pdfjs-dist/legacy/build/pdf.js' // 注意导入的写法
import { nextTick, onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'funWangEditor',
  setup() {
    const state = reactive({})
    let pdfDoc: any = '' // 保存加载的pdf文件流
    // eslint-disable-next-line
    let pdfPages = 0
    const pdfScale = 1.0
    let numPages = 0

    const renderPage = (num: any) => {
      pdfDoc.getPage(num).then((page: any) => {
        const canvas: any = document.createElement('canvas')
        const ctx: any = canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const bsr =
          ctx.webkitBackingStorePixelRatio ||
          ctx.mozBackingStorePixelRatio ||
          ctx.msBackingStorePixelRatio ||
          ctx.oBackingStorePixelRatio ||
          ctx.backingStorePixelRatio ||
          1
        const ratio = dpr / bsr
        const viewport = page.getViewport({ scale: pdfScale })
        canvas.width = viewport.width * ratio
        canvas.height = viewport.height * ratio
        canvas.style.width = `${viewport.width}px`
        canvas.style.height = `${viewport.height}px`
        ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
        const renderContext = {
          canvasContext: ctx,
          viewport
        }
        page.render(renderContext)
        document.getElementById('pdf-container').appendChild(canvas)
        if (num < numPages) {
          renderPage(num + 1)
        }
      })
    }

    const loadFile = (): void => {
      PdfJs.GlobalWorkerOptions.workerSrc = import('pdfjs-dist/build/pdf.worker.entry')
      const loadingTask = PdfJs.getDocument(
        '/person/resume-attachment-preview?resumeId=6&token=98bd0c53308aa9659d63a8617ecfe302'
      )
      loadingTask.promise.then((pdf) => {
        numPages = pdf.numPages
        pdfDoc = pdf
        pdfPages = pdfDoc.numPages
        nextTick(() => {
          renderPage(1)
        })
      })
    }

    // 页面加载时
    onMounted(() => {
      loadFile()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
