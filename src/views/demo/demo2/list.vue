<template>
  <div class="main">
    <div class="title">demo</div>
    <el-card>
      <div class="formbox">
        <el-form
          ref="form"
          :model="formData"
          label-width="80px"
          class="demo-form-inline"
          :inline="true"
          size="small"
        >
          <el-form-item label="单位检索" prop="companySearch">
            <el-input
              v-model="formData.companySearch"
              placeholder="请填写单位名称或者ID"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位性质" prop="nature">
            <el-select v-model="formData.nature" placeholder="不限" style="width: 150px">
              <el-option
                v-for="item in unitList.companyNatureList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所在地区" prop="areaId">
            <Region v-model="formData.areaId" @change="changeArea"></Region>
          </el-form-item>
          <el-form-item label="所属行业" prop="industryId">
            <el-select v-model="formData.industryId" placeholder="不限" style="width: 150px">
              <el-option
                v-for="item in unitList.companyIndustryList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="addTime">
            <el-col :span="11">
              <el-date-picker
                v-model="formData.addTime"
                type="date"
                placeholder="请选择创建时间"
                style="width: 200%"
              ></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="formData.mobile" placeholder="请填写联系人手机号码"></el-input>
          </el-form-item>
          <el-form-item label="入网来源" prop="accessSource">
            <el-select v-model="formData.accessSource" placeholder="不限" style="width: 158px">
              <el-option
                v-for="item in unitList.companySourceTypeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="会员类型" prop="packageName">
            <el-select v-model="formData.packageName" placeholder="不限" style="width: 150px">
              <el-option
                v-for="item in unitList.companyPackageList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="最近登陆" prop="lastLoginTime">
            <el-col :span="11">
              <el-date-picker
                v-model="formData.lastLoginTime"
                type="date"
                placeholder="请选择最近登陆时间"
                style="width: 150px"
              ></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="单位状态" prop="status">
            <el-select v-model="formData.status" placeholder="不限" style="width: 150px">
              <el-option
                v-for="item in unitList.companyAccountsList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业务员" prop="salesman">
            <el-input
              v-model="formData.salesman"
              placeholder="请输入业务员姓名或账号"
              style="width: 105%"
            ></el-input>
          </el-form-item>
          <el-form-item class="buttomBox">
            <el-button type="primary" @click="searchBtn(formData)">搜索</el-button>
            <el-button type="default" @click="resetForm('form')">重置</el-button>
            <el-button type="default" @click="downloadExcel({ ...formData, export: 1 })"
              >下载</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="unitCount">
        共计：<span class="danger">{{ pagination.total }}</span
        >所单位
      </div>
      <div class="showTable">
        <el-table :data="tableData" border style="width: 100%" align="center">
          <el-table-column prop="id" label="UID" width="120" align="center" />
          <el-table-column prop="username" label="单位名称" width="180" sortable align="center" />
          <el-table-column prop="fullName" label="用户名" width="120" align="center" />
          <el-table-column prop="contact" label="联系人" align="center" />
          <el-table-column prop="mobile" label="联系电话" align="center" width="120" />
          <el-table-column prop="packageNameTxt" label="会员类型" width="120" align="center" />
          <el-table-column prop="addTime" label="创建时间" sortable align="center" />
          <el-table-column prop="lastLoginTime" label="最近登陆" sortable align="center" />
          <el-table-column prop="address" label="所在地" align="center" />
          <el-table-column prop="statusCompany" label="操作" align="center" width="180px">
            <template #default="scope">
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-button
                    type="primary"
                    size="small"
                    class="opbutton"
                    @click="checkDetails(scope.row.id)"
                  >
                    查看</el-button
                  >
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="success"
                    size="small"
                    class="opbutton"
                    @click="editUnitBtn(scope.row.id)"
                  >
                    编辑</el-button
                  >
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="warning"
                    size="small"
                    class="opbutton"
                    @click="memberLogBtn(scope.row)"
                  >
                    日志</el-button
                  >
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="danger"
                    class="opbutton"
                    size="small"
                    @click="disableUnit({ id: scope.row.memberId, status: statusMember })"
                  >
                    {{ tableData.statusMember === '1' ? '可用' : '禁用' }}</el-button
                  >
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="paging">
        <Paging :total="pagination.total" @change="change"></Paging>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue'
import { getList, getUnitList } from '/@/api/unitManage'
import Region from '../../../components/base/select/region.vue'
import Paging from '/@/components/base/paging.vue'

export default defineComponent({
  name: 'yincangMenu',

  components: { Region, Paging },

  setup() {
    const state = reactive({
      formData: {
        companySearch: '', // 单位检索
        nature: '', // 单位性质
        areaId: '', // 所在地区
        industryId: '', // 所属行业
        addTime: '', // 创建时间
        mobile: '', // 手机号码
        accessSource: '', // 入网来源
        packageName: '', // 会员类型
        lastLoginTime: '', // 最近登陆
        status: '', // 单位状态
        salesman: '', // 业务员姓名
        // 排序页码
        page: 1,
        pageSize: 20
      },
      unitList: {},
      tableData: [],
      // 分页信息
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      downloaUrl: ''
    })
    const form = ref()
    const getData = async () => {
      const data = await getList(state.formData)
      state.tableData = data.list
      state.pagination = data.pages
      state.unitList = await getUnitList()
    }
    onMounted(() => {
      getData()
    })
    // 搜索按钮
    const searchBtn = async (val: any) => {
      const { areaId, ...args } = val
      const res = await getList({ ...args, areaId: areaId.join() })
      state.tableData = res.list
    }
    // 重置
    const resetForm = () => {
      form.value.resetFields()
    }
    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getData()
    }
    // 下载
    const downloadExcel = async (data: Object) => {
      const res = await getList(data)
      state.downloaUrl = res.excelUrl
      window.location.href = res.excelUrl
    }
    return { ...toRefs(state), searchBtn, form, resetForm, change, downloadExcel }
  }
})
</script>

<style lang="scss" scoped>
.title {
  border-left: 2px solid #196bf9;
  text-indent: 1em;
  margin-bottom: 10px;
}
.buttomBox {
  margin-left: 30px;
}
.unitCount {
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.opbutton {
  padding: 4px;
}
.paging {
  margin-top: 30px;
}
</style>
