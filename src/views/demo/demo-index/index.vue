<template>
  <div class="main">
    <div class="box">
      <el-form ref="form2" label-width="100px" :model="formData">
        <div class="flex">
          <el-form-item class="span-4" label="人才检索" prop="userKeyword">
            <el-input
              clearable
              @keyup.enter="handleSearch"
              v-model="formData.userKeyword"
              placeholder="输入人才姓名/ID/用户名"
            ></el-input>
          </el-form-item>
          <el-form-item class="span-4" label="手机号" prop="mobile">
            <el-input
              clearable
              @keyup.enter="handleSearch"
              v-model="formData.mobile"
              placeholder="请输入手机号"
            >
              <template #prepend>
                <el-select
                  v-model="formData.mobileCode"
                  style="width: 83px"
                  clearable
                  placeholder="号段"
                >
                  <el-option-group
                    v-for="{ type, list } in prefixOptions"
                    :key="type"
                    :label="type"
                  >
                    <el-option v-for="{ country, code } in list" :key="code" :value="code">
                      <span style="float: left">{{ country }}</span>
                      <span style="float: right"> {{ code }} </span>
                    </el-option>
                  </el-option-group>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="span-4" label="最高学历" prop="educationId">
            <Education clearable v-model="formData.educationId" placeholder="不限" />
          </el-form-item>
          <el-form-item class="span-4" label-width="10px">
            <el-form-item label-width="10px">
              <div class="nowrap">
                <el-link
                  :underline="false"
                  type="primary"
                  size="small"
                  class="ml-12"
                  @click="showMore2 = !showMore2"
                >
                  {{ !showMore2 ? '展开更多' : '收起' }}
                </el-link>
              </div>
            </el-form-item>
          </el-form-item>
        </div>

        <div v-show="showMore2">
          <div class="flex">
            <el-form-item class="span-4" label="身份" prop="identityType">
              <IdentityType v-model="formData.identityType" />
            </el-form-item>
            <el-form-item
              v-if="formData.identityType !== '2'"
              class="span-4"
              label="工作年限"
              prop="workYears"
            >
              <WorkExperience
                v-model="formData.workYears"
                :disabled="formData.identityType === '-1'"
                placeholder="不限"
              />
            </el-form-item>
            <el-form-item
              v-if="formData.identityType !== '2'"
              class="span-4"
              label="工作年限"
              prop="workYears"
            >
              <WorkExperience
                v-model="formData.workYears"
                :disabled="formData.identityType === '-1'"
                placeholder="不限"
              />
            </el-form-item>
            <el-form-item
              v-if="formData.identityType === '2'"
              class="span-4"
              label="毕业时间"
              prop="graduateDate"
            >
              <DatePickerRange
                v-model:start="formData.graduateBeginDate"
                v-model:end="formData.graduateEndDate"
              />
            </el-form-item>
            <el-form-item class="span-4" label="户籍/国籍" prop="householdRegisterId">
              <NativePlace v-model="formData.householdRegisterId" placeholder="不限" />
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item class="span-4" label="现居住地" prop="areaId">
              <Region multiple collapse-tags v-model="formData.areaId" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="注册时间">
              <DatePickerRange
                v-model:start="formData.startCreateTime"
                v-model:end="formData.endCreateTime"
              />
            </el-form-item>
            <el-form-item class="span-4" label="最近登录时间">
              <DatePickerRange
                v-model:start="formData.startLastLoginTime"
                v-model:end="formData.endLastLoginTime"
              />
            </el-form-item>
            <el-form-item class="span-4" label="最近更新时间">
              <DatePickerRange
                v-model:start="formData.startLastUpdateTime"
                v-model:end="formData.endLastUpdateTime"
              />
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item class="span-4" label="求职意向" prop="jobCategory">
              <JobCategory :multiple="false" v-model="formData.jobCategory" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="意向城市" prop="cityId">
              <Region multiple collapse-tags v-model="formData.cityId" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="工作性质" prop="natureType">
              <WorkNature v-model="formData.natureType" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="薪资要求" prop="wageId">
              <Wage v-model="formData.wageId" placeholder="不限" />
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item class="span-4" label="到岗时间" prop="arriveDateType">
              <ArriveDate v-model="formData.arriveDateType" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="是否开通代投" prop="isProxyDeliver">
              <HelpSend v-model="formData.isProxyDeliver" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="海外经历" prop="isAbroad">
              <AbroadExperience v-model="formData.isAbroad" placeholder="不限" />
            </el-form-item>
            <el-form-item class="span-4" label="985/211" prop="isProjectSchool">
              <ProjectSchool v-model="formData.isProjectSchool" placeholder="不限" />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="default" @click="resetForm">重置</el-button>
        <el-button type="default">下载</el-button>
        <el-button type="default">全部下载</el-button>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-button type="primary">+新增单位</el-button>
        <el-button type="default">批量编辑</el-button>
        <el-button @click="handleOpen" type="primary">点我弹框</el-button>
        <el-button type="default">更多按钮</el-button>
        <el-button type="default">更多按钮</el-button>
        <el-button type="default">更多按钮</el-button>
        <el-button type="default">更多按钮</el-button>
        <el-button type="default">更多按钮</el-button>
      </el-row>
      <el-table
        :data="list"
        border
        size="small"
        align="center"
        v-loading="tableLoading"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="uid" label="UID" />
        <el-table-column width="200px" prop="username" label="标签栏目">
          <template #default="{ row }">
            <el-tag
              v-for="(tag, index) in tags"
              :key="index"
              :type="getTagType(index)"
              class="label-margin"
            >
              {{ tag }}
            </el-tag>
          </template> </el-table-column
        >>
        <el-table-column prop="baseInfo" label="基本信息" />
        <el-table-column prop="resumeCompletePercent" label="简历完整度" sortable="custom" />
        <el-table-column label="空默认空白">
          <template #default="{ row }"> </template>
        </el-table-column>
        <el-table-column prop="resumeType" label="简历类型" />
        <el-table-column prop="onSiteApplyAmount" label="站内投递">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="toJobApply(scope.row.memberId, scope.row.onSiteApplyAmount, 1)"
              >{{ scope.row.onSiteApplyAmount }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" sortable="custom" />
        <el-table-column prop="lastLoginTime" label="最近登录" sortable="custom" />
        <el-table-column label="是否开启直聊功能" prop="isChat">
          <template #default="{ row }">
            <el-select
              v-model="row.isChat"
              @change="beforeChange(row.isChat, row.memberId, row)"
              placeholder="请选择"
            >
              <el-option label="开启" :value="'1'"></el-option>
              <el-option label="关闭" :value="'2'"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="resumeTag" label="人才标签">
          <template #default="{ row }">
            <el-input v-model="row.resumeCompletePercent" @keyup.enter="handleChangeSort()" />
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="240px">
          <template #default="scope">
            <el-button type="primary" size="small" class="button-margin"> 编辑 </el-button>
            <el-button type="danger" size="small" class="button-margin">
              {{ scope.row.status == 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="primary" size="small" class="button-margin"> 查看日志 </el-button>
            <el-button type="info" size="small" class="button-margin"> 隐藏 </el-button>
            <el-button type="danger" size="small" class="button-margin" disabled>
              {{ scope.row.status == 1 ? '关闭' : '关闭' }}
            </el-button>
            <el-popover placement="left" :width="20" trigger="click">
              <template #reference>
                <el-button plain size="small">...</el-button>
              </template>
              <div class="column btns">
                <el-button class="w100 mx-0 my-5" plain size="small">查看简历</el-button>
                <el-button class="w100 mx-0 my-5" plain size="small">查看简历</el-button>
                <el-button class="w100 mx-0 my-5" plain size="small">查看简历</el-button>
                <el-button class="w100 mx-0 my-5" plain size="small">查看简历</el-button>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <Paging :total="pagination.total"></Paging>
      </div>
      <SalesmanChangeDialog @update="getTableList()" ref="salesmanChangeDialog" />
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Education from '/@select/educationSearch.vue'
import Region from '/@select/region.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import WorkExperience from '/@select/workExperience.vue'
import NativePlace from '/@select/nativePlace.vue'
import JobCategory from '/@select/jobCategory.vue'
import WorkNature from '/@select/workNature.vue'
import Wage from '/@select/wage.vue'
import HelpSend from '/@select/helpSend.vue'
import ProjectSchool from '/@select/projectSchool.vue'
import ArriveDate from '/@select/arriveDate.vue'
import IdentityType from '/@select/identityType.vue'
import Paging from '/@/components/base/paging.vue'
import { editChatConfig } from '/@/api/account'
import { ElMessage } from 'element-plus'
import router from '/@/router'
import SalesmanChangeDialog from '/@/views/configuration/business/components/salesmanChangeDialog.vue'

export default defineComponent({
  name: 'company',
  components: {
    SalesmanChangeDialog,
    Paging,
    Region,
    ProjectSchool,
    JobCategory,
    Wage,
    ArriveDate,
    WorkExperience,
    IdentityType,
    DatePickerRange,
    WorkNature,
    Education,
    NativePlace,
    HelpSend,
    AbroadExperience
  },
  setup() {
    const state = reactive({
      tableLoading: false,
      prefixOptions: <any>[],
      region: '',
      showMore: false,
      showMore2: false,
      formData: {
        userKeyword: '',
        mobile: '',
        mobileCode: '',
        educationId: '',
        identityType: '',
        workYears: '',
        graduateBeginDate: '',
        graduateEndDate: '',
        householdRegisterId: '',
        areaId: [],
        startCreateTime: '',
        endCreateTime: '',
        startLastLoginTime: '',
        endLastLoginTime: '',
        startLastUpdateTime: '',
        endLastUpdateTime: '',
        jobCategory: '',
        cityId: [],
        natureType: '',
        wageId: '',
        arriveDateType: '',
        isProxyDeliver: '',
        isAbroad: '',
        isProjectSchool: ''
      },
      resetFormData: {},
      // 分页信息
      pagination: {
        total: 0,
        limit: 10,
        page: 1
      },
      list: [] as any[],
      tags: ['标签1', '是标签2', '标签3', '的观点', 'sasa', '根'],
      tableData: [
        {
          uid: 'UID001',
          username: '张三',
          baseInfo: '男/28岁/本科/5年经验',
          resumeCompletePercent: 85,
          resumeType: '附件简历',
          onSiteApplyAmount: 12,
          createTime: '2024-01-15',
          lastLoginTime: '2024-03-18',
          isChat: '1',
          memberId: 'M001',
          status: 1
        },
        {
          uid: 'UID002',
          username: '李四',
          baseInfo: '女/26岁/硕士/3年经验',
          resumeCompletePercent: 92,
          resumeType: '在线简历',
          onSiteApplyAmount: 8,
          createTime: '2024-02-01',
          lastLoginTime: '2024-03-19',
          isChat: '2',
          memberId: 'M002',
          status: 1
        },
        {
          uid: 'UID003',
          username: '王五',
          baseInfo: '男/32岁/博士/7年经验',
          resumeCompletePercent: 95,
          resumeType: '附件简历',
          onSiteApplyAmount: 15,
          createTime: '2024-01-20',
          lastLoginTime: '2024-03-20',
          isChat: '1',
          memberId: 'M003',
          status: 2
        }
      ]
    })

    const form = ref()

    const getTableList = () => {
      state.tableLoading = true
      state.list = JSON.parse(JSON.stringify(state.tableData))
      state.tableLoading = false
    }
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      // getAnnounceSearch()
      alert('触发重置')
      getTableList()
    }
    const handleSearch = () => {
      getTableList()
    }

    const getTagType = (index) => {
      const types = ['primary', 'success', 'warning']
      return types[index % types.length] // 根据索引循环使用颜色
    }
    const beforeChange = (isChat: string, memberId: string) => {
      editChatConfig({ isChat, memberId })
    }
    const handleChangeSort = async () => {
      ElMessage.success('修改成功')
    }

    const handleSortChange = ({ prop, order }) => {
      console.log({ prop, order })
      getTableList()
    }

    const toJobApply = (row) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      router.push(`/person/jobApplyDetail/${row.memberId}`)
    }
    const salesmanChangeDialog = ref()

    const handleOpen = () => {
      salesmanChangeDialog.value.open()
    }

    getTableList()
    return {
      ...toRefs(state),
      salesmanChangeDialog,
      handleOpen,
      toJobApply,
      handleChangeSort,
      getTagType,
      handleSearch,
      resetForm,
      form,
      getTableList,
      beforeChange,
      handleSortChange
    }
  }
})
</script>
<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.form-box el-form-item el-input {
  width: 230px;
}
.button-margin {
  margin: 5px; /* 下边距 5px */
}
.label-margin {
  margin: 5px;
}
.paging {
  margin-top: 30px;
}
.btns {
  .el-button + .el-button {
    margin-left: 0;
  }
}
</style>
