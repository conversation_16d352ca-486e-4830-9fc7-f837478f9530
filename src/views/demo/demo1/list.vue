<template>
  <div class="layout-view-bg-white p-20">
    <el-form size="small" ref="form" :model="form" label-width="80px">
      <div class="p-20">平分空间 -> flex-1</div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>

      <div class="p-20">特殊情况(例如某个宽度要宽一些) flex-2至flex-5</div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-2" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-2" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-3" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-2" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-3" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-4" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-2" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-2" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item class="flex-1" label="test">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive } from 'vue'

export default {
  name: '401',
  setup() {
    const state = reactive({
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      }
    })
    return {
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.el-form .el-form-item:last-of-type {
  margin-bottom: 18px !important;
}
</style>
