<template>
  <el-card class="card-1">
    <div class="card-1-1">
      <div class="title">会员信息</div>
      <div
        class="link"
        v-if="info.vipType === '1'"
        @click="
          redirectOrder(info.encryptId, info.vipType === '1' ? (info.vipLevel === '1' ? 1 : 3) : 1)
        "
      >
        查看VIP订单
      </div>
    </div>
    <el-descriptions>
      <el-descriptions-item label="用户ID：">{{ info.encryptId }}</el-descriptions-item>
      <el-descriptions-item label="用户名：">{{ info.username }}</el-descriptions-item>
      <el-descriptions-item label="姓名：">{{ info.name }}</el-descriptions-item>
      <el-descriptions-item label="会员类型：">{{ info.vipTypeText }}</el-descriptions-item>
      <el-descriptions-item label="会员有效期：" v-if="info.vipType === '1'">
        {{ info.vipBeginTime }} ～ {{ info.vipExpireTime }}
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
  <el-card class="card-2">
    <div class="card-2-1">
      <div class="title">增值服务</div>
      <div class="link" @click="redirectOrder(info.encryptId)">查看关联订单</div>
      <div class="link" v-if="info.isResumePackageAdminSetting === '1'" @click="handleRouter">
        查看套餐配置记录
      </div>
    </div>
    <div class="card-2-2">
      <el-card class="box-card" v-for="item in info.equityDetail">
        <template #header>
          <div class="card-header">
            <span>{{ item.name }}</span>
          </div>
        </template>
        <div class="card-2-2-2">
          <div class="card-2-2-2-1">有效期：</div>
          <div>
            <div>{{ item.expireText }}</div>
          </div>
        </div>
        <div class="text item">
          已购{{ item.categoryName }}：{{ item.number }}
          <span class="link" @click="redirectOrder(info.encryptId, item.id, 1)">详情</span>
        </div>
      </el-card>
    </div>
  </el-card>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { useRoute, useRouter } from 'vue-router'
import { getVipInfo } from '/@/api/member.ts'

export default {
  name: 'memberVipInfo',
  components: { Paging },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      logSearch: '',
      // 路由参数
      data: { id: route.query.id },
      info: <any>[]
    })
    const isShow = ref(route.query.isShow)

    // 实际的查询
    const searchSubmit = async () => {
      const res = await getVipInfo(state.data)
      state.info = res
    }

    const redirectOrder = (resumeId, type = 0, status = '') => {
      const queryParams = {
        resumeId
      }
      if (type > 0) {
        queryParams.equityPackageCategoryId = type
      }
      if (status) {
        queryParams.status = status
      }
      router.push({
        path: '/configuration/order',
        query: queryParams
      })
    }

    const handleRouter = () => {
      router.push({ path: '/configuration/setMeal', query: { id: state.info.encryptId } })
    }

    onMounted(() => {
      searchSubmit()
    })

    return {
      ...toRefs(state),
      searchSubmit,
      redirectOrder,
      handleRouter,
      isShow
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  height: 30px;
  font-size: 20px;
  font-weight: 700;
}

.link {
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  color: #409eff;
  cursor: pointer;
  margin-left: 20px;
}

.card-1 {
  margin-bottom: 15px;
  border-radius: 10px;
}

.card-2 {
  border-radius: 10px;
  height: 640px;
}

.card-1-1 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-2-1 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-2-2 {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.card-2-2-2 {
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
}

.card-2-2-2-1 {
  width: 60px;
}

.box-card {
  width: 24.5%;
  margin-bottom: 15px;
  margin-right: 0.5%;
}
</style>
