<template>
  <div class="main">
    <div class="title">会员日志</div>
    <el-card>
      <el-tabs v-model="activeName" @tab-change="searchSubmit">
        <el-tab-pane label="操作日志" name="0">
          <div class="flex ai-center">
            操作：
            <el-input
              v-model="data.keyword"
              class="search-input"
              placeholder="请输入关键词"
              clearable
            />
            操作人：
            <el-select placeholder="请选择" v-model="data.contact">
              <el-option
                v-for="item in (filterList as any)"
                :key="item.v"
                :label="item.k"
                :value="item.v"
              ></el-option>
            </el-select>
            <el-button type="primary" class="search" @click="searchSubmit">搜索</el-button>
          </div>
          <div class="show-table">
            <el-table :data="tableData" style="width: 100%" align="left">
              <el-table-column prop="addTime" label="时间" width="180" />
              <el-table-column prop="content" label="操作" width="120" />
              <el-table-column prop="ip" label="IP" width="180" />
              <el-table-column prop="ascriptionIp" label="IP归属地" />
              <el-table-column prop="platform" label="来源" />
              <el-table-column prop="contact" label="操作人" />
              <el-table-column prop="department" label="所在部门" />
            </el-table>
          </div>
          <!-- 分页组件 -->
          <div class="paging">
            <Paging :total="data.total" @change="changeStatus"></Paging>
          </div>
        </el-tab-pane>
        <el-tab-pane label="登陆日志" name="1" v-if="isShow">
          <div class="flex ai-center">
            操作人：
            <el-select placeholder="请选择" v-model="contact">
              <el-option
                v-for="item in (filterList as any)"
                :key="item.v"
                :label="item.k"
                :value="item.v"
              ></el-option>
            </el-select>
            <el-button type="primary" class="search" @click="searchSubmit">搜索</el-button>
          </div>
          <div class="show-table">
            <el-table :data="tableData" style="width: 100%" align="left" v-loading="loading">
              <el-table-column prop="addTime" label="时间" width="180" />
              <el-table-column prop="ip" label="IP" width="180" />
              <el-table-column prop="ascriptionIp" label="IP归属地" />
              <el-table-column prop="platform" label="来源" />
              <el-table-column prop="contact" label="操作人" />
              <el-table-column prop="department" label="所在部门" />
            </el-table>
          </div>
          <!-- 分页组件 -->
          <div class="paging">
            <Paging :total="data.total" @change="changeStatus"></Paging>
          </div>
        </el-tab-pane>

        <el-tab-pane label="单位状态&子账号数量 变更记录" name="changeRecord">
          <el-table :data="tableData" border>
            <el-table-column align="center" prop="addTime" label="时间" />
            <el-table-column align="center" prop="operationContent" label="变更内容">
              <template #default="{ row }">
                <div>
                  <div>{{ row.operationContent }}</div>
                  <div>（ 操作类型：{{ row.operationTypeName }}）</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="operationPortName" label="操作端口" />
            <el-table-column align="center" prop="operationPersonName" label="操作人" />
          </el-table>

          <div class="paging">
            <Paging :total="data.total" @change="changeStatus" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, computed } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { useRoute } from 'vue-router'
import { getLogList, getLogfilter, getOperationLogList } from '/@/api/member'

export default {
  name: 'memberLogList',
  components: { Paging },
  setup() {
    const route = useRoute()
    const state = reactive({
      activeName: '0',
      logSearch: '',
      // 路由参数
      data: {
        id: route.query.id,
        isLogTab: computed(() => state.activeName),
        pageSize: '',
        page: '',
        total: 0,
        keyword: '',
        contact: ''
      },
      contact: '',
      tableData: [],
      logData: [],
      filterList: [],
      loading: false
    })
    const isShow = ref(route.query.isShow)

    // 实际的查询
    const searchSubmit = async () => {
      state.loading = true
      if (state.activeName === 'changeRecord') {
        const { list, pages } = await getOperationLogList({ id: state.data.id })
        state.tableData = list
        state.data.total = pages.total
      } else {
        const loginData = { ...state.data, contact: state.contact }
        const postData = state.activeName === '0' ? state.data : loginData
        const { list, pages } = await getLogList(postData)
        state.tableData = list
        state.data.total = pages.total
      }
      state.loading = false
    }

    const getFilterList = async () => {
      const { contactSelect } = await getLogfilter({ id: state.data.id })
      state.filterList = contactSelect
    }

    const changeStatus = (data: any) => {
      state.data.page = data.page
      state.data.pageSize = data.limit
      searchSubmit()
    }

    getFilterList()
    searchSubmit()

    return { ...toRefs(state), searchSubmit, changeStatus, isShow } as any
  }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  .title {
    height: 30px;
    background-color: #f0f0f0;
    font-size: 16px;
    font-weight: 700;
    padding-left: 30px;
  }
  .show-table {
    padding-top: 30px;
  }
  .paging {
    margin-top: 90px;
  }

  .search-input {
    width: 200px;
    margin-right: 20px;
  }

  .search {
    margin-left: 20px;
  }
}
</style>
