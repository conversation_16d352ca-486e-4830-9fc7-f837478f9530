<template>
  <div class="main">
    <!--      使用说明-->
    <el-alert class="description" title="使用说明：" type="error" :closable="false">
      <div slot="desc">
        <p>1、Tabs属于导出维度；维度不同，数据不同。</p>
        <p>2、数据表头请根据自己需求勾选，避免系统不必要的性能消耗；系统限定最多选择30个表头。</p>
        <p>3、根据需求选择自身对应筛选项来获取数据。</p>
      </div>
    </el-alert>
    <el-tabs type="border-card" v-model="tabsDefault" @tab-click="clickTabs">
      <el-tab-pane v-for="item in tabsList" :label="item.v" :name="item.k">
        <el-form ref="formVm" :model="formData">
          <div class="search">
            <span>筛选项</span>
            <el-input
              v-model="formData.filterValue"
              placeholder="请输入关键字全匹配搜索"
              class="form-item"
            >
              <template #prepend>
                <el-select
                  v-model="formData.filterKey"
                  clearable
                  placeholder="请选择"
                  style="width: 100px"
                >
                  <el-option
                    v-for="filterItem in filterList"
                    :label="filterItem.label"
                    :value="filterItem.value"
                  />
                </el-select>
              </template>
            </el-input>
            <div class="form-item">
              <el-select
                v-model="formData.filterTimeKey"
                clearable
                placeholder="请选择"
                style="width: 45%"
              >
                <el-option
                  v-for="filterTimeItem in filterTimeList"
                  :label="filterTimeItem.label"
                  :value="filterTimeItem.value"
                />
              </el-select>
              <DatePickerRange
                v-model:start="formData.addTimeStart"
                v-model:end="formData.addTimeEnd"
              />
            </div>

            <div v-if="tabsDefault === 2" class="form-item batch">
              <el-checkbox v-model="formData.isImportFile" true-label="1">批量查询</el-checkbox>
              <el-upload
                class="upload-file"
                action="statement-report-builder/upload"
                multiple
                :on-remove="handleRemove"
                :limit="1"
                :on-success="handleSuccess"
              >
                <el-button
                  v-if="!formData.filePath"
                  :disabled="!formData.isImportFile"
                  type="primary"
                  >上传文件</el-button
                >
              </el-upload>
              <el-button type="primary" text class="download" @click="downloadFile"
                >下载导入模板</el-button
              >
            </div>
          </div>
          <el-tabs type="border-card" v-model="tabsTypeDefault" @tab-click="clickTabsType">
            <el-tab-pane
              v-for="tabsTypeItem in tabsTypeList"
              :label="tabsTypeItem.name"
              :name="tabsTypeItem.id"
            >
              <div class="title">
                <span>表头选取</span>
                <el-checkbox-group v-model="titleSelect" :max="30">
                  <el-checkbox
                    v-for="configItem in configData"
                    :key="configItem.code"
                    :label="configItem.code"
                    border
                    style="width: 14%; margin-bottom: 10px"
                  >
                    {{ configItem.title }}
                    <el-tooltip placement="top" effect="light" v-if="configItem.remark">
                      <template #content>
                        <div>{{ configItem.remark }}</div>
                      </template>
                      <el-icon :size="18" class="p-s-icon">
                        <i class="el-icon-warning-outline"></i>
                      </el-icon>
                    </el-tooltip>
                  </el-checkbox>
                </el-checkbox-group>
                <div class="checkbox-tips">
                  <el-checkbox
                    v-model="formData.isTips"
                    v-if="tabsTypeItem.isTipsList"
                    prop="isTips"
                    >是否直接弹出数据</el-checkbox
                  >
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <div class="submit">
      <div>
        <el-button type="warning" @click="resetExportData">重置</el-button>
        <el-button type="primary" @click="submitExportData">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { tabsData, configList, create, filterData } from '/@/api/reportBuilder'
import { ElMessage } from 'element-plus'
import { reactive, toRefs, ref } from 'vue'

export default {
  name: 'reportBuilder',
  components: {
    DatePickerRange
  },
  setup() {
    const titleSelect = ref([])
    const state = reactive({
      tabsDefault: 1,
      tabsTypeDefault: 1,
      tabsList: [],
      tabsTypeList: [],
      titleList: [],
      tabsTypeConfig: [],
      configData: [],
      filterList: [],
      filterTimeList: [],
      filterData: {
        normalList: [],
        timeList: []
      },
      formData: {
        isTips: false,
        tabs: 1,
        tabsType: 1,
        addTimeStart: '',
        addTimeEnd: '',
        filterValue: '',
        filterKey: '',
        filterTimeKey: '',

        isImportFile: '',
        filePath: ''
      }
    })

    /**
     * 获取标签页选项
     */
    const getTabsList = async () => {
      const { tabs, tabsType } = await tabsData()
      state.tabsList = tabs
      state.tabsTypeConfig = tabsType
      state.tabsTypeList = tabsType[state.tabsDefault]
      state.titleList = await configList()
      state.configData = state.titleList[state.tabsDefault][state.tabsTypeDefault]
    }
    getTabsList()

    /**
     * 筛选项
     */
    const getFilterData = async () => {
      state.filterData = await filterData()
      state.filterList = state.filterData.normalList[state.formData.tabs]
      state.filterTimeList = state.filterData.timeList[state.formData.tabs]
    }
    getFilterData()

    /**
     * 切换标签页
     * @param tab
     */
    const clickTabs = (tab) => {
      resetExportData()
      state.tabsDefault = tab.paneName
      state.tabsTypeDefault = 1
      state.formData.tabs = tab.paneName
      state.tabsTypeList = state.tabsTypeConfig[state.tabsDefault]
      state.configData = state.titleList[state.tabsDefault][state.tabsTypeDefault]
      state.filterList = state.filterData.normalList[state.formData.tabs]
      state.filterTimeList = state.filterData.timeList[state.formData.tabs]
    }

    /**
     * 切换标签页type
     * @param tab
     */
    const clickTabsType = (tab) => {
      titleSelect.value = []
      state.tabsTypeDefault = tab.paneName
      state.formData.tabsType = tab.paneName
      state.formData.isTips = state.configData =
        state.titleList[state.tabsDefault][state.tabsTypeDefault]
    }

    const handleSuccess = (res: any) => {
      state.formData.filePath = res.data.url
    }

    const handleRemove = () => {
      state.formData.filePath = ''
    }

    const downloadFile = () => {
      window.location.href = '/static/template/statement_report_builder_company_list_template.xlsx'
    }

    /**
     * 重置
     */
    const resetExportData = () => {
      state.formData = {
        isTips: false,
        tabs: 1,
        tabsType: 1,
        addTimeStart: '',
        addTimeEnd: '',
        filterValue: '',
        filterKey: '',
        filterTimeKey: '',

        isImportFile: '',
        filePath: ''
      }
      titleSelect.value = []
    }

    /**
     * 提交
     */
    const submitExportData = () => {
      const { isImportFile, filePath } = state.formData
      if (titleSelect.value.length === 0) {
        ElMessage.warning('请选择表头')
        return
      }
      if (isImportFile && !filePath) {
        ElMessage.warning('请上传文件')
        return
      }
      const params = {
        ...state.formData,
        titleSelect: titleSelect.value.join(',')
      }
      console.log(params)
      create(params).then((res) => {
        console.log(res)
        // if (res.code === 200) {
        //   ElMessageBox.confirm('导出成功，是否下载？', '提示', {
        //     confirmButtonText: '下载',
        //     cancelButtonText: '取消',
        //     type: 'success'
        //   }).then(() => {
        //     window.open(res.data)
        //   }).catch(() => {
        //     ElMessage.success('已取消下载')
        //   })
        // }
      })
    }

    return {
      ...toRefs(state),
      clickTabs,
      clickTabsType,
      titleSelect,
      resetExportData,
      submitExportData,
      handleSuccess,
      downloadFile,
      handleRemove
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.description {
  margin-bottom: 20px;
  padding: 10px 20px;
}

.description p {
  font-size: 13px;
}

.form-item {
  width: 25%;
  margin-right: 20px;
  display: inline-flex;
  align-items: center;
}

.search {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px 20px 0 20px;
  position: relative;
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.search span {
  position: absolute;
  top: -10px;
  left: 20px;
  color: #909399;
  background-color: #fff;
  padding: 0 10px;
}

.title {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-top: 10px;
  padding: 25px 15px 5px 15px;
  position: relative;
}

.title span {
  position: absolute;
  top: -10px;
  left: 20px;
  color: #909399;
  background-color: #fff;
  padding: 0 10px;
}

.checkbox-tips {
  text-align: right;
}

.submit {
  margin-top: 15px;
  text-align: right;
}

.border-text {
  font-weight: bold;
}

.batch {
  margin-left: 30px;

  .upload-file {
    display: flex;
    align-items: center;
    margin: 0 15px;

    :deep() {
      .el-upload-list {
        margin: 0;
      }
      .el-upload-list__item {
        transition: none;
        margin-bottom: 0;
      }
    }

    .el-button {
      transition: none;
    }
  }

  .download {
    padding: 0;

    &.el-button--primary:hover,
    &.el-button--primary:focus {
      color: var(--el-color-primary);
      background-color: transparent;
    }
  }
}
</style>
