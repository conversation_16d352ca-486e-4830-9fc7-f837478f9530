<template>
  <div class="main">
    <el-card>
      <div class="formbox">
        <el-form
          ref="form"
          :model="formData"
          label-width="100px"
          class="demo-form-inline"
          :inline="true"
        >
          <el-row>
            <el-col :span="5">
              <el-form-item label="sitemap检索" prop="fileName">
                <el-input v-model="formData.fileName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="检索类型" prop="type">
                <el-select v-model="formData.type" clearable filterable>
                  <el-option v-for="item in typeList" :key="item.k" :label="item.v" :value="item.k">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="发布时间" prop="startAddTime">
                <DatePickerRange
                  v-model:start="formData.startAddTime"
                  v-model:end="formData.endAddTime"
                />
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="域名" prop="platformType">
                <el-select v-model="formData.platformType" clearable filterable>
                  <el-option
                    v-for="item in platformTypeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-button type="primary" @click="getList">搜索</el-button>
              <el-button type="default" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="showFilePath = true">路径</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="showTable" v-loading="listLoading">
        <el-dialog v-model="detailDialogVisible" title="编辑文件内容">
          <div>
            <el-input type="textarea" v-model="content" :autosize="{ maxRows: 20 }"></el-input>
          </div>
          <el-button type="primary" class="mt-20" @click="submitContent">确定</el-button>
        </el-dialog>

        <el-table :data="tableData" border style="width: 100%" align="center">
          <el-table-column prop="fileName" label="sitemap文件" align="center" />
          <el-table-column prop="filePath" label="文件路径" align="center" />
          <el-table-column prop="typeText" label="文件类型" align="center" />
          <el-table-column prop="addTime" label="发布时间" align="center" />
          <el-table-column prop="platformTypeText" label="端口域名" align="center" />
          <el-table-column prop="statusCompany" label="操作" align="center">
            <template #default="{ row }">
              <el-button type="success" @click="getDetail(row)">编辑</el-button>
              <el-button type="danger" :disabled="row.type === '7'" @click="deleteSeo(row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="paging mt-20">
        <Paging :total="formData.total" @change="change"></Paging>
      </div>
    </el-card>
    <el-dialog v-model="showFilePath" title="文件路径" width="40%">
      <div v-for="item in filePathList" :key="item">
        <span>{{ item }}</span>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import { getSearchParams, getSeoList, getSeoEdit, deleteSeoFile, seoFileSave } from '/@/api/seo'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

export default defineComponent({
  name: 'seoBaiduZZPushList',
  components: { Paging, DatePickerRange },
  setup() {
    const form = ref()
    const state = reactive({
      formData: {
        fileName: '',
        type: '',
        startAddTime: '',
        endAddTime: '',
        platformType: '',
        page: 1,
        pageSize: 20,
        total: 0
      },
      tableData: [],
      detailDialogVisible: false,
      listLoading: false,
      showFilePath: false,
      typeList: [],
      platformTypeList: [],
      content: '',
      id: '',
      filePathList: ''
    })

    const getParamsList = async () => {
      const { typeList, platformTypeList } = await getSearchParams()
      state.typeList = typeList
      state.platformTypeList = platformTypeList
    }

    const getList = async () => {
      state.listLoading = true
      const { list, pages, filePathList } = await getSeoList(state.formData)
      state.tableData = list
      state.filePathList = filePathList
      state.formData.pageSize = pages.size
      state.formData.total = pages.total
      state.listLoading = false
    }

    getParamsList()

    getList()

    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getList()
    }

    const getDetail = async (row: any) => {
      state.listLoading = true
      const { id } = row
      const { content } = await getSeoEdit({ id })
      state.id = id
      state.content = content
      state.listLoading = false
      state.detailDialogVisible = true
    }

    const resetForm = () => {
      form.value.resetFields()
      getList()
    }

    const deleteSeo = async (row: any) => {
      const { id } = row
      await deleteSeoFile({ id })
      state.tableData = state.tableData.filter((item: any) => item.id !== id)
    }

    const submitContent = async () => {
      await seoFileSave({ id: state.id, content: state.content })
      state.detailDialogVisible = false
    }
    return {
      ...toRefs(state),
      form,
      getList,
      change,
      getDetail,
      resetForm,
      deleteSeo,
      submitContent
    }
  }
})
</script>
