<template>
  <div class="main">
    <el-card>
      <div class="showTable" v-loading="listLoading">
        <el-table :data="tableData" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="name" label="文件名" align="center" />
          <el-table-column prop="type" label="类型" align="center" />
          <el-table-column prop="size" label="大小" align="center" />
        </el-table>
        <br />
        <el-button type="primary" @click="submitDownload">下载</el-button>
        <el-button type="primary" @click="getTableData">刷新</el-button>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox, ElTable } from 'element-plus'

import { downloadNginxLog, getNginxLogList } from '/@/api/seo'

const listLoading = ref(false)
const tableData = ref([])
const multipleSelection = ref([])

const getTableData = () => {
  listLoading.value = true
  getNginxLogList().then((res) => {
    listLoading.value = false
    tableData.value = res.list
  })
}

const submitDownload = () => {
  const params = {
    name: multipleSelection.value.map((item) => item.name),
    type: multipleSelection.value.map((item) => item.type)
  }

  // 如果没有选中就先提示选中
  if (params.name.length === 0) {
    ElMessageBox.alert('请选择要下载的文件', '提示', {
      confirmButtonText: '确定'
    })
    return
  }

  // 给一个二次确认是否确定下载
  ElMessageBox.confirm('下载需要产生一定流量费用，文件越大费用越高，确定下载吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    downloadNginxLog(params)
  })
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

getTableData()
</script>
