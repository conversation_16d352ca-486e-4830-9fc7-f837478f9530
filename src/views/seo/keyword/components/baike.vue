<template>
  <el-form ref="form" :model="formData" :inline="true">
    <div class="flex">
      <el-form-item class="span-4" label="百科词检索" prop="keyword">
        <el-input
          clearable
          @keyup.enter="handleSearch"
          v-model="formData.keyword"
          placeholder=""
        ></el-input>
      </el-form-item>
      <el-form-item class="span-4" label="CODE检索" prop="code">
        <el-input clearable @keyup.enter="handleSearch" v-model="formData.code" placeholder="">
        </el-input>
      </el-form-item>
      <el-form-item class="span-4" label="创建时间" prop="startAddTime">
        <DatePickerRange v-model:start="formData.startAddTime" v-model:end="formData.endAddTime" />
      </el-form-item>
    </div>
  </el-form>
  <el-row style="margin-bottom: 20px">
    <el-button type="primary" @click="handleSearch">搜索</el-button>
    <el-button type="default" @click="resetForm">重置</el-button>
  </el-row>
  <el-row style="margin-bottom: 20px">
    <el-button type="primary" @click="handleAdd()">创建词条</el-button>
    <el-button type="primary" @click="handleBatchAdd()">批量创建</el-button>
  </el-row>
  <el-table :data="tableList" border size="small">
    <el-table-column prop="id" label="ID" align="center" />
    <el-table-column prop="code" label="code" align="center" />
    <el-table-column prop="keyword" label="百科词" align="center" />
    <el-table-column prop="addTime" label="创建时间" align="center" />
    <el-table-column label="操作" align="center">
      <template #default="{ row }">
        <el-button type="primary" size="small" @click="handleEdit(row.keyword, row.id)">
          编辑
        </el-button>
        <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <Paging class="mt-20" :total="total" @change="handlePageChange" />

  <baikeKeywords
    v-model="editDialogVisible"
    v-model:keyword="keyword"
    :id="keywordId"
    @update="getTableList"
  ></baikeKeywords>
  <baikeBatch v-model="batchDialogVisible" @update="getTableList" />
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessageBox, ElForm } from 'element-plus'
import { getSeoJobWikiList, delSeoJobWiki } from '/@/api/seo'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import baikeKeywords from './baikeKeywords.vue'
import baikeBatch from './baikeBatch.vue'

const currentPage = ref(1)
const pageSize = ref(20)

const formData = ref({
  keyword: '',
  code: '',
  startAddTime: '',
  endAddTime: '',
  page: computed(() => currentPage.value),
  pageSize: computed(() => pageSize.value)
})

const total = ref(0)

const editDialogVisible = ref(false)
const batchDialogVisible = ref(false)

const tableList = ref([])
const form = ref<InstanceType<typeof ElForm>>()

const keyword = ref('')
const keywordId = ref('')

const getTableList = async () => {
  const {
    list,
    pages: { count }
  } = await getSeoJobWikiList(formData.value)

  tableList.value = list
  total.value = count - 0
}

getTableList()

// 重置
const resetForm = async () => {
  await form.value?.resetFields()
  getTableList()
}

const handleSearch = () => {
  getTableList()
}

const handleDelete = (id: string) => {
  ElMessageBox.confirm('删除后将不可恢复，确认要删除百科词吗？', '删除百科词')
    .then(async () => {
      await delSeoJobWiki({ ids: id })
      await getTableList()
    })
    .catch(() => false)
}

const handlePageChange = (data: any) => {
  const { page, limit } = data

  currentPage.value = page
  pageSize.value = limit

  getTableList()
}

const handleAdd = () => {
  keywordId.value = ''
  editDialogVisible.value = true
}

const handleBatchAdd = () => {
  batchDialogVisible.value = true
}

const handleEdit = (value: string, id: string) => {
  keyword.value = value
  keywordId.value = id
  editDialogVisible.value = true
}
</script>

<style lang="scss" scoped></style>
