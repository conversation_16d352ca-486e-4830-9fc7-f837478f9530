<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :title="`批量创建`"
    width="600px"
    @close="handleCloseDialog"
  >
    <el-form ref="keywordRef" :rules="rules" :model="formData">
      <el-form-item label="上传文件" prop="filePath">
        <div class="flex">
          <el-upload
            ref="uploadRef"
            accept=".xlsx, .xls"
            :action="excelUploadPath"
            :limit="1"
            :on-success="excelSuccess"
            :on-remove="removeExcel"
            :before-upload="beforeUpload"
          >
            <template #trigger>
              <el-button class="mx-10" type="primary">选择文件</el-button>
            </template>
            <span class="fs-13 td-none">文件限EXCEL格式，大小不超过5M</span>
          </el-upload>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import { batchSaveSeoJobWiki } from '/@/api/seo'

const emits = defineEmits(['update:modelValue', 'update:keyword'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  id: {
    type: String,
    default: ''
  }
})

const excelUploadPath = '/seo-job-wiki/upload-batch-save-excel'
// const excelUploadPath = 'https://dev.admin.gcjob.jugaocai.com/seo-job-wiki/upload-batch-save-excel'

const keywordRef = ref<InstanceType<typeof ElForm>>()

const uploadRef = ref({})

const formData = ref({
  filePath: ''
})

const dialogVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const beforeUpload = (file) => {
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件！')
  }
  return isExcel
}

const excelSuccess = (res: any) => {
  if (res.result === 0) return
  formData.value.filePath = res.data.url
}
const removeExcel = () => {
  formData.value.filePath = ''
}

const handleEditSubmit = () => {
  keywordRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await batchSaveSeoJobWiki(formData.value)
      emits('update')

      dialogVisible.value = false
    }
  })
}

const rules = ref({
  filePath: [
    {
      required: true,
      message: '请先上传文件',
      trigger: 'blur'
    }
  ]
})

const handleCloseDialog = () => {
  keywordRef.value?.resetFields()
  uploadRef.value.clearFiles()
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped></style>
