<template>
  <el-dialog
    v-model="editDialogVisible"
    :close-on-click-modal="false"
    :title="`${titleText}百科词`"
    width="600px"
    @open="open"
    @close="handleCloseDialog"
  >
    <el-form ref="keywordRef" :rules="keywordRules" :model="formData">
      <el-form-item label="职位名称" prop="keyword">
        <el-input v-model="formData.keyword" maxlength="80" />
      </el-form-item>

      <el-form-item label="职位简介" prop="jobIntroduction">
        <el-input type="textarea" v-model="formData.jobIntroduction" maxlength="800" />
      </el-form-item>

      <el-form-item label="职位内容" prop="jobContent">
        <el-input type="textarea" v-model="formData.jobContent" maxlength="2000" />
      </el-form-item>

      <el-form-item label="跳转链接" prop="jumpLink">
        <el-input v-model="formData.jumpLink" />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { saveSeoJobWiki, getSeoJobWikiDetail } from '/@/api/seo'

const emits = defineEmits(['update:modelValue', 'update:keyword'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  keyword: {
    type: String,
    default: ''
  },

  id: {
    type: String,
    default: ''
  }
})

const keywordRef = ref({})

const titleText = computed({
  get() {
    return props.id ? '编辑' : '创建'
  },
  set() {}
})

const keyword = computed({
  get() {
    return props.keyword
  },
  set(val: boolean) {
    emits('update:keyword', val)
  }
})

const formData = ref({
  id: props.id,
  keyword: keyword.value,
  jobIntroduction: '',
  jobContent: '',
  jumpLink: ''
})

const getDetail = async () => {
  const res = await getSeoJobWikiDetail({ id: props.id })
  formData.value = res
}

const open = () => {
  if (props.id) {
    getDetail()
  }
}

const editDialogVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const handleEditSubmit = () => {
  keywordRef.value.validate(async (valid: boolean) => {
    if (valid) {
      await saveSeoJobWiki(formData.value)
      emits('update')

      editDialogVisible.value = false
    }
  })
}

const validateEditKeywords = (rule, value, callback) => {
  if (value === '') {
    callback('请录入职位名称')
    return
  }

  callback()
}

const validateJumpLink = (rule, value, callback) => {
  // if (value === '') {
  //   callback('请录入正确的链接')
  //   return
  // }

  callback()
}

const keywordRules = ref({
  keyword: [
    {
      required: true,
      trigger: 'blur',
      validator: validateEditKeywords
    }
  ],
  jumpLink: [
    {
      trigger: 'blur',
      validator: validateJumpLink
    }
  ]
})

const handleCloseDialog = () => {
  keywordRef.value?.resetFields()
  formData.value.id = ''
  editDialogVisible.value = false
}
</script>

<style lang="scss" scoped></style>
