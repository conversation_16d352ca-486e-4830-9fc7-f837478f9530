<template>
  <el-dialog v-model="editDialogVisible" title="编辑关键词" width="30%" @close="handleCloseDialog">
    <el-form ref="keywordItem" :rules="keywordRules" :model="formData">
      <el-form-item prop="keyword">
        <el-input v-model="keyword" />
      </el-form-item>
    </el-form>

    <div class="flex">
      <el-button @click="handleCloseDialog">取消</el-button>
      <el-button type="primary" @click="handleEditSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { editKeywords } from '/@/api/seo'

const emits = defineEmits(['update:modelValue', 'update:keyword'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  keyword: {
    type: String,
    default: ''
  },

  id: {
    type: String,
    default: ''
  }
})

const keywordItem = ref({})

const keyword = computed({
  get() {
    return props.keyword
  },
  set(val: boolean) {
    emits('update:keyword', val)
  }
})

const formData = ref({
  keyword: keyword.value
})

const editDialogVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const handleEditSubmit = () => {
  keywordItem.value.validate(async (valid: boolean) => {
    if (valid) {
      await editKeywords({ id: props.id, keyword: keyword.value })
      emits('update')

      editDialogVisible.value = false
    }
  })
}

const validateEditKeywords = (rule, value, callback) => {
  if (keyword.value === '') {
    callback('请录入关键词')
    return
  }

  if (keyword.value.split('').length > 50) {
    callback('限录入50个字符')
    return
  }

  callback()
}

const keywordRules = ref({
  keyword: [
    {
      trigger: 'blur',
      validator: validateEditKeywords
    }
  ]
})

const handleCloseDialog = () => {
  keywordItem.value.clearValidate()
  editDialogVisible.value = false
}
</script>

<style lang="scss" scoped></style>
