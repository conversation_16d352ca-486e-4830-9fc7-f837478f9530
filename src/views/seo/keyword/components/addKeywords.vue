<template>
  <el-dialog v-model="addDialogVisible" @close="handleDialogClose">
    <div class="mb-20">关键词（每行一个）</div>
    <el-input type="textarea" v-model="keyword" :rows="20" />
    <el-button class="mt-20" type="primary" @click="handleSubmit">确定</el-button>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { addKeywords } from '/@/api/seo'
import { ElMessage } from 'element-plus'

const emits = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const addDialogVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const keyword = ref('')

const handleDialogClose = () => {
  keyword.value = ''
  addDialogVisible.value = false
}

const handleSubmit = async () => {
  if (keyword.value === '') {
    ElMessage.error('请录入关键词!')
    return
  }
  await addKeywords({ keyword: keyword.value })

  emits('update')

  handleDialogClose()
}
</script>

<style lang="scss" scoped></style>
