<template>
  <div class="main">
    <el-card>
      <el-tabs v-model="type" @tab-change="handleTagChange">
        <el-tab-pane label="百度推送" :name="1"></el-tab-pane>
        <el-tab-pane label="indexnow" :name="2"></el-tab-pane>
      </el-tabs>

      <div class="formbox">
        <el-form
          ref="form"
          :model="formData"
          label-width="80px"
          class="demo-form-inline"
          :inline="true"
        >
          <el-form-item label="时间" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="daterange"
              value-format="YYYY-MM-DD"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>

          <el-form-item label="发送类型" prop="wayType">
            <el-select v-model="formData.wayType" clearable filterable>
              <el-option v-for="item in wayTypeList" :key="item.k" :label="item.v" :value="item.k">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="域名" prop="urlType">
            <el-select v-model="formData.urlType" clearable filterable>
              <el-option v-for="item in urlTypeList" :key="item.k" :label="item.v" :value="item.k">
              </el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="单位检索" prop="name">
            <el-input v-model="formData.name" placeholder="请填写单位名称或者ID"></el-input>
          </el-form-item> -->
          <el-form-item class="buttomBox">
            <el-button type="primary" @click="getList()">搜索</el-button>
            <el-button type="default" @click="resetForm()">重置</el-button>
            <el-button type="default" @click="showSendForm()">手动发送</el-button>
            <!-- <el-button type="default" @click="downloadExcel()">下载</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <el-descriptions border>
        <el-descriptions-item label="昨天数据"
          ><p v-html="yesterdayDescribe"></p
        ></el-descriptions-item>
        <el-descriptions-item label="今天数据"><p v-html="todayDescribe"></p></el-descriptions-item>
      </el-descriptions>
      <br />
      <div class="showTable" v-loading="listLoading">
        <el-dialog v-model="detailDialogVisible">
          <el-descriptions border column="1">
            <el-descriptions-item label="成功域名"
              ><p v-html="detail.sendUrl"></p
            ></el-descriptions-item>
            <el-descriptions-item label="失败域名">{{ detail.failUrl }}</el-descriptions-item>
            <el-descriptions-item label="百度返回信息">
              {{ detail.returnLog }}</el-descriptions-item
            >
          </el-descriptions>
          <template #footer>
            <span class="dialog-footer">
              <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
            </span>
          </template>
        </el-dialog>
        <el-dialog v-model="sendDialogVisible" :before-close="handleClose">
          <el-form :model="sendForm" label-width="120px">
            <el-form-item label="域名">
              <el-radio-group v-model="sendForm.urlType">
                <el-radio v-for="item in urlTypeList" :label="item.k" :key="item.k">{{
                  item.v
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="链接(每行一个)">
              <el-input v-model="sendForm.urls" type="textarea" :rows="20" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmitSend">确定</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>

        <el-table :data="tableData" border style="width: 100%" align="center">
          <el-table-column prop="addTime" label="时间" align="center" />
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column prop="wayTxt" label="发送类型" align="center" />
          <el-table-column prop="urlTxt" label="域名" align="center" />
          <el-table-column prop="sendCount" label="发送数量" align="center" />
          <el-table-column prop="successCount" label="成功数量" align="center" />
          <el-table-column prop="failCount" label="失败数量" align="center" />
          <el-table-column prop="statusCompany" label="操作" align="center">
            <template #default="scope">
              <el-button type="success" @click="getDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="mt-20">
        <Paging :total="pagination.total" @change="change"></Paging>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
import { ElMessageBox } from 'element-plus'
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue'
import {
  getBaiduZZPushList,
  getBaiduZZPushDetail,
  getParams,
  baiduZZPush,
  getBaiduZzPushDaily
} from '/@/api/seo'
import Paging from '/@/components/base/paging.vue'

export default defineComponent({
  name: 'seoBaiduZZPushList',
  components: { Paging },
  setup() {
    const form = ref()
    const state = reactive({
      type: 1,

      formData: {
        wayType: '',
        urlType: '',
        date: '',
        page: 1,
        pageSize: 20
      },
      tableData: [],
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      detailDialogVisible: false,
      listLoading: false,
      sendDialogVisible: false,
      detail: {
        addTime: '',
        sendUrl: [],
        returnLog: '',
        failUrl: []
      },
      sendForm: {
        urlType: '1',
        urls: ''
      },
      urlTypeList: [],
      wayTypeList: [],
      yesterdayDescribe: '',
      todayDescribe: ''
    })

    const getList = async () => {
      state.listLoading = true
      const query = { ...state.formData, type: state.type }
      const rs = await getBaiduZZPushList(query)
      state.listLoading = false
      state.tableData = rs.list
      state.pagination.total = rs.pages.count
    }

    const change = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getList()
    }
    const getDetail = async (row: any) => {
      const r = await getBaiduZZPushDetail(row.id)
      state.detail.sendUrl = r.sendUrl.join('<br>')
      state.detail.returnLog = r.returnLog
      state.detail.failUrl = r.failUrl ? r.failUrl.join('<br>') : ''

      state.detailDialogVisible = true
    }

    const resetForm = () => {
      form.value.resetFields()
      getList()
    }

    const showSendForm = () => {
      state.sendForm.urlType = '1'
      state.sendForm.urls = ''
      state.sendDialogVisible = true
    }

    const handleClose = (done: () => void) => {
      ElMessageBox.confirm('确定关闭?')
        .then(() => {
          done()
        })
        .catch(() => {
          // catch error
        })
    }

    const downloadExcel = () => {}

    const onSubmitSend = () => {
      const postData = { ...state.sendForm, type: state.type }
      baiduZZPush(postData).then((r) => {
        state.sendDialogVisible = false
        ElMessageBox.alert(r, '发送成功', {
          confirmButtonText: 'OK',
          callback: () => {
            resetForm()
          }
        })
      })
    }

    const getPushDaily = () => {
      getBaiduZzPushDaily({ type: state.type }).then((r) => {
        const { yesterday } = r
        const { today } = r
        state.yesterdayDescribe = `推送总数：${yesterday.total.total}，成功数：${yesterday.total.successTotal}，失败数：${yesterday.total.failTotal} <br> pc推送总数：${yesterday.pc.total}，成功数：${yesterday.pc.successTotal}，失败数：${yesterday.pc.failTotal} <br> h5推送总数：${yesterday.h5.total}，成功数：${yesterday.h5.successTotal}，失败数：${yesterday.h5.failTotal}`
        state.todayDescribe = `推送总数：${today.total.total}，成功数：${today.total.successTotal}，失败数：${today.total.failTotal} <br> pc推送总数：${today.pc.total}，成功数：${today.pc.successTotal}，失败数：${today.pc.failTotal} <br> h5推送总数：${today.h5.total}，成功数：${today.h5.successTotal}，失败数：${today.h5.failTotal}`
      })
    }

    const handleTagChange = () => {
      getList()
      getPushDaily()
    }

    onMounted(() => {
      getParams().then((rs) => {
        state.urlTypeList = rs.urlTypeList
        state.wayTypeList = rs.wayTypeList
      })
      getPushDaily()
      getList()
    })

    return {
      ...toRefs(state),
      handleTagChange,
      form,
      getList,
      change,
      getDetail,
      resetForm,
      showSendForm,
      onSubmitSend,
      handleClose,
      downloadExcel
    }
  }
})
</script>
