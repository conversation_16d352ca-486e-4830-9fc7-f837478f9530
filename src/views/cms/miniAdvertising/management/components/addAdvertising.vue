<template>
  <el-dialog top="5vh" v-model="visible" :title="title" width="912px" @close="handleCancle">
    <div v-loading="loading">
      <el-form
        ref="form"
        :model="formData"
        :rules="formRules"
        class="mt-15 pr-50"
        label-width="90px"
      >
        <el-form-item prop="packingId" label="关联维护" v-if="formData.packingId">
          -----------广告位关联维护-----------
        </el-form-item>
        <el-form-item prop="type" label="广告位类型">
          <el-checkbox-group v-model="formData.type">
            <el-checkbox label="1" name="type">付费</el-checkbox>
            <el-checkbox label="2" name="type">RPO</el-checkbox>
            <el-checkbox label="3" name="type">异议</el-checkbox>
            <el-checkbox label="4" name="type">客情</el-checkbox>
            <el-checkbox label="5" name="type">推广</el-checkbox>
            <el-checkbox label="6" name="type">高级</el-checkbox>
            <el-checkbox label="7" name="type">旧链</el-checkbox>
            <el-checkbox label="8" name="type">免费</el-checkbox>
            <el-checkbox label="9" name="type">其他</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="type" label="广告位标签">
          <el-checkbox-group v-model="tags">
            <el-checkbox
              @change="(value) => tagsChange(value, item.homePositionIds)"
              v-for="item in tagList"
              :key="item.tagId"
              :label="item.name"
            >
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="homePositionId" label="广告位置">
          <AdPositionList
            v-model="formData.homePositionId"
            :multiple="true"
            :showAllLevels="false"
          />
        </el-form-item>

        <el-form-item prop="title" label="广告标题">
          <el-input v-model="formData.title" placeholder=""></el-input>
        </el-form-item>

        <el-form-item prop="subTitle" label="副标题">
          <el-input v-model="formData.subTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item prop="secondTitle" label="次标题">
          <el-input v-model="formData.secondTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item class="ai-center" prop="imageUrl" label="图片地址">
          <el-input
            @blur="handleImageInputBlur"
            class="flex-1"
            v-model="formData.imageUrl"
            clearable
            placeholder=""
          ></el-input>
          <el-image
            v-if="showImagePreview"
            class="preview"
            style="width: 100px; height: 100px"
            :src="formData.imageUrl"
            :preview-src-list="[formData.imageUrl]"
            :initial-index="4"
            fit="contain"
          />
        </el-form-item>
        <el-form-item prop="imageAlt" label="上传">
          <el-upload
            class="avatar-uploader"
            action="/upload/image"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <el-button type="primary">上传</el-button>
            &nbsp;&nbsp;
            <p class="logo">支持JPG/PNG图片格式，文件小于5M</p>
          </el-upload>
        </el-form-item>
        <el-form-item prop="imageAlt" label="图片说明文字">
          <el-input v-model="formData.imageAlt" placeholder="请填写图片文字说明"></el-input>
        </el-form-item>

        <el-form-item prop="onlineTime" label="生效时间">
          <DatePicker v-model="formData.onlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>
        <el-form-item prop="offlineTime" label="失效时间">
          <DatePicker v-model="formData.offlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>

        <!-- 里面的都在一行-->
        <el-form-item prop="targetLinkType" label="跳转类型">
          <el-row :gutter="10" style="flex: 1">
            <el-col :span="8">
              <el-select
                v-model="formData.targetLinkType"
                placeholder="请选择"
                @change="setTargetLinkType"
              >
                <el-option label="请选择" value="0"></el-option>
                <el-option
                  v-for="item in showcaseTargetLinkTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="8" v-show="showLinkPageLinkType">
              <el-select
                v-model="formData.pageLinkType"
                placeholder="请选择"
                @change="setPageLinkType"
              >
                <el-option label="请选择" value="0"></el-option>
                <el-option
                  v-for="item in showcasePageLinkTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
            </el-col>

            <el-col :span="8" v-show="showLinkItemInThisItem">
              <el-input v-model="formData.targetLink" :placeholder="targetLinkDesc"></el-input>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item prop="targetLink" :label="targetLinkLabel" v-show="showLinkItemInNextItem">
          <el-input v-model="formData.targetLink" :placeholder="targetLinkDesc"></el-input>
        </el-form-item>
        <!-- <el-form-item label="关联位置">
          <div class="flex">
            <div class="flex-1">
              <el-form-item prop="contactHomePositionId" label-width="0px">
                <el-input v-model="formData.contactHomePositionId" placeholder=""></el-input>
              </el-form-item>
            </div>
            <div class="flex-1">
              <el-form-item prop="contactHomePositionSort" label-width="15px">
                <el-input v-model="formData.contactHomePositionSort" placeholder="排序"></el-input>
              </el-form-item>
            </div>
          </div>
        </el-form-item> -->
        <el-form-item prop="sort" label="广告排序">
          <el-input v-model="formData.sort" placeholder="数字越大越靠前，不填默认”0“"></el-input>
        </el-form-item>
        <el-form-item prop="describe" label="广告描述">
          <el-input
            v-model="formData.describe"
            type="textarea"
            rows="3"
            resize="none"
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button :loading="submitLoading" @click="submit" type="primary">保存</el-button>
          <el-button plain @click="handleCancle">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, ref, onMounted, toRefs, nextTick, watch } from 'vue'
import DatePicker from '/@/components/base/datePicker.vue'
import AdPositionList from '/@select/adMiniPosition.vue'

import {
  addShowcase,
  getShowcaseDetails,
  editShowcase,
  getPositionRelationTag
} from '/@/api/advertising'
import { ElMessage } from 'element-plus'
import { getShowcaseTargetLinkTypeList, getShowcasePageLinkTypeList } from '/@/api/config'

export default {
  components: { DatePicker, AdPositionList },
  name: 'cmsAdvertisingAdd',
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      loading: false,
      title: '新增广告',
      showImagePreview: false,
      submitLoading: false,
      showLinkPageLinkType: false,
      showLinkItemInNextItem: false,
      showLinkItemInThisItem: false,
      targetLinkDesc: '',
      targetLinkLabel: '',
      formData: <any>{
        id: '',
        homePositionId: [],
        title: '',
        subTitle: '',
        secondTitle: '',
        imageUrl: '',
        imageLink: '',
        imageAlt: '',
        onlineTime: '',
        offlineTime: '',
        targetLink: '',
        contactHomePositionId: '',
        targetLinkType: '',
        pageLinkType: '',
        contactHomePositionSort: '',
        sort: '',
        packingId: '',
        describe: '',
        type: []
      },
      formRules: {
        homePositionId: [{ required: true, message: '请选择广告位置', trigger: 'change' }],
        title: [{ required: true, message: '请输入广告标题', trigger: 'blur' }],
        onlineTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
        offlineTime: [{ required: true, message: '请选择失效时间', trigger: 'change' }]
      },
      imageUrl: '',
      showcaseTargetLinkTypeList: [],
      allShowcasePageLinkTypeList: [],
      showcasePageLinkTypeList: [],

      tagList: [],
      tags: []
    })

    const setTargetLinkType = (value) => {
      // 清空url
      state.formData.targetLink = ''

      switch (value) {
        case '1':
          // 站内指定页面
          state.showLinkPageLinkType = true
          state.showcasePageLinkTypeList = state.allShowcasePageLinkTypeList.filter((item: any) => {
            return item.type === 1
          })
          // 默认选上第一个
          state.formData.pageLinkType = '0'
          state.showLinkItemInThisItem = false
          state.showLinkItemInNextItem = false
          break
        case '2':
          // 网页
          state.formData.pageLinkType = '0'
          state.showLinkPageLinkType = false
          state.showLinkItemInThisItem = false
          state.showLinkItemInNextItem = true
          state.targetLinkDesc = '请以https://开头'
          state.targetLinkLabel = '链接地址'

          break
        case '3':
          // 视频号
          state.showLinkPageLinkType = true
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = false
          state.showcasePageLinkTypeList = state.allShowcasePageLinkTypeList.filter((item: any) => {
            return item.type === 3
          })
          // 默认选上第一个
          if (!state.formData.pageLinkType) {
            state.formData.pageLinkType = '7'
          }
          break
        case '4':
          // 小程序
          state.formData.pageLinkType = '0'
          state.showLinkPageLinkType = false
          state.showLinkItemInThisItem = false
          state.showLinkItemInNextItem = true
          state.targetLinkDesc = '请输入'
          state.targetLinkLabel = '小程序链接'
          break
        default:
          state.showLinkPageLinkType = false
          state.showLinkItemInThisItem = false
          state.showLinkItemInNextItem = false
          console.log(value)
          break
      }
    }

    const setPageLinkType = (value) => {
      state.formData.targetLink = ''
      state.formData.pageLinkType = value
      switch (value) {
        case '1':
          // 职位详情
          state.showLinkItemInThisItem = true
          state.showLinkItemInNextItem = false
          state.targetLinkDesc = '职位ID'
          break
        case '2':
          // 公告详情
          state.showLinkItemInThisItem = true
          state.showLinkItemInNextItem = false
          state.targetLinkDesc = '公告ID'
          break
        case '3':
          // 单位详情
          state.showLinkItemInThisItem = true
          state.showLinkItemInNextItem = false
          state.targetLinkDesc = '单位ID'
          break
        case '4':
          // 职位搜索结果页
          state.showLinkItemInThisItem = true
          state.showLinkItemInNextItem = false
          state.targetLinkDesc = '请输入带搜索词PC页面链接'
          break
        case '5':
          // 公告搜索结果页
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = true
          state.targetLinkDesc = '请输入带搜索词PC页面链接'
          break
        case '6':
          // 单位搜索结果页
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = true
          state.targetLinkDesc = '请输入带搜索词PC页面链接'
          break
        case '7':
          // 视频号主页
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = false
          break
        case '8':
          // 视频号ID
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = true
          state.targetLinkDesc = '视频id'
          break
        case '9':
          // 活动详情
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = true
          state.targetLinkDesc = '活动id'
          break
        case '10':
          // 专场详情
          state.showLinkItemInNextItem = false
          state.showLinkItemInThisItem = true
          state.targetLinkDesc = '专场id'
          break
        default:
          break
      }
    }

    watch(
      () => state.formData.imageUrl,
      (value) => {
        state.formData.imageLink = value
      }
    )

    // 监听跳转类型
    // watch(
    //   () => state.formData.targetLinkType,
    //   (value) => {
    //     setTargetLinkType(value)
    //   }
    // )
    //
    // watch(
    //   () => state.formData.pageLinkType,
    //   (value) => {
    //     setPageLinkType(value)
    //   }
    // )

    const getTag = () => {
      getPositionRelationTag().then((resp) => {
        state.tagList = resp
      })
    }

    const tagsChange = (flag: any, ids: any) => {
      const { homePositionId } = state.formData
      const arr = [...homePositionId]

      if (flag) {
        ids.forEach((item: any) => {
          if (!arr.includes(item)) {
            arr.push(item)
          }
        })
      } else {
        ids.forEach((item: any) => {
          const index = arr.indexOf(item)
          if (index === -1) return
          arr.splice(index, 1)
        })
      }
      state.formData.homePositionId = arr
    }

    const getDetails = async () => {
      state.loading = true
      await getShowcaseDetails({ id: state.formData.id }).then((resp: any) => {
        const { targetLink, pageLinkType } = resp
        state.formData = resp

        setTargetLinkType(resp.targetLinkType)
        setPageLinkType(pageLinkType)
        state.formData.targetLink = targetLink

        const { imageUrl } = resp
        state.showImagePreview = !!imageUrl

        state.loading = false
      })
    }

    // 获取后台数据类型
    onMounted(() => {
      getTag()

      getShowcaseTargetLinkTypeList()
        .then((resp: any) => {
          state.showcaseTargetLinkTypeList = resp
        })
        .catch(() => {
          state.showcaseTargetLinkTypeList = []
        })
      getShowcasePageLinkTypeList()
        .then((resp: any) => {
          state.allShowcasePageLinkTypeList = resp
        })
        .catch(() => {
          state.showcasePageLinkTypeList = []
        })
    })

    const open = async (id: string, string = '') => {
      state.title = id ? '编辑广告' : '新增广告'
      if (id) {
        state.formData.id = id
        await getDetails()
        if (string === 'copy') {
          state.title = '复制广告'
          state.formData.id = ''
          state.formData.homePositionId = []
          state.formData.describe = ''
        }
      }
      state.visible = true
      state.tags = []
    }

    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    // 重置
    const resetForm = () => {
      state.formData = {}
      nextTick(() => {
        form.value.clearValidate()
      })
    }

    const handleCancle = () => {
      resetForm()
      state.visible = false
      state.showImagePreview = false
    }
    const submit = () => {
      form.value.validate(async (valid: any) => {
        if (valid) {
          state.submitLoading = true
          const typeString = state.formData.type ? state.formData.type.join() : ''
          const { id, ...data } = state.formData
          if (!id) {
            await addShowcase({ ...data, typeString }).catch(() => {
              state.submitLoading = false
            })
          } else {
            await editShowcase({ ...state.formData, typeString }).catch(() => {
              state.submitLoading = false
            })
          }
          state.submitLoading = false
          emit('update', !!id)
          resetForm()
          state.visible = false
        }
      })
    }

    // 略缩图上传
    const beforeImageUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }
    const handleImageSuccess = (res: any, file: any) => {
      state.formData.imageUrl = res.data.fullUrl
      state.showImagePreview = true
    }

    const handleImageInputBlur = () => {
      const { imageUrl } = state.formData
      state.showImagePreview = !!imageUrl
    }

    return {
      form,
      open,
      submit,
      tagsChange,
      handleDisabledDate,
      handleCancle,
      handleImageInputBlur,
      beforeImageUpload,
      handleImageSuccess,
      setTargetLinkType,
      setPageLinkType,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
.preview {
  width: 100px;
  height: 100px;
  margin-left: 15px;
  border-radius: 4px;
  border: var(--el-input-border, var(--el-border-base));
}
</style>
