<template>
  <div class="formbigbox">
    <el-form ref="form" :model="formData" label-width="80px">
      <div class="flex">
        <el-form-item class="span-6" label="广告位名称" prop="name">
          <el-input
            class="block"
            v-model="formData.name"
            placeholder="请填写单位名称"
            clearable
            @keyup.enter="search"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-6" label="所属平台" prop="platformType">
          <AdPlatform v-model="formData.platformType" />
        </el-form-item>
        <el-form-item class="span-6" label="创建人" prop="creator">
          <el-input
            v-model="formData.creator"
            placeholder="请填写创建人"
            clearable
            @keyup.enter="search"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-6" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            :start="formData.addTimeStart"
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-6" label="显示状态" prop="isShow">
          <AdShowStatus v-model="formData.isShow" />
        </el-form-item>
        <el-form-item class="span-6" label-width="10px">
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button type="default" @click="resetForm()">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div class="mb-15">
      <el-button @click="openAddDialog()" type="primary">+ 新增广告位</el-button>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      border
      size="small"
      v-loading="listLoading"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" header-align="center" width="35" />
      <el-table-column
        prop="number"
        label="位置编号"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="platformTypeTitle"
        label="所属平台"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="name"
        label="广告位名称"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column label="版位尺寸" align="center" header-align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.width }}*{{ row.height }}px</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="showcaseCount"
        label="广告数"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column label="显示状态" align="center" header-align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            {{ row.status == '1' ? '显示' : '隐藏' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="describe"
        label="版位描述"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="sort" label="排序" align="center" header-align="center">
        <template #default="{ row }">
          <div>
            <div v-show="row.id === sortEditId">
              <el-input
                :input-style="{ 'text-align': 'center' }"
                v-model.trim="row.sort"
                @keydown.enter="handleSort(row.id, row.sort)"
              ></el-input>
            </div>
            <span v-show="row.id !== sortEditId">{{ row.sort }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="creator"
        label="创建人"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="addTime"
        label="创建时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="name"
        label="操作"
        align="center"
        header-align="center"
        min-width="200px"
      >
        <template #default="{ row }">
          <div>
            <!-- <el-link type="primary" class="fs-13" :underline="false">预览{{ row.id }}</el-link> -->
            &nbsp;
            <!-- <el-link type="primary" class="fs-13" :underline="false">调用代码</el-link>&nbsp; -->
            <el-link
              @click="handleOpenAdManagement(row.id)"
              type="primary"
              class="fs-13"
              :underline="false"
              >广告列表</el-link
            >&nbsp;
            <el-link @click="openAddDialog(row.id)" type="primary" class="fs-13" :underline="false"
              >编辑</el-link
            >&nbsp;
            <el-link @click="handleDelete(row.id)" type="primary" class="fs-13" :underline="false"
              >删除</el-link
            >&nbsp;
            <!-- <el-popover
              @after-enter="handleShowEcharts"
              placement="left"
              :width="550"
              trigger="click"
            >
              <template #reference>
                <el-link type="primary" class="fs-13" :underline="false">统计</el-link>
              </template>
              <div>
                <div class="jc-between">
                  <div class="as-center fs-12 fw-bold">时间:</div>
                  <el-link :underline="false" class="as-center fs-12">近7天</el-link>
                  <el-link :underline="false" class="as-center fs-12">近30天</el-link>
                  <el-link :underline="false" class="as-center fs-12">近6个月</el-link>
                  <div class="w-200">
                    <DatePickerRange
                      :start="formData.addTimeStart"
                      v-model:start="formData.addTimeStart"
                      v-model:end="formData.addTimeEnd"
                    />
                  </div>
                  <el-button @click="handleLookTrend"  type="warning">查询</el-button>
                </div>
                <div style="height: 250px" ref="myStatistical"></div>
              </div>
            </el-popover> -->
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty></el-empty>
      </template>
    </el-table>
    <div v-if="tableData.length" class="mt-15 jc-between">
      <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          label="全选"
          class="mr-10"
        ></el-checkbox>
        <el-select
          clearable
          :disabled="!multipleSelection.length"
          v-model="batchValue"
          placeholder="批量操作"
          @change="handleBatchChange"
        >
          <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k">
          </el-option>
        </el-select>
      </div>
      <Paging :total="pages.total" @change="changePage"></Paging>
    </div>
    <Add @update="search" ref="add" />
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { reactive, ref, onMounted, toRefs, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { debounce } from 'throttle-debounce'

import Paging from '/@/components/base/paging.vue'
import Add from './components/addAdvertisingPosition.vue'
import AdPlatform from '/@select/adPlatform.vue'
import AdShowStatus from '/@select/adShowStatus.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

import {
  getAdPositionList,
  deleteHomePosition,
  changeHomePositionSort,
  changeHomePositionShow
} from '/@/api/advertising'

export default {
  components: { Paging, Add, AdShowStatus, AdPlatform, DatePickerRange },
  name: 'cmsAdvertisingList',
  setup() {
    const add = ref()
    const form = ref()
    const table = ref()
    const myStatistical = ref()
    const router = useRouter()
    const state = reactive({
      formData: {
        name: '',
        platformType: '',
        creator: '',
        addTimeStart: '',
        addTimeEnd: '',
        isShow: '',

        page: 1,
        limit: 20
      },

      multipleSelection: [],
      batchValue: '',
      batchOptions: [
        { k: 1, v: '批量显示' },
        { k: 2, v: '批量隐藏' },
        { k: -1, v: '批量删除' }
      ],
      checkedAll: false,

      sortEditId: '',
      tableData: [
        // {
        //   id: 1,
        //   sort: 3
        // },
        // {
        //   id: 2,
        //   sort: 5
        // }
      ],
      listLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      }
    })

    const initEcharts = () => {
      const myChart = echarts.init(myStatistical.value)
      const option = {
        grid: {
          top: 20,
          right: 20,
          bottom: 30,
          left: 40
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          boundaryGap: false,
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ]
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 4
          }
        ],
        series: [
          {
            name: '点击量',
            type: 'line',
            data: [50, 85, 22, 155, 170, 25, 224, 245, 285, 300, 415, 641],
            itemStyle: {
              color: '#febb50'
            }
          }
        ]
      }
      myChart.setOption(option)
    }

    const search = () => {
      state.listLoading = true
      getAdPositionList(state.formData).then((resp: any) => {
        state.tableData = resp.list
        state.pages.total = resp.page.count
        state.listLoading = false
      })
    }

    // 获取后台数据类型
    onMounted(() => {
      search()
    })

    const openAddDialog = (id: string = '') => {
      add.value.open(id)
    }

    const handleChange = () => {
      table.value.toggleAllSelection()
    }
    const handleOpenAdManagement = (id: string) => {
      console.log(id)
      router.push({
        path: '/cms/advertisingList',
        query: {
          positionId: id
        }
      })
      // ({
      //   path: '/cms/advertisingList',
      //   params: {
      //     queryId: id
      //   }
      // })
    }

    const handleSelectionChange = (val) => {
      state.multipleSelection = val
      if (val.length === state.tableData.length) {
        state.checkedAll = true
      } else {
        state.checkedAll = false
      }
      if (!val.length) {
        state.batchValue = ''
      }
    }

    // 鼠标移入单元格时
    const handleCellMouseEnter = (row, column) => {
      if (column.label === '排序') {
        state.sortEditId = row.id
      }
    }
    // 鼠标移出单元格时
    const handleCellMouseLeave = () => {
      state.sortEditId = ''
    }
    const changeSort = (data: any) => {
      changeHomePositionSort(data)
    }
    // 添加防抖
    const debounceFn = debounce(3000, true, changeSort)
    // 修改排序
    const handleSort = (id, sort) => {
      debounceFn({ id, sort })
    }

    const handleShowEcharts = () => {
      initEcharts()
    }

    const handleDelete = (id: string, batch = false) => {
      ElMessageBox({
        title: '提示',
        message: batch ? '确定批量删除广告吗？' : '确定删除该广告吗？',
        center: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            // eslint-disable-next-line no-param-reassign
            instance.confirmButtonLoading = true
            await deleteHomePosition({ id })
            search()
            done()
          } else {
            done()
          }
        }
      })
    }

    const handleLookTrend = () => {}

    // 重置
    const resetForm = () => {
      form.value.resetFields()
      nextTick(() => {
        search()
      })
    }

    const handleBatchChange = async (val: any) => {
      if (!val) return
      const ids = state.multipleSelection.map((item: any) => item.id).join(',')
      if (val === -1) {
        handleDelete(ids, true)
      } else {
        await changeHomePositionShow({ id: ids, status: val })
        search()
      }
    }

    const changePage = (r: any) => {
      state.formData.page = r.page
      state.formData.limit = r.limit
      search()
    }

    return {
      form,
      add,
      table,
      myStatistical,
      openAddDialog,
      search,
      resetForm,
      changePage,
      handleCellMouseEnter,
      handleCellMouseLeave,
      handleSort,
      handleShowEcharts,
      handleLookTrend,
      handleBatchChange,
      handleSelectionChange,
      handleChange,
      handleDelete,
      handleOpenAdManagement,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}
</style>
