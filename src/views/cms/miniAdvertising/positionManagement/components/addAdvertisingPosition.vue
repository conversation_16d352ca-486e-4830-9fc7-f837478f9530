<template>
  <el-dialog
    top="12vh"
    :close-on-click-modal="false"
    v-model="visible"
    :title="title"
    width="650px"
  >
    <div v-loading="loading">
      <el-form
        ref="form"
        :model="formData"
        :rules="formRules"
        class="mt-15 pr-50"
        label-width="80px"
      >
        <el-form-item prop="platformType" label="所属平台">
          <AdPlatform v-model="formData.platformType" />
        </el-form-item>
        <el-form-item prop="number" label="位置编号">
          <el-input v-model="formData.number" placeholder=""></el-input>
        </el-form-item>
        <el-form-item prop="name" label="位置名称">
          <el-input v-model="formData.name" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="版位尺寸">
          <div class="flex">
            <el-form-item class="span-2" prop="width" label-width="0px">
              <div class="flex">
                宽:
                <div class="flex-1">
                  <el-input v-model="formData.width" placeholder=""></el-input>
                </div>
                &nbsp; px&nbsp;
              </div>
            </el-form-item>
            <el-form-item class="span-2" prop="height" label-width="0px">
              <div class="flex">
                &nbsp;&nbsp; 高:
                <div class="flex-1">
                  <el-input v-model="formData.height" placeholder=""></el-input>
                </div>
                &nbsp; px
              </div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item prop="status" label="是否显示">
          <el-radio-group v-model="formData.status">
            <el-radio label="1">显示</el-radio>
            <el-radio label="2">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="describe" label="广告位描述">
          <el-input
            v-model="formData.describe"
            type="textarea"
            rows="5"
            resize="none"
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="submit" type="primary">保存</el-button>
          <el-button plain @click="handleCancle">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, ref, onMounted, toRefs } from 'vue'
import AdPlatform from '/@select/adPlatform.vue'

import { getHomePositionDetails, addHomePosition, editHomePosition } from '/@/api/advertising'

export default {
  components: { AdPlatform },
  name: 'cmsAdvertisingAdd',
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      loading: false,
      submitLoading: false,
      title: '新增广告位',
      formData: {
        id: '',
        platformType: '',
        number: '',
        name: '',
        width: '',
        height: '',
        status: '1', // status,状态，1:显示；2:隐藏；9:删除
        describe: ''
      },
      formRules: {
        platformType: [{ required: true, message: '所属平台', trigger: 'blur' }],
        number: [{ required: true, message: '请填写位置编号', trigger: 'blur' }],
        name: [{ required: true, message: '请填写位置名称', trigger: 'blur' }],
        // width: [{ required: true, message: '请填写宽度', trigger: 'blur' }],
        // height: [{ required: true, message: '请填写高度', trigger: 'blur' }],
        status: [{ required: true, message: '是否显示', trigger: 'change' }]
      },
      imageUrl: ''
    })

    const getDetails = () => {
      state.loading = true
      getHomePositionDetails({ id: state.formData.id }).then((resp: any) => {
        const { id, platformType, number, name, width, height, status, describe } = resp
        state.formData = { id, platformType, number, name, width, height, status, describe }
        state.loading = false
      })
    }

    onMounted(() => {})

    const open = (id: string) => {
      if (id) {
        state.formData.id = id
        state.title = '编辑广告位'
        getDetails()
      }
      state.visible = true
    }

    // 重置
    const resetForm = () => {
      form.value.resetFields()
      setTimeout(() => {
        form.value.clearValidate()
      }, 10)
    }

    const handleCancle = () => {
      state.visible = false
      resetForm()
    }
    const submit = () => {
      form.value.validate(async (valid: any) => {
        state.submitLoading = true
        if (valid) {
          if (!state.formData.id) {
            Reflect.deleteProperty(state.formData, 'id')
            await addHomePosition(state.formData)
              .then((resp: any) => {
                console.log(resp)
              })
              .catch(() => {
                state.submitLoading = false
              })
          } else {
            await editHomePosition(state.formData)
              .then((resp: any) => {
                console.log(resp)
              })
              .catch(() => {
                state.submitLoading = false
              })
          }
          state.submitLoading = false
          state.visible = false
          emit('update')
          resetForm()
        }
      })
    }

    return {
      form,
      open,
      submit,
      handleCancle,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
.avatar-uploader-icon svg {
  margin-top: 74px; /* (178px - 28px) / 2 - 1px */
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
