<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="500px" :before-close="handleClose">
    <div class="pr-20 py-15">
      <!-- 批量编辑属性 -->
      <el-form
        v-show="operationType == 1"
        ref="attributeForm"
        label-width="110px"
        :model="attributeFormData"
      >
        <el-form-item label="文档属性：" prop="attribute">
          <el-checkbox-group v-model="attributeFormData.attribute">
            <el-checkbox v-for="item in attributeList" :label="item.value">{{
              item.label
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文档ID：" prop="newsIds">
          <el-input
            disabled
            v-model="attributeFormData.newsIds"
            placeholder="请填写资讯标题或编号"
          ></el-input>
        </el-form-item>
        <div class="center">
          <el-button
            @click="submit('attribute')"
            :disabled="!attributeFormData.attribute.length"
            class="w-100"
            type="primary"
            :loading="btnLoading"
            >确认提交</el-button
          >
        </div>
      </el-form>
      <!-- 批量复制文档 -->
      <el-form
        v-show="operationType == 2"
        ref="copyForm"
        label-width="110px"
        :model="copyFormData"
        :rules="copyFormDataRules"
      >
        <el-form-item label="复制到栏目：" prop="homeColumnId">
          <el-cascader
            clearable
            class="block"
            placeholder="请选择所属栏目"
            :show-all-levels="false"
            v-model="copyFormData.homeColumnId"
            :options="columnList"
            :props="{ value: 'k', label: 'v', emitPath: false }"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="每篇复制数量" prop="amount">
          <el-input
            @input="handleAmountInput"
            v-model="copyFormData.amount"
            placeholder="请填写复制数量"
          ></el-input>
        </el-form-item>

        <div class="mb-15 ml-20">
          <el-alert
            :closable="false"
            title="复制到的目标栏目页必须和当前模型类型一致，否则程序会自动忽略不符合的文档"
            type="warning"
          >
          </el-alert>
        </div>
        <div class="center">
          <el-button
            class="w-100"
            type="primary"
            :disabled="!copyFormData.amount || !copyFormData.homeColumnId"
            :loading="btnLoading"
            @click="submit('copy')"
            >确认提交</el-button
          >
        </div>
      </el-form>
      <!-- 批量移动文档 -->
      <el-form v-show="operationType == 3" ref="moveForm" label-width="110px" :model="moveFormData">
        <el-form-item label="复制到栏目：" prop="homeColumnId">
          <el-cascader
            clearable
            class="block"
            placeholder="请选择所属栏目"
            :show-all-levels="false"
            v-model="moveFormData.homeColumnId"
            :options="columnList"
            :props="{ value: 'k', label: 'v', emitPath: false }"
          ></el-cascader>
        </el-form-item>
        <div class="mb-15 ml-20">
          <el-alert
            :closable="false"
            title="复制到的目标栏目页必须和当前模型类型一致，否则程序会自动忽略不符合的文档"
            type="warning"
          >
          </el-alert>
        </div>
        <div class="center">
          <el-button
            :disabled="!moveFormData.homeColumnId"
            @click="submit('move')"
            class="w-100"
            type="primary"
            :loading="btnLoading"
            >确认提交</el-button
          >
        </div>
      </el-form>
      <!-- 批量审核文档 -->
      <el-form
        v-show="operationType == 5"
        ref="auditForm"
        label-width="100px"
        :model="auditFormData"
      >
        <el-form-item label="审核结果：" prop="title">
          <el-radio-group v-model="auditFormData.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="-1">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="center">
          <el-button @click="submit('audit')" class="w-100" type="primary" :loading="btnLoading"
            >确认提交</el-button
          >
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue'

import {
  getAttributeNewsList,
  newsChageAttribute,
  newsCopy,
  newsCarryToColumn,
  auditNews
} from '/@/api/news'

export default {
  name: 'newsOperationDialog',
  props: {
    columnList: {
      type: Array,
      default: () => []
    }
  },
  components: {},
  emits: ['update'],
  setup(props, { emit }) {
    const attributeForm = ref()
    const copyForm = ref()
    const moveForm = ref()
    const auditForm = ref()
    const state = reactive({
      visible: false,
      btnLoading: false,
      dialogTitle: '',
      operationType: '',
      attributeList: <any>[],

      attributeFormData: {
        attribute: [],
        newsIds: ''
      },
      copyFormData: {
        newsIds: '',
        homeColumnId: '',
        amount: ''
      },
      moveFormData: {
        newsIds: '',
        homeColumnId: ''
      },
      auditFormData: {
        newsIds: '',
        status: '1' // 审核状态，1:通过；-1:审核拒绝
      }
    })
    const amountValid = (rule, value, callback) => {
      console.log(value)
      console.log(typeof value)
      if (!value) {
        callback('请填写复制数量')
      }
      if (value > 10) {
        callback('请填写数量必须小于10')
      }
      callback()
    }
    const copyFormDataRules = ref({
      amount: [
        {
          validator: amountValid,
          trigger: ['blur', 'change']
        }
      ]
    })
    const getAttributeList = async () => {
      state.attributeList = await (<any>getAttributeNewsList())
    }

    onMounted(() => {
      getAttributeList()
    })

    const open = (operationType: string, newsIds: string, dialogTitle: string = '') => {
      state.operationType = operationType
      switch (operationType) {
        case '1':
          state.dialogTitle = '批量编辑属性'
          state.attributeFormData.newsIds = newsIds
          break
        case '2':
          state.dialogTitle = '批量复制文档'
          state.copyFormData.newsIds = newsIds
          break
        case '3':
          state.dialogTitle = '批量移动文档'
          state.moveFormData.newsIds = newsIds
          break
        case '5':
          state.dialogTitle = '批量审核文档'
          state.auditFormData.newsIds = newsIds
          break
        default:
          break
      }
      if (dialogTitle) {
        state.dialogTitle = dialogTitle
      }
      state.visible = true
    }

    const handleAmountInput = (val: any) => {
      state.copyFormData.amount = /^([1-9][0-9]*)$/g.test(val) ? val : ''
    }

    const handleClose = (done) => {
      attributeForm.value.resetFields()
      copyForm.value.resetFields()
      moveForm.value.resetFields()
      auditForm.value.resetFields()
      done()
    }

    const submit = (type: string) => {
      switch (type) {
        case 'attribute':
          state.btnLoading = true
          newsChageAttribute({
            attribute: state.attributeFormData.attribute.join(','),
            newsIds: state.attributeFormData.newsIds
          })
            .then(() => {
              state.btnLoading = false
              state.visible = false
              emit('update')
            })
            .catch(() => {
              state.btnLoading = false
            })
          break
        case 'copy':
          copyForm.value.validate(async (valid: Boolean) => {
            if (!valid) return
            state.btnLoading = true

            newsCopy(state.copyFormData)
              .then(() => {
                state.btnLoading = false
                state.visible = false
                emit('update')
              })
              .catch(() => {
                state.btnLoading = false
              })
          })
          break
        case 'move':
          state.btnLoading = true
          newsCarryToColumn(state.moveFormData)
            .then(() => {
              state.btnLoading = false
              state.visible = false
              emit('update')
            })
            .catch(() => {
              state.btnLoading = false
            })
          break
        case 'audit':
          state.btnLoading = true

          auditNews(state.auditFormData)
            .then(() => {
              state.btnLoading = false
              state.visible = false
              emit('update')
            })
            .catch(() => {
              state.btnLoading = false
            })
          break
        default:
          break
      }
    }

    return {
      attributeForm,
      copyForm,
      moveForm,
      auditForm,
      copyFormDataRules,
      handleAmountInput,
      open,
      handleClose,
      submit,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped></style>
