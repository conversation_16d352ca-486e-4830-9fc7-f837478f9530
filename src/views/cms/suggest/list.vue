<template>
  <div class="box">
    <el-form :inline="true" :model="searchForm" size="small">
      <el-form-item label="投诉建议">
        <el-select v-model="searchForm.departmentId" placeholder="请选择" filterable clearable>
          <el-option v-for="item in typeList" :key="item.k" :label="item.v" :value="item.k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态">
        <el-select v-model="searchForm.departmentId" placeholder="请选择" filterable clearable>
          <el-option v-for="item in typeList" :key="item.k" :label="item.v" :value="item.k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈时间">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="searchForm.addTime"
          type="daterange"
          range-separator="到"
          start-placeholder="开始"
          end-placeholder="结束"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button size="small" type="primary" @click="search">搜索</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border class="table" v-loading="tableLoading" size="small">
      <el-table-column align="center" prop="name" label="处理情况" />
      <el-table-column align="center" prop="department" label="内容" />
      <el-table-column align="center" prop="position" label="联系人" />
      <el-table-column align="center" prop="username" label="联系电话" />
      <el-table-column align="center" prop="addTime" label="反馈时间" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button type="primary" link size="small" @click="handleEdit(scope)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'

export default {
  name: 'cmsSuggestList',
  setup() {
    const state = reactive({
      searchForm: {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        addTime: ''
      },
      list: [],
      tableLoading: false,
      typeList: <any>[]
    })

    // 搜索
    const search = () => {
      // state.tableLoading = true
    }

    const handleEdit = (scope) => {
      console.log(scope)
    }

    // 重置
    const resetSearch = () => {
      state.searchForm = {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        addTime: ''
      }
      search()
    }

    onMounted(() => {
      search()
    })

    const submit = () => {}

    return {
      ...toRefs(state),
      search,
      submit,
      handleEdit,
      resetSearch
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.red {
  color: red;
}
.table {
  margin-top: 10px;
}
.el-card {
  border: none;
  padding: 0 30px;
  :deep(.el-card__header) {
    padding: 15px 0;
    border-bottom-color: #f2f2f2;
  }
  :deep(.el-card__body) {
    padding: 10px 0 30px;
  }
  .content {
    .title {
      border-left: 2px solid var(--color-primary);
    }
    .btn {
      width: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .cursor-default {
    :deep(.el-input__inner) {
      cursor: inherit;
    }
  }
}
</style>
