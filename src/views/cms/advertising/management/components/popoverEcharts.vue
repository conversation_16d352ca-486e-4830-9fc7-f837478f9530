<template>
  <div ref="echartsBox" class="echarts-box" v-loading="loading">
    <div class="jc-between">
      <div class="as-center fs-12 fw-bold">时间:</div>
      <el-link
        @click="handleShowEcharts(1)"
        :underline="false"
        class="as-center fs-12"
        :type="adRangeTypeActive === 1 ? 'primary' : 'default'"
        >近7天</el-link
      >
      <el-link
        @click="handleShowEcharts(2)"
        :underline="false"
        class="as-center fs-12"
        :type="adRangeTypeActive === 2 ? 'primary' : 'default'"
        >近30天</el-link
      >
      <el-link
        @click="handleShowEcharts(3)"
        :underline="false"
        class="as-center fs-12"
        :type="adRangeTypeActive === 3 ? 'primary' : 'default'"
        >近6个月</el-link
      >
      <div class="w-200">
        <DatePickerRange
          popper-class="data-pick-reference"
          size="small"
          v-model:start="adStatistical.timeStart"
          v-model:end="adStatistical.timeEnd"
        />
      </div>
      <el-button @click="handleShowEcharts(-1)" size="small" type="warning">查询</el-button>
    </div>
    <div v-show="hasData" style="height: 250px; width: 500px" ref="myStatistical"></div>
    <div v-show="!hasData" style="height: 250px; width: 500px">
      <el-empty :image-size="120"></el-empty>
    </div>
    <div class="arrow"></div>
  </div>
</template>
<script lang="ts">
import { reactive, onMounted, ref, toRefs, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { formatDate } from '/@/utils/formatTime'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { createPopper } from '@popperjs/core'

import { getShowcaseClick } from '/@/api/advertising'
/**
 * 定义在reactive时，
 * tooltips不显示，原因不详
 */
let myCharts = <any>''

export default {
  components: { DatePickerRange },
  name: 'popoverEcharts',
  emits: ['update'],
  setup() {
    const echartsBox = ref()
    const myStatistical = ref()
    const state = reactive({
      loading: false,
      popover: <any>'',
      adRangeTypeActive: 1,
      hasData: true,
      adStatistical: {
        id: '',
        timeStart: '',
        timeEnd: '',
        grainType: '1' // 1:日；2:月
      }
    })

    const handleClose = () => {
      state.adStatistical.id = ''
      if (state.popover === '') return
      state.popover.destroy()
    }

    const addEventListenerFn = (e: any) => {
      const noCloseEl = document.getElementsByClassName('data-pick-reference')[0]
      const isSelf = echartsBox.value.contains(e.target) || noCloseEl.contains(e.target)
      if (!isSelf && e.target.className !== 'reference') {
        handleClose()
      }
    }

    // 获取后台数据类型
    onMounted(async () => {
      myCharts = echarts.init(myStatistical.value)
      document.addEventListener('click', addEventListenerFn)
    })

    onBeforeUnmount(() => {
      document.removeEventListener('click', addEventListenerFn)
    })

    const updateEcharts = (data: any) => {
      const xData = [] as any
      const yData = [] as any
      data.map((item: any) => {
        xData.push(item.time)
        yData.push(item.count)
        return true
      })
      const option = {
        grid: {
          top: 20,
          right: 30,
          bottom: 30,
          left: 40
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          boundaryGap: false,
          data: xData,
          axisLabel: {
            fontSize: 11
          }
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 4,
            smallnterval: 1
          }
        ],
        series: [
          {
            name: '点击量',
            type: 'line',
            data: yData,
            itemStyle: {
              color: '#febb50'
            }
          }
        ]
      }
      myCharts.setOption(option)
    }

    const handleShowEcharts = async (rangeType: number = 1) => {
      state.adRangeTypeActive = rangeType
      const startDate = new Date()
      state.adStatistical.grainType = '1'
      if (rangeType !== -1) {
        switch (rangeType) {
          // -1 自定义、1 7天、2 30天、 3 6个月
          case 1:
            startDate.setDate(startDate.getDate() - 7)
            break
          case 2:
            startDate.setDate(startDate.getDate() - 30)
            break
          case 3:
            startDate.setMonth(startDate.getMonth() - 6)
            state.adStatistical.grainType = '2'
            break
          default:
            break
        }
        const timeStart = formatDate(startDate, 'YYYY-mm-dd')
        const timeEnd = formatDate(new Date(), 'YYYY-mm-dd')
        state.adStatistical.timeStart = timeStart
        state.adStatistical.timeEnd = timeEnd
      }
      state.loading = true
      const data = await getShowcaseClick(state.adStatistical)
      state.loading = false
      if (!data.length) {
        state.hasData = false
        return
      }
      state.hasData = true
      updateEcharts(data)
    }

    const open = (id: any, reference: any) => {
      if (!id || state.adStatistical.id === id) {
        handleClose()
        return
      }
      state.adStatistical.id = id
      state.popover = createPopper(reference, echartsBox.value, {
        placement: 'right',
        modifiers: [
          {
            name: 'offset',
            options: {
              offset: [0, 10]
            }
          }
        ]
      })
      handleShowEcharts()
    }

    return {
      echartsBox,
      myStatistical,
      open,
      handleShowEcharts,
      handleClose,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
.bg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1500;
  background: rgba(0, 0, 0, 0.3);
  left: 0;
  top: 0;
}
.echarts-box {
  left: calc(0px - 100%);
  position: fixed;
  background: #fff;
  width: 550px;
  border-radius: 4px;
  border: 1px solid#e4e7ed;
  padding: 12px;
  z-index: 1800;
  line-height: 1.4;
  text-align: justify;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  word-break: break-all;
  .arrow {
    position: absolute;
    top: calc(50% - 5px);
    right: -5px;
    width: 10px;
    height: 10px;
    z-index: -1;
    &::before {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      border: 1px solid #e4e7ed;
      border-left-color: transparent;
      border-bottom-color: transparent;
      transform: rotate(45deg);
      background: #fff;
      box-sizing: border-box;
    }
  }
}
</style>
