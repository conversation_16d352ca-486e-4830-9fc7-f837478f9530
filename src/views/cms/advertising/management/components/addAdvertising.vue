<template>
  <el-dialog top="5vh" v-model="visible" :title="title" width="912px" @close="handleCancle">
    <div v-loading="loading">
      <el-form
        ref="form"
        :model="formData"
        :rules="formRules"
        class="mt-15 pr-50"
        label-width="90px"
      >
        <el-form-item prop="packingId" label="关联维护" v-if="formData.packingId">
          -----------广告位关联维护-----------</el-form-item
        >
        <el-form-item prop="type" label="广告位类型">
          <el-checkbox-group v-model="formData.type">
            <el-checkbox label="1" name="type">付费</el-checkbox>
            <el-checkbox label="2" name="type">RPO</el-checkbox>
            <el-checkbox label="3" name="type">异议</el-checkbox>
            <el-checkbox label="4" name="type">客情</el-checkbox>
            <el-checkbox label="5" name="type">推广</el-checkbox>
            <el-checkbox label="6" name="type">高级</el-checkbox>
            <el-checkbox label="7" name="type">旧链</el-checkbox>
            <el-checkbox label="8" name="type">免费</el-checkbox>
            <el-checkbox label="9" name="type">其他</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="type" label="广告位标签">
          <el-checkbox-group v-model="tags">
            <el-checkbox
              @change="(value) => tagsChange(value, item.homePositionIds)"
              v-for="item in tagList"
              :key="item.tagId"
              :label="item.name"
            >
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="homePositionId" label="广告位置">
          <AdPositionList
            v-model="formData.homePositionId"
            :multiple="true"
            :showAllLevels="false"
          />
        </el-form-item>

        <el-form-item prop="title" label="广告标题">
          <el-input v-model="formData.title" placeholder=""></el-input>
        </el-form-item>

        <el-form-item prop="subTitle" label="副标题">
          <el-input v-model="formData.subTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item prop="secondTitle" label="次标题">
          <el-input v-model="formData.secondTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item class="ai-center" prop="imageUrl" label="图片地址">
          <el-input
            @blur="handleImageInputBlur"
            class="flex-1"
            v-model="formData.imageUrl"
            clearable
            placeholder=""
          ></el-input>
          <el-image
            v-if="showImagePreview"
            class="preview"
            style="width: 100px; height: 100px"
            :src="formData.imageUrl"
            :preview-src-list="[formData.imageUrl]"
            :initial-index="4"
            fit="contain"
          />
        </el-form-item>
        <el-form-item prop="imageAlt" label="上传">
          <el-upload
            class="avatar-uploader"
            action="/upload/image"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <el-button type="primary">上传</el-button>
            &nbsp;&nbsp;
            <p class="logo">支持JPG/PNG图片格式，文件小于5M</p>
          </el-upload>
        </el-form-item>
        <el-form-item prop="imageAlt" label="图片说明文字">
          <el-input v-model="formData.imageAlt" placeholder="请填写图片文字说明"></el-input>
        </el-form-item>
        <el-form-item prop="onlineTime" label="生效时间">
          <DatePicker v-model="formData.onlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>
        <el-form-item prop="offlineTime" label="失效时间">
          <DatePicker v-model="formData.offlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>
        <el-form-item prop="targetLink" label="跳转地址">
          <el-input v-model="formData.targetLink" placeholder="请填写广告落地页地址"></el-input>
        </el-form-item>
        <!-- <el-form-item label="关联位置">
          <div class="flex">
            <div class="flex-1">
              <el-form-item prop="contactHomePositionId" label-width="0px">
                <el-input v-model="formData.contactHomePositionId" placeholder=""></el-input>
              </el-form-item>
            </div>
            <div class="flex-1">
              <el-form-item prop="contactHomePositionSort" label-width="15px">
                <el-input v-model="formData.contactHomePositionSort" placeholder="排序"></el-input>
              </el-form-item>
            </div>
          </div>
        </el-form-item> -->
        <el-form-item prop="sort" label="广告排序">
          <el-input v-model="formData.sort" placeholder="数字越大越靠前，不填默认”0“"></el-input>
        </el-form-item>
        <el-form-item prop="describe" label="广告描述">
          <el-input
            v-model="formData.describe"
            type="textarea"
            rows="3"
            resize="none"
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button :loading="submitLoading" @click="submit" type="primary">保存</el-button>
          <el-button plain @click="handleCancle">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, ref, onMounted, toRefs, nextTick, watch } from 'vue'
import DatePicker from '/@/components/base/datePicker.vue'
import AdPositionList from '/@select/adPosition.vue'

import {
  addShowcase,
  getShowcaseDetails,
  editShowcase,
  getPositionRelationTag
} from '/@/api/advertising'
import { ElMessage } from 'element-plus'

export default {
  components: { DatePicker, AdPositionList },
  name: 'cmsAdvertisingAdd',
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      loading: false,
      title: '新增广告',
      showImagePreview: false,
      submitLoading: false,
      formData: <any>{
        id: '',
        homePositionId: [],
        title: '',
        subTitle: '',
        secondTitle: '',
        imageUrl: '',
        imageLink: '',
        imageAlt: '',
        onlineTime: '',
        offlineTime: '',
        targetLink: '',
        contactHomePositionId: '',
        contactHomePositionSort: '',
        sort: '',
        packingId: '',
        describe: '',
        type: []
      },
      formRules: {
        homePositionId: [{ required: true, message: '请选择广告位置', trigger: 'change' }],
        title: [{ required: true, message: '请输入广告标题', trigger: 'blur' }],
        onlineTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
        offlineTime: [{ required: true, message: '请选择失效时间', trigger: 'change' }]
      },
      imageUrl: '',

      tagList: [],
      tags: []
    })

    watch(
      () => state.formData.imageUrl,
      (value) => {
        state.formData.imageLink = value
      }
    )

    const getTag = () => {
      getPositionRelationTag().then((resp) => {
        state.tagList = resp
      })
    }

    const getDetails = async () => {
      state.loading = true
      await getShowcaseDetails({ id: state.formData.id }).then((resp: any) => {
        state.formData = resp

        const { imageUrl } = resp
        state.showImagePreview = !!imageUrl

        state.loading = false
      })
    }

    // 获取后台数据类型
    onMounted(async () => {
      getTag()
    })

    const open = async (id: string, string = '') => {
      state.title = id ? '编辑广告' : '新增广告'
      if (id) {
        state.formData.id = id
        await getDetails()
        if (string === 'copy') {
          state.title = '复制广告'
          state.formData.id = ''
          state.formData.homePositionId = ''
          state.formData.describe = ''
        }
      }
      state.visible = true
      state.tags = []
    }

    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    // 重置
    const resetForm = () => {
      state.formData = {}
      nextTick(() => {
        form.value.clearValidate()
      })
    }

    const handleCancle = () => {
      resetForm()
      state.visible = false
      state.showImagePreview = false
    }
    const submit = () => {
      form.value.validate(async (valid: any) => {
        if (valid) {
          state.submitLoading = true
          // type 有可能没有，如果是有就拼接成,隔开，要避免报错
          const typeString = state.formData.type ? state.formData.type.join(',') : ''
          const { id, ...data } = state.formData
          if (!id) {
            await addShowcase({ ...data, typeString }).catch(() => {
              state.submitLoading = false
            })
          } else {
            await editShowcase({ ...state.formData, typeString }).catch(() => {
              state.submitLoading = false
            })
          }
          state.submitLoading = false
          emit('update', !!id)
          resetForm()
          state.visible = false
        }
      })
    }

    // 略缩图上传
    const beforeImageUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }

    const handleImageSuccess = (res: any, file: any) => {
      state.formData.imageUrl = res.data.fullUrl
      state.showImagePreview = true
    }

    const handleImageInputBlur = () => {
      const { imageUrl } = state.formData
      state.showImagePreview = !!imageUrl
    }

    const tagsChange = (flag: any, ids: any) => {
      const { homePositionId } = state.formData
      const arr = [...homePositionId]

      if (flag) {
        ids.forEach((item: any) => {
          if (!arr.includes(item)) {
            arr.push(item)
          }
        })
      } else {
        ids.forEach((item: any) => {
          const index = arr.indexOf(item)
          if (index === -1) return
          arr.splice(index, 1)
        })
      }
      state.formData.homePositionId = arr
    }

    return {
      form,
      open,
      submit,
      handleDisabledDate,
      tagsChange,
      handleCancle,
      handleImageInputBlur,
      handleImageSuccess,
      beforeImageUpload,
      ...toRefs(state)
    }
  }
}
</script>
<style lang="scss" scoped>
.preview {
  width: 100px;
  height: 100px;
  margin-left: 15px;
  border-radius: 4px;
  border: var(--el-input-border, var(--el-border-base));
}
</style>
