<template>
  <el-dialog v-model="visible1" title="贴标签" width="350px" @close="resetRef">
    <div class="content">
      <div class="wrap jc-center">
        <el-form
          ref="form1"
          label-width=""
          :model="formData1"
          :rules="rules1"
          @submit.native.prevent
        >
          <el-form-item class="span-4" label="" prop="tag">
            <el-select
              v-model="formData1.tag"
              class="w100"
              multiple
              clearable
              placeholder="请选择标签"
              filterable
            >
              <el-option
                v-for="(item, index) in tagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                >{{ item.name }}</el-option
              >
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="flex-end clearfix">
        <div class="fl">
          <el-link class="fs-12 ml-10" type="primary" :underline="false" @click="handleAdd"
            >新建标签</el-link
          >
        </div>
        <div class="fr">
          <el-button size="small" @click="handleClose(1)">取消</el-button>
          <el-button size="small" type="primary" @click="handleConfirm(1)">确定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="visible2" title="广告位标签" width="450px">
    <div class="content">
      <div class="mx-auto">
        <el-form ref="form2" label-width="" :model="formData2" :rules="rules2">
          <el-form-item label="" prop="tagName">
            <el-input
              class="w200"
              clearable
              type="text"
              placeholder="请输入标签名称"
              v-model="formData2.tagName"
              maxlength="20"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button size="small" @click="handleClose(2)">取消</el-button>
        <el-button size="small" type="primary" @click="handleConfirm(2)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs, ref, nextTick, watch } from 'vue'
import { addPositionTag, editPositionTag } from '/@/api/advertising'

export default {
  name: 'tagDialog',
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    tagList: {
      type: Array,
      default() {
        return []
      }
    },
    homePositionId: {
      type: String,
      default() {
        return ''
      }
    }
  },
  emits: ['confirm', 'getTagList'],
  setup(props, { emit }) {
    const form1 = ref()
    const form2 = ref()
    const state = reactive({
      visible1: false,
      visible2: false,
      defaultList: [],
      formData1: {
        // 人才标签
        tag: []
      },
      formData2: {
        // 人才标签
        tagName: ''
      },
      rules1: {
        tag: [{ required: false, message: '请选择标签', trigger: 'change' }]
      },
      rules2: {
        tagName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }]
      }
    })

    onMounted(() => {
      state.defaultList = JSON.parse(JSON.stringify((props as any).data)).map((item: any) => {
        return {
          k: item.k,
          v: item.v,
          select: !!item.default
        }
      })
    })

    watch(
      () => props.data,
      (val: any) => {
        state.formData1.tag = val
      },
      { deep: true }
    )

    const open = () => {
      state.visible1 = true
    }

    const resetRef = () => {
      nextTick(() => {
        form1 && form1.value.resetFields()
      })
    }

    // 新建标签
    const handleAdd = (item: any) => {
      state.visible2 = true
      nextTick(() => {
        form2 && form2.value.resetFields()
      })
    }

    const handleClose = (value) => {
      state['visible' + value] = false
    }

    const handleConfirm = (value) => {
      form1.value.validate((valid: any) => {
        if (valid) {
          if (value === 2) {
            addPositionTag({
              name: state.formData2.tagName
            }).then((res) => {
              emit('getTagList')
              handleClose(value)
            })
          } else {
            editPositionTag({
              tagIds: state.formData1.tag.join(','),
              homePositionId: props.homePositionId
            }).then((res) => {
              // console.log(res);
              // emit('confirm', JSON.parse(JSON.stringify(toRaw(state.list))))
              emit('confirm')
              handleClose(value)
            })
          }
        }
      })
    }

    return {
      form1,
      form2,
      open,
      handleConfirm,
      handleClose,
      handleAdd,
      resetRef,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss"></style>
