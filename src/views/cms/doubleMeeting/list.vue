<template>
  <div class="formbigbox">
    <el-form ref="form" :model="formData" label-width="70px" :size="formSize">
      <div class="flex">
        <el-form-item class="span-4" label="表单检索">
          <el-input
            class="w100"
            v-model="formData.name"
            placeholder="请输入表单名称或编号"
            clearable
            @keyup.enter="search()"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            :size="formSize"
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-4" label-width="10px">
          <el-button type="primary" @click="search()">搜索</el-button>
          <el-button type="default" @click="handleAddActivity()">新增报名表</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table ref="table" :data="tableData" border v-loading="loading" size="small" stripe>
      <el-table-column
        prop="id"
        label="表单ID"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="name" label="表单名称" align="center" header-align="center" />
      <el-table-column
        prop="addTime"
        label="创建时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="registrationFormNumber"
        label="表单数量"
        align="center"
        header-align="center"
      />
      <el-table-column prop="code" label="表单代码" align="center" header-align="center" />
      <el-table-column
        label="操作"
        align="center"
        header-align="center"
        min-width="150px"
        fixed="right"
      >
        <template #default="{ row }">
          <div>
            <el-link type="primary" :underline="false">
              <router-link class="edit-btn" :to="`/cms/double-meeting/${row.id}`">编辑</router-link>
            </el-link>
            &nbsp;
            <el-link type="primary" @click="handleCopy(row.link)" :underline="false"
              >报名链接</el-link
            >
            &nbsp;
            <el-link type="primary" @click="showSignList(row)" :underline="false">签到链接</el-link>
            &nbsp;
            <el-link
              :disabled="row.registrationFormNumber == 0"
              :type="row.registrationFormNumber == 0 ? 'info' : 'primary'"
              :underline="false"
              @click="handleExportData(row)"
              >导出数据</el-link
            >
            &nbsp;
            <el-link
              type="primary"
              v-if="row.registrationFormNumber == 0"
              :underline="false"
              @click="handleDelete(row.id)"
              >删除</el-link
            >
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty></el-empty>
      </template>
    </el-table>
    <div v-show="tableData.length" class="mt-15">
      <Paging :total="total" :page="formData.page" @change="changePage"></Paging>
    </div>

    <el-dialog
      v-model="exportVisible"
      title="导出数据"
      width="450px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="时间段" label-width="50px">
          <el-date-picker
            v-model="exportDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="[
              {
                text: '最近一周',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                  return [start, end]
                }
              },
              {
                text: '最近一个月',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                  return [start, end]
                }
              },
              {
                text: '最近三个月',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                  return [start, end]
                }
              },
              {
                text: '本周',
                value: () => {
                  // 从周一到现在
                  const end = new Date()
                  const start = new Date()
                  start.setDate(start.getDate() - start.getDay())
                  return [start, end]
                }
              },
              {
                text: '本月',
                value: () => {
                  // 从1号到现在
                  const end = new Date()
                  const start = new Date()
                  start.setDate(1)
                  return [start, end]
                }
              }
            ]"
          />
        </el-form-item>
        <el-form-item label-width="50px">
          <p>* 若未选择时间段，则导出全量数据</p>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleDownloadExcel">下载excel</el-button>
          <el-button type="primary" @click="renameVisible = true"> 下载附件 </el-button>
          <el-button type="primary" @click="showDownloadSignList"> 下载签到数据 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="renameVisible" title="下载附件" width="500px">
      <el-form>
        <el-form-item label="命名方式">
          <el-select v-model="renameMode" style="width: 100%">
            <el-option label="文件命名规则" value="default" />
            <el-option label="自定义命名" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="renameMode === 'default'" label="文件命名规则">
          <el-select v-model="renameType" placeholder="请选择命名规则">
            <el-option
              v-for="item in renameTypeOption"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="renameMode === 'custom'" label="自定义命名字段">
          <el-select
            v-model="renameFields"
            multiple
            filterable
            placeholder="请选择命名字段（可多选并排序）"
            style="width: 100%"
          >
            <el-option
              v-for="item in customRenameFields"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <template v-if="renameMode === 'default'">
          默认文件名规则示例：序号_姓名_附件n_{附件名称}
        </template>
        <template v-else>
          自定义命名规则示例：
          <span v-for="(f, idx) in renameFields" :key="f">
            {{ getFieldLabel(f) }}<span v-if="idx !== renameFields.length - 1">-</span>
          </span>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleDownload">立即下载</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="signOptionVisible" title="签到链接" width="600px">
      <div>请选择本活动的签到选项/场次：</div>
      <br />
      <el-select v-model="signOptionId" class="sign-option" filterable>
        <el-option v-for="item in signOptionList" :key="item.k" :label="item.v" :value="item.k" />
      </el-select>
      <br />
      <br />
      <div v-show="signOptionId">
        <div>
          <span>签到链接</span>&nbsp;<el-button type="primary" @click="hanCopySignUrl(signUrl)"
            >复制</el-button
          >
        </div>
        <br />
        <div>
          <a
            :href="signUrl"
            target="_blank"
            style="color: #409eff; display: block; text-decoration: none"
            >{{ signUrl }}</a
          >
        </div>
        <br />
        <div>
          <span>签到码</span>&nbsp;<el-button
            type="primary"
            @click="hanDownloadSignCodeUrl(signCodeUrl)"
            >下载</el-button
          >
        </div>
        <img :src="signCodeUrl" />
      </div>
    </el-dialog>

    <!-- 下载签到数据 -->
    <el-dialog v-model="downSignOptionVisible" title="签到链接" width="600px">
      <div>请选择本活动的签到选项/场次：</div>
      <br />
      <el-select v-model="downSignOptionId" class="sign-option" filterable>
        <el-option
          v-for="item in downSignOptionList"
          :key="item.k"
          :label="item.v"
          :value="item.k"
        />
      </el-select>
      <div v-show="downSignOptionId != ''">
        <br />
        <p v-show="!haveSign" style="color: red">⚠️ 该选项暂无报名数据，不支持下载</p>
        <el-button :disabled="!haveSign" type="primary" @click="handelDownloadSignExcel()"
          >立即下载</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, onMounted, toRefs, defineComponent, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import commonFunction from '/@/utils/commonFunction.ts'
import Paging from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

import {
  getActivityList,
  downloadExcel,
  downattachment,
  deleteActivityFrom,
  getIntentionOptionList,
  downSignExcel
} from '/@/api/doubleMeeting'

export default defineComponent({
  components: {
    DatePickerRange,
    Paging
  },
  name: 'cmsDoubleMeeting',
  setup() {
    const router = useRouter()
    const { copyText } = commonFunction()

    const state = reactive({
      loading: false,
      formSize: 'default',
      formData: {
        name: '',
        addTimeStart: '',
        addTimeEnd: '',
        page: 1,
        limit: 20
      },

      tableData: [],
      total: 0,

      exportVisible: false,
      renameVisible: false,
      signOptionVisible: false,
      downSignOptionVisible: false,
      haveSign: false,
      signOptionList: [],
      signOptionId: '',
      downSignOptionList: [],
      downSignOptionId: '',
      signUrl: '',
      signCodeUrl: '',
      signTitle: '',
      renameMode: 'custom',
      renameType: '1',
      renameFields: ['name', 'major'],
      renameTypeOption: [
        {
          k: '1',
          v: '附件名称（默认）'
        },
        {
          k: '2',
          v: '毕业学校'
        },
        {
          k: '3',
          v: '二级学院'
        },
        {
          k: '4',
          v: '所学专业'
        },
        {
          k: '5',
          v: '学历水平'
        },
        {
          k: '6',
          v: '手机号'
        }
      ],
      customRenameFields: [
        { k: 'name', v: '姓名' },
        { k: 'major', v: '所学专业' },
        { k: 'school', v: '毕业学校' },
        { k: 'education', v: '学历水平' }
      ],

      exportFileId: '',
      exportData: {
        exportAttachmentLink: '',
        exportRegistrationFormLink: ''
      },
      exportDateRange: []
    })

    const getList = () => {
      state.loading = true
      getActivityList(state.formData).then((resp: any) => {
        state.total = resp.page.count
        state.tableData = resp.list
        state.loading = false
      })
    }

    const search = () => {
      state.formData.page = 1
      getList()
    }

    const changePage = (r: any) => {
      state.formData.page = r.page
      state.formData.limit = r.limit
      getList()
    }

    // 获取后台数据类型
    onMounted(async () => {
      getList()
    })

    const handleCopy = (link) => {
      const suffix = '?fromType=1'
      const copyLink = `${link + suffix}`
      const message = `<a href='${copyLink}' target='_blank' style='text-align:center;color:#409eff;display:block;text-decoration:none'>${copyLink}</a>`
      ElMessageBox.alert(message, '报名链接', {
        confirmButtonText: '复制',
        dangerouslyUseHTMLString: true,
        callback: (action) => {
          if (action === 'confirm') {
            copyText(copyLink)
          }
        }
      })
    }

    const showSignList = (row) => {
      const { id } = row
      // 先重置
      state.signOptionId = ''
      getIntentionOptionList({ activityFormId: id }).then((resp: any) => {
        state.signOptionVisible = true
        state.signOptionList = resp.map((item: any) => {
          return {
            k: item.optionId,
            v: item.title,
            signUrl: item.signUrl,
            signCodeUrl: item.signCodeUrl
          }
        })
      })
    }

    const handleExportData = (row) => {
      const { id, registrationFormNumber } = row
      if (/^0$/.test(registrationFormNumber)) {
        return
      }
      state.exportVisible = true
      state.exportFileId = id
      state.exportDateRange = []
      state.renameMode = 'custom'
      state.renameType = '1'
      state.renameFields = ['name', 'major']
      state.renameVisible = false
      state.downSignOptionVisible = false
      state.downSignOptionId = ''
    }

    const handleDownloadExcel = () => {
      const { exportFileId } = state
      downloadExcel({
        activityFormId: exportFileId,
        beginTime: state.exportDateRange?.[0],
        endTime: state.exportDateRange?.[1]
      })
      state.exportVisible = false
    }

    const handleDownload = () => {
      state.exportVisible = false
      const { exportFileId, renameMode, renameType, renameFields } = state
      const beginTime = state.exportDateRange?.[0]
      const endTime = state.exportDateRange?.[1]
      if (renameMode === 'default') {
        downattachment({ activityFormId: exportFileId, type: renameType, beginTime, endTime }).then(
          () => {
            state.renameType = '1'
          }
        )
      } else {
        if (!renameFields.length) {
          ElMessageBox.alert('请至少选择一个命名字段', '提示')
          return
        }
        downattachment({
          activityFormId: exportFileId,
          fields: renameFields,
          beginTime,
          endTime
        }).then(() => {
          state.renameFields = ['name', 'major']
        })
      }
      state.renameVisible = false
    }

    const handleAddActivity = () => {
      router.push({ name: 'cmsDoubleMeetingAdd' })
    }

    const handleDelete = (id) => {
      ElMessageBox.alert('确定删除该活动表单吗？', '报名链接', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        dangerouslyUseHTMLString: true,
        callback: async (action) => {
          if (action === 'confirm') {
            await deleteActivityFrom({ activityFormId: id })
            getList()
          }
        }
      })
    }

    const hanCopySignUrl = (url) => {
      copyText(url)
    }

    // 从这里开始是制作下载图的一些辅助方法
    async function loadImage(params) {
      // 图片src 必传
      const { src } = params
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = src
        img.crossOrigin = 'anonymous'
        img.onload = () => {
          resolve(img)
        }
        img.onerror = reject
      })
    }

    // 图片转base64
    function img2Base64(image) {
      // 图片画到canvas
      const canvas = document.createElement('canvas')
      canvas.width = image.width
      canvas.height = image.height
      const context = canvas.getContext('2d')
      context.drawImage(image, 0, 0)
      return canvas.toDataURL('image/jpg', 1.0)
    }
    // base64转blob
    function base64ToBlob(base64Code) {
      const parts = base64Code.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], {
        type: contentType
      })
    }

    const hanDownloadSignCodeUrl = (codeUrl) => {
      // 一个图片下载
      loadImage({ src: codeUrl }).then((image) => {
        const base64 = img2Base64(image)
        const blob = base64ToBlob(base64)
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        // title.jpg
        // 找到场次名称
        a.download = `${state.signTitle}.jpg`
        a.click()
        URL.revokeObjectURL(url)
      })
    }

    const showDownloadSignList = () => {
      const { exportFileId } = state
      state.downSignOptionVisible = true
      state.downSignOptionId = ''
      getIntentionOptionList({ activityFormId: exportFileId }).then((resp: any) => {
        state.downSignOptionList = resp.map((item: any) => {
          return {
            k: item.optionId,
            v: item.title,
            haveSign: item.haveSign
          }
        })
      })
    }

    const handelDownloadSignExcel = () => {
      downSignExcel({
        optionId: state.downSignOptionId,
        beginTime: state.exportDateRange?.[0],
        endTime: state.exportDateRange?.[1]
      }).then((_) => {
        // 关闭弹窗
        state.downSignOptionVisible = false
        state.exportVisible = false
      })
    }

    // 写个watch监听
    watch(
      () => state.signOptionId,
      (val) => {
        if (val === '') {
          state.signUrl = ''
          state.signCodeUrl = ''
          return
        }
        const selectItem = state.signOptionList.find((item) => item.k === val)
        state.signUrl = selectItem.signUrl
        state.signCodeUrl = selectItem.signCodeUrl
        state.signTitle = selectItem.v
      }
    )

    watch(
      () => state.downSignOptionId,
      (val) => {
        if (val === '') {
          state.haveSign = false
          return
        }
        const selectItem = state.downSignOptionList.find((item) => item.k === val)
        if (selectItem.haveSign === 1) {
          state.haveSign = true
        } else {
          state.haveSign = false
        }
      }
    )

    // 监听命名方式切换，切换到自定义命名时默认勾选"姓名"和"所学专业"
    watch(
      () => state.renameMode,
      (val) => {
        if (val === 'custom') {
          state.renameFields = ['name', 'major']
        } else if (val === 'default') {
          state.renameFields = []
        }
      }
    )

    // 字段label映射
    const getFieldLabel = (key: string) => {
      const item = state.customRenameFields.find((f) => f.k === key)
      return item ? item.v : key
    }

    return {
      getList,
      search,
      handleAddActivity,
      changePage,
      handleCopy,
      handleExportData,
      handleDownloadExcel,
      handleDownload,
      handleDelete,
      showSignList,
      hanCopySignUrl,
      hanDownloadSignCodeUrl,
      showDownloadSignList,
      handelDownloadSignExcel,
      getFieldLabel,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}

.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.edit-btn {
  color: inherit;
  text-decoration: none;
}
.sign-option {
  width: 100%;
}
</style>
