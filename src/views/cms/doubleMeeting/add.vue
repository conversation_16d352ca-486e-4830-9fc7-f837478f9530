<template>
  <div class="formbigbox">
    <el-form
      class="form-activity"
      ref="activityFormEl"
      :model="formData"
      :rules="activityRules"
      label-width="150px"
      :size="formSize"
      v-loading="loading"
    >
      <el-form-item label-width="auto">
        <template #label>
          <span class="common-title">{{ formTitle }}报名表</span>
        </template>
      </el-form-item>
      <el-form-item label="表单名称：" prop="name">
        <el-input v-model="formData.name" :maxlength="50" placeholder="请输入表单名称，最多50字" />
      </el-form-item>
      <el-form-item label="背景图上传：">
        <el-upload
          :disabled="!!formData.backgroundUrl"
          ref="uploadEl"
          class="upload-content upload-bg"
          :accept="acceptType"
          :action="imgUploadApi"
          :before-upload="(uploadFile) => handleBeforeUpload({ maxSize: 5, size: uploadFile.size })"
          :on-success="handleBGSuccess"
          :show-file-list="false"
          list-type="picture"
        >
          <template #trigger>
            <el-button type="primary" :disabled="!!formData.backgroundUrl">选择文件</el-button>
          </template>
          <span class="tips">支持jpg、png、jpeg格式；图片尺寸：1200px*300px；大小：5M</span>

          <template v-if="formData.backgroundUrl">
            <ul class="upload-file">
              <li class="upload-file-item">
                <img
                  @click="handleImgPreview(formData.backgroundUrl)"
                  class="upload-img"
                  :src="formData.backgroundUrl"
                />
                <i @click="handleBGRemove" class="el-icon el-icon--close">
                  <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fill="currentColor"
                      d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                    ></path>
                  </svg>
                </i>
              </li>
            </ul>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="导语：">
        <WangEditor
          ref="introductionEditorEl"
          v-model="formData.introduction"
          showCustomUploadFile
        />
      </el-form-item>
      <el-form-item label="结束语：">
        <WangEditor ref="conclusionEditorEl" v-model="formData.conclusion" showCustomUploadFile />
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          v-model="formData.remark"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="仅作运营备注使用，不在前端展示"
        />
      </el-form-item>

      <el-form-item label-width="auto">
        <template #label>
          <span class="wrapper-title">报名意向设置</span>
        </template>
      </el-form-item>
      <el-form-item label="填写说明：">
        <el-input
          v-model="formData.intentExplain"
          :rows="3"
          type="textarea"
          resize="none"
          :maxlength="100"
          placeholder="最多100字"
        />
      </el-form-item>
      <el-form-item label="选项设置：" class="option-setting special" prop="intentionOption">
        <el-button type="primary" @click="handleIntentionAdd">新增选项</el-button>
        <el-table
          :data="formData.intentionOption"
          border
          :default-sort="{ prop: 'sort', order: null }"
          class="table"
        >
          <el-table-column
            header-align="center"
            align="center"
            prop="id"
            label="选项ID"
            width="180"
          />
          <el-table-column
            header-align="center"
            align="center"
            prop="title"
            label="选项文字"
            show-overflow-tooltip
          />
          <el-table-column
            header-align="center"
            align="center"
            label="选项说明"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.type === 1">无</span>
              <span v-if="row.type === 2">自定义文本</span>
              <span v-if="row.type === 3">超链接</span>
            </template>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            prop="sort"
            sortable
            :sort-method="handleSort"
            label="排序"
          />
          <el-table-column header-align="center" align="center" label="操作">
            <template #default="{ row, $index }">
              <el-link type="primary" :underline="false" @click="handleIntentionEdit(row, $index)"
                >编辑</el-link
              >&nbsp;
              <el-link
                type="primary"
                :underline="false"
                @click="handleIntentionStatus({ isShow: row.isShow, $index })"
                >{{ row.isShow == 1 ? '隐藏' : '显示' }}</el-link
              >&nbsp;
              <el-link
                v-if="row.isOldChoose !== 1"
                type="primary"
                :underline="false"
                @click="handleIntentionDelete($index)"
                >删除</el-link
              >&nbsp;
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <!-- 单选 -->
      <el-form-item label="可报名次数：" prop="isSetMaxCount">
        <el-radio-group v-model="formData.isSetMaxCount">
          <el-radio label="1">不限</el-radio>
          <el-radio label="2">最多</el-radio>
        </el-radio-group>
        <el-input-number
          v-model="formData.maxCount"
          :min="0"
          :max="99"
          :disabled="formData.isSetMaxCount === '1'"
          class="ml-4"
        />
      </el-form-item>

      <el-form-item label-width="auto">
        <template #label>
          <span class="wrapper-title">其他题目设置</span>
        </template>
      </el-form-item>
      <el-form-item label="配置前端显示题目：">
        <el-table :data="typeShowSetOption" border>
          <el-table-column label="请选择要显示的题目">
            <template #default="{ row }">
              <el-checkbox v-model="row.checked" :label="row.label" />
            </template>
          </el-table-column>
          <el-table-column label="是否必填">
            <template #default="{ row }">
              <el-radio-group v-model="row.isRequired" :disabled="!row.checked" class="ml-4">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2">否</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column label="排序">
            <template #default="{ row }">
              <el-input
                v-model="row.sort"
                :disabled="!row.checked"
                :rows="1"
                type="number"
                :step="1"
                :min="0"
                :max="999"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item v-show="typeShowSetOption[3]['checked']" label="附件简历上传说明：">
        <el-input
          v-model="formData.uploadInstructions"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多100字"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item v-show="typeShowSetOption[4]['checked']" label="微信号填写说明：">
        <el-input
          v-model="formData.wechatInstructions"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多100字"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item v-show="typeShowSetOption[5]['checked']" label="推荐人填写说明：">
        <el-input
          v-model="formData.referenceInstructions"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多100字"
          :maxlength="100"
        /> </el-form-item
      ><el-form-item v-show="typeShowSetOption[7]['checked']" label="博士后培养机构填写说明：">
        <el-input
          v-model="formData.postdoctorInstitutionInstructions"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多100字"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item v-show="typeShowSetOption[8]['checked']" label="博士毕业后的海外连续工作时长：">
        <el-input
          v-model="formData.postdoctorOverseasDurationInstructions"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多100字"
          :maxlength="100"
        />
      </el-form-item>

      <el-form-item label-width="auto">
        <template #label>
          <span class="wrapper-title">报名成功设置</span>
        </template>
      </el-form-item>
      <el-form-item label="报名成功提醒：">
        <el-input
          v-model="formData.successTips"
          :rows="3"
          type="textarea"
          resize="none"
          placeholder="最多300字"
          :maxlength="300"
        />
      </el-form-item>
      <el-form-item label="二维码/社群引导：" class="special">
        <el-upload
          class="upload-content upload-qrcode"
          :action="imgUploadApi"
          :accept="acceptType"
          :disabled="formData.communityFileIds.length > 3"
          :before-upload="(uploadFile) => handleBeforeUpload({ maxSize: 2, size: uploadFile.size })"
          :on-success="handleQRCodeSuccess"
          :show-file-list="false"
          list-type="picture"
        >
          <template #trigger>
            <el-button type="primary" :disabled="formData.communityFileIds.length > 3"
              >选择文件</el-button
            >
          </template>
          <span class="tips">支持jpg、png、jpeg格式；图片尺寸：200px*200px；大小：2M</span>

          <template v-if="formData.communityFileIds.length">
            <ul class="upload-file">
              <li
                class="upload-file-item"
                v-for="(item, index) in formData.communityFileIds"
                :key="item.id"
              >
                <img
                  @click="handleImgPreview(item.fullUrl)"
                  class="upload-img"
                  :src="item.fullUrl"
                />
                <i @click="handleQRCodeRemove(index)" class="el-icon el-icon--close">
                  <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fill="currentColor"
                      d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                    ></path>
                  </svg>
                </i>
              </li>
            </ul>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item class="form-bottom" label-width="0px">
        <div class="form-btn">
          <el-button @click="handleCancle">取消</el-button>
          <el-button @click="handleSubmit" :loading="submitLoading" type="primary">保存</el-button>
        </div>
      </el-form-item>
    </el-form>

    <el-dialog
      v-model="intentionDialogVisible"
      @close="handleIntentionDialogClose"
      title="新增/编辑选项"
      width="500px"
    >
      <el-form
        class="form-option"
        :model="intentionFormData"
        label-width="100px"
        :rules="intentionOptionRules"
        :size="formSize"
        ref="intentionUpdateEl"
      >
        <el-form-item label="选项文字：" prop="title">
          <el-input
            v-model="intentionFormData.title"
            :maxlength="50"
            placeholder="请输入选项文字，最多50字"
          />
        </el-form-item>
        <el-form-item label="选项说明：">
          <el-radio-group v-model="intentionFormData.type">
            <el-radio v-for="item in intentionType" :key="item.k" :label="item.k">{{
              item.v
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="intentionFormData.type === 2">
          <el-form-item prop="version" label=" ">
            <el-input
              v-model="intentionFormData.version"
              :rows="3"
              type="textarea"
              resize="none"
              :maxlength="50"
              placeholder="请输入选项说明，50字以内"
            />
          </el-form-item>
        </template>
        <template v-if="intentionFormData.type === 3">
          <el-form-item label="链接" prop="link">
            <el-input v-model="intentionFormData.link" placeholder="请输入网址" />
          </el-form-item>
          <el-form-item label="链接文字">
            <el-input
              v-model="intentionFormData.linkVersion"
              :maxlength="10"
              placeholder="最多10字，默认为：查看详情"
            />
          </el-form-item>
        </template>
        <el-form-item label="排序：" class="sort">
          <el-input
            v-model="intentionFormData.sort"
            :maxlength="3"
            placeholder="序号"
            @input="verifiySort"
          />
          <span class="tips">0～999，数字越大排越前</span>
        </el-form-item>
        <el-form-item label="签到说明：" class="sort">
          <!--          textarea-->
          <el-input
            type="textarea"
            v-model="intentionFormData.signDesc"
            :maxlength="300"
            placeholder="请输入签到说明，最多300字"
          />
        </el-form-item>
        <el-form-item class="form-bottom" label-width="0px">
          <div class="form-btn">
            <el-button @click="intentionDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleIntentionSave" :loading="intentionLoading"
              >确定</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog v-model="previewDialogVisible">
      <div class="preview-content">
        <img class="img" :src="previewImageUrl" />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, onMounted, toRefs, ref, defineComponent, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ElMessage, ElMessageBox } from 'element-plus'

import WangEditor from '/@/components/wangEditor/index.vue'

import { verifyUrl, verifiyNumberInteger } from '/@/utils/toolsValidate'

import {
  saveActivityFormintentionOption,
  getIntentionType,
  getShowMessageOption,
  saveActivity,
  getActivityFormInfo,
  editActivity
} from '/@/api/doubleMeeting.ts'

export default defineComponent({
  components: { WangEditor },
  name: 'cmsDoubleMeetingAdd',
  setup() {
    const route = useRoute()
    const router = useRouter()

    const uploadEl = ref()
    const intentionUpdateEl = ref()
    const activityFormEl = ref()
    const introductionEditorEl = ref()
    const conclusionEditorEl = ref()

    const state = reactive({
      formTitle: computed(() => {
        const { name } = route
        return name === 'cmsDoubleMeetingEdit' ? '编辑' : '新增'
      }),
      loading: false,

      formSize: 'default',
      acceptType: 'image/jpg, image/png, image/jpeg',
      imgUploadApi: '/upload/image',

      previewImageUrl: '',
      previewDialogVisible: false,

      // 配置前端显示题目
      initTypeShowSetOption: [] as any,
      typeShowSetOption: [
        {
          id: '1',
          label: '意向就业单位类型',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '2',
          label: '获取活动的渠道来源',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '3',
          label: '简历代投',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '4',
          label: '附件简历上传',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '5',
          label: '微信号填写',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '6',
          label: '推荐人填写',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '7',
          label: '目前的学业/就业状态',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '8',
          label: '博士后培养机构',
          checked: false,
          isRequired: 2,
          sort: 0
        },
        {
          id: '9',
          label: '博士毕业后的海外连续工作时长',
          checked: false,
          isRequired: 2,
          sort: 0
        }
      ] as any,

      submitLoading: false,
      initFormData: {} as any,
      formData: {
        id: '',
        name: '',
        backgroundUrl: '',
        introduction: '',
        conclusion: '',
        remark: '',
        intentionOption: [] as any,
        intentExplain: '',
        showMessage: [] as any,
        uploadInstructions: '',
        wechatInstructions: '',
        referenceInstructions: '',
        postdoctorInstitutionInstructions: '',
        postdoctorOverseasDurationInstructions: '',
        successTips: '',
        isSetMaxCount: '1',
        maxCount: 0,
        communityFileIds: [] as any
      },

      intentionDialogVisible: false,
      intentionLoading: false,
      intentionType: [] as any,
      intentionEditIndex: 0,
      // 意向选项表单初始值
      initIntentionFormData: {} as any,
      intentionFormData: {
        id: '',
        title: '',
        sort: '0',
        isShow: 1,
        version: '',
        link: '',
        linkVersion: '',
        signDesc: '',
        // 1:无，2:自定义文本；3:超链接,
        type: 1
      },

      // 默认前端显示题目
      defaultShowType: [
        { id: 4, isRequired: 2 },
        { id: 5, isRequired: 2 }
      ]
    })

    const formRules = reactive({
      intentionOptionRules: {
        title: [{ required: true, message: '请输入选项文字', trigger: 'blur' }],
        link: [
          {
            required: true,
            validator: (rule: any, value: any, callback: any) => {
              if (value === '') {
                callback(new Error('请输入网址'))
              } else if (!verifyUrl(state.intentionFormData.link)) {
                callback(new Error('请输入正确的网址'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        version: [{ required: true, message: '请输入选项说明', trigger: 'blur' }]
      },

      activityRules: {
        name: [{ required: true, message: '请输入表单名称', trigger: 'blur' }],
        intentionOption: [{ required: true, message: '请新增选项设置', trigger: 'change' }]
      }
    })

    const verifiySort = (val) => {
      state.intentionFormData.sort = verifiyNumberInteger(val)
    }
    /**
     * @params checkedOption 数组对象
     * @description id: 选项id， isRequired： 是否必填
     */
    const handleShowSetOption = (checkedOption) => {
      const { initTypeShowSetOption, typeShowSetOption } = state
      state.typeShowSetOption = JSON.parse(JSON.stringify(initTypeShowSetOption))
      const ids = typeShowSetOption.map((item: any) => item.id)
      checkedOption.forEach((item) => {
        const { id, isRequired, sort } = item
        const index = ids.indexOf(id)
        if (index >= 0) {
          state.typeShowSetOption[index].checked = true
          state.typeShowSetOption[index].isRequired = isRequired
          state.typeShowSetOption[index].sort = sort
        }
      })
    }

    const getAllOption = async () => {
      state.intentionType = await getIntentionType()

      const typeShowSetOption = await getShowMessageOption()
      state.typeShowSetOption = typeShowSetOption.map((item) => {
        return {
          id: item.k,
          label: item.v,
          checked: false,
          isRequired: 2,
          sort: 0
        }
      })
    }

    const getDetails = async () => {
      state.loading = true
      const { id: activityId } = state.formData
      const res = await getActivityFormInfo({ id: activityId })
      const { showMessageList, intentionOption, communityFileList, ...other } = res

      const formObj = {
        ...other,
        intentionOption,
        communityFileIds: communityFileList,
        showMessage: showMessageList
      }

      const { formData } = state

      Object.keys(formData).forEach((key) => {
        if (key === 'showMessage') {
          handleShowSetOption(formObj.showMessage)
        } else {
          state.formData[key] = formObj[key]
        }
      })

      if (res.maxCount == 0) {
        state.formData.isSetMaxCount = '1'
      } else {
        state.formData.isSetMaxCount = '2'
      }
      state.loading = false
    }

    onMounted(async () => {
      await getAllOption()

      const { id: intentionId, ...options } = state.intentionFormData
      state.initIntentionFormData = { ...options }
      state.initFormData = JSON.parse(JSON.stringify(state.formData))
      state.initTypeShowSetOption = JSON.parse(JSON.stringify(state.typeShowSetOption))
      handleShowSetOption(state.defaultShowType)

      const { id } = route.params
      if (id) {
        state.formData.id = <string>id
        getDetails()
      }
    })

    // 图片处理 start
    const handleBeforeUpload = (uploadFile) => {
      const { size, maxSize } = uploadFile
      if (size / 1000 > maxSize * 1024) {
        ElMessage.error(`上传图片不能大于${maxSize}M`)
        return false
      }
      return true
    }

    const handleBGSuccess = ({ data = {} }: any) => {
      state.formData.backgroundUrl = data.fullUrl
    }

    const handleBGRemove = () => {
      uploadEl.value.clearFiles()
      state.formData.backgroundUrl = ''
    }

    const handleQRCodeSuccess = ({ data = {} }: any) => {
      const { id, fullUrl } = data
      state.formData.communityFileIds.push({ id, fullUrl })
    }

    const handleQRCodeRemove = (index) => {
      const { communityFileIds } = state.formData
      communityFileIds.splice(index, 1)
      state.formData.communityFileIds = communityFileIds
    }

    const handleImgPreview = (url) => {
      state.previewImageUrl = url
      state.previewDialogVisible = true
    }
    // 图片处理 end

    // 意向选项 start
    const validateIntention = () => {
      activityFormEl.value.validateField(['intentionOption'])
    }

    const handleIntentionAdd = () => {
      state.intentionDialogVisible = true
      state.intentionFormData = { ...state.initIntentionFormData }
    }

    const handleIntentionDelete = (index) => {
      ElMessageBox.confirm(`确定删除该选项吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        ElMessage({
          type: 'success',
          message: '操作成功'
        })
        const { intentionOption } = state.formData
        intentionOption.splice(index, 1)
        state.formData.intentionOption = intentionOption
        validateIntention()
      })
    }

    const handleIntentionEdit = (rowIntentionFormData, index) => {
      state.intentionDialogVisible = true
      state.intentionEditIndex = index
      state.intentionFormData = { ...rowIntentionFormData }
    }

    const handleIntentionStatus = ({ isShow, $index }) => {
      const statusMessage = /1/.test(isShow) ? '隐藏' : '显示'
      ElMessageBox.confirm(`确定${statusMessage}该选项吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        ElMessage({
          type: 'success',
          message: '操作成功'
        })
        state.formData.intentionOption[$index].isShow = /1/.test(isShow) ? 2 : 1
      })
    }

    const handleIntentionSave = () => {
      const { id: intentionId } = state.intentionFormData

      intentionUpdateEl.value.validate(async (valid) => {
        if (valid) {
          state.intentionLoading = true
          const { intentionFormData } = state
          if (intentionId) {
            const { intentionOption } = state.formData
            const { intentionEditIndex } = state
            intentionOption.splice(intentionEditIndex, 1, { ...intentionFormData })
          } else {
            const res = await saveActivityFormintentionOption(state.intentionFormData)
            const intentionObj = { ...intentionFormData, id: res.id }
            state.formData.intentionOption.unshift(intentionObj)
          }
          state.intentionDialogVisible = false
          state.intentionLoading = false
          validateIntention()
        }
      })
    }

    const handleIntentionDialogClose = () => {
      intentionUpdateEl.value.resetFields()
    }
    // 意向选项 end

    const handleClearForm = () => {
      introductionEditorEl.value.clearEditor()
      conclusionEditorEl.value.clearEditor()
      state.formData = JSON.parse(JSON.stringify(state.initFormData))
      state.typeShowSetOption = JSON.parse(JSON.stringify(state.initTypeShowSetOption))
      handleShowSetOption(state.defaultShowType)
    }

    const handleSubmit = () => {
      activityFormEl.value.validate(async (valid) => {
        if (valid) {
          state.submitLoading = true
          const {
            formData: { id, intentionOption, communityFileIds, ...options }
          } = state

          const postData = { ...options } as any

          // 后端就只有一个字段承载，这里就不考虑做个字段了
          if (postData.isSetMaxCount === '1') {
            postData.maxCount = 0
          }

          let showMessage = state.typeShowSetOption.filter((item: any) => item.checked)
          showMessage = showMessage.map((item) => {
            const { checked, ...other } = item
            return other
          })
          const toJSONObj = [
            { intentionOption },
            { showMessage },
            { communityFileIds, handleKey: 'id' }
          ]
          toJSONObj.forEach((item) => {
            const key = Object.keys(item)[0]
            const { handleKey } = item
            let value = ''
            if (handleKey) {
              value = item[key].map((i: any) => i[handleKey]).join()
            } else {
              value = JSON.stringify(item[key])
            }
            postData[key] = value
          })
          if (id) {
            await editActivity({ id, ...postData })
          } else {
            await saveActivity(postData)
          }
          state.submitLoading = false
          handleClearForm()
        }
      })
    }

    const handleSort = (a, b) => {
      return Number(a.sort) - Number(b.sort)
    }

    const handleCancle = () => {
      router.back()
    }

    return {
      uploadEl,
      intentionUpdateEl,
      activityFormEl,
      introductionEditorEl,
      conclusionEditorEl,

      handleBGSuccess,
      handleBGRemove,

      handleQRCodeSuccess,
      handleQRCodeRemove,

      handleBeforeUpload,
      handleImgPreview,

      handleIntentionAdd,
      handleIntentionDelete,
      handleIntentionEdit,
      handleIntentionStatus,
      handleIntentionSave,
      handleIntentionDialogClose,

      handleCancle,
      handleSubmit,
      verifiySort,

      handleSort,

      ...toRefs(formRules),
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.form-activity {
  .el-form-item {
    min-width: 600px;
    max-width: 900px;
    &.special {
      min-width: 750px;
      max-width: 1100px;
    }
  }
  .common-title {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 15px;
    padding-right: 15px;

    &::before {
      content: '';
      width: 3px;
      height: 15px;
      border-radius: 2px;
      background-color: var(--color-primary);
      margin-right: 10px;
    }
  }

  .wrapper-title {
    color: #333;
    font-weight: bold;
    font-size: 15px;
    text-indent: 2em;
  }

  .upload-content {
    .tips {
      margin-left: 10px;
      opacity: 0.6;
    }

    .upload-file {
      margin-top: 10px;
    }

    .upload-file-item {
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      list-style: none;
    }

    .upload-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .el-icon--close {
      position: absolute;
      font-size: 20px;
      right: 5px;
      top: 5px;
      cursor: pointer;

      &:hover {
        color: var(--color-primary);
      }
    }
  }
  .upload-bg {
    .upload-file-item {
      width: 600px;
      height: 130px;
    }
  }

  .option-setting {
    .table {
      margin-top: 18px;
      width: 100%;
    }
  }

  .upload-qrcode {
    .upload-file-item {
      width: 150px;
      height: 150px;
      display: inline-block;
      margin-right: 15px;
    }
  }

  .form-bottom {
    margin-top: 50px;
    .form-btn {
      flex-grow: 1;
      display: flex;
      justify-content: center;
    }
  }
}

.form-option {
  .sort {
    :deep() {
      .el-form-item__content {
        display: flex;
      }
      .el-input {
        width: 100px;
      }
    }

    .tips {
      margin-left: 10px;
      opacity: 0.7;
      font-size: 13px;
    }
  }

  .form-bottom {
    margin-top: 30px;
    .form-btn {
      flex-grow: 1;
      display: flex;
      justify-content: center;
    }
  }
}
.preview-content {
  width: 100%;
  text-align: center;
  .img {
    width: 100%;
    object-fit: contain;
  }
}
</style>
