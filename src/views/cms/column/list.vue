<template>
  <div class="container">
    <div class="box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item label="一级栏目名称">
          <el-input v-model="searchForm.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="一级栏目名称" v-model="searchForm.areaId"> </el-form-item>
        <!-- <el-form-item label="处理状态">
        <el-select v-model="formData.departmentId" placeholder="请选择" filterable clearable>
          <el-option v-for="item in typeList" :key="item.k" :label="item.v" :value="item.k">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈时间">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="formData.addTime"
          type="daterange"
          range-separator="到"
          start-placeholder="开始"
          end-placeholder="结束"
        >
        </el-date-picker>
      </el-form-item> -->

        <el-form-item>
          <el-button size="small" type="primary" @click="getData">搜索</el-button>
          <!-- <el-button size="small" @click="resetSearch">重置</el-button> -->
          <el-button type="primary" @click="showAddForm" size="small">+ 添加栏目</el-button>
          <el-button v-show="!expandAll" @click="expandAll = true" size="small">展开所有</el-button>
          <el-button v-show="expandAll" @click="expandAll = false" size="small">折叠所有</el-button>
        </el-form-item>
      </el-form>
      <br />
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        size="small"
        border
        lazy
        :expand-row-keys="expandRowKeys"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        v-loading="listLoading"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="name" label="栏目名称" width="200" header-align="center">
        </el-table-column>
        <el-table-column prop="id" label="ID" width="100" header-align="center"> </el-table-column>
        <el-table-column prop="creator" label="添加人" width="150" header-align="center">
        </el-table-column>
        <el-table-column prop="templateTypeTxt" label="内容类型" width="150" header-align="center">
        </el-table-column>
        <el-table-column prop="isHideTxt" label="显示" width="100" header-align="center">
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="100" header-align="center">
        </el-table-column>
        <el-table-column label="操作" #default="scope" align="center">
          <el-button type="success" size="small" @click="showAddForm(scope.row)">编辑</el-button>
          <el-button type="warning" size="small" @click="showDictionaryShow(scope.row)"
            >字典</el-button
          >
          <!-- <el-button type="danger" size="small">删除</el-button> -->
          <!-- <el-button
            type="primary"
            plain
            size="small"
            v-if="!scope.row.parentId"
            @click="addSonColumn(scope.row)"
            >添加子栏目</el-button
          > -->
          <!-- <el-button type="primary" size="small">浏览</el-button> -->
          <!-- <el-button type="warning" size="small">隐藏</el-button> -->
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="addFormVisible" title="添加/修改栏目">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="first">
          <el-form :model="formData" label-width="120px">
            <el-form-item label="栏目名称">
              <el-input v-model="formData.name" placeholder="请输入栏目名称"></el-input>
            </el-form-item>

            <el-form-item label="内容类型">
              <el-select v-model="formData.contentType" placeholder="请选择">
                <el-option
                  v-for="item in contentTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
              &nbsp;&nbsp;内容模型字段可在<span style="color: blue">模型管理</span>中修改
            </el-form-item>

            <el-form-item label="所属栏目">
              <el-select v-model="formData.parentId">
                <el-option label="顶级栏目" value="0"></el-option>
                <el-option
                  v-for="item in list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="栏目模板">
              <el-select v-model="formData.templateType" placeholder="请选择栏目模板">
                <el-option
                  v-for="item in templateTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="详情模板">
              <el-select v-model="formData.detailType" placeholder="请选择文章详情模板">
                <el-option
                  v-for="item in detailTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="栏目属性">
              <el-select v-model="formData.operateAttribute" placeholder="请选择文章栏目属性">
                <el-option
                  v-for="item in operateAttributeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="是否显示">
              <el-radio-group v-model="formData.isHide">
                <el-radio v-for="item in isHideList" :key="item.k" :label="item.k">
                  {{ item.v }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="调用栏目代码">
              <el-input v-model="formData.code" placeholder="多个代码用逗号隔开"></el-input>
            </el-form-item>

            <el-form-item label="调用特色标签" placeholder="请选择">
              <el-select v-model="formData.label">
                <el-option label="年度招聘" value="1"></el-option>
                <el-option label="海归人才" value="2"></el-option>
                <el-option label="政府引才" value="3"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submit" :loading="submitLoading">保存</el-button>
              <el-button @click="addFormVisible = false">取消</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="高级设置" name="second">
          <el-form :model="formData" label-width="120px">
            <el-form-item label="栏目域名">
              <el-input v-model="formData.path" placeholder="不填则使用系统默认"></el-input>
            </el-form-item>

            <el-form-item label="SEO标题">
              <el-input
                v-model="formData.seoTitle"
                placeholder="针对搜索引擎设置的标题；不填则使用系统默认"
              ></el-input>
            </el-form-item>

            <el-form-item label="SEO关键词">
              <el-input
                v-model="formData.seoKeywords"
                placeholder="多个关键词请用英文“,“隔开；建议3-5个关键词"
              ></el-input>
            </el-form-item>

            <el-form-item label="SEO描述">
              <el-input
                v-model="formData.seoDescription"
                type="textarea"
                placeholder="不填则使用系统默认"
              ></el-input>
            </el-form-item>

            <el-form-item label="栏目描述">
              <el-input
                v-model="formData.content"
                type="textarea"
                placeholder="不填则使用系统默认"
              ></el-input>
            </el-form-item>

            <el-form-item label="排序">
              <el-input v-model="formData.sort" placeholder="不填则使用系统默认"></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submit" :loading="submitLoading">保存</el-button>
              <el-button @click="addFormVisible = false">取消</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog title="字典关联" v-model="dictionaryFormVisible">
      <el-form :model="dictionaryForm" label-width="120px">
        <el-form-item label="学科字典">
          <el-select v-model="dictionaryForm.majorId" placeholder="学科" filterable clearable>
            <el-option v-for="item in majorList" :key="item.k" :label="item.v" :value="item.k">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地区字典">
          <el-cascader
            :options="areaList"
            :props="{ checkStrictly: true, emitPath: false }"
            clearable
            v-model="dictionaryForm.areaId"
          />
        </el-form-item>
        <el-form-item label="职位类型">
          <el-cascader
            :options="categoryList"
            :props="{ checkStrictly: true, emitPath: false }"
            clearable
            v-model="dictionaryForm.categoryId"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitDictionary">保存</el-button>
          <el-button @click="dictionaryFormVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, computed, defineComponent, unref } from 'vue'
import { getColumnList, toUpdate, getParams, getDictionary, setDictionary } from '/@/api/column'
import Region from '/@select/region.vue'

export default defineComponent({
  name: 'cmsColumnList',
  components: {
    Region
  },
  setup() {
    const state = reactive({
      tableData: [],
      addFormVisible: false,
      dictionaryFormVisible: false,
      submitLoading: false,
      expandAll: false,
      expandKeys: [],
      expandRowKeys: computed(() => (state.expandAll ? state.expandKeys : [])),
      formData: {
        name: '',
        contentType: '1',
        column: '1',
        templateType: '',
        detailType: '',
        isHideType: '2',
        code: '',
        label: '',
        sort: '',
        parentId: '',
        isHide: '',
        path: '',
        seoKeywords: '',
        seoDescription: '',
        content: '',
        seoTitle: '',
        id: '',
        operateAttribute: ''
      },
      activeName: 'first',
      contentTypeList: [],
      detailTypeList: [],
      operateAttributeList: [],
      isHideList: [],
      list: [],
      templateTypeList: [],
      showMore: true,
      listLoading: false,
      searchForm: {
        name: '',
        areaId: []
      },
      dictionaryForm: {
        id: '',
        majorId: '',
        areaId: ''
      },
      majorList: [],
      areaList: [],
      categoryList: []
    })

    // 拿数据方法
    const getData = async () => {
      // 出个loading
      state.listLoading = true
      const data = await getColumnList(state.searchForm)
      state.listLoading = false
      // 目前只考虑两级栏目
      state.tableData = data.map((item: any) => {
        const parentId = item.id
        const { children } = item
        const result = { ...item, children: [] }
        state.expandKeys.push(item.id as never)
        if (children.length) {
          result.children = children.map((it: any) => ({ ...it, parentId }))
        }
        return result
      })
    }

    onMounted(() => {
      getData()
      state.listLoading = true
      getParams({}).then((res: any) => {
        state.listLoading = false
        // 递归把含有children的k=>label,v=>value
        res.areaList = res.areaList.map((item: any) => ({
          value: item.k,
          label: item.v,
          children: item.children.map((it: any) => ({
            value: it.k,
            label: it.v
          }))
        }))

        res.categoryList = res.categoryList.map((item: any) => ({
          value: item.k,
          label: item.v,
          children: item.children.map((it: any) => ({
            value: it.k,
            label: it.v
          }))
        }))

        const keys = Object.keys(res)
        keys.forEach((key: string) => {
          state[key] = res[key]
        })
      })
    })

    const showAddForm = (row: any) => {
      state.formData = { ...row }
      if (!state.formData.parentId) {
        state.formData.parentId = '0'
      }
      state.addFormVisible = true
    }

    const submit = () => {
      state.submitLoading = true
      const {
        formData: { id, ...data },
        formData
      } = state
      const isTopLevel = !formData.parentId

      toUpdate(id ? formData : data)
        .then(() => {
          state.submitLoading = false

          state.tableData.forEach((item: any, index) => {
            if (isTopLevel) {
              if (item.id === id) {
                const { children = [] } = item
                const result = { ...formData, children } as never

                state.tableData.splice(index, 1, result)
              }
              return
            }

            if (item.id === formData.parentId) {
              item.children.forEach((it, idx) => {
                if (it.id === id) {
                  item.children.splice(idx, 1, { ...formData })
                }
              })
            }
          })
          // 修改完更新列表，避免再次编辑保留的是初始数据
          getData()
          state.addFormVisible = false
        })
        .catch(() => {
          state.submitLoading = false
        })
    }

    const submitDictionary = () => {
      const data = {
        majorId: state.dictionaryForm.majorId,
        areaId: state.dictionaryForm.areaId,
        id: state.dictionaryForm.id,
        categoryId: state.dictionaryForm.categoryId
      }

      setDictionary(data).then(() => {
        state.submitLoading = false
        state.dictionaryFormVisible = false
      })
    }

    const showDictionaryShow = (row: any) => {
      getDictionary(row.id).then((res: any) => {
        state.dictionaryForm = {
          majorId: res.majorId,
          areaId: res.areaId,
          categoryId: res.categoryId,
          id: row.id
        }
        state.dictionaryFormVisible = true
      })
    }

    const addSonColumn = (row: any) => {
      state.formData = {
        name: '',
        contentType: '',
        column: '',
        templateType: '',
        detailType: '',
        isHideType: '',
        code: '',
        label: '',
        sort: '',
        parentId: '',
        isHide: '',
        path: '',
        seoKeywords: '',
        seoDescription: '',
        content: '',
        seoTitle: '',
        id: ''
      }
      state.formData.parentId = row.id

      state.addFormVisible = true
    }

    return {
      ...toRefs(state),
      showAddForm,
      submit,
      getData,
      addSonColumn,
      showDictionaryShow,
      submitDictionary
    }
  }
})
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
</style>
