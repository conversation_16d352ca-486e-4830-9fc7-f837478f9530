<template>
  <div class="abroad-advertising">
    <el-form ref="form" :model="formData" label-width="70px" :size="formSize">
      <div class="flex">
        <el-form-item class="span-5" label="广告标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请填写广告标题"
            clearable
            @keyup.enter="search()"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="广告位置" prop="homePositionId">
          <AdBoShiHouPosition v-model="formData.homePositionId" multiple collapseTags />
        </el-form-item>
        <el-form-item class="span-5" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            :size="formSize"
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5" label="生效时间" prop="onlineTimeStart">
          <DatePickerRange
            :size="formSize"
            v-model:start="formData.onlineTimeStart"
            v-model:end="formData.onlineTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5" label="失效时间" prop="offlineTimeStart">
          <DatePickerRange
            :size="formSize"
            v-model:start="formData.offlineTimeStart"
            v-model:end="formData.offlineTimeEnd"
          />
        </el-form-item>
      </div>

      <div class="flex">
        <el-form-item class="span-5" label="广告状态" prop="status">
          <AdStatus v-model="formData.status" multiple collapseTags />
        </el-form-item>
        <el-form-item class="span-5" label="显示状态" prop="isShow">
          <AdShowStatus v-model="formData.isShow" multiple collapseTags />
        </el-form-item>
        <el-form-item class="span-5" label="是否打包" prop="isPacking">
          <AdPackingStatus v-model="formData.isPacking" />
        </el-form-item>

        <el-form-item class="span-5" label="创建人" prop="creator">
          <el-input
            v-model="formData.creator"
            placeholder="创建人"
            clearable
            @keyup.enter="search()"
          ></el-input>
        </el-form-item>
      </div>

      <div class="flex">
        <el-form-item class="span-5" label="广告类型" prop="type">
          <el-select
            multiple
            collapseTags
            v-model="formData.type"
            placeholder="广告类型"
            filterable
            clearable
          >
            <el-option v-for="item in typeOptions" :key="item.k" :label="item.v" :value="item.k" />
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="选择单位" prop="companyId">
          <SelectCompany v-model="formData.companyId" />
        </el-form-item>
      </div>
    </el-form>

    <el-row style="margin-bottom: 20px">
      <el-button type="primary" @click="search()">搜索</el-button>
      <el-button type="default" @click="resetForm()">重置</el-button>
    </el-row>

    <div class="mb-15">
      <el-button @click="openAddDialog()" type="primary" :size="formSize">+ 新增广告</el-button>
    </div>

    <el-table
      ref="table"
      :data="tableData"
      border
      v-loading="loading"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortable"
      :cell-class-name="tableRowClassName"
      size="small"
      height="610"
    >
      <el-table-column type="selection" align="center" header-align="center" width="55" />
      <el-table-column
        prop="packingId"
        label="打包id"
        align="center"
        width="60"
        header-align="center"
      />
      <!--      <el-table-column prop="id" label="编号" align="center" header-align="center" />-->
      <el-table-column prop="typeName" label="广告位类型" header-align="center" width="140">
        <template #default="{ row }">
          <span v-for="item in row.typeName" :key="item.k">
            <span class="btn-sm btn-m-flex bg-color-1" v-if="item.type === '1'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-2" v-else-if="item.type === '2'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-3" v-else-if="item.type === '3'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-4" v-else-if="item.type === '4'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-5" v-else-if="item.type === '5'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-6" v-else-if="item.type === '6'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-7" v-else-if="item.type === '7'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-8" v-else-if="item.type === '8'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-9" v-else-if="item.type === '9'">{{
              item.name
            }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        label="广告标题"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="name"
        label="广告位置"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        sortable="custom"
        prop="sortAddTime"
        label="创建时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.addTime }}
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="sortOnlineTime"
        label="生效时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.onlineTime }}
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="sortOfflineTime"
        label="失效时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.offlineTime }}
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="sortIsShow"
        label="显示状态"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.isShow == '1' ? '显示' : '隐藏' }}
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="sortSort"
        label="排序"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>
            <div v-show="row.id === sortEditId">
              <el-input
                :input-style="{ 'text-align': 'center' }"
                v-model.trim="row.sort"
                @keydown.enter="handleSort(row)"
              ></el-input>
            </div>
            <span v-show="row.id !== sortEditId">{{ row.sort }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="creator"
        label="创建人"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column label="广告状态" align="center" header-align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.statusTxt }}
        </template>
      </el-table-column>
      <el-table-column label="关联单位" align="center" header-align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.companyName }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="操作"
        header-align="center"
        min-width="150px"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="openAddDialog(row.id)"
            class="button-margin"
          >
            编辑
          </el-button>
          <el-button type="default" size="small" @click="copy(row)" class="button-margin">
            复制
          </el-button>
          <el-button type="success" size="small" class="button-margin" style="padding: 0px">
            <span @click="openEcharts(row.id, $event)" class="reference" style="padding: 9px 10px"
              >统计</span
            >
          </el-button>

          <el-button type="warning" size="small" @click="handleShow(row)" class="button-margin">
            <span v-if="row.isShow == '1'">隐藏</span>
            <span v-else>显示</span>
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row.id)" class="button-margin">
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty></el-empty>
      </template>
    </el-table>

    <div v-if="tableData.length" class="mt-15 jc-between">
      <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          label="全选"
          class="mr-10"
        ></el-checkbox>
        <el-select
          clearable
          :disabled="!multipleSelection.length"
          v-model="batchValue"
          placeholder="批量操作"
          @change="handleBatchChange"
        >
          <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k">
          </el-option>
        </el-select>
      </div>
      <Paging
        ref="pageFef"
        :total="pages.total"
        :page="formData.page"
        @change="changePage"
      ></Paging>
    </div>

    <Add @update="(isEdit) => search(isEdit)" ref="add" />

    <PopoverEcharts ref="popoverEcharts" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, nextTick } from 'vue'
import { ElMessageBox } from 'element-plus'
import { debounce } from 'throttle-debounce'
import { useRoute } from 'vue-router'

import Paging from '/@/components/base/paging.vue'
import Add from './components/add.vue'
import AdStatus from '/@select/adStatus.vue'
import AdShowStatus from '/@select/adShowStatus.vue'
import AdPackingStatus from '/@select/adPackingStatus.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AdBoShiHouPosition from '/@select/adBoShiHouPosition.vue'
import SelectCompany from './components/selectCompany.vue'
import PopoverEcharts from './components/popoverEcharts.vue'

import { verifiyAllNumberInteger, verifiyNumberInteger } from '/@/utils/toolsValidate'

import {
  getShowcaseList,
  deleteShowcase,
  changeShowcaseSort,
  changeShowcaseShow
} from '/@/api/advertising'

const formSize = ref('default')
const add = ref()
const form = ref()
const table = ref()
const pageFef = ref()
const popoverEcharts = ref()
const route = useRoute()
/// /广告位类型1付费  2RPO 3异议 4客情 8免费 9其他
const typeOptions = reactive([
  {
    k: '1',
    v: '付费'
  },
  {
    k: '2',
    v: 'RPO'
  },
  {
    k: '3',
    v: '异议'
  },
  {
    k: '4',
    v: '客情'
  },
  {
    k: '5',
    v: '推广'
  },
  {
    k: '6',
    v: '高级'
  },
  {
    k: '7',
    v: '旧链'
  },
  {
    k: '8',
    v: '免费'
  },
  {
    k: '9',
    v: '其他'
  }
])

const formData: any = reactive({
  title: '',
  platformId: 7,
  homePositionId: [],
  addTimeStart: '',
  addTimeEnd: '',
  onlineTimeStart: '',
  onlineTimeEnd: '',
  offlineTimeStart: '',
  offlineTimeEnd: '',
  status: [],
  isShow: [],
  isPacking: '',
  companyId: '',
  creator: '',
  type: [],
  name: '',
  page: 1,
  limit: 20
})

let sortFormData: any = {
  sortAddTime: '',
  sortOnlineTime: '',
  sortOfflineTime: '',
  sortIsShow: '',
  sortSort: ''
}

let packingIdList: any = []
const multipleSelection = ref([])
const batchValue = ref('')
const batchOptions = ref([
  { k: 1, v: '批量显示' },
  { k: 2, v: '批量隐藏' },
  { k: -1, v: '批量删除' }
])
const checkedAll = ref(false)

const sortEditId = ref('')
const tableData = ref([])
const loading = ref(false)
const pages = reactive({
  page: 1,
  total: 0
})

const getList = () => {
  loading.value = true
  packingIdList = []
  const { homePositionId, status, isShow, type, ...other } = formData
  getShowcaseList({
    homePositionId: homePositionId.join(),
    status: status.join(),
    isShow: isShow.join(),
    type: type.join(),
    ...other,
    ...sortFormData
  }).then((resp: any) => {
    const {
      page: { count },
      list
    } = resp

    pages.total = count
    tableData.value = list
    loading.value = false
  })
}

const search = (isEdit = false) => {
  packingIdList = []
  table.value.clearSort()
  if (!isEdit && pageFef.value !== null) {
    formData.page = 1
  }
  sortFormData = {}
  getList()
}

onMounted(async () => {
  const { positionId } = route.query
  if (positionId) {
    formData.homePositionId = <string>positionId
  }
  getList()
})

const openAddDialog = (id: string = '') => {
  add.value.open(id)
}

const handleChange = () => {
  table.value.toggleAllSelection()
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
  if (val.length === tableData.value.length) {
    checkedAll.value = true
  } else {
    checkedAll.value = false
  }
  if (!val.length) {
    batchValue.value = ''
  }
}

// 鼠标移入单元格时
const handleCellMouseEnter = (row, column) => {
  if (column.label === '排序') {
    sortEditId.value = row.id
  }
}
// 鼠标移出单元格时
const handleCellMouseLeave = () => {
  sortEditId.value = ''
}

// 修改排序
const handleSort = (row: any) => {
  const { id, sort } = row
  const newSort = verifiyAllNumberInteger(sort)
  row.sort = newSort
  debounce(3000, changeShowcaseSort({ id, sort: newSort }))
}

const handleDelete = (id: string, batch = false) => {
  ElMessageBox({
    title: '提示',
    message: batch ? '确定批量删除广告吗？' : '确定删除该广告吗？',
    center: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        // eslint-disable-next-line no-param-reassign
        instance.confirmButtonLoading = true
        await deleteShowcase({ id })
        if (checkedAll.value || tableData.value.length === 1) {
          formData.page = 1
        }
        getList()
        done()
      } else {
        done()
      }
    }
  })
}

const handleShow = (row: any) => {
  ElMessageBox({
    title: '提示',
    message: row.isShow === '1' ? '确定隐藏该广告吗？' : '确定显示该广告吗？',
    center: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        // eslint-disable-next-line no-param-reassign
        instance.confirmButtonLoading = true
        await changeShowcaseShow({ id: row.id, isShow: row.isShow === '1' ? '2' : '1' })
        getList()
        done()
      } else {
        done()
      }
    }
  })
}

// 重置
const resetForm = () => {
  form.value.resetFields()
  nextTick(() => {
    search()
  })
}

const handleBatchChange = async (val: any) => {
  if (!val) return
  const ids = multipleSelection.value.map((item: any) => item.id).join(',')
  if (val === -1) {
    handleDelete(ids, true)
  } else {
    await changeShowcaseShow({ id: ids, isShow: val })
    getList()
  }
}
const changePage = (r: any) => {
  const { page, limit } = r
  formData.page = page
  formData.limit = limit
  getList()
}

const openEcharts = (id: any, el: any) => {
  popoverEcharts.value.open(id, el.target)
}

const copy = (row) => {
  add.value.open(row.id, 'copy')
}

// 排序
const handleSortable = ({ prop, order }) => {
  let sort = <any>''
  if (order === 'ascending') {
    // 正序
    sort = 2
  } else if (order === 'descending') {
    sort = 1
  }

  if (prop !== null) {
    sortFormData[prop] = sort
  }
  getList()
}

const tableRowClassName = ({ row, column }) => {
  if (column.property === 'packingId') {
    const { packingId } = row
    if (packingIdList.indexOf(packingId) === -1) {
      packingIdList.push(packingId)
    }

    // 找到这个值在数组中的索引
    const num = packingIdList.findIndex((item) => item === packingId) + 1

    return `table-row-${num}`
  }

  return ''
}
</script>
<style lang="scss" scoped>
.el-button--small {
  padding: 9px 10px;
}

.abroad-advertising {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.button-margin {
  margin: 5px; /* 下边距 5px */
}

.btn-sm {
  border: 1px solid #eeeeee;
  border-radius: 5px;
  padding: 2px 5px;
  color: #000000;
  margin-right: 4px;
}

$bgColor: 1 #ffc6b3, 2 #b3b3ff, 3 #fe7f7f, 4 #7ffe8e, 5 #a0fded, 6#fe7ff8, 7 #d4d4d4, 8 #e2ffb3,
  9 #fffbb3;

@each $i, $color in $bgColor {
  .bg-color-#{$i} {
    background-color: #{$color};
  }
}

// 定义20个颜色
$rowBg: #ffc6b3, #ffd9b3, #ffecb3, #ffffb3, #ecffb3, #d9ffb3, #c6ffb3, #b3ffb3, #b3ffc6, #b3ffd9,
  #b3ffda, #b3ffec, #b3ffff, #b3ecff, #b3d9ff, #b3c6ff, #b3b3ff, #c6b3ff, #d9b3ff, #ecb3ff, #ffb3ff,
  #ffb3ec, #ffb3d9, #ffb3c6;

:deep() {
  .el-select {
    width: 100%;
  }

  .el-form-item__label {
    padding-right: 5px !important;
  }

  @each $color in $rowBg {
    $i: index($rowBg, $color);
    .table-row-#{$i} {
      background-color: #{$color};
    }
  }
}
</style>
