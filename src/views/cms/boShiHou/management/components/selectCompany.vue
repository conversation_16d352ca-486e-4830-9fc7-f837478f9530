<template>
  <el-select
    v-model="value"
    filterable
    remote
    reserve-keyword
    placeholder="请选择单位"
    :remote-method="fetchList"
    clearable
  >
    <el-option v-for="item in list" :key="item.id" :label="item.fullName" :value="item.id">
    </el-option>
  </el-select>
</template>

<script lang="ts" setup>
import { computed, ref, toRef, watch } from 'vue'

import { getCompanyList } from '/@/api/configuration'

const props = defineProps({
  modelValue: String,
  name: String
})
const emit = defineEmits(['update:modelValue'])

const list: any = ref([])

const value = computed({
  get: () => toRef(props, 'modelValue').value,
  set: (setValue) => {
    emit('update:modelValue', setValue)
  }
})

const fetchList = (kw: string = '') => {
  getCompanyList({ name: kw }).then((resp: any) => {
    list.value = resp
  })
}

watch(
  () => props.name,
  (kw) => {
    fetchList(kw)
  }
)

fetchList()
</script>

<style lang="scss" scoped></style>
