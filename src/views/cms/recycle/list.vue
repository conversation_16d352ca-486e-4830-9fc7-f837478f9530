<template>
  <div>
    <el-card>
      <el-form v-show="activeName !== 'adList'" ref="formData" label-width="80px" :model="form">
        <el-row>
          <el-col :span="4">
            <el-form-item label="标题检索" prop="titleNum">
              <el-input
                v-model="form.titleNum"
                placeholder="请输入标题/职位名称或编号"
                clearable
                @keyup.enter="getAnnounceSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="所属栏目" prop="homeColumnId">
              <el-cascader
                v-model="form.homeColumnId"
                :options="columnList"
                :props="{ value: 'k', label: 'v' }"
                filterable
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="发布人" prop="creator">
              <el-input
                v-model="form.creator"
                placeholder="请输入发布人信息"
                clearable
                @keyup.enter="getAnnounceSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="所属单位" prop="companyName">
              <el-input
                v-model="form.companyName"
                placeholder="请输入单位"
                clearable
                @keyup.enter="getAnnounceSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="删除日期" prop="deleteTimeStart">
              <DatePickerRange
                v-model:start="form.deleteTimeStart"
                v-model:end="form.deleteTimeEnd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button class="button" type="primary" @click="getAnnounceSearch">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="公告列表" name="announceList">
            <el-table
              v-loading="listLoading"
              border
              :data="announceData"
              ref="announce"
              @selection-change="handleAnnounceChange"
              @sort-change="handleSortable"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column label="编号" prop="announcementUid" align="center" />
              <el-table-column label="标题" prop="title" align="center">
                <template #default="{ row }">
                  <span class="color-primary point" @click="toAnnounceDetail(row.aid)">{{
                    row.title
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属单位" prop="companyName" align="center">
                <template #default="{ row }">
                  <span class="color-primary point" @click="toCompanyDetail(row.companyId)">{{
                    row.companyName
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属栏目" prop="columnName" align="center" />
              <el-table-column label="点击" prop="click" align="center" />
              <el-table-column
                label="更新时间"
                prop="sortUpdateTime"
                align="center"
                sortable="custom"
              >
                <template #default="{ row }">
                  {{ row.updateTime }}
                </template>
              </el-table-column>
              <el-table-column
                label="删除时间"
                prop="sortDeleteTime"
                align="center"
                sortable="custom"
              >
                <template #default="{ row }">
                  {{ row.deleteTime }}
                </template>
              </el-table-column>
              <el-table-column label="发布人" prop="creatorName" align="center" />
              <el-table-column label="操作" align="center">
                <template #default="{ row }">
                  <el-button type="primary" link @click="reductionAnnounce(row.aid)"
                    >还原</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-15 jc-between">
              <div class="ai-center">
                <el-checkbox
                  v-model="checkAll"
                  label="全选"
                  class="mr-10"
                  @change="announceChange"
                ></el-checkbox>
                <el-select
                  v-model="batchValue"
                  placeholder="批量操作"
                  :disabled="!announceSelection.length"
                  @change="announceSelectionBatch"
                >
                  <el-option
                    v-for="item in batchOptions"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  />
                </el-select>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="职位列表" name="jobList">
            <el-table
              v-loading="listLoading"
              border
              :data="jobData"
              ref="job"
              @selection-change="handleJobChange"
              @sort-change="handleSortable"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column label="编号" prop="jobUid" align="center" />
              <el-table-column label="职位名称" prop="name" align="center">
                <template #default="{ row }">
                  <span class="color-primary point" @click="showJobDetail(row.id)">{{
                    row.name
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="基本信息" prop="basicInformation" align="center" />
              <el-table-column label="关联公告" prop="title" align="center">
                <template #default="{ row }">
                  <span class="color-primary point" @click="toAnnounceDetail(row.announcementId)">{{
                    row.title
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属单位" prop="company" align="center">
                <template #default="{ row }">
                  <span class="color-primary point" @click="toCompanyDetail(row.companyId)">{{
                    row.company
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属栏目" prop="columnName" align="center" />
              <el-table-column
                label="删除日期"
                prop="sortDeleteTime"
                align="center"
                sortable="custom"
              >
                <template #default="{ row }">
                  {{ row.deleteTime }}
                </template>
              </el-table-column>
              <el-table-column label="发布人" prop="creator" align="center" />
              <el-table-column label="操作" align="center">
                <template #default="{ row }">
                  <el-button type="primary" link @click="reductionJob(row.id)">还原</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-15 jc-between">
              <div class="ai-center">
                <el-checkbox
                  v-model="checkAllJob"
                  label="全选"
                  class="mr-10"
                  @change="jobChange"
                ></el-checkbox>
                <el-select
                  v-model="jobBatchValue"
                  placeholder="批量操作"
                  :disabled="!jobSelection.length"
                  @change="jobSelectionBatch"
                  clearable
                >
                  <el-option
                    v-for="item in batchOptions"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  />
                </el-select>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="广告列表" name="adList">
            <el-form label-width="80px" ref="adFormRef" :model="adForm" size="small">
              <div class="flex">
                <el-form-item class="span-6" label="广告标题" prop="title">
                  <el-input
                    class="w100"
                    v-model="adForm.title"
                    placeholder="请填写广告标题"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item class="span-6" label="所属平台" prop="platformId">
                  <Adplatform is-all v-model="adForm.platformId" />
                </el-form-item>
                <el-form-item class="span-6" label="广告位置" prop="homePositionId">
                  <el-cascader
                    v-model="adForm.homePositionId"
                    class="w100"
                    :options="homePositionOptions"
                    :props="{ value: 'k', label: 'v', emitPath: false, multiple: false }"
                    placeholder="请选择"
                    clearable
                    filterable
                    :change-on-select="true"
                  ></el-cascader>
                </el-form-item>
                <el-form-item class="span-6" label="创建时间" prop="addTimeStart">
                  <DatePickerRange
                    size="small"
                    :start="adForm.addTimeStart"
                    v-model:start="adForm.addTimeStart"
                    v-model:end="adForm.addTimeEnd"
                  />
                </el-form-item>
                <el-form-item class="span-6" label="生效时间" prop="onlineTimeStart">
                  <DatePickerRange
                    size="small"
                    :start="adForm.onlineTimeStart"
                    v-model:start="adForm.onlineTimeStart"
                    v-model:end="adForm.onlineTimeEnd"
                  />
                </el-form-item>
                <el-form-item class="span-6" label="失效时间" prop="offlineTimeStart">
                  <DatePickerRange
                    size="small"
                    :start="adForm.offlineTimeStart"
                    v-model:start="adForm.offlineTimeStart"
                    v-model:end="adForm.offlineTimeEnd"
                  />
                </el-form-item>
              </div>
              <div class="flex">
                <el-form-item class="span-6" label="显示状态" prop="isShow">
                  <AdShowStatus v-model="adForm.isShow" />
                </el-form-item>
                <el-form-item class="span-6" label="是否打包" prop="isPacking">
                  <AdPackingStatus v-model="adForm.isPacking" />
                </el-form-item>
                <el-form-item class="span-6" label="创建人" prop="creator">
                  <el-input v-model="adForm.creator" placeholder="创建人" clearable></el-input>
                </el-form-item>

                <el-form-item class="span-6" label-width="10px">
                  <el-button type="primary" @click="fetchAdList()">搜索</el-button>
                  <el-button type="default" @click="resetAdForm()">重置</el-button>
                </el-form-item>
              </div>
            </el-form>

            <el-table
              v-loading="listLoading"
              border
              :data="adList"
              ref="ad"
              @selection-change="handleAdChange"
            >
              <el-table-column type="selection" width="55" />

              <el-table-column
                prop="title"
                label="广告标题"
                align="center"
                header-align="center"
                show-overflow-tooltip
              />
              <el-table-column
                prop="platformTypeTitle"
                label="所属平台"
                align="center"
                header-align="center"
              />
              <el-table-column
                prop="name"
                label="广告位置"
                align="center"
                header-align="center"
                show-overflow-tooltip
              />
              <el-table-column
                prop="sortAddTime"
                label="创建时间"
                align="center"
                header-align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.addTime }}
                </template>
              </el-table-column>
              <el-table-column
                prop="sortOnlineTime"
                label="生效时间"
                align="center"
                header-align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.onlineTime }}
                </template>
              </el-table-column>
              <el-table-column
                prop="sortOfflineTime"
                label="失效时间"
                align="center"
                header-align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.offlineTime }}
                </template>
              </el-table-column>
              <el-table-column
                prop="sortIsShow"
                label="显示状态"
                align="center"
                header-align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.isShow == '1' ? '显示' : '隐藏' }}
                </template>
              </el-table-column>

              <el-table-column
                prop="creator"
                label="创建人"
                align="center"
                header-align="center"
                show-overflow-tooltip
              />
              <el-table-column
                label="广告状态"
                align="center"
                header-align="center"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.statusTxt }}
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="操作"
                align="center"
                header-align="center"
                min-width="150px"
                fixed="right"
              >
                <template #default="{ row }">
                  <div>
                    <el-link
                      @click="reductionAd(row.id)"
                      type="primary"
                      class="fs-13"
                      :underline="false"
                      >还原 </el-link
                    >&nbsp;
                  </div>
                </template>
              </el-table-column>
              <template #empty>
                <el-empty></el-empty>
              </template>
            </el-table>

            <div class="mt-15 jc-between">
              <div class="ai-center">
                <el-checkbox
                  v-model="checkAllAd"
                  label="全选"
                  class="mr-10"
                  @change="adChange"
                ></el-checkbox>
                <el-select
                  v-model="jobBatchValue"
                  placeholder="批量操作"
                  :disabled="!adSelection.length"
                  @change="adSelectionBatch"
                  clearable
                >
                  <el-option
                    v-for="item in batchOptions"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  />
                </el-select>
              </div>
            </div>

            <div class="paging">
              <Paging :total="adTotal" @change="adChangePage" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="paging" v-show="activeName !== 'adList'">
        <Paging
          :total="pages.total"
          :page="adForm.page"
          :default-page-size="adPageSize"
          @change="changePage"
        />
      </div>
    </el-card>
    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'
import { getHomeColumnList } from '/@/api/news'
import {
  announcementRecycleBatchReduction,
  announcementRecycleReduction,
  getAnnouncementRecycleList,
  getJobRecycleList,
  jobRecycleBatchReduction,
  jobRecycleReduction,
  getShowcaseList,
  adRecycleReduction
} from '/@/api/recycle'
import { getHomePositionList } from '/@/api/advertising'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

import AdStatus from '/@select/adStatus.vue'
import AdShowStatus from '/@select/adShowStatus.vue'
import AdPackingStatus from '/@select/adPackingStatus.vue'
import Adplatform from '/@select/adPlatform.vue'

export default defineComponent({
  name: 'cmsRecycle',

  components: {
    DatePickerRange,
    Paging,
    JobDetailDialog,
    AdStatus,
    AdShowStatus,
    AdPackingStatus,
    Adplatform
  },

  setup() {
    const state = reactive({
      form: {
        titleNum: '',
        homeColumnId: '',
        creator: '',
        companyName: '',
        deleteTimeStart: '',
        deleteTimeEnd: '',
        sortDeleteTime: '',
        page: 1,
        limit: ''
      },
      columnList: [],
      activeName: 'announceList',
      announceData: [],
      jobData: [],
      listLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      batchOptions: [{ k: 1, v: '还原' }],
      announceSelection: [],
      jobSelection: [],
      announceIds: '',
      jobIds: '',
      checkAll: false,
      checkAllJob: false,
      batchValue: '',
      jobBatchValue: '',

      checkAllAd: false,
      adSelection: [],
      homePositionOptions: [],
      adForm: {
        title: '',
        platformId: '',
        homePositionId: '',
        addTimeStart: '',
        addTimeEnd: '',
        onlineTimeStart: '',
        onlineTimeEnd: '',
        offlineTimeStart: '',
        offlineTimeEnd: '',
        status: '9',
        isShow: '',
        isPacking: '',
        creator: '',
        sortAddTime: '',
        sortOnlineTime: '',
        sortOfflineTime: '',
        sortIsShow: '',
        sortSort: '',
        type: '',
        isAllPlatform: '1',
        page: 1
      },
      adList: [],
      adPageSize: 20,
      adTotal: 0
    })
    const formData = ref()
    const announce = ref()
    const job = ref()
    const ad = ref()
    const adFormRef = ref()
    const jobDetailDialog = ref()
    const router = useRouter()
    const getData = async () => {
      const { columnList } = await getHomeColumnList()
      state.columnList = columnList
    }
    getData()
    const getAnnounceSearch = () => {
      state.listLoading = true
      getAnnouncementRecycleList(state.form).then((res: any) => {
        state.announceData = res.list
        state.pages.size = res.pages.size
        state.pages.total = res.pages.total
        state.listLoading = false
      })
    }
    getAnnounceSearch()

    const fetchAdList = () => {
      state.listLoading = true

      getShowcaseList(state.adForm).then((res: any) => {
        const {
          list,
          page: { count, limit }
        } = res
        state.adList = list
        state.adPageSize = limit
        state.adTotal = count
        state.listLoading = false
      })
    }

    const resetAdForm = () => {
      adFormRef.value.resetFields()
      state.adForm.page = 1
      nextTick(() => {
        fetchAdList()
      })
    }

    const reductionAd = async (id) => {
      await adRecycleReduction({ id })
      fetchAdList()
    }

    const adChange = () => {
      ad.value.toggleAllSelection()
    }

    const handleAdChange = (data: any) => {
      state.adSelection = data
      if (state.adSelection.length === state.adList.length) {
        state.checkAllAd = true
      } else {
        state.checkAllAd = false
      }
    }

    const adSelectionBatch = async (val: number) => {
      const ids = state.adSelection.map((item: any) => item.id).join()
      if (val === 1) {
        await reductionAd(ids)
      }
    }

    const getAdPosition = async () => {
      await getHomePositionList({ homePositionName: '', isAllPlatform: 1 }).then((resp: any) => {
        state.homePositionOptions = resp
      })
    }

    getAdPosition()

    const getJobSearch = async () => {
      state.listLoading = true
      const { list, pages } = await getJobRecycleList(state.form)
      state.jobData = list
      state.pages.size = pages.size
      state.pages.total = pages.total
      state.listLoading = false
    }
    const changePage = (r: any) => {
      if (state.activeName === 'announceList') {
        state.form.page = r.page
        state.form.limit = r.total
        getAnnounceSearch()
      } else {
        getJobSearch()
      }
    }
    const reset = () => {
      formData.value.resetFields()
      getAnnounceSearch()
    }
    const handleClick = (tab: any) => {
      if (tab.paneName === 'jobList') {
        getJobSearch()
      } else if (tab.paneName === 'announceList') {
        getAnnounceSearch()
      } else {
        fetchAdList()
      }
    }
    const reductionAnnounce = async (id: string) => {
      await announcementRecycleReduction({ id })
      getAnnounceSearch()
    }
    const reductionJob = async (id: string) => {
      await jobRecycleReduction({ id })
      getJobSearch()
    }

    const announceChange = () => {
      announce.value.toggleAllSelection()
    }

    const handleAnnounceChange = (data: any) => {
      state.announceSelection = data
      state.announceIds = state.announceSelection.map((item: any) => item.aid).join()
      if (state.announceSelection.length === state.announceData.length) {
        state.checkAll = true
      } else {
        state.checkAll = false
      }
    }
    const announceSelectionBatch = async (val: number) => {
      if (val === 1) {
        await announcementRecycleBatchReduction({ ids: state.announceIds })
        getAnnounceSearch()
      }
    }
    const jobChange = () => {
      job.value.toggleAllSelection()
    }
    const handleJobChange = (data: any) => {
      state.jobSelection = data
      state.jobIds = state.jobSelection.map((item: any) => item.id).join()
      if (state.jobSelection.length === state.jobData.length) {
        state.checkAllJob = true
      } else {
        state.checkAllJob = false
      }
    }
    const jobSelectionBatch = async (val: number) => {
      if (val === 1) {
        await jobRecycleBatchReduction({ ids: state.jobIds })
      }
      getJobSearch()
    }

    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.form, 'sortUpdateTime')
      Reflect.deleteProperty(state.form, 'sortDeleteTime')

      if (order === 'ascending') {
        // 正序
        state.form[prop] = 2
      } else if (order === 'descending') {
        state.form[prop] = 1
      }
      if (state.activeName === 'announceList') {
        getAnnounceSearch()
      } else {
        getJobSearch()
      }
    }
    const showJobDetail = (id: string) => {
      jobDetailDialog.value.open(id)
    }
    const toAnnounceDetail = (id: string) => {
      router.push(`/cms/announcementDetail/${id}`)
    }

    const toCompanyDetail = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }

    const adChangePage = (r: any) => {
      state.adForm.page = r.page
      fetchAdList()
    }

    return {
      ...toRefs(state),
      changePage,
      getAnnounceSearch,
      formData,
      adFormRef,
      adChange,
      adSelectionBatch,
      reductionAd,
      handleAdChange,
      reset,
      handleClick,
      reductionAnnounce,
      reductionJob,
      announce,
      announceChange,
      handleAnnounceChange,
      announceSelectionBatch,
      adChangePage,
      resetAdForm,
      fetchAdList,
      job,
      ad,
      jobChange,
      handleJobChange,
      jobSelectionBatch,
      handleSortable,
      jobDetailDialog,
      showJobDetail,
      toAnnounceDetail,
      toCompanyDetail
    }
  }
})
</script>

<style lang="scss" scoped>
.button {
  margin-left: 30px;
}
.paging {
  margin-top: 20px;
}
</style>
