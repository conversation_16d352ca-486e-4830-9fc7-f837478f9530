<template>
  <div class="box">
    <el-form ref="form" size="small" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-4" label="发布状态">
          <el-select v-model="formData.status" filterable clearable placeholder="请输入单位">
            <el-option v-for="item in statusList" :key="item.v" :label="item.v" :value="item.k">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-4" label="选择时间">
          <DatePickerRange
            v-model:start="formData.dateFrom"
            v-model:end="formData.dateTo"
            size="small"
            placeholder="操作时间"
          />
        </el-form-item>

        <el-form-item class="span-4" label-width="10px">
          <div class="nowrap">
            <el-button type="primary" @click="search">搜索</el-button>
            <el-button @click="handleResetField">重置</el-button>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <el-table :data="list" border size="small" ref="table" v-loading="loading">
      <el-table-column type="selection"></el-table-column>
      <el-table-column
        prop="id"
        label="编号"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="belongDate"
        label="日期"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="statusTxt"
        label="发布状态"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="addTime"
        label="生成时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="操作" align="center" header-align="center" min-width="200px">
        <template #default="{ row }">
          <el-button @click="handleEdit(row.id)" size="small" type="primary" link>编辑</el-button>
          <el-button
            v-if="row.status == 9"
            @click="handleRelease(row.id, 9)"
            size="small"
            type="primary"
            link
            >发布</el-button
          >
          <el-button
            v-if="row.status == 1"
            @click="handleRelease(row.id, 1)"
            size="small"
            type="primary"
            link
            >撤回</el-button
          >
          <el-button @click="handleDelete(row.id)" size="small" type="primary" link>删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <br />
    <Pagination :total="pagination.total" @change="handlePaginationChange" />
    <!-- <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          class="mr-10"
          label="全选"
        ></el-checkbox>
        <el-select :disabled="!multipleSelection.length" size="small" v-model="batchId">
          <el-option
            v-for="item in batchList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
      </div> -->

    <!-- <el-dialog v-model="visible" :title="dialogTitle" width="500px" :before-close="handleClose">
      <div class="pr-20 py-15">

        <el-form
          v-show="batchId == 1"
          ref="auditForm"
          size="small"
          label-width="100px"
          :model="auditFormData"
        >
          <el-form-item label="审核结果：" prop="changeRelease">
            <el-radio-group v-model="auditFormData.status">
              <el-radio :label="1">通过</el-radio>
              <el-radio :label="-1">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="center">
            <el-button class="w-100" type="primary">确认提交</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog> -->
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import { getList, changeRelease, submitDelete } from '/@/api/dailyAnnouncementSummary'

export default {
  name: 'cmsDailyAnnouncementSummaryList',
  components: { DatePickerRange, Pagination },
  setup() {
    const form = ref()
    const table = ref()
    const attributeForm = ref()
    const copyForm = ref()
    const moveForm = ref()
    const auditForm = ref()
    const router = useRouter()
    const state = reactive({
      loading: false,
      // 审核状态
      statusList: [
        {
          k: 9,
          v: '未发布'
        },
        {
          k: 1,
          v: '已发布'
        }
      ],
      batchList: [
        {
          k: 1,
          v: '批量撤回'
        },
        {
          k: 2,
          v: '批量发布'
        }
      ],

      batchId: <any>'',
      checkedAll: false,
      visible: false,
      dialogTitle: '批量编辑属性',
      // 当前排序ID
      // 选中项
      multipleSelection: [],
      formData: {
        dateFrom: '',
        dateTo: '',
        status: '',
        pageSize: 20,
        page: 1
      },
      auditFormData: {
        attributeIds: [],
        newsIds: '',
        status: ''
      },
      pagination: {
        total: 0
      },
      list: []
    })
    const search = () => {
      state.loading = true
      getList(state.formData)
        .then((r) => {
          state.list = r.list
          state.pagination.total = r.pages.count
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }
    const handleRelease = (id: string, status) => {
      const actionTitle = status == 9 ? '发布' : '撤回'

      ElMessageBox.confirm(`此操作${actionTitle}选中的数据，是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          changeRelease(id).then(() => {
            search()
          })
        })
        .catch(() => {})
    }

    const handleDelete = (id: string) => {
      ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          submitDelete(id).then(() => {
            search()
          })
        })
        .catch(() => {})
    }

    watch(
      () => state.batchId,
      (val: number) => {
        switch (val) {
          case 1:
            state.dialogTitle = '批量编辑属性'
            break
          case 2:
            state.dialogTitle = '批量复制文档'
            break
          case 3:
            state.dialogTitle = '批量移动文档'
            break
          case 5:
            state.dialogTitle = '批量审核文档'
            break
          default:
            break
        }
        if ([1, 2, 3, 5].includes(val)) {
          state.visible = true
        } else {
          handleDelete('1')
        }
      }
    )

    onMounted(() => {
      search()
    })

    const handleResetField = () => {
      form.value.resetFields()
      search()
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      search()
    }

    // 是否全选
    const handleChange = () => {
      table.value.toggleAllSelection()
    }
    const handleAudit = (id: string) => {
      handleDelete(id)
    }
    const handleEdit = (id: string) => {
      router.push({
        path: `/cms/dailyAnnouncementSummaryDetail/${id}`
      })
    }

    const handleClose = () => {
      attributeForm.value.resetFields()
      copyForm.value.resetFields()
      moveForm.value.resetFields()
      auditForm.value.resetFields()
    }

    return {
      form,
      table,
      attributeForm,
      copyForm,
      moveForm,
      auditForm,
      handleResetField,
      getList,
      handlePaginationChange,
      handleChange,
      handleRelease,
      handleAudit,
      handleEdit,
      handleDelete,
      handleClose,
      search,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
</style>
