<template>
  <div class="main">
    <el-form label-width="95px" size="small" ref="form">
      <WangEditor v-model="content" :height="1000" />
      <el-row class="mt-15">
        <el-col :span="16">
          <div class="jc-center">
            <el-button @click="submit()" type="primary">保存</el-button>
            <el-button @click="cancel()">取消</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts">
import { reactive, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getJobDetails, editContent } from '/@/api/dailyAnnouncementSummary'
import WangEditor from '/@/components/wangEditor/index.vue'

export default {
  name: 'cmsDailyAnnouncementSummaryDetail',
  setup() {
    const form = ref()
    const route = useRoute()
    const router = useRouter()
    const { id } = route.params
    const state = reactive({
      content: ''
    })

    const getData = () => {
      getJobDetails(id as string)
        .then((res) => {
          const { content } = res
          state.content = content
        })
        .catch(() => {
          router.back()
        })
    }

    getData()

    const cancel = () => {
      router.back()
    }
    const submit = async () => {
      try {
        await editContent({ id, content: state.content })
      } catch {
        //
      }
    }

    return {
      ...toRefs(state),
      form,
      cancel,
      submit
    }
  },
  components: { WangEditor }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 5px;
  padding: 20px;
  padding-top: 40px;
}

.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
