<template>
  <div>
    <el-card>
      <el-row class="base-title">{{ announcementBaseInfo.title }}</el-row>
      <el-row>
        <el-col :span="6">{{ announcementBaseInfo.companyTitle }}</el-col>
        <el-col :span="8"
          >招{{ announcementBaseInfo.amountNum }}人，共计{{
            announcementBaseInfo.jobNum
          }}个岗位</el-col
        >
      </el-row>

      <el-row class="title"> 基本信息</el-row>
      <el-row>
        <el-col :span="8">发布时间：{{ announcementBaseInfo.releaseTime }}</el-col>
        <el-col :span="8">截止日期：{{ announcementBaseInfo.periodDate }}</el-col>
        <el-col :span="8">学历要求：{{ announcementBaseInfo.educationTxt }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="8">所属省份：{{ announcementBaseInfo.areaProvinceTxt }}</el-col>
        <el-col :span="8">工作地点：{{ announcementBaseInfo.areaCityTxt }}</el-col>
        <el-col :span="8">需求学科：{{ announcementBaseInfo.majorTxt }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="8">所属栏目：{{ announcementBaseInfo.homeColumnTxt }}</el-col>
        <el-col :span="8">所属副栏目：{{ announcementBaseInfo.homeSubColumnTxt }}</el-col>
        <el-col :span="8">调用副栏目：{{ announcementBaseInfo.columnTxt }}</el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="8">报名方式：{{ announcementBaseInfo.applyTypeTxt }}</el-col>
        <el-col :span="8">投递地址：{{ announcementBaseInfo.applyAddress }}</el-col>
        <el-col :span="8" v-if="announcementBaseInfo.companyDeliveryType === '2'">
          投递通知邮箱：{{ announcementBaseInfo.extraNotifyAddress }}
        </el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="8" v-if="announcementBaseInfo.isCooperation !== '1'">
          地址隐藏：{{ announcementBaseInfo.addressHideStatusText }}
        </el-col>
        <el-col :span="8">是否附件提示：{{ announcementBaseInfo.isAttachmentNoticeTxt }}</el-col>
      </el-row>

      <el-row class="info">
        <el-col :span="8">公告简标题：{{ announcementBaseInfo.subTitle }}</el-col>
        <el-col :span="8">亮点描述：{{ announcementBaseInfo.highlightsDescribe }}</el-col>
      </el-row>

      <el-row class="title"> 公告详情</el-row>
      <div v-html="announcementBaseInfo.content" class="detail-main"></div>

      <JobAttachment v-if="fileList.length" :fileList="fileList" />

      <el-row class="title"> 职位详情</el-row>
      <el-table :data="jobList" border>
        <el-table-column prop="code" label="岗位代码" />
        <el-table-column prop="name" label="职位名称">
          <template #default="{ row }">
            <el-button type="primary" link @click="jobDetail(row.id)">{{ row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="information" label="基本信息" show-overflow-tooltip />
        <el-table-column prop="applyInfo" label="投递方式/投递地址" show-overflow-tooltip />

        <el-table-column prop="department" label="用人部门" />
        <el-table-column prop="jobContact" label="职位联系人">
          <template #default="{ row }">
            <div class="flex">
              <div>{{ row.jobContact?.contact }}</div>
              <div class="ml-5">{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</div>
            </div>
            <div>
              <span class="mr-5">{{ row.jobContact?.email }}</span>
              <span class="mr-5">{{ row.jobContact?.mobile }}</span>
              <span v-if="row.jobContact?.department">/{{ row.jobContact?.department }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="jobContactSynergyNum" label="协同子账号">
          <template #default="{ row }">
            <div v-if="row.jobContactSynergyNum === 0">{{ row.jobContactSynergyNum }}</div>
            <el-popover v-else placement="top-start" :width="200">
              <template #reference>
                {{ row.jobContactSynergyNum }}
              </template>
              <h4>协同子账号</h4>
              <div v-for="item in row.jobContactSynergy" :key="item.id">
                <div class="flex mt-5">
                  <span class="color-danger" v-if="item.isContact === 1">联</span>
                  <div>{{ item.contact }} / {{ item.department }}</div>
                </div>
                <div>{{ item.email }} {{ item.mobile }}</div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'
import { getAnnouncementDetail } from '/@/api/announcement'
import JobAttachment from './component/jobAttachment.vue'

export default defineComponent({
  name: 'cmsannouncementDetail',

  components: { JobDetailDialog, JobAttachment },

  setup() {
    const state = reactive({
      announcementBaseInfo: [] as any,
      jobList: [],
      fileList: []
    })
    const route = useRoute()
    const jobDetailDialog = ref()
    const getData = async () => {
      const { id, type } = route.params

      const { baseInfo, jobList, fileList } = await getAnnouncementDetail({ id, type })
      state.fileList = fileList
      state.announcementBaseInfo = baseInfo
      state.jobList = jobList
    }
    const jobDetail = (id: string) => {
      jobDetailDialog.value.open(id)
    }
    getData()
    return { ...toRefs(state), getData, jobDetailDialog, jobDetail }
  }
})
</script>

<style lang="scss" scoped>
.base-title {
  font-weight: bold;
  font-size: 18px;
  margin: 10px 0;
}
.title {
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.322);
  margin: 20px 0;
  padding: 10px 5px;
}
.detail-main {
  :deep() {
    p {
      color: rgba(51, 51, 51, 0.8);
      font-size: 14px;
      line-height: 2;
    }

    img {
      display: block;
      margin: 20px auto;
      max-width: 100%;
    }

    table {
      margin: 20px auto;
      width: 100%;
      border-collapse: collapse;

      th {
        background-color: #fafafc;
      }

      th,
      td {
        padding: 10px 0;
        text-align: center;
        border: 1px solid #ccc;
      }
    }
  }
}
.info {
  margin: 20px 0;
}
</style>
