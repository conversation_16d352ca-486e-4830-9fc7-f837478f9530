<template>
  <div class="main">
    <div class="box">
      <el-form ref="form" :model="formData" label-width="100px">
        <div class="flex">
          <el-form-item class="span-4" label="公告检索" prop="announcementTitleNum">
            <el-input
              v-model="formData.announcementTitleNum"
              placeholder="请输入公告标题或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>

          <el-form-item class="span-4" label="职位检索" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入职位标题或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>

          <el-form-item class="span-4" label="单位检索" prop="companyName">
            <el-input
              v-model="formData.companyName"
              placeholder="请输入单位名称或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>

          <el-form-item class="span-4" label-width="10px">
            <div class="nowrap">
              <el-link
                :underline="false"
                type="primary"
                size="small"
                class="ml-12"
                @click="showMore = !showMore"
              >
                {{ !showMore ? '展开更多' : '收起' }}
              </el-link>
            </div>
          </el-form-item>
        </div>

        <div v-show="showMore">
          <div class="flex">
            <el-form-item class="span-4" label="所属栏目" prop="homeColumnId">
              <Colunm v-model="formData.homeColumnId" :columnList="columnList" />
            </el-form-item>

            <el-form-item class="span-4" label="审核状态" prop="auditStatus">
              <el-select v-model="formData.auditStatus" placeholder="不限" filterable clearable>
                <el-option
                  v-for="item in auditStatusList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="span-4" label="职位类型" prop="jobCategoryId">
              <JobCategory v-model="formData.jobCategoryId" />
            </el-form-item>

            <el-form-item class="span-4" label="招聘状态" prop="status">
              <el-select v-model="formData.status" placeholder="不限" filterable clearable>
                <el-option
                  v-for="item in statusRecruitList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item class="span-4" label="需求专业" prop="majorId">
              <MajorCategory v-model="formData.majorId" :multiple="false" :deep="2" />
            </el-form-item>

            <el-form-item class="span-4" label="学历要求" prop="educationType">
              <el-select v-model="formData.educationType" placeholder="不限" filterable clearable>
                <el-option
                  v-for="item in educationTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="span-4" label="工作城市" prop="city">
              <Region v-model="formData.city" />
            </el-form-item>

            <el-form-item class="span-4" label="用人部门" prop="department">
              <el-input
                v-model="formData.department"
                placeholder="请输入用人部门"
                clearable
                @keyup.enter="search"
              ></el-input>
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item class="span-4" label="公告属性" prop="attribute">
              <el-select
                v-model="formData.notOverseasAttribute"
                placeholder="不限"
                clearable
                filterable
              >
                <el-option
                  v-for="item in notOverseasAttributeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="span-4" label="发布人" prop="creator" label-width="70px">
              <el-input
                v-model="formData.creator"
                placeholder="请输入发布人信息"
                clearable
                @keyup.enter="search"
              ></el-input>
            </el-form-item>

            <el-form-item class="span-4" label="显示状态" prop="isShow">
              <el-select v-model="formData.isShow" placeholder="不限" filterable clearable>
                <el-option
                  v-for="item in showStatusList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="span-4" label="发布时间" prop="refreshTimeStart">
              <DatePickerRange
                v-model:start="formData.refreshTimeStart"
                v-model:end="formData.refreshTimeEnd"
              />
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item class="span-4" label="初始发布时间" prop="firstReleaseTimeStart">
              <DatePickerRange
                v-model:start="formData.firstReleaseTimeStart"
                v-model:end="formData.firstReleaseTimeEnd"
              />
            </el-form-item>

            <el-form-item class="span-4" label="刷新时间" prop="realRefreshTimeStart">
              <DatePickerRange
                v-model:start="formData.realRefreshTimeStart"
                v-model:end="formData.realRefreshTimeEnd"
              />
            </el-form-item>

            <el-form-item
              class="span-4"
              label="高才海外相关属性"
              prop="overseasAttribute"
              label-width="125px"
            >
              <el-select
                v-model="formData.overseasAttribute"
                placeholder="不限"
                clearable
                filterable
              >
                <el-option
                  v-for="item in overseasAttributeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="是否小程序" prop="isMiniapp" label-width="82px">
              <IsMiniapp v-model="formData.isMiniapp" />
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item class="span-4" label="编制类型" prop="establishmentType">
              <EstablishmentType v-model="formData.establishmentType" />
            </el-form-item>
          </div>
        </div>
      </el-form>

      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-row>

      <div class="formbigbox" v-show="!isDetail">
        <div class="formbox">
          <el-button type="primary" @click="checkDetails">+新增公告</el-button>
          &nbsp;&nbsp;
          <el-switch
            v-model="isSimple"
            size="large"
            active-text="简版(只支持部分搜索条件和显示结果)"
            inactive-text="全功能"
          />
          <div class="unitCount">
            <el-link
              :underline="false"
              type="primary"
              size="small"
              @click="handleOpenCustomAnnounce"
              >选择列
            </el-link>
          </div>
          <div class="showTable">
            <el-tabs v-model="activeName" @tab-change="handleChange">
              <el-tab-pane label="公告列表" name="announceList">
                <el-table
                  :data="announceData"
                  ref="announce"
                  border
                  style="width: 100%"
                  align="center"
                  v-loading="listLoading"
                  @selection-change="handleAnnounceChange"
                  @sort-change="handleSortable"
                >
                  <el-table-column type="selection" width="40" />
                  <template v-for="(item, index) in customAnnouncement">
                    <el-table-column
                      v-if="item.select && item.k === 1"
                      :key="index"
                      :label="item.v"
                      prop="announcementUid"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 2"
                      :key="index"
                      :label="item.v"
                      prop="title"
                      align="center"
                      width="300"
                    >
                      <template #default="{ row }">
                        <router-link
                          class="bg-primary td-none"
                          :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
                          >{{ row.title }}
                        </router-link>
                        <p class="tip red">
                          {{ row.tip }}
                        </p>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 3"
                      :key="index"
                      :label="item.v"
                      prop="fullName"
                      align="center"
                      show-overflow-tooltip
                    >
                      <template #default="{ row }">
                        <el-button type="primary" link @click="companyDetails(row.companyId)">
                          {{ row.fullName }}
                        </el-button>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 4"
                      :key="index"
                      :label="item.v"
                      prop="columnName"
                      align="center"
                    />
                    <el-table-column
                      min-width="110px"
                      v-if="item.select && item.k === 5"
                      :key="index"
                      :label="item.v"
                      prop="sortHomeSort"
                      align="center"
                      sortable="custom"
                    >
                      <template #header>
                        <el-tooltip
                          class="box-item"
                          effect="dark"
                          content="此排序仅对单位主页中的公告排序生效"
                          placement="top"
                        >
                          <i class="fa-question-circle-o fa"></i>
                        </el-tooltip>
                        排序
                      </template>
                      <template #default="{ row }">
                        <div class="sort ai-center">
                          {{ row.homeSort }}
                          <img
                            @click="handleChangeSort(row)"
                            class="sort-edit"
                            src="/src/assets/icons/edit.svg"
                            alt=""
                          />
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 6"
                      :key="index"
                      :label="item.v"
                      prop="sortClickCount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.click }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 7"
                      :key="index"
                      :label="item.v"
                      prop="recruitStatusTxt"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 8"
                      :key="index"
                      :label="item.v"
                      prop="auditStatusTxt"
                      align="center"
                    >
                      <template #default="{ row }">
                        <el-popover
                          placement="top"
                          :width="200"
                          trigger="click"
                          :content="row.opinion"
                          v-if="row.auditStatus === '-1'"
                        >
                          <template #reference>
                            <el-button type="primary" link>{{ row.auditStatusTxt }}</el-button>
                          </template>
                        </el-popover>
                        <div v-else>{{ row.auditStatusTxt }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 9"
                      :key="index"
                      :label="item.v"
                      prop="sortRefreshTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.refreshTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 10"
                      :key="index"
                      :label="item.v"
                      prop="sortOfflineTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.offlineTime }}
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.select && item.k === 11"
                      :key="index"
                      :label="item.v"
                      prop="creatorName"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 12"
                      :key="index"
                      :label="item.v"
                      prop="operation"
                      align="center"
                      fixed="right"
                      width="200px"
                    >
                      <template #default="{ row }">
                        <el-row>
                          <el-col :span="8">
                            <el-button
                              size="small"
                              type="success"
                              @click="announceEdit(row.aid)"
                              :disabled="row.auditStatus === '7' || row.status === '2'"
                              >编辑
                            </el-button>
                          </el-col>
                          <el-col :span="8">
                            <el-button
                              size="small"
                              type="danger"
                              @click="deleteAnnounce(row.aid)"
                              :disabled="!row.canDel"
                              >删除
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.status === '1'">
                            <el-button
                              size="small"
                              type="info"
                              :disabled="row.status === '2'"
                              @click="announceLine({ id: row.aid, actionType: '2' })"
                              >下线
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.status === '2'">
                            <el-button
                              size="small"
                              type="warning"
                              @click="announceLine({ id: row.aid, actionType: '1' })"
                              >再发布
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.isShow === '2' && row.status !== '3'">
                            <el-button
                              size="small"
                              type="warning"
                              @click="announceIsShow({ id: row.aid, actionType: '1' })"
                              >显示
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.isShow === '1' && row.status !== '3'">
                            <el-button
                              size="small"
                              type="danger"
                              @click="announceIsShow({ id: row.aid, actionType: '2' })"
                              >隐藏
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.status === '1'">
                            <el-button size="small" type="primary" @click="refresh(row.aid)"
                              >刷新
                            </el-button>
                          </el-col>
                          <el-col :span="8" v-if="row.status === '1'">
                            <el-popover placement="left" :width="20" trigger="click">
                              <template #reference>
                                <el-button size="small" @click="getAttributeData(row.articleId)">
                                  更多
                                </el-button>
                              </template>
                              <div class="column btns">
                                <el-button
                                  class="w100 mx-0 my-5 ml-0"
                                  plain
                                  size="small"
                                  v-for="item in attributeData"
                                  :key="item.type"
                                  @click="refreshSortTime(item.type, item.articleId)"
                                  >{{ item.typeTxt }}
                                </el-button>
                              </div>
                            </el-popover>
                          </el-col>
                        </el-row>
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.select && item.k === 13"
                      :key="index"
                      :label="item.v"
                      prop="sortAddTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.addTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 14"
                      :key="index"
                      :label="item.v"
                      prop="sortRealRefreshTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.realRefreshTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 15"
                      :key="index"
                      :label="item.v"
                      prop="isShowTxt"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 16"
                      :key="index"
                      :label="item.v"
                      prop="subColumnInfo"
                      align="center"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      v-if="item.select && item.k === 17"
                      :key="index"
                      :label="item.v"
                      prop="sortOnlineJobCount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.onlineCount }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 18"
                      :key="index"
                      :label="item.v"
                      prop="sortOfflineJobCount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.offlineCount }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 19"
                      :key="index"
                      :label="item.v"
                      prop="sortRecruitCount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.amount }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 20"
                      :key="index"
                      :label="item.v"
                      prop="sortOffApplyCount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        <el-button
                          type="primary"
                          link
                          @click="openApplyListDialog(row.aid, row.jobOffApplyNum)"
                          >{{ row.jobOffApplyNum }}
                        </el-button>
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.select && item.k === 21"
                      :key="index"
                      :label="item.v"
                      prop="sortFirstReleaseTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.firstReleaseTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 22"
                      :key="index"
                      :label="item.v"
                      prop="majorTxt"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 23"
                      :key="index"
                      :label="item.v"
                      prop="isMiniappTxt"
                      align="center"
                    >
                      <template #default="{ row }">
                        <isMiniappChange
                          :value="row.isMiniapp"
                          type="announcement"
                          :id="row.aid"
                        ></isMiniappChange>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
                <div class="mt-15 jc-between">
                  <div class="ai-center">
                    <el-checkbox
                      v-model="checkAll"
                      label="全选"
                      class="mr-10"
                      @change="announceChange"
                      :indeterminate="isIndeterminate"
                    ></el-checkbox>
                    <el-select
                      v-model="batchValue"
                      placeholder="批量操作"
                      :disabled="!announceSelection.length"
                      @change="announceSelectionBatch"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in batchOptions"
                        :key="item.k"
                        :label="item.v"
                        :value="item.k"
                      />
                    </el-select>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="职位列表" name="jobList">
                <el-table
                  :data="jobData"
                  ref="job"
                  border
                  style="width: 100%"
                  align="center"
                  v-loading="listLoading"
                  @selection-change="handleJobChange"
                  @sort-change="handleSortable"
                >
                  <el-table-column type="selection" width="55" />
                  <template v-for="(item, index) in customJob">
                    <el-table-column
                      v-if="item.select && item.k === 1"
                      :key="index"
                      :label="item.v"
                      prop="uid"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 2"
                      :key="index"
                      :label="item.v"
                      prop="name"
                      align="center"
                    >
                      <template #default="{ row }">
                        <el-button type="primary" link @click="jobDetail(row.id)"
                          >{{ row.name }}
                        </el-button>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 3"
                      :key="index"
                      :label="item.v"
                      prop="basicInformation"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 4"
                      :key="index"
                      :label="item.v"
                      prop="title"
                      align="center"
                    >
                      <template #default="{ row }">
                        <router-link
                          class="bg-primary td-none"
                          :to="`/cms/announcementDetail/${row.announcementId}/${row.status}`"
                          >{{ row.title }}
                        </router-link>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 5"
                      :key="index"
                      :label="item.v"
                      prop="company"
                      align="center"
                    >
                      <template #default="{ row }">
                        <el-button type="primary" link @click="companyDetails(row.companyId)"
                          >{{ row.company }}
                        </el-button>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 6"
                      :key="index"
                      :label="item.v"
                      prop="sortApplyNum"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        <router-link
                          :to="{
                            path: '/announcement/business',
                            query: { id: row.announcementId }
                          }"
                          class="bg-primary td-none"
                          v-if="row.jobApplyNum !== 0"
                        >
                          {{ row.jobApplyNum }}
                        </router-link>
                        <div v-else class="bg-primary">
                          {{ row.jobApplyNum }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 7"
                      :key="index"
                      :label="item.v"
                      prop="homeColumnTitle"
                      align="center"
                    />

                    <el-table-column
                      v-if="item.select && item.k === 8"
                      :key="index"
                      :label="item.v"
                      prop="sortClick"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.click }}
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.select && item.k === 9"
                      :key="index"
                      :label="item.v"
                      prop="statusTitle"
                      align="center"
                    />

                    <el-table-column
                      v-if="item.select && item.k === 10"
                      :key="index"
                      :label="item.v"
                      prop="auditStatusTitle"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 11"
                      :key="index"
                      :label="item.v"
                      prop="operation"
                      align="center"
                      fixed="right"
                    >
                      <template #default="{ row }">
                        <el-row :gutter="10">
                          <el-col :span="6">
                            <el-tooltip content="审核" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-s-check"
                                :disabled="row.auditStatus !== '7'"
                                @click="pushAudit(row.announcementId)"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6">
                            <el-tooltip content="编辑" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-edit-outline"
                                :disabled="
                                  row.auditStatus === '7' ||
                                  row.status === 0 ||
                                  row.announcementAuditStatus === 7
                                "
                                @click="jobEdit(row.id)"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6">
                            <el-tooltip content="删除" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-delete"
                                :disabled="row.jobApplyNum !== 0"
                                @click="deleteJob(row.id)"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6" v-if="row.status === 1 && row.articleStatus === '1'">
                            <el-tooltip content="下线" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-bottom"
                                :disabled="row.status === 0"
                                @click="jobLine({ id: row.id, actionType: '2' })"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6" v-if="row.status === 0 && row.articleStatus !== '2'">
                            <el-tooltip content="再发布" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-top"
                                @click="jobLine({ id: row.id, actionType: '1' })"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6" v-if="row.isShow === '2' && row.status !== 3">
                            <el-tooltip content="显示" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="el-icon-view"
                                @click="jobShow({ id: row.id, actionType: '1' })"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6" v-if="row.isShow === '1' && row.status !== 3">
                            <el-tooltip content="隐藏" placement="top">
                              <el-button
                                size="small"
                                type="primary"
                                link
                                class="iconfont icon-yincangmima"
                                style="padding: 0; font-size: 14px"
                                @click="jobShow({ id: row.id, actionType: '2' })"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                          <el-col :span="6">
                            <el-tooltip content="刷新" placement="top">
                              <el-button
                                :disabled="row.status !== 1"
                                size="small"
                                type="primary"
                                link
                                class="el-icon-refresh-right"
                                @click="refreshJob(row.id)"
                              ></el-button>
                            </el-tooltip>
                          </el-col>
                        </el-row>
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.select && item.k === 12"
                      :key="index"
                      :label="item.v"
                      prop="creator"
                      align="center"
                    />

                    <el-table-column
                      v-if="item.select && item.k === 13"
                      :key="index"
                      :label="item.v"
                      prop="sortUpdateTime"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.updateTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 14"
                      :key="index"
                      :label="item.v"
                      prop="sortRealRefreshTime"
                      sortable="custom"
                      align="center"
                    >
                      <template #default="{ row }">
                        {{ row.realRefreshTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 15"
                      :key="index"
                      :label="item.v"
                      prop="sortPeriodDate"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.periodDate }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 16"
                      :key="index"
                      :label="item.v"
                      prop="isShowTxt"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 17"
                      :key="index"
                      :label="item.v"
                      prop="sortAmount"
                      align="center"
                      sortable="custom"
                    >
                      <template #default="{ row }">
                        {{ row.amount }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 18"
                      :key="index"
                      :label="item.v"
                      prop="homeSubColumnTitle"
                      show-overflow-tooltip
                      align="center"
                    />

                    <el-table-column
                      v-if="item.select && item.k === 19"
                      :key="index"
                      :label="item.v"
                      prop="sortFirstReleaseTime"
                      sortable="custom"
                      align="center"
                    >
                      <template #default="{ row }">
                        {{ row.firstReleaseTime }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.select && item.k === 20"
                      :key="index"
                      :label="item.v"
                      prop="majorTxt"
                      align="center"
                    />
                    <el-table-column
                      v-if="item.select && item.k === 21"
                      :key="index"
                      :label="item.v"
                      prop="isMiniappTxt"
                      align="center"
                    >
                      <template #default="{ row }">
                        <isMiniappChange
                          :value="row.isMiniapp"
                          type="job"
                          :id="row.id"
                        ></isMiniappChange>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
                <div class="mt-15 jc-between">
                  <div class="ai-center">
                    <el-checkbox
                      v-model="checkAllJob"
                      label="全选"
                      class="mr-10"
                      @change="jobChange"
                    ></el-checkbox>
                    <el-select
                      size="small"
                      v-model="jobBatchValue"
                      placeholder="批量操作"
                      :disabled="!jobSelection.length"
                      @change="jobSelectionBatch"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in jobOptions"
                        :key="item.k"
                        :label="item.v"
                        :value="item.k"
                      />
                    </el-select>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div class="paging">
            <Paging :total="pages.total" @change="changePage"></Paging>
          </div>
        </div>
      </div>
    </div>
    <CustomColumnDialog ref="customAnnounceDialog" v-model:data="customData" />
    <AnnouncementBatchCopy ref="announcementBatchCopy" />
    <AnnouncementApplyListDialog ref="announcementApplyListDialog" />
    <AnnouncementAudit ref="announcementAudit" />
    <AnnouncementEdit ref="announcementEdit" :list="attributeDocument" />
    <JobDetailDialog ref="jobDetailDialog" />
    <SortChangeDialog ref="sortChangeDialog" />
    <router-view></router-view>
  </div>
</template>

<script lang="ts">
import { reactive, ref, unref, onMounted, toRefs, watch } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { getTableStagingField } from '/@/api/config'
import { exportCooperationList } from '/@/api/unitManage'
import router from '/@/router'
import { useRoute } from 'vue-router'
import Region from '/@/components/base/select/region.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomColumnDialog from '../../../components/business/customColumnDialog.vue'
import SortChangeDialog from '/src/components/business/sortChangeDialog.vue'
import EstablishmentType from '/@/components/base/select/establishmentType.vue'

import {
  announcementBatchDel,
  announcementBatchOnline,
  announcementBatchRefresh,
  announcementDelete,
  announcementIsShow,
  announcementJobOnline,
  announcementLine,
  announcementRefresh,
  announcementRefreshJob,
  announcementRefreshSortTime,
  getAnnouncementAttributeData,
  getAnnouncementJobList,
  getAnnouncementSimpleJobList,
  getAnnouncementList,
  getSimpleAnnouncementList,
  getAnnounceSearchParams,
  jobBatchOnline,
  jobBatchRefresh,
  jobIsShow,
  temporaryJobDelete,
  batchHidden,
  batchShow
} from '/@/api/announcement'
import MajorCategory from '/@/components/base/select/majorCategory.vue'
import JobCategory from '/@/components/base/select/jobCategory.vue'
import AnnouncementBatchCopy from '/@/components/dialog/announcementBatchCopy.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AnnouncementAudit from './component/announcementAudit.vue'
import AnnouncementEdit from './component/announcementEdit.vue'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'
import Colunm from '/@/components/base/colunm.vue'
import AnnouncementApplyListDialog from './component/announcementApplyListDialog.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'

export default {
  components: {
    Paging,
    Region,
    CustomColumnDialog,
    MajorCategory,
    JobCategory,
    AnnouncementBatchCopy,
    DatePickerRange,
    AnnouncementAudit,
    AnnouncementEdit,
    JobDetailDialog,
    Colunm,
    AnnouncementApplyListDialog,
    SortChangeDialog,
    IsMiniapp,
    isMiniappChange,
    EstablishmentType
  },
  name: 'cmsAnnouncementList',
  setup() {
    const state = reactive({
      activeName: 'announceList',
      customData: [],
      formData: {
        announcementTitleNum: '',
        name: '',
        companyName: '',
        homeColumnId: '',
        auditStatus: '',
        jobCategoryId: '',
        status: '',
        majorId: '',
        educationType: '',
        city: [],
        department: '',
        attribute: '',
        creator: '',
        isShow: '',
        refreshTimeStart: '', // 发布时间开始
        refreshTimeEnd: '', // 发布时间结束
        realRefreshTimeStart: '', // 刷新时间开始
        realRefreshTimeEnd: '', // 刷新时间结束
        firstReleaseTimeStart: '', // 初始发布时间开始
        firstReleaseTimeEnd: '', // 初始发布时间结束
        page: '',
        limit: '',
        isCooperation: 2,
        isArticle: 1,
        isMiniapp: '',
        establishmentType: [],
        notOverseasAttribute: '',
        overseasAttribute: ''
      },
      isSimple: true,
      showMore: false,
      announceData: <any>[],
      jobData: <any>[],
      listLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      isDetail: false,
      columnList: [],
      auditStatusList: <any>[],
      jobTypeList: [],
      statusRecruitList: <any>[],
      majorList: [],
      educationTypeList: <any>[],
      attributeDocument: <any>[],
      showStatusList: <any>[],
      overseasAttributeList: <any>[],
      notOverseasAttributeList: <any>[],
      batchValue: '',
      jobBatchValue: '',
      batchOptions: [
        { k: 1, v: '再发布文档' },
        { k: 2, v: '下线文档' },
        { k: 3, v: '刷新文档' },
        { k: 4, v: '编辑属性' },
        { k: 5, v: '复制文档' },
        { k: 6, v: '移动文档' },
        { k: 7, v: '删除文档' },
        { k: 8, v: '审核文档' },
        { k: 9, v: '显示文档' },
        { k: 10, v: '隐藏文档' }
      ],
      jobOptions: [
        { k: 1, v: '再发布职位' },
        { k: 2, v: '下线职位' },
        { k: 3, v: '刷新文档' }
      ],
      checkAll: false,
      checkAllJob: false,
      announceSelection: [],
      jobSelection: [],
      announceIds: '',
      jobIds: '',
      attributeData: <any>[]
    })

    const announce = ref()
    const job = ref()
    const route = useRoute()
    const customAnnounceDialog = ref()
    const announcementBatchCopy = ref()
    const announcementAudit = ref()
    const announcementEdit = ref()
    const jobDetailDialog = ref()
    const isIndeterminate = ref(false)
    const customAnnouncement = ref([
      {
        k: 1,
        v: '公告编号',
        name: 'announcementUid',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '公告标题',
        name: 'title',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '所属单位',
        name: 'fullName',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '所属栏目',
        name: 'columnName',
        select: true,
        default: true
      },
      {
        k: 5,
        v: '排序',
        name: 'sort',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '点击量',
        name: 'click',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '招聘状态',
        name: 'recruitStatusTxt',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '审核状态',
        name: 'auditStatusTxt',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '发布时间',
        name: 'refreshTime',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '下线时间',
        name: 'offlineTime',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '发布人',
        name: 'creatorName',
        select: true,
        default: true
      },
      {
        k: 12,
        v: '操作',
        name: 'operation',
        select: true,
        default: true
      },
      {
        k: 13,
        v: '创建时间',
        name: 'addTime',
        select: true,
        default: false
      },
      {
        k: 14,
        v: '刷新时间',
        name: 'realRefreshTime',
        select: true,
        default: false
      },
      {
        k: 15,
        v: '显示状态',
        name: 'isShowTxt',
        select: true,
        default: false
      },
      {
        k: 16,
        v: '副栏目',
        name: 'subColumnInfo',
        select: true,
        default: false
      },
      {
        k: 17,
        v: '在线职位',
        name: 'onlineCount',
        select: true,
        default: false
      },
      {
        k: 18,
        v: '已下线职位',
        name: 'offlineCount',
        select: true,
        default: false
      },
      {
        k: 19,
        v: '招聘人数',
        name: 'amount',
        select: true,
        default: false
      },
      {
        k: 20,
        v: '投递次数',
        name: 'jobOffApplyNum',
        select: true,
        default: false
      },
      {
        k: 21,
        v: '初始发布时间',
        name: 'firstReleaseTime',
        select: true,
        default: false
      },
      {
        k: 22,
        v: '学科专业',
        name: 'majorTxt',
        select: true,
        default: false
      },
      {
        k: 23,
        v: '是否小程序',
        name: 'isMiniappTxt',
        select: true,
        default: false
      }
    ])
    const customJob = ref([
      {
        k: 1,
        v: '编号',
        name: 'uid',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '职位名称',
        name: 'name',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '基本信息',
        name: 'basicInformation',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '关联公告',
        name: 'title',
        select: true,
        default: true
      },
      {
        k: 5,
        v: '所属单位',
        name: 'companyName',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '投递次数',
        name: 'applyNumber',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '所属栏目',
        name: 'homeColumnTxt',
        select: true,
        default: true
      },

      {
        k: 8,
        v: '点击量',
        name: 'click',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '招聘状态',
        name: 'status',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '审核状态',
        name: 'auditStatus',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      },
      {
        k: 12,
        v: '发布人',
        name: 'createTxt',
        select: true,
        default: true
      },
      {
        k: 13,
        v: '发布时间',
        name: 'refreshTime',
        select: true,
        default: true
      },
      {
        k: 14,
        v: '刷新时间',
        name: 'realRefreshTime',
        select: true,
        default: false
      },
      {
        k: 15,
        v: '下线时间',
        name: 'periodDate',
        select: true,
        default: false
      },
      {
        k: 16,
        v: '显示状态',
        name: 'isShowTxt',
        select: true,
        default: false
      },
      {
        k: 17,
        v: '招聘人数',
        name: 'recruitNumber',
        select: true,
        default: false
      },
      {
        k: 18,
        v: '副栏目',
        name: 'homeSubColumnTxt',
        select: true,
        default: false
      },
      {
        k: 19,
        v: '初始发布时间',
        name: 'firstReleaseTime',
        select: true,
        default: false
      },
      {
        k: 20,
        v: '学科专业',
        name: 'majorTxt',
        select: true,
        default: false
      },
      {
        k: 21,
        v: '是否小程序',
        name: 'majorTxt',
        select: true,
        default: false
      }
    ])

    const arrayToStringData = (data: any) => {
      const r = {
        ...data,
        establishmentType: data.establishmentType.join(',')
      }
      return r
    }

    const announcementApplyListDialog = ref()
    const sortChangeDialog = ref()

    const getAnnounceSearch = () => {
      const data = arrayToStringData(unref(state.formData))
      state.listLoading = true
      // 这里做一个简单的处理,如果是选择了简版就请求一个简版的接口,如果是选择了详版就请求一个详版的接口
      if (state.isSimple) {
        getSimpleAnnouncementList(data).then((res: any) => {
          state.announceData = res.list
          state.pages.size = res.pages.size
          state.pages.total = res.pages.total
          state.listLoading = false
        })
      } else {
        getAnnouncementList(data).then((res: any) => {
          state.announceData = res.list
          state.pages.size = res.pages.size
          state.pages.total = res.pages.total
          state.listLoading = false
        })
      }
    }

    // 获取后台数据类型
    onMounted(async () => {
      if (route.name === 'companyCooperationListDetail') {
        state.isDetail = true
      } else {
        state.isDetail = false
      }
      // getAnnounceSearch()
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
      const data = await getTableStagingField('announcementCacheList')
      if (!data.value) {
        state.customData = JSON.parse(JSON.stringify(customAnnouncement.value))
      } else {
        const value = data.value.split(',')
        state.customData = <any>customAnnouncement.value.map((item: any) => {
          return {
            ...item,
            select: value.includes(item.name)
          }
        })
      }
      const resp = await getAnnounceSearchParams()
      Object.keys(resp).forEach((key) => {
        state[key] = resp[key]
      })
    })

    const searchJobList = async () => {
      state.listLoading = true
      // 根据不同的搜索条件请求不同的接口
      let rs = []
      const data = arrayToStringData(unref(state.formData))
      if (state.isSimple) {
        rs = await getAnnouncementSimpleJobList(data).catch(() => {
          // 出现个提示,告诉部分功能不可用
          ElMessage({
            message:
              '如果你看到这个报错,证明你在使用简版的部分功能,现在这个暂不可用,需要点击重置按钮',
            type: 'warning',
            duration: 5000
          })
          state.listLoading = false
          // 重置表单
        })
      } else {
        rs = await getAnnouncementJobList(data)
      }
      state.jobData = rs.list
      state.pages.size = rs.page.size
      state.pages.total = rs.page.count
      state.listLoading = false
    }

    const handleChange = async (name: string) => {
      if (name === 'jobList') {
        const data = await getTableStagingField('jobList')
        if (!data.value) {
          state.customData = JSON.parse(JSON.stringify(customJob.value))
        } else {
          const value = data.value.split(',')
          state.customData = <any>customJob.value.map((item: any) => {
            return {
              ...item,
              select: value.includes(item.name)
            }
          })
        }
        // searchJobList()
      } else {
        state.customData = JSON.parse(JSON.stringify(customAnnouncement.value))
        // getAnnounceSearch()
      }
    }

    watch(
      () => route.name,
      (n: any) => {
        if (n === 'companyCooperationListDetail') {
          state.isDetail = true
        } else {
          state.isDetail = false
        }
      }
    )
    watch(
      () => state.customData,
      (n: any) => {
        if (state.activeName === 'jobList') {
          customJob.value = n
        } else {
          customAnnouncement.value = n
        }
      },
      {
        deep: true
      }
    )

    const search = () => {
      if (state.activeName === 'announceList') {
        getAnnounceSearch()
      } else {
        searchJobList()
      }
    }
    const form = ref()
    // 重置
    const resetForm = () => {
      form.value.resetFields()
      state.formData.jobCategoryId = ''
      getAnnounceSearch()
    }

    const checkDetails = () => {
      router.push({ name: 'cmsAnnouncementAdd' })
    }
    const changePage = (r: any) => {
      if (state.activeName === 'announceList') {
        state.formData.page = r.page
        state.formData.limit = r.total
        getAnnounceSearch()
      } else {
        state.formData.page = r.page
        searchJobList()
      }
    }
    const excel = () => {
      exportCooperationList(state.formData).then((res) => {
        window.location.href = res.downloadUrl
      })
    }
    const handleOpenCustomAnnounce = () => {
      if (state.activeName === 'jobList') {
        customAnnounceDialog.value.open('jobList')
      } else {
        customAnnounceDialog.value.open('announcementCacheList')
      }
    }
    // 公告操作
    // 审核
    const pushAudit = (id: any) => {
      router.push(`/cms/announcementAuditDetail/${id}`)
    }
    // 删除公告
    const deleteAnnounce = (id: string) => {
      ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await announcementDelete({ id })
        state.announceData = state.announceData.filter((item: any) => item.aid !== id)
      })
    }

    interface announceOffline {
      id: number
      actionType: string
    }

    // 显示隐藏
    const announceIsShow = async (type: announceOffline) => {
      await announcementIsShow(type)
      getAnnounceSearch()
    }

    // 上线/再发布
    const announceLine = async (type: any) => {
      await announcementLine(type)
      getAnnounceSearch()
    }

    // 编辑公告
    const announceEdit = (id: any) => {
      router.push({ path: `/cms/announcementEdit/${id}` })
    }
    // 编辑职位
    const jobEdit = (id: any) => {
      router.push({ path: `/job/edit/${id}` })
    }
    const handleAnnounceChange = (data: any) => {
      state.announceSelection = data
      state.announceIds = data.map((item: any) => item.aid).join()
      if (data.length === state.announceData.length) {
        state.checkAll = true
        isIndeterminate.value = false
      } else {
        state.checkAll = false
        isIndeterminate.value = data.length > 0
      }
    }
    const handleJobChange = (data: any) => {
      state.jobSelection = data
      state.jobIds = data.map((item: any) => item.id).join()
      if (data.length === state.jobData.length) {
        state.checkAllJob = true
      } else {
        state.checkAllJob = false
      }
    }
    const announceChange = () => {
      announce.value.toggleAllSelection()
    }
    const jobChange = () => {
      job.value.toggleAllSelection()
    }
    const changeHideState = (changeState, type) => {
      ElMessageBox.confirm(`确定要${type === 1 ? '显示' : '隐藏'}文档吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { content } = await changeState({ ids: state.announceIds })
        if (content) {
          ElMessageBox.alert(content, '提示', {
            dangerouslyUseHTMLString: true,
            showConfirmButton: false
          })
        }
        getAnnounceSearch()
      })
    }
    const announceSelectionBatch = async (val: any) => {
      state.batchValue = ''
      if (val === 9) {
        changeHideState(batchShow, 1)
      }
      if (val === 10) {
        changeHideState(batchHidden, 2)
      }
      if (val === 5 || val === 6) {
        announcementBatchCopy.value.openBatchCopy(val, state.announceIds)
      }
      if (val === 7) {
        ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await announcementBatchDel({ ids: state.announceIds })
          getAnnounceSearch()
        })
      }
      if (val === 8) {
        announcementAudit.value.open(state.announceIds)
      }
      if (val === 4) {
        announcementEdit.value.open(state.announceIds)
      }
      if (val === 1 || val === 2) {
        await announcementBatchOnline({ ids: state.announceIds, actionType: val })
        getAnnounceSearch()
      }
      if (val === 3) {
        await announcementBatchRefresh({ ids: state.announceIds })
        getAnnounceSearch()
      }
    }
    const jobSelectionBatch = async (val: any) => {
      if (val === 3) {
        await jobBatchRefresh({ ids: state.jobIds })
      } else {
        await jobBatchOnline({ ids: state.jobIds, actionType: val })
      }
      searchJobList()
    }
    const handleSortable = ({ prop, order }) => {
      // 简版排序不可用
      if (state.isSimple) {
        // 出个提示
        ElMessageBox.alert('简版排序不可用', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      }
      Reflect.deleteProperty(state.formData, 'sortHomeSort')
      Reflect.deleteProperty(state.formData, 'sortClickCount')
      Reflect.deleteProperty(state.formData, 'sortAddTime')
      Reflect.deleteProperty(state.formData, 'sortOfflineTime')
      Reflect.deleteProperty(state.formData, 'sortRealRefreshTime')
      Reflect.deleteProperty(state.formData, 'sortRefreshTime')
      Reflect.deleteProperty(state.formData, 'sortOnlineJobCount')
      Reflect.deleteProperty(state.formData, 'sortOfflineJobCount')
      Reflect.deleteProperty(state.formData, 'sortRecruitCount')
      Reflect.deleteProperty(state.formData, 'sortOffApplyCount')
      Reflect.deleteProperty(state.formData, 'sortFirstReleaseTime')

      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      if (state.activeName === 'announceList') {
        getAnnounceSearch()
      } else {
        searchJobList()
      }
    }
    const companyDetails = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }
    const openApplyListDialog = (id: any, amount: string) => {
      if (amount === '0') return
      announcementApplyListDialog.value.open(id)
    }

    const refresh = async (id: any) => {
      await announcementRefresh({ id })
      getAnnounceSearch()
    }
    const deleteJob = (id: any) => {
      ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await temporaryJobDelete({ id, isTemp: 2 })
        state.jobData = state.jobData.filter((item: any) => item.id !== id)
      })
    }
    const refreshJob = async (id: number) => {
      await announcementRefreshJob({ id })
    }
    const jobStatus = (val: any) => {
      let currentIndex = 0
      const res = state.jobData.filter((item: any, index: number) => {
        if (item.id === val.id) {
          currentIndex = index
          return true
        }
        return false
      })[0]
      const data = { ...res, status: val.actionType }
      state.jobData.splice(currentIndex, 1, data)
    }
    const jobLine = async (val: any) => {
      await announcementJobOnline(val)
      jobStatus(val)
    }

    const jobShow = async (val: any) => {
      await jobIsShow(val)
      jobStatus(val)
    }

    const jobDetail = (id: number) => {
      jobDetailDialog.value.open(id)
    }

    // 获取公告文档属性数据
    const getAttributeData = async (articleId: string) => {
      state.attributeData = await getAnnouncementAttributeData({ articleId })
    }

    // 公告操作-刷新属性排序时间
    const refreshSortTime = async (attributeId: string, articleId: string) => {
      await announcementRefreshSortTime({ attributeId, articleId })
    }

    // 排序
    const handleChangeSort = (row) => {
      const { aid: id, homeSort: sort } = row
      const callback = (newSort) => {
        row.homeSort = newSort
      }
      sortChangeDialog.value.open({ id, sort }, callback)
    }

    return {
      ...toRefs(state),
      announce,
      job,
      search,
      resetForm,
      checkDetails,
      changePage,
      excel,
      form,
      customAnnounceDialog,
      customAnnouncement,
      handleOpenCustomAnnounce,
      deleteAnnounce,
      handleAnnounceChange,
      announceChange,
      announceSelectionBatch,
      announcementBatchCopy,
      pushAudit,
      handleSortable,
      companyDetails,
      announceIsShow,
      announceLine,
      announceEdit,
      customJob,
      handleJobChange,
      jobChange,
      announcementApplyListDialog,
      openApplyListDialog,
      announcementAudit,
      announcementEdit,
      refresh,
      jobEdit,
      deleteJob,
      refreshJob,
      jobLine,
      jobShow,
      jobSelectionBatch,
      jobDetailDialog,
      jobDetail,
      getAttributeData,
      refreshSortTime,
      handleChangeSort,
      handleChange,
      sortChangeDialog,
      batchShow,
      batchHidden,
      isIndeterminate
    }
  }
}
</script>
<style lang="scss" scoped>
.red {
  color: red;
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  .flex {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;

    .span-4 {
      width: 25%;
      padding: 0 10px;
      margin-bottom: 20px;
    }
  }

  :deep(.el-form-item__label) {
    padding-right: 12px;
  }
}

.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;

  .buttomBox {
    margin-left: 10px;
  }

  .unitCount {
    padding: 0 15px;
    margin: 20px 0;
    height: 30px;
    line-height: 30px;
    text-align: right;
    background-color: #edf9ff;
  }

  .el-row {
    .el-col-6 {
      i {
        cursor: pointer;
      }
    }
  }

  .paging {
    margin-top: 90px;
  }

  .opbutton {
    padding: 4px;
  }
}

.tip {
  font-size: 12px;
}

.sort {
  display: flex;
  align-items: center;
  justify-content: center;

  .sort-edit {
    width: 20px;
    opacity: 0.6;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
