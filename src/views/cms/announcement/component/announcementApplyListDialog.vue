<template>
  <el-dialog v-model="visible" title="投递列表" width="850px" :before-close="handleClose">
    <el-form ref="form" :model="formData" class="mt-10" :inline="true">
      <el-form-item prop="nameNum">
        <el-input
          v-model="formData.nameNum"
          placeholder="人才姓名/人才编号/职位名称/职位编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="applyDateStart">
        <DatePickerRange
          v-model:start="formData.applyDateStart"
          v-model:end="formData.applyDateEnd"
          placeholder="投递时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border size="small" v-loading="loading">
      <el-table-column
        prop="id"
        align="center"
        header-align="center"
        label="序号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="jobUid"
        align="center"
        header-align="center"
        label="职位编号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="jobName"
        align="center"
        header-align="center"
        label="职位名称"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="resumeUid"
        align="center"
        header-align="center"
        label="人才编号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="resumeName"
        align="center"
        header-align="center"
        label="人才姓名"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="resumeAttachmentName"
        align="center"
        header-align="center"
        label="附件简历"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="applyDate"
        align="center"
        header-align="center"
        label="投递时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="applyStatusTxt"
        align="center"
        header-align="center"
        label="投递进度"
        show-overflow-tooltip
      >
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-if="pagination.total > 0"
      @change="receivedPaginationChange"
      class="mt-15"
      :total="pagination.total"
    />
  </el-dialog>
</template>

<script lang="ts">
import { nextTick, reactive, ref, toRefs } from 'vue'
// import { ElMessage } from 'element-plus'
import Pagination from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

import { getAnnouncementOffJobApplyList } from '/@/api/announcement'

export default {
  name: 'announcementApplyListDialog',
  components: { Pagination, DatePickerRange },
  emits: ['update'],
  setup() {
    const form = ref()

    const state = reactive({
      visible: false,
      loading: false,
      formData: {
        announcementId: '',
        nameNum: '',
        applyDateStart: '',
        applyDateEnd: '',
        pageSize: 20,
        page: 1
      },
      pagination: {
        total: 0
      },
      list: []
    })
    const getList = () => {
      state.loading = true
      getAnnouncementOffJobApplyList(state.formData).then((resp: any) => {
        const { list, page } = resp
        state.list = list
        state.formData.pageSize = page.pageSize
        state.pagination.total = page.count
        state.loading = false
      })
    }

    const open = (id: any) => {
      if (!id) return
      state.formData.announcementId = id
      state.visible = true
      getList()
    }

    const handleReset = () => {
      form.value.resetFields()
      nextTick(() => {
        form.value.clearValidate()
      })
    }

    const handleClose = (done) => {
      handleReset()
      done()
    }
    const receivedPaginationChange = (data: any) => {
      state.formData.pageSize = data.limit
      state.formData.page = data.page
      getList()
    }
    return {
      form,
      open,
      handleClose,
      getList,
      receivedPaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.right {
  position: absolute;
  left: calc(100% + 10px);
  white-space: nowrap;
}
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
