<template>
  <div>
    <el-drawer
      v-model="showJobAdd"
      direction="rtl"
      size="80%"
      :before-close="closeJobAdd"
      :title="title"
    >
      <div v-if="editShow">
        添加方式：
        <el-radio v-model="single" :label="1" @change="handleChangeSingle">单个新增职位</el-radio>
        <el-radio v-model="single" :label="2" @change="handleChangeBatch">批量新增职位</el-radio>
      </div>
      <div class="single" v-if="show">
        <div v-if="editShow">
          职位模板：
          <el-select
            placeholder="请输入现有职位名称/编号，选择所要复用的职位模板"
            style="width: 400px"
            clearable
            remote
            filterable
            :remote-method="filterJobTemplate"
            v-model="singleForm.jobTemplateId"
            @change="changeTemplate"
          >
            <el-option
              v-for="item in jobNameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <el-form :model="singleForm" :rules="rules" ref="form">
          <div class="title line-1 mb-20 fs-16 fw-bold pl-15">基本信息</div>
          <div>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row :gutter="10">
                  <el-col :span="16">
                    <el-form-item label="职位名称" prop="name">
                      <el-input
                        type="textarea"
                        v-model="singleForm.name"
                        show-word-limit
                        resize="none"
                        placeholder="请填写职位名称"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label-width="0" prop="code">
                      <el-input
                        type="textarea"
                        v-model="singleForm.code"
                        resize="none"
                        placeholder="职业代码"
                        style="width: 120px"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="职位类型" prop="jobCategoryId">
                  <JobCategory v-model="singleForm.jobCategoryId" :multiple="false" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="学历要求" prop="educationType">
                      <Education v-model="singleForm.educationType" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="需求专业" prop="majorId">
                  <!-- <MajorCategory v-model="singleForm.majorId" :deep="2" isLimit /> -->
                  <MajorDialog v-model="singleForm.majorId" v-model:title="singleForm.majorTitle" />
                  <el-row :gutter="5">
                    <el-col :span="20"
                      ><el-input
                        v-model="majorText"
                        placeholder="输入识别文案"
                        clearable
                        maxlength="2000"
                    /></el-col>
                    <el-col :span="4">
                      <el-button type="primary" @click="recognition">识别</el-button>
                    </el-col>
                  </el-row>

                  <el-select style="width: 400px" clearable remote filterable @change="changeMajor">
                    <el-option
                      v-for="item in jobList as any"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="工作性质" prop="natureType">
                      <WorkNature v-model="singleForm.natureType" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <Salary
                  :data="{
                    wageType: singleForm.wageType,
                    wageId: singleForm.wageId,
                    minWage: singleForm.minWage,
                    maxWage: singleForm.maxWage,
                    isNegotiable: singleForm.isNegotiable
                  }"
                  @change="salarChage"
                />
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="其他要求">
                  <el-row>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="experienceType">
                        <Experience
                          v-model="singleForm.experienceType"
                          placeholder="工作经验"
                          isLimit
                        />
                      </el-form-item>
                    </div>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="ageType">
                        <Age
                          el-type="input"
                          is-limit
                          v-model="singleForm.ageType"
                          placeholder="年龄"
                        />
                      </el-form-item>
                    </div>

                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="titleType">
                        <LevelTitle v-model="singleForm.titleType" isLimit placeholder="职称" />
                      </el-form-item>
                    </div>

                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="politicalType">
                        <Political
                          v-model="singleForm.politicalType"
                          placeholder="政治面貌"
                          isLimit
                        />
                      </el-form-item>
                    </div>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="abroadType">
                        <AbroadExperience
                          v-model="singleForm.abroadType"
                          placeholder="海外经验"
                          isLimit
                        />
                      </el-form-item>
                    </div>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <ApplyMethods
              :isCooperation="isCooperation"
              :accountType="deliveryType"
              v-model="applyMethodsData"
              :isCooperationJob="false"
            />

            <JobCooperate
              v-if="isCooperation"
              :companyId="singleForm.companyId"
              v-model="synergyData"
              identifyShow
              :email="singleForm.applyAddress"
              :hasAccount="hasAccount"
            />
          </div>
          <div class="title line-1 mb-20 fs-16 fw-bold pl-15">职位详情</div>
          <div class="pl-26">
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="招聘人数" prop="amount">
                      <el-input
                        v-model.trim="singleForm.amount"
                        placeholder="请输入具体招聘人数或者“若干”"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="用人部门" prop="department">
                  <el-input v-model="singleForm.department" placeholder="请输入用人部门"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="工作地点" prop="areaName">
                      <Region v-model="singleForm.areaName" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="职位福利" prop="welfareText">
                  <el-input
                    readonly
                    class="cursor-default"
                    v-model="welfareText"
                    @click="openDialogWelfare"
                    placeholder="请选择职位福利"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="职位编制" prop="jobEstablishment">
                  <announcement-check-box
                    v-model="singleForm.establishmentType"
                    :check-box-list="establishmentTypeList"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="职位有效期" prop="periodDate">
                  <el-date-picker
                    v-model="singleForm.periodDate"
                    type="date"
                    placeholder="请选择职位有效期"
                    value-format="YYYY-MM-DD"
                    :disabled-date="handleDisabledDate"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="岗位职责" prop="duty">
                  <el-input
                    v-model="singleForm.duty"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写岗位职责(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="任职要求" prop="requirement">
                  <el-input
                    v-model="singleForm.requirement"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写任职要求(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="其他说明" prop="remark">
                  <el-input
                    v-model="singleForm.remark"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写其他说明(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0" v-if="isCooperation">
              <el-col :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <DeliveryLimitType v-model="singleForm.deliveryLimitType" />
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item>
                  <span class="color-danger fs-12">
                    *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="batch" v-else>
        <div class="title line-1 mb-20 fs-16 fw-bold pl-15">批量发布职位</div>
        <el-form>
          <el-form-item label="文件路径">
            <el-row>
              <el-upload
                v-if="showUpload"
                class="upload-demo"
                action="/announcement/upload-excel"
                :on-remove="handleRemove"
                :limit="1"
                :on-success="handleSuccess"
              >
                <el-button size="small" type="primary">选择文件</el-button>
              </el-upload>
              <p class="upload" @click="downloadFile">下载职位上传模板</p>
            </el-row>
          </el-form-item>
          <el-row :gutter="0">
            <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
              <el-form-item>
                <span class="color-danger fs-12">
                  *岗位职责与任职要求请勿输入公司邮箱、移动电话、性别歧视字眼及其他外链。
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <footer>
        <el-row class="mb-30">
          <el-button class="btn" @click="closeJobAdd">取消</el-button>
          <el-button class="btn" type="primary" @click="submit">确定</el-button>
        </el-row>
      </footer>
    </el-drawer>
    <DialogWelfare
      ref="dialogWelfare"
      title="职位福利"
      @confirm="handleWelfare"
      :memberId="singleForm.companyId"
    ></DialogWelfare>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DialogWelfare from '/@/components/base/welfare.vue'
import JobCategory from '/@/components/base/select/jobCategory.vue'
import Education from '/@/components/base/select/education.vue'
import { useRoute } from 'vue-router'
import MajorDialog from './majorDialog.vue'
import WorkNature from '/@/components/base/select/workNature.vue'
import Salary from '/@/components/business/salary.vue'

import Experience from '/@/components/base/select/experience.vue'
import Age from '/@/components/base/select/age.vue'
import LevelTitle from '/@/components/base/select/levelTitle.vue'
import Political from '/@/components/base/select/political.vue'
import AbroadExperience from '/@/components/base/select/abroadExperience.vue'
import Region from '/@/components/base/select/region.vue'
import {
  getJobEidtData,
  getJobNameList,
  getTemporaryJobEidt,
  jobBatchAdd,
  temporaryJobSave
} from '/@/api/announcement'
import { aiRecognition } from '/@/api/major'
import DeliveryLimitType from '../../job/components/deliveryLimitType.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import { getJobEstablishmentList } from '/@/api/cmsJob'
import JobCooperate from '/@/components/job/jobCooperate.vue'

export default defineComponent({
  name: 'addPosition',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    deliveryType: {
      type: Number,
      default: null
    },
    isCooperation: {
      type: Boolean,
      default: false
    },
    jobList: {
      type: Array,
      default: () => []
    },
    subAccountConfig: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    DialogWelfare,
    JobCategory,
    Education,
    MajorDialog,
    WorkNature,
    Salary,
    Experience,
    AnnouncementCheckBox,
    Age,
    LevelTitle,
    Political,
    AbroadExperience,
    Region,
    DeliveryLimitType,
    ApplyMethods,
    JobCooperate
  },
  emits: ['update:modelValue', 'jobTempData'],
  setup(props, { emit }) {
    const state = <any>reactive({
      editShow: true,
      showJobAdd: false, // 弹窗开关
      // 单个职位新增
      single: 1,
      batch: '2',
      dialogWelfare: false,
      showUpload: true,
      establishmentTypeList: [], // 职位编制列表
      // 新增单个职位表单
      singleForm: {
        announcementId: '',
        jobTemplateId: '',
        establishmentType: '',
        name: '', // 职位名称
        code: '', // 职位代码
        jobCategoryId: '', // 职位类型
        educationType: '', // 学历要求
        majorId: [],
        natureType: '',
        wageType: '',
        isNegotiable: '2',
        wageId: '',
        minWage: '',
        maxWage: '',
        experienceType: '',
        ageType: '',
        titleType: '',
        politicalType: '',
        abroadType: '',
        welfareTag: '',
        isTemp: 1,
        areaName: [],
        jobTempId: '',
        department: '',
        provinceId: '',
        cityId: '',
        deliveryLimitType: '',
        companyId: '',
        applyType: '',
        applyAddress: '',
        deliveryWay: [],
        extraNotifyAddress: '',
        jobContactId: '',
        jobContactSynergyIds: []
      },
      applyMethodsData: computed({
        get() {
          const {
            singleForm: { applyType, applyAddress, extraNotifyAddress, deliveryWay }
          } = state
          return { applyType, applyAddress, extraNotifyAddress, deliveryWay }
        },
        set(val: Object) {
          Object.keys(val).forEach((key) => {
            state.singleForm[key] = val[key]
          })
        }
      }),

      synergyData: computed({
        get() {
          const {
            singleForm: { jobContactId, jobContactSynergyIds }
          } = state
          return { jobContactId, jobContactSynergyIds }
        },
        set(val: object) {
          Object.keys(val).forEach((key) => {
            state.singleForm[key] = val[key]
          })
        }
      }),

      hasAccount: computed(() => props.subAccountConfig?.total !== '0'),
      // 批量新增返回路径
      filePath: '',
      rules: {
        name: [
          {
            required: true,
            message: '请填写职位名称',
            trigger: 'blur'
          }
        ],
        jobCategoryId: [
          {
            required: true,
            message: '请选择职位类型',
            trigger: 'change'
          }
        ],
        educationType: [
          {
            required: true,
            message: '请选择学历',
            trigger: 'change'
          }
        ],
        amount: [
          {
            required: true,
            message: '请填写输入招聘人数',
            trigger: 'blur'
          },
          {
            pattern: /^([1-9]\d{0,3}|\u82e5\u5e72)$/,
            message: '请输入数字或者“若干”',
            trigger: 'blur'
          }
        ],
        areaName: [
          {
            required: true,
            message: '请选择工作地点',
            trigger: 'change'
          }
        ],
        duty: [
          {
            required: true,
            message: '请填写岗位职责',
            trigger: 'blur'
          }
        ],
        requirement: [
          {
            required: true,
            message: '请填写任职要求',
            trigger: 'blur'
          }
        ]
      },
      welfareArray: [],
      welfareText: '',
      jobNameList: [],
      title: '',
      index: null as any,
      date: '',
      majorText: ''
    })

    const show = ref(true)
    const dialogWelfare = ref()
    const form = ref()
    const route = useRoute()
    const handleChangeSingle = (val: any) => {
      if (val === 1) {
        show.value = true
      }
    }

    // 职位编制
    const getJobEstablishment = () => {
      getJobEstablishmentList().then((resp: any) => {
        state.establishmentTypeList = resp
      })
    }
    getJobEstablishment()
    const handleChangeBatch = (val: any) => {
      if (val === 2) {
        show.value = false
      }
    }
    const getData = async (data: any) => {
      state.jobNameList = await getJobNameList({
        nameNumber: data,
        companyId: state.singleForm.companyId
      })
    }
    const handleWelfare = (welfare: any) => {
      state.welfareArray = welfare
      state.welfareText = welfare.map((item: any) => item.v).join('，')
      state.singleForm.welfareTag = welfare.map((item: any) => item.k).join(',')
    }

    const open = async (
      date: any,
      announceCompanyId: any,
      id: string,
      isTemp: number,
      announceId: any,
      index: any = null
    ) => {
      state.date = date
      state.singleForm.companyId = announceCompanyId
      state.jobNameList = await getJobNameList({ nameNumber: '', companyId: announceCompanyId })
      if (typeof index === 'number') {
        state.index = index
      } else {
        state.index = null
      }
      if (id) {
        state.title = '编辑职位'
        state.editShow = false
        show.value = true
        state.single = 1
        const res = await getTemporaryJobEidt({ id, isTemp })

        // 临时职位修改，ID不用传过去
        if (isTemp === 1) {
          state.singleForm = { ...res }
        } else {
          state.singleForm = { ...res, jobId: res.id }
        }
        state.singleForm.majorId = res.majorId.split(',')
        state.singleForm.deliveryWay = res.deliveryWay ? res.deliveryWay?.split(',') : []
        handleWelfare(res.welfareTage)
      } else {
        state.title = '添加职位'
        state.editShow = true
        state.showUpload = true
        state.singleForm.announcementId = route.params.id
      }
      state.showJobAdd = true
    }

    const openDialogWelfare = () => {
      dialogWelfare.value.openDialog(state.welfareArray)
    }

    const colse = () => {
      state.singleForm = {}
      state.welfareText = ''
      state.showUpload = false
      state.welfareArray = []
      state.showJobAdd = false
      state.singleForm.isNegotiable = '2'
      state.single = 2
      state.applyMethodsData = {
        applyType: '',
        applyAddress: '',
        extraNotifyAddress: '',
        deliveryWay: []
      }
      state.synergyData = {
        jobContactId: '',
        jobContactSynergyIds: []
      }
      state.majorText = ''
      show.value = true
      state.single = 1
    }
    // 关闭之前的回调
    const closeJobAdd = () => {
      ElMessageBox.confirm('确定要关闭本页面吗？关闭后本页面内容将不做保存！', '提示')
        .then(() => {
          if (show.value) {
            form.value.resetFields()
            setTimeout(() => {
              form.value?.clearValidate()
            }, 100)
          }
          colse()
        })
        .catch(() => {})
    }
    const submit = async () => {
      if (state.single === 1) {
        if (Number(state.singleForm.minWage) > Number(state.singleForm.maxWage)) {
          ElMessage.error('最低薪资大于最高薪资')
          return
        }
        form.value.validate(async (valide: boolean) => {
          const [provinceId, cityId] = state.singleForm.areaName

          if (valide) {
            const postData = {
              ...state.singleForm,
              provinceId,
              cityId,
              deliveryWay: state.singleForm.deliveryWay?.join()
            }

            const { jobTempData } = await temporaryJobSave(postData)
            emit('jobTempData', { ...jobTempData, index: state.index })
            ElMessage.success('操作成功')
            form.value?.resetFields()
            colse()
            setTimeout(() => {
              form.value?.clearValidate()
            }, 100)
          } else {
            return false
          }
          return valide
        })
      } else {
        try {
          const res = await jobBatchAdd({
            filePath: state.filePath,
            announcementId: state.singleForm.announcementId,
            companyId: state.singleForm.companyId
          })
          emit('jobTempData', { res, batch: true })
          colse()
        } catch (msg: any) {
          ElMessage.closeAll()
          ElMessage({
            message: msg,
            type: 'error',
            duration: 10000,
            showClose: true
          })
        }
      }
    }
    const filterJobTemplate = (query: any) => {
      getData(query)
    }
    const changeTemplate = async (val: any) => {
      const res = await getJobEidtData({ id: val })
      state.singleForm = { ...state.singleForm, ...res }
      // 非合作单位剔除deliveryWay字段
      if (!props.isCooperation) {
        delete res.deliveryWay
      }
      state.singleForm.periodDate = ''
      state.singleForm.deliveryWay = res.deliveryWay ? res.deliveryWay?.split(',') : []
      state.singleForm.announcementId = route.params.id
      state.singleForm.isTemp = 1

      handleWelfare(res.welfareTage)
    }
    const downloadFile = () => {
      window.location.href = '/static/template/job_template.xlsx'
    }
    const handleSuccess = (res: any) => {
      state.filePath = res.data.url
    }
    const handleRemove = () => {
      state.filePath = ''
    }

    const handleDisabledDate = (time: any) => {
      const current = new Date()
      const curYear = current.getFullYear()
      const curMonth = current.getMonth() + 1
      const curDate = current.getDate()
      const todayTimestamp = new Date(`${curYear}/${curMonth}/${curDate} 00:00:00`).getTime()
      const nextYearTimestamp = todayTimestamp + 365 * 24 * 60 * 60 * 1000
      const limitDateTimestamp = new Date(`${state.date?.replace('-', '/')} 00:00:00`).getTime()

      return (
        time.getTime() < todayTimestamp ||
        time.getTime() > nextYearTimestamp ||
        time.getTime() > limitDateTimestamp
      )
    }

    const changeMajor = (val: any) => {
      // 找到对应的job里面的信息
      const job = <any>props.jobList.find((item: any) => item.id === val)
      // 给一个confirm
      ElMessageBox.confirm(`确定复制(${job.majorTxt})到该职位?`, '提示')
        .then(() => {
          // 解除数据绑定
          const { majorId } = job
          state.singleForm.majorId = majorId.split(',')
          state.singleForm.majorTitle = job.majorTxt
        })
        .catch(() => {})
    }

    const salarChage = (data) => {
      Object.assign(state.singleForm, data)
    }

    const recognition = () => {
      aiRecognition(state.majorText).then((r) => {
        state.singleForm.majorId = r.majorIds
        state.singleForm.majorTitle = r.majorNames.join(',')
      })
    }

    return {
      ...toRefs(state),
      form,
      show,
      open,
      closeJobAdd,
      handleChangeSingle,
      handleChangeBatch,
      openDialogWelfare,
      dialogWelfare,
      submit,
      handleWelfare,
      filterJobTemplate,
      changeTemplate,
      downloadFile,
      handleSuccess,
      handleRemove,
      handleDisabledDate,
      changeMajor,
      salarChage,
      recognition
    } as any
  }
})
</script>

<style lang="scss" scoped>
.title {
  margin-top: 15px;
  border-left: 2px solid var(--color-primary);
}
.upload {
  cursor: pointer;
  color: var(--color-primary);
  margin-left: 15px;
}
:deep(.el-form-item__content) {
  align-items: flex-start;
}
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
.single {
  height: calc(100% - 100px);
  overflow: auto;
}
</style>
