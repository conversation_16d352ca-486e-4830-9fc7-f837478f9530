<template>
  <div>
    <el-tooltip content="若未上传，则显示默认背景图。">
      <i class="el-icon el-icon-question mr-10" />
    </el-tooltip>
    <div class="flex ai-center" v-if="!showUploadPreview">
      <el-upload
        action="/upload/image"
        :show-file-list="false"
        :limit="1"
        :on-success="handleBackgroundImgSuccess"
      >
        <el-button type="primary">+上传图片</el-button>
      </el-upload>
    </div>

    <div class="image" v-else>
      <img :src="backgroundImgUrl" alt="" />

      <div class="delete" @click="handleDelete">x</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const emits = defineEmits(['update:modelValue', 'update:backgroundImg'])

const props = defineProps({
  backgroundImg: {
    type: String,
    default: ''
  },

  modelValue: {
    type: String,
    default: ''
  }
})

const backgroundImgFileId = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})

const backgroundImgUrl = computed({
  get() {
    return props.backgroundImg
  },
  set(val) {
    emits('update:backgroundImg', val)
  }
})

const showUploadPreview = computed(
  () => backgroundImgFileId.value !== '' && backgroundImgFileId.value !== '0'
)

const handleBackgroundImgSuccess = (resp) => {
  const { data } = resp

  emits('update:modelValue', data.id)
  emits('update:backgroundImg', data.fullUrl)
}

const handleDelete = () => {
  emits('update:modelValue', '')
  emits('update:backgroundImg', '')
}
</script>

<style lang="scss" scoped>
.image {
  position: relative;
  margin-top: 15px;
  img {
    width: 100px;
    height: 100px;
  }

  .delete {
    position: absolute;
    top: 0;
    left: 80px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 20px;
    font-size: 20px;
    background: var(--color-whites);
    border-radius: 50%;
  }
}
</style>
