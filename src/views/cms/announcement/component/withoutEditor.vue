<template>
  <div class="editor">
    <div class="pb-10 border-bottom">
      <div class="fw-bold mb-10 color-danger">
        {{ baseInfo.jobNum }}个职位，共招{{ baseInfo.amountCount }}人
      </div>
      <div class="fw-bold mb-10 color-dark">基本信息</div>
      <div class="flex mb-10">
        <div class="span-3">申请审核时间：{{ baseInfo.applyAuditTime }}</div>
        <div class="span-3">截止日期：{{ baseInfo.periodDate }}</div>
        <div class="span-3">学历要求：{{ baseInfo.educationTxt }}</div>
      </div>
      <div class="flex mb-10">
        <div class="span-3">所属省份：{{ baseInfo.areaProvinceTxt }}</div>
        <div class="span-3">工作城市：{{ baseInfo.areaCityTxt }}</div>
        <div class="span-3">需求学科：{{ baseInfo.majorTxt }}</div>
      </div>
      <div class="flex mb-10">
        <div class="span-3">所属栏目：{{ baseInfo.homeColumnTxt }}</div>
        <div class="span-3">所属副栏目：{{ baseInfo.homeSubColumnTxt }}</div>
        <div class="span-3">调用副栏目：{{ baseInfo.columnTxt }}</div>
      </div>
      <div class="flex mb-10">
        <div class="span-3">报名方式：{{ baseInfo.applyTypeTxt }}</div>
        <div class="span-3">投递地址：{{ baseInfo.applyAddress }}</div>
        <div class="span-3" v-if="baseInfo.companyDeliveryType === 2">
          投递通知邮箱：{{ baseInfo.extraNotifyAddress }}
        </div>
      </div>
      <div class="flex mb-10">
        <div class="span-3" v-if="baseInfo.isCooperation !== '1'">
          地址隐藏：{{ baseInfo.addressHideStatusText }}
        </div>
        <div class="span-3">是否附件提示：{{ baseInfo.isAttachmentNoticeTxt }}</div>
      </div>

      <div class="flex mb-10">
        <div class="span-3">公告简标题：{{ baseInfo.subTitle }}</div>
        <div class="span-3">亮点描述：{{ baseInfo.highlightsDescribe }}</div>
      </div>

      <div class="flex mb-10">
        <div class="span-3" v-if="baseInfo.activityAnnouncementListText">
          关联活动：{{ baseInfo.activityAnnouncementListText }}
        </div>
        <div class="span-3" v-if="baseInfo.activityJobContent">
          招聘岗位：{{ baseInfo.activityJobContent }}
        </div>
      </div>

      <div class="fw-bold mb-10 color-dark">公告详情</div>
      <div class="detail-main" v-html="baseInfo.content"></div>
    </div>
    <div class="py-20 border-bottom">
      <div class="fw-bold mb-10 color-dark" id="job">职位列表</div>
      <el-table :data="tableData" border>
        <el-table-column prop="code" label="岗位代码" />
        <el-table-column prop="name" label="职位名称">
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="jobDetail(baseInfo.id, $index + 1)">{{
              row.name
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="基本信息" prop="information" width="300px" class-name="information">
          <template #default="{ row }">
            <el-tooltip
              popper-class="information-class"
              class="box-item"
              :content="row.information"
              placement="top-start"
            >
              {{ row.information }}
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="department" label="用人部门" />
        <el-table-column prop="contact" label="职位联系人">
          <template #default="{ row }">
            <div class="flex">
              <div>{{ row.jobContact?.contact }}</div>
              <div class="ml-5">{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</div>
            </div>
            <div>
              <span class="mr-5">{{ row.jobContact?.email }}</span>
              <span class="mr-5">{{ row.jobContact?.mobile }}</span>
              <span v-if="row.jobContact?.department">/{{ row.jobContact?.department }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contactSynergyNum" label="协同子账号">
          <template #default="{ row }">
            <div v-if="row.jobContactSynergyNum === 0">{{ row.jobContactSynergyNum }}</div>
            <el-popover v-else placement="top-start" :width="200">
              <template #reference>
                {{ row.jobContactSynergyNum }}
              </template>
              <h4>协同子账号</h4>
              <div v-for="item in row.jobContactSynergy" :key="item.id">
                <div class="flex mt-5">
                  <span class="color-danger" v-if="item.isContact === 1">联</span>
                  <div>{{ item.contact }} / {{ item.department }}</div>
                </div>
                <div>{{ item.email }} {{ item.mobile }}</div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="job-file">
      <JobAttachment v-if="fileList.length" :fileList="fileList" />
    </div>
    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import JobAttachment from './jobAttachment.vue'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'

export default defineComponent({
  name: 'withoutEditor',

  components: { JobDetailDialog, JobAttachment },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    fileList: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    const jobDetailDialog = ref()

    const jobDetail = (id: string, page: number) => {
      jobDetailDialog.value.open(id, page)
    }

    return { jobDetailDialog, jobDetail }
  }
})
</script>

<style lang="scss" scoped>
@use '/src/theme/app' as *;

.editor {
  .detail-main {
    :deep() {
      p {
        color: rgba(51, 51, 51, 0.8);
        font-size: 14px;
        line-height: 2;
      }

      img {
        display: block;
        margin: 20px auto;
        max-width: 100%;
      }

      table {
        margin: 20px auto;
        width: 100%;
        border-collapse: collapse;

        th {
          background-color: #fafafc;
        }

        th,
        td {
          padding: 10px 0;
          text-align: center;
          border: 1px solid #ccc;
        }
      }
    }
  }

  :deep(.information) {
    .cell {
      @include utils-ellipsis-lines(2, 1.5);
    }
  }
}
</style>

<style lang="scss">
.information-class {
  width: 250px;
}
</style>
