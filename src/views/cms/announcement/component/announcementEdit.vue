<template>
  <div>
    <el-dialog v-model="batchEditVisible" title="批量编辑属性" width="500px">
      <AnnouncementCheckBox v-model="form.attribute" :checkBoxList="list" />
      <el-button type="primary" @click="submit">确定</el-button>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { defineComponent, reactive, toRefs } from 'vue'
import { announcementBatchEdit } from '/@/api/announcement'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'

export default defineComponent({
  name: 'announcementEdit',

  components: { AnnouncementCheckBox },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },

  setup() {
    const state = reactive({
      batchEditVisible: false,
      form: {
        ids: '',
        attribute: ''
      }
    })
    const open = (id: any) => {
      state.form.ids = id
      state.batchEditVisible = true
    }
    const submit = async () => {
      if (state.form.attribute === '') {
        ElMessage.error('请选择属性')
      } else {
        await announcementBatchEdit(state.form)
        state.batchEditVisible = false
        state.form.attribute = ''
      }
    }
    return { ...toRefs(state), open, submit }
  }
})
</script>

<style lang="scss" scoped></style>
