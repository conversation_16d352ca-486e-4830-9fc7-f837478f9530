<template>
  <div class="pb-10 border-bottom">
    <div class="fw-bold mb-10 color-dark" v-if="baseInfo.editorType === '2'">
      修改类型：{{ baseInfo.editorTypeTxt
      }}<router-link
        class="ml-10 bg-primary td-none"
        :to="`/cms/announcementDetail/${baseInfo.id}/${baseInfo.status}`"
        >预览原公告</router-link
      >
    </div>
    <div class="py-20 border-bottom">
      <div class="fw-bold mb-10 color-dark" id="job">新增职位详情：</div>
      <el-table border size="small" :data="baseInfo.authJobList">
        <el-table-column prop="code" label="岗位代码" />
        <el-table-column prop="name" label="职位名称">
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="openJobDetail(baseInfo.id, $index + 1)">{{
              row.name
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="基本信息" prop="information" width="300px" class-name="information">
          <template #default="{ row }">
            <el-tooltip
              popper-class="information-class"
              class="box-item"
              :content="row.information"
              placement="top-start"
            >
              {{ row.information }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="用人部门" />
        <el-table-column prop="contact" label="职位联系人">
          <template #default="{ row }">
            <div class="flex">
              <div>{{ row.jobContact?.contact }}</div>
              <div class="ml-5">{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</div>
            </div>
            <div>
              <span class="mr-5">{{ row.jobContact?.email }}</span>
              <span class="mr-5">{{ row.jobContact?.mobile }}</span>
              <span v-if="row.jobContact?.department">/{{ row.jobContact?.department }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contactSynergyNum" label="协同子账号">
          <template #default="{ row }">
            <div v-if="row.jobContactSynergy === 0">{{ row.jobContactSynergy }}</div>
            <el-popover v-else placement="top-start" :width="200">
              <template #reference>
                {{ row.jobContactSynergyNum }}
              </template>
              <h4>协同子账号</h4>
              <div v-for="item in row.jobContactSynergy" :key="item.id">
                <div class="flex mt-5">
                  <span class="color-danger" v-if="item.isContact === 1">联</span>
                  <div>{{ item.contact }} / {{ item.department }}</div>
                </div>
                <div>{{ item.email }} {{ item.mobile }}</div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'

export default defineComponent({
  name: 'addJobEditor',

  components: { JobDetailDialog },
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  setup() {
    const jobDetailDialog = ref()
    const openJobDetail = (id: string, page: number) => {
      jobDetailDialog.value.open(id, page)
    }
    return { openJobDetail, jobDetailDialog }
  }
})
</script>

<style lang="scss" scoped>
@use '/src/theme/app' as *;

.border-bottom {
  :deep() {
    .information {
      .cell {
        @include utils-ellipsis-lines(2, 1.5);
      }
    }
  }
}
</style>

<style lang="scss">
.information-class {
  width: 250px;
}
</style>
