<template>
  <div class="pb-10 border-bottom">
    <div class="fw-bold mb-10 color-dark">
      修改类型：{{ baseInfo.editorTypeTxt
      }}<router-link
        class="ml-10 bg-primary td-none"
        :to="`/cms/announcementDetail/${baseInfo.id}/${baseInfo.status}`"
        >预览原公告</router-link
      >
    </div>
    <div v-for="(item, index) in baseInfo.modifyBeforeList" :key="index">
      <div class="fw-bold mb-10 color-dark">修改前：</div>
      <div class="fw-bold mb-10 color-dark">职位名称：{{ item.jobName }}</div>
      <div class="flex mb-10">
        <div class="span-2" v-show="item.jobHandleBefore.duty">
          职责：{{ item.jobHandleBefore.duty }}
        </div>
        <div class="span-3" v-show="item.jobHandleBefore.requirement">
          要求：{{ item.jobHandleBefore.requirement }}
        </div>
        <div class="span-3" v-show="item.jobHandleBefore.remark">
          其他说明：{{ item.jobHandleBefore.remark }}
        </div>
        <JobAttachment
          v-show="item.jobHandleBefore.fileIds"
          :fileList="item.jobHandleBefore.fileList"
        />
      </div>
      <div class="fw-bold mb-10 color-dark">修改后：</div>
      <div class="fw-bold mb-10 color-dark">职位名称：{{ item.jobName }}</div>

      <div class="flex mb-10">
        <div class="span-3" v-show="item.jobHandleAfter.duty">
          职责：{{ item.jobHandleAfter.duty }}
        </div>
        <div class="span-3" v-show="item.jobHandleAfter.requirement">
          要求：{{ item.jobHandleAfter.requirement }}
        </div>
        <div class="span-3" v-show="item.jobHandleAfter.remark">
          其他说明：{{ item.jobHandleAfter.remark }}
        </div>
        <JobAttachment
          v-show="item.jobHandleAfter.fileIds"
          :fileList="item.jobHandleAfter.fileList"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import JobAttachment from './jobAttachment.vue'

export default defineComponent({
  name: 'editAnnounceJobDetails',
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  setup() {
    return {}
  },
  components: { JobAttachment }
})
</script>
