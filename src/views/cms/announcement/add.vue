<template>
  <div class="main" v-loading="loading">
    <el-form :model="form" label-width="80px" size="default" :rules="rules" ref="fromData">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>公告信息</span>
              </div>
            </template>
            <div class="flex">
              <el-form-item class="flex-1" label="公告标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
              <el-form-item class="flex-1" label="所属栏目" prop="homeColumnId">
                <Colunm v-model="form.homeColumnId" :columnList="columnList"></Colunm>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                class="flex-1"
                label="所属副栏目"
                label-width="94px"
                prop="homeSubColumnIds"
              >
                <SubColumn v-model="form.homeSubColumnIds" :columnList="columnList"></SubColumn>
              </el-form-item>
              <el-form-item
                class="flex-1"
                label-width="94px"
                label="调用副栏目"
                v-if="form.announcementId"
              >
                <el-input disabled v-model="columnTxt"></el-input>
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item label-width="90px" class="flex-1" label="公告简标题">
                <el-input v-model="form.subTitle" />
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item class="flex-1" label-width="100px" label="公告亮点描述">
                <el-input
                  placeholder="请输入公告亮点/推荐理由（如福利待遇、是否有编）"
                  v-model="form.highlightsDescribe"
                />
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item class="flex-1" label="公告详情"> </el-form-item>
            </div>
            <div class="flex">
              <WangEditor ref="editorRef" v-model="form.content" showCustomUploadFile />
            </div>
            <div class="flex" v-show="form.status !== '1'">
              <el-button class="button submit" type="primary" @click="testHtml">识别</el-button>
            </div>
          </el-card>
          <el-card class="job-card mt20">
            <template #header>
              <div class="card-header jc">
                <span>职位附件</span>
                <el-button class="button" type="primary" @click="openJobFileUpload"
                  >+上传附件</el-button
                >
              </div>
            </template>
            <JobFileUpload ref="jobFile" v-model="fileList" />
          </el-card>
          <el-card class="job-card">
            <template #header>
              <div class="card-header">
                <span>职位信息</span>
                <div style="display: flex">
                  <el-upload
                    v-if="form.companyId"
                    ref="batchUploadRef"
                    action="/announcement/upload-excel"
                    :show-file-list="false"
                    :on-success="batchUpload"
                  >
                    <el-button class="button" type="primary">+批量导入</el-button>
                  </el-upload>

                  <el-button v-else class="button" type="primary" @click="batchUploadMessage"
                    >+批量导入</el-button
                  >
                  <el-button class="button ml-15 mb-10" type="primary" @click="openJobAdd"
                    >+添加职位</el-button
                  >
                </div>
              </div>
            </template>
            <el-table :data="jobList" align="center" border size="default">
              <el-table-column prop="id" label="职位编号" />
              <el-table-column prop="name" label="职位名称">
                <template #default="{ row, $index }">
                  <el-button type="primary" link @click="editJob(row, $index)">
                    {{ row.name }}
                  </el-button>
                </template>
              </el-table-column>

              <el-table-column
                label="基本信息"
                prop="information"
                width="300px"
                class-name="information"
              >
                <template #default="{ row }">
                  <el-tooltip
                    popper-class="information-class"
                    class="box-item"
                    :content="row.information"
                    placement="top-start"
                  >
                    {{ row.information }}
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="department" label="用人部门" />
              <el-table-column
                prop="jobContact?.contact"
                label="职位联系人"
                v-if="hasAccount && isCooperation"
              >
                <template #default="{ row }">
                  <el-popover placement="top-start">
                    <template #reference>
                      <div>
                        <span class="mr-5">{{ row.jobContact?.contact }}</span>
                        <span>{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</span>
                      </div>
                    </template>
                    <div>
                      <h4>职位联系人</h4>
                      <div>
                        <span class="mr-5">
                          {{ row.jobContact?.companyMemberType === '0' ? '主' : '子' }}
                        </span>

                        <span>{{ row.jobContact?.contact }}</span>
                        <span v-if="row.jobContact?.department">
                          /{{ row.jobContact?.department }}
                        </span>
                      </div>
                      <div>
                        <span>{{ row.jobContact?.email }}</span>
                        <span>{{ row.jobContact?.mobile }}</span>
                      </div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                prop="jobContactSynergyNum"
                label="协同子账号"
                v-if="hasAccount && isCooperation"
              >
                <template #default="{ row }">
                  <div v-if="row.jobContactSynergyNum === 0">{{ row.jobContactSynergyNum }}</div>
                  <el-popover v-else placement="top-start" :width="200">
                    <template #reference>
                      {{ row.jobContactSynergyNum }}
                    </template>
                    <div>协同子账号</div>
                    <div v-for="item in row.jobContactSynergy" :key="item.id">
                      <div class="flex mt-5">
                        <span class="color-danger mr-5" v-if="item.isContact === 1">联</span>
                        <div>{{ item.contact }} / {{ item.department }}</div>
                      </div>
                      <div>{{ item.email }} {{ item.mobile }}</div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="auditStatusTxt" label="审核状态" />
              <el-table-column prop="operation" label="操作" width="200px" fixed="right">
                <template #default="{ row, $index }">
                  <el-row>
                    <el-col :span="8">
                      <el-button
                        type="primary"
                        link
                        @click="editJob(row, $index)"
                        :disabled="row.status === '0'"
                        >编辑</el-button
                      >
                    </el-col>

                    <el-col :span="8" v-if="row.canDel">
                      <el-button type="primary" link @click="deleteJob(row)" :disabled="row.isApply"
                        >删除</el-button
                      >
                    </el-col>
                    <el-col :span="8">
                      <el-button type="primary" link @click="copyJob(row)">复制</el-button>
                    </el-col>
                  </el-row>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>公告属性</span>
              </div>
            </template>
            <div class="flex">
              <el-form-item class="flex-1" label="官网公告调用属性" prop="comboAttribute">
                <AttributeCheck
                  :checkBoxList="notOverseasAttributeList"
                  v-model="form.comboAttribute"
                  v-model:date="attributeData"
                ></AttributeCheck>
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item class="flex-1" label="高才海外调用属性" prop="overseasAttribute">
                <AttributeCheck
                  :checkBoxList="overseasAttributeList"
                  v-model="form.overseasAttribute"
                  v-model:date="attributeData"
                  :date-type="'globalAttributeTimeDate'"
                ></AttributeCheck>
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item class="flex-1" label="截止日期" prop="periodDate">
                <el-date-picker
                  class="block"
                  v-model="form.periodDate"
                  type="date"
                  placeholder="请选择截止日期,不选代表详见正文"
                  value-format="YYYY-MM-DD"
                  :disabled-date="handleDisableDate"
                >
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="合作类型">
                <el-radio
                  v-model="form.isCooperation"
                  :disabled="status === '1' || isJobLength"
                  label="2"
                  @change="changeCoopertaion"
                  >非合作单位</el-radio
                >
                <el-radio
                  v-model="form.isCooperation"
                  :disabled="status === '1' || isJobLength"
                  label="1"
                  @change="changeCoopertaion"
                  >合作单位</el-radio
                >
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="选择单位" prop="companyTxt">
                <el-select
                  v-model="form.companyTxt"
                  filterable
                  remote
                  clearable
                  placeholder="请输入单位"
                  :remote-method="remoteMethod"
                  @change="handleChange"
                  :disabled="status === '1' || isJobLength"
                >
                  <el-option
                    v-for="item in companyTypeList"
                    :key="item.id"
                    :label="item.fullName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <el-button type="primary" link v-if="addCompany === '2'" @click="addWaitCooperation"
                  >添加单位</el-button
                >
              </el-form-item>
            </div>

            <el-form-item class="flex" label="单位属性">
              <div class="flex">
                <el-input disabled class="flex-1 ml-5" v-model="typeTxt" />
                <el-input disabled class="flex-1 ml-5" v-model="natureTxt" />
              </div>
            </el-form-item>
            <el-form-item label="投递配置" v-if="isCooperation">
              <el-input disabled v-model="companyDeliveryTypeTxt" />
            </el-form-item>
            <ApplyMethods
              v-model="applyMethodsData"
              :isCooperation="isCooperation"
              :accountType="companyDeliveryType"
            />

            <template v-if="!isCooperation">
              <el-form-item label="地址隐藏">
                <el-radio-group v-model="form.addressHideStatus">
                  <el-radio label="1">隐藏</el-radio>
                  <el-radio label="2">展示</el-radio>
                </el-radio-group>

                <el-tooltip content="选择“隐藏”，前端公告详情页默认不展示投递邮箱地址">
                  <i class="el-icon el-icon-question" style="margin-left: 20px" />
                </el-tooltip>
              </el-form-item>
            </template>

            <el-form-item label="附件提示">
              <el-checkbox
                v-model="form.isAttachmentNotice"
                size="large"
                true-label="1"
                false-label="2"
              />
              &nbsp;&nbsp;1（提示）
              <el-tooltip class="box-item" effect="dark" placement="bottom">
                <template #content>
                  若勾选1（提示），求职者在投递该公告下职位时，将展示提示文案：<br />系统校验到该职位所关联的公告正文提示报名需提交附件材料，请确认是否已上传应聘材料。 </template
                ><i class="el-icon el-icon-question"></i
              ></el-tooltip>
            </el-form-item>

            <p>高级设置</p>
            <br />
            <el-upload
              class="avatar-uploader"
              action="/upload/image"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <p>略缩图：</p>
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <p class="logo" v-if="!imageUrl">支持JPG/PNG图片格式，文件小于5M</p>
            </el-upload>
            <br />
            <div class="flex">
              <el-form-item class="flex-1" label="公告摘要" prop="seoDescription">
                <el-input
                  v-model="form.seoDescription"
                  autosize
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="关键词" prop="seoKeywords">
                <el-input
                  v-model="form.seoKeywords"
                  autosize
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="页面模板" prop="templateId">
                <el-select v-model="form.templateId" filterable clearable>
                  <el-option
                    v-for="item in templateList"
                    :key="item.k"
                    :value="item.k"
                    :label="item.v"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="flex" v-if="showBackgroundUpload">
              <el-form-item label="背景图">
                <el-radio-group v-model="form.backgroundImgFileType">
                  <el-radio
                    v-for="item in backgroundImgFileTypeList"
                    :key="item.k"
                    :label="item.k"
                    >{{ item.v }}</el-radio
                  >
                </el-radio-group>
                <template v-if="form.backgroundImgFileType === '3'">
                  <CutOutUploadImg
                    v-model:file-id="form[templateMapKey.formImgKey]"
                    :uploadText="uploadBackgroundImgTips"
                    width="88px"
                    height="auto"
                    :width-size="cutBackgroundSize.width"
                    :height-size="cutBackgroundSize.height"
                    v-model:full-url="reviewData[templateMapKey.reviewImgKey]"
                  />
                </template>
              </el-form-item>
            </div>
            <p>活动设置</p>
            <br />
            <div class="flex">
              <el-form-item class="flex-1" label="关联活动" prop="activityAnnouncement">
                <el-select v-model="form.activityAnnouncement" multiple filterable clearable>
                  <el-option
                    v-for="item in activityList"
                    :key="item.k"
                    :value="item.k"
                    :label="item.v"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="招聘岗位" prop="activityJobContent">
                <el-input
                  v-model="form.activityJobContent"
                  :rows="2"
                  maxlength="500"
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex button-box">
              <el-button @click="submitAnnounce('2')" type="primary">发布</el-button>
              <el-button v-if="status !== '1'" @click="submitAnnounce('1')">保存</el-button>
              <el-button v-if="status !== '1'" @click="submitAnnounce('1', 'preview')"
                >预览</el-button
              >
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>

    <AddPosition
      :isCooperation="isCooperation"
      ref="showJobAdd"
      :deliveryType="companyDeliveryType"
      :jobList="jobList"
      @jobTempData="getJobTempData"
      :subAccountConfig="subAccountConfig"
    ></AddPosition>

    <el-dialog v-model="dialogAddVisible" title="添加单位">
      <el-form :model="addForm">
        <el-form-item label="单位名称">
          <el-input v-model="addForm.fullName" autocomplete="off" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="单位属性">
          <el-select v-model="addForm.type" placeholder="请选择单位类型">
            <el-option
              v-for="item in typeList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
          <el-select v-model="addForm.nature" placeholder="请选择单位性质">
            <el-option
              v-for="item in companyNatureList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option
          ></el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogAddVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCompany">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
  getCurrentInstance,
  computed
} from 'vue'

import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import AddPosition from './component/addPosition.vue'
import {
  announcementJobOnline,
  getAddParams,
  getAnnouncementEdit,
  getCompanyTypeList,
  identityEditor,
  announcementSubmitAdd,
  temporaryJobCopy,
  jobBatchAdd,
  getAnnouncementTitleOnly,
  hwActivityList
} from '/@/api/announcement'
import { addUnit, getUnitList } from '/@/api/unitManage'
import Colunm from '/@/components/base/colunm.vue'
import SubColumn from '/@/components/base/subColumn.vue'
import AttributeCheck from '/@/components/base/attributeCheck.vue'
import { formatDate } from '/@/utils/formatTime'
import JobFileUpload from './component/jobFileUpload.vue'
import WangEditor from '/@/components/wangEditor/index.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import CutOutUploadImg from '/@/components/upload/cutOutUploadImg.vue'

export default defineComponent({
  components: {
    AddPosition,
    Colunm,
    SubColumn,
    AttributeCheck,
    JobFileUpload,
    WangEditor,
    ApplyMethods,
    CutOutUploadImg
  },
  name: 'cmsAnnouncementAdd',
  setup() {
    const state = reactive({
      fileList: [],
      wangeditorLoading: false,
      editorInstance: <any>null,
      loading: false,
      companyDeliveryType: null,
      companyDeliveryTypeTxt: '',
      isAttachmentNotice: '',
      jobList: [],
      form: {
        title: '',
        homeColumnId: '', // 栏目
        homeSubColumnIds: '', // 所属副栏目
        periodDate: '', // 截止日期
        content: '', // 公告详情
        isCooperation: '2', // 合作类型
        coverThumb: '', // 图片
        seoDescription: '', // 公告摘要
        seoKeywords: '', // 关键词
        companyId: '', // 单位Id
        templateId: '', // 页面模板
        jobIds: [], // 职位
        subTitle: '',
        highlightsDescribe: '', // 亮点描述
        backgroundImgFileType: '1',
        backgroundImgFileId: '', // 背景图文件
        backgroundImgFileId2: '', // 高级模版2背景图片
        backgroundImgFileId3: '', // 高级模版3背景图片
        submitType: '',
        announcementId: '',
        extraNotifyAddress: '',
        applyType: '',
        applyAddress: '',
        addressHideStatus: '2',
        deliveryWay: [],
        comboAttribute: [],
        overseasAttribute: [],
        activityJobContent: '',
        activityAnnouncement: []
      },
      isCooperation: computed(() => state.form.isCooperation === '1'),
      isJobLength: computed(() => !!state.jobList.length),

      showBackgroundUpload: computed(() => {
        // 展示背景设置项
        const id = [2, 4, 5]
        const { templateId } = state.form
        return id.includes(Number(templateId))
      }),

      // 页面模板映射表
      templateData: {
        'template-2': {
          width: 1920,
          height: 234,
          size: 5,
          formImgKey: 'backgroundImgFileId',
          reviewImgKey: 'backgroundImg'
        },
        'template-4': {
          width: 1920,
          height: 400,
          size: 5,
          formImgKey: 'backgroundImgFileId2',
          reviewImgKey: 'backgroundImg2'
        },
        'template-5': {
          width: 460,
          height: 276,
          size: 5,
          formImgKey: 'backgroundImgFileId3',
          reviewImgKey: 'backgroundImg3'
        }
      },

      currentTemplateData: computed(() => {
        const {
          templateData,
          form: { templateId }
        } = state

        return templateData[`template-${templateId}`] || {}
      }),

      templateMapKey: computed(() => {
        const { formImgKey, reviewImgKey } = state.currentTemplateData

        return { formImgKey, reviewImgKey }
      }),

      uploadBackgroundImgTips: computed(() => {
        const { width, height, size } = state.currentTemplateData
        return `建议上传尺寸：${width}px*${height}px，${size}M以内`
      }),

      cutBackgroundSize: computed(() => {
        const { width, height } = state.currentTemplateData
        return { width, height }
      }),

      reviewData: {
        backgroundImg: '',
        backgroundImg2: '',
        backgroundImg3: ''
      },

      addForm: {
        fullName: '',
        type: '',
        nature: ''
      },
      applyMethodsData: computed({
        get() {
          const {
            form: { applyType, applyAddress, extraNotifyAddress, deliveryWay }
          } = state
          return { applyType, applyAddress, extraNotifyAddress, deliveryWay }
        },
        set(val: Object) {
          Object.keys(val).forEach((key) => {
            state.form[key] = val[key]
          })
        }
      }),
      rules: {
        title: [
          {
            required: true,
            message: '请输入公告标题',
            trigger: 'blur'
          }
        ],
        homeColumnId: [
          {
            required: true,
            message: '请选择所属栏目',
            trigger: 'change'
          }
        ],
        companyTxt: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'blur'
          }
        ],
        isCooperation: [
          {
            required: true,
            message: '请选择合作类型',
            trigger: 'change'
          }
        ],
        companyId: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'blur'
          }
        ]
      },
      documentList: [],
      overseasAttributeList: [],
      notOverseasAttributeList: [],
      applyList: [],
      tagList: [],
      recommendList: [],
      templateList: <any>[],
      activityList: <any>[],
      companyCooperationList: [],
      imageUrl: '',
      companyTypeList: [],
      natureTxt: '',
      typeTxt: '',
      dialogAddVisible: false,
      addCompany: '1',
      companyNatureList: <any>[],
      typeList: <any>[],
      columnList: [],
      relationCompany: [],
      isTempJobList: [],
      companyId: '',
      companyTxt: '',
      status: '',
      articleId: '',
      columnTxt: '',
      searchJob: '',
      checkedData: '',
      subAccountConfig: {},
      attributeData: {
        indexTopEndTime: '',
        columnTopEndTime: '',
        doctorPushEndTime: '',
        overseasIndexTopEndTime: '',
        overseasColumnTopEndTime: ''
      },
      hasAccount: computed(() => state.subAccountConfig?.total !== '0'),
      backgroundImgFileTypeList: []
    })
    const fromData = ref()
    const showJobAdd = ref()
    const editorRef = ref()
    const jobFile = ref()
    const route = useRoute()
    const router = useRouter()
    const { proxy } = getCurrentInstance() as any

    const getData = async () => {
      const resp = await getAddParams()

      Object.keys(resp).forEach((key) => {
        state[key] = resp[key]
      })

      // 带路由id就是编辑公告
      if (route.params.id) {
        const {
          announcementInfo,
          announcementInfo: { backgroundImg, backgroundImg2, backgroundImg3 },
          announcementJobList,
          fileList
        } = await getAnnouncementEdit({
          id: route.params.id
        })

        state.form = {
          ...state.form,
          ...announcementInfo
        }

        state.fileList = fileList
        state.articleId = announcementInfo.articleId
        state.form.announcementId = announcementInfo.id
        state.imageUrl = announcementInfo.coverThumb
        state.jobList = announcementJobList
        state.isTempJobList = announcementJobList
        state.natureTxt = announcementInfo.natureTxt
        state.typeTxt = announcementInfo.typeTxt
        state.companyDeliveryType = Number(announcementInfo.companyDeliveryType)
        state.companyDeliveryTypeTxt = announcementInfo.companyDeliveryTypeTxt
        state.columnTxt = announcementInfo.columnTxt
        state.companyTxt = announcementInfo.companyTxt
        state.status = announcementInfo.status
        state.form.deliveryWay = announcementInfo.deliveryWay
          ? announcementInfo.deliveryWay?.split(',')
          : []
        state.form.activityAnnouncement = announcementInfo.activityAnnouncement
          ? announcementInfo.activityAnnouncement?.split(',')
          : []

        state.backgroundImg = announcementInfo.backgroundImg

        Object.keys(state.attributeData).forEach((key) => {
          state.attributeData[key] = announcementInfo[key]
        })

        state.reviewData = { backgroundImg, backgroundImg2, backgroundImg3 }
        state.loading = false
      } else {
        // 没有路由id是新增，直接拿发布参数
        const { companyTypeList, companyNatureList } = await getUnitList()
        state.typeList = companyTypeList
        state.companyNatureList = companyNatureList
        state.loading = false
      }
    }

    const getHwActivityList = async () => {
      const res = await hwActivityList({})
      state.activityList = res || []
    }

    const remoteMethod = (params: any) => {
      if (params !== '') {
        setTimeout(async () => {
          const { list } = await getCompanyTypeList({
            type: state.form.isCooperation,
            fullName: params
          })
          state.companyTypeList = list
        }, 1000)
      }
      // 监听搜索单位回来的列表，空的就要新增非合作单位
      watch(
        () => state.companyTypeList,
        () => {
          // 返回列表为空并且是非合作单位时新增
          if (state.companyTypeList.length === 0 && !state.isCooperation && params !== '') {
            state.addCompany = '2'
          } else {
            state.addCompany = '1'
          }
        },
        { immediate: true }
      )
    }

    const handleChange = (val: string) => {
      state.form.companyId = val
      const res = state.companyTypeList.filter((item: any) => item.id.indexOf(val) > -1)
      state.natureTxt = res[0].natureTxt
      state.typeTxt = res[0].typeTxt
      state.companyDeliveryTypeTxt = res[0].deliveryTypeTxt
      state.companyDeliveryType = Number(res[0].deliveryType)
      state.subAccountConfig = res[0].subAccountConfig
      if (state.companyDeliveryType === 2) {
        state.form.deliveryWay = ['1']
      }
      if (state.companyDeliveryType === 3) {
        state.form.deliveryWay = ['66']
      }
      // 没有单位时置空
      if (res.length === 0 || val === '') {
        state.natureTxt = ''
        state.typeTxt = ''
        state.companyDeliveryTypeTxt = ''
        state.companyDeliveryType = null
      }
    }

    // 页面加载时
    onMounted(() => {
      state.loading = true
      getData()
      getHwActivityList()
    })
    watch(
      () => state.form.isCooperation,
      async () => {
        const { list } = await getCompanyTypeList({ type: state.form.isCooperation })
        state.companyTypeList = list
      },
      { immediate: true, deep: true }
    )

    /*
    提交分3种情况
    1、发布 => 清空页面内容
    2、保存 => 跳去编辑页面
    3、保存并预览（进公告详情页面）

    */

    const announcementPreview = async () => {
      // 预览改为跳转到对于的前端页面,这里因为比较特殊，所以直接用window.open,并且url自己处理了,首先是去掉admin,然后加载
      const { id } = await announcementSubmitAdd({
        ...state.form,
        jobIds: state.form.jobIds.join(),
        deliveryWay: state.form.deliveryWay?.join()
      })
      proxy.mittBus.emit('closeCurrentViewTag')
      router.replace({ path: `/cms/announcementEdit/${id}` }).then(() => {
        router.push({ path: `/cms/announcementDetail/${id}/3` })
      })
    }

    const reset = () => {
      state.form = {
        applyType: '',
        applyAddress: '',
        extraNotifyAddress: '',
        deliveryWay: [],
        isCooperation: '2',
        addressHideStatus: '2'
      }
      Object.keys(state.attributeData).forEach((key) => {
        state.attributeData[key] = ''
      })

      state.form.content = ''
      state.jobList = []
      state.status = ''
      state.natureTxt = ''
      state.typeTxt = ''
      state.companyDeliveryTypeTxt = ''
      state.companyDeliveryType = null
      editorRef.value.clearEditor()
      fromData.value.resetFields()
      state.fileList = []
      jobFile.value.clear()
    }

    const announcementSubmit = async (type: String) => {
      const postData = {
        ...state.form,
        ...state.attributeData,
        jobIds: state.form.jobIds ? state.form.jobIds.join() : '',
        fileIds: state.fileList?.map((item: any) => item.id).join(),
        deliveryWay: state.form.deliveryWay?.join(),
        activityAnnouncement: state.form.activityAnnouncement?.join()
      }

      const { id } = await announcementSubmitAdd(postData)

      if (type === '1') {
        proxy.mittBus.emit('closeCurrentViewTag')
        router.replace({ path: `/cms/announcementEdit/${id}` })
      } else {
        ElMessageBox.alert('提交成功', {
          confirmButtonText: '我知道了',
          callback: () => {
            reset()
            // 这里移除校验会报错，做个处理
            try {
              fromData.value.resetFields()
            } catch (err) {
              return null
            }
            return true
          }
        })
      }
    }

    const submitAnnounce = (type: String, isPriview: String = '') => {
      const {
        showBackgroundUpload,
        form,
        form: { backgroundImgFileType },
        currentTemplateData: { formImgKey }
      } = state
      /*
      type:发布类型 ，isPriview:是否预览
      type: 2 =>发布 1 => 保存
      */
      const current = formatDate(new Date(), 'YYYY-mm-dd')
      // 先拿到职位ID
      state.form.jobIds = state.jobList.map((item: any) => item.id)
      if (state.form.jobIds.length === 0) {
        ElMessage.error('职位不能为空')
        return
      }
      // 截止日期与当前做比较
      if (state.form.periodDate !== '' && current > state.form.periodDate) {
        ElMessage.error('截止日期不能早于当前')
        return
      }

      const isRequiredBgImg =
        showBackgroundUpload &&
        backgroundImgFileType === '3' &&
        (!form[formImgKey] || form[formImgKey] === '0')
      // 背景图
      if (isRequiredBgImg) {
        ElMessage.error('请上传自定义背景图')
        return
      }

      if (isPriview) {
        state.form.submitType = type
        fromData.value.validate(async (valide: Boolean) => {
          if (valide) {
            const { titleOnly, uid } = await getAnnouncementTitleOnly({
              title: state.form.title,
              id: state.articleId
            })
            if (titleOnly) {
              ElMessageBox.confirm(`标题与公告：${uid} 重复，确定要提交吗？`, '提示')
                .then(() => {
                  announcementPreview()
                })
                .catch(() => {})
            } else {
              announcementPreview()
            }
          }
        })
      } else {
        // 如果是编辑的话就赋值id
        if (route.params.id) {
          state.form.announcementId = route.params.id
        }
        state.form.submitType = type
        fromData.value.validate(async (valide: Boolean) => {
          if (valide) {
            const { titleOnly, uid } = await getAnnouncementTitleOnly({
              title: state.form.title,
              id: state.articleId
            })
            if (titleOnly) {
              ElMessageBox.confirm(`标题与公告：${uid} 重复，确定要提交吗？`, '提示')
                .then(() => {
                  announcementSubmit(type)
                })
                .catch(() => {})
            } else {
              announcementSubmit(type)
            }
          }
        })
      }
    }

    const testHtml = async () => {
      try {
        const res = await identityEditor({ content: state.form.content })
        state.form = { ...res, jobIds: [] }
        state.form.deliveryWay = res.deliveryWay ? state.form.deliveryWay.split(',') : []
        editorRef.value.updateEditor(res.content)
        state.natureTxt = res.companyNatureTxt
        state.typeTxt = res.companyTypeTxt
        state.companyDeliveryTypeTxt = res.companyDeliveryTypeTxt
        state.companyDeliveryType = Number(res.companyDeliveryType)
        state.isAttachmentNotice = res.isAttachmentNotice
      } catch {
        //
      }
    }

    const openJobAdd = () => {
      if (state.form.companyId === '') {
        ElMessage.error('请先选择单位')
      } else {
        showJobAdd.value.open(state.form.periodDate, state.form.companyId)
      }
    }
    // 略缩图上传
    const beforeAvatarUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }
    const handleAvatarSuccess = (res: any, file: any) => {
      state.form.coverThumb = res.data.fullUrl
      state.imageUrl = URL.createObjectURL(file.raw)
    }
    const addWaitCooperation = () => {
      state.dialogAddVisible = true
    }

    // 新增没有的非合作单位
    const submitCompany = async () => {
      await addUnit(state.addForm)
      state.dialogAddVisible = false
      const { list } = await getCompanyTypeList({ type: state.form.isCooperation })
      state.companyTypeList = list
    }

    /*
      1、新增 ：子组件传数据直接unshift()
      2、修改 ：子组件传一个index，判断是否有index，有就是修改splice()
      3、批量 ：子组件传一个batch是否为true判断是否为批量，unshift()进jobList
    */

    const getJobTempData = (val: any) => {
      const { jobList } = state
      const { res, batch } = val
      if (batch) {
        jobList.unshift(...res)
      } else {
        const { index } = val
        if (typeof index === 'number') {
          jobList.splice(index, 1, val)
        } else {
          jobList.unshift(val)
        }
      }
    }

    const batchUploadMessage = () => {
      ElMessage.error('请先选择单位')
    }

    const batchUpload = (file: any) => {
      const { url } = file.data
      const { companyId, announcementId } = state.form

      jobBatchAdd({
        filePath: url,
        announcementId,
        companyId
      })
        .then((res) => {
          getJobTempData({ res, batch: true })
        })
        .catch((res: any) => {
          // 先清理前一个message
          ElMessage.closeAll()
          ElMessage({
            message: res,
            type: 'error',
            duration: 10000, // 不自动关闭
            showClose: true // 显示关闭按钮
          })
        })
    }

    // 此删除仅在前端页面做删除，到最后发布发请求把页面有的jobId传过去，后端做删除处理
    const deleteJob = (val: any) => {
      state.jobList = state.jobList.filter((item: any) => item.id !== val.id)
      ElMessage.success('操作成功')
    }

    /* open里需要传几个参数给子组件
    1、date：有效期，在子组件做职位有效期不能早于或晚于公告有效期判断
    2、announcementCompanyId：单位id，去拿职位模板用
    3、id：通过审核的正式职位id
    4、jobTemplateId：临时职位id，用于未发布临时职位编辑修改
    5、isTemp：是否临时职位
    6、announceId：公告id，传给后端，有就是编辑，没有就是新增
    7、index：传给子组件，index===number就是修改职位
    */

    const editJob = (val: any, index) => {
      showJobAdd.value.open('', state.form.companyId, val.id, val.isTemp, route.params.id, index)
    }
    const copyJob = async (val: any) => {
      const { jobTempData } = await temporaryJobCopy({ id: val.id, isTemp: val.isTemp })
      state.jobList.unshift(jobTempData)
    }
    // const realtionChange = (val: any) => {
    //   if (!val) {
    //     state.form.relationCompanyIds = []
    //   }
    // }
    const offline = async (val: any, actionType: any, status: any) => {
      await announcementJobOnline({ id: val.id, actionType })
      let index = 0
      const res = state.jobList.filter((item: any, ind: number) => {
        if (item.id === val.id) {
          index = ind
          return true
        }
        return false
      })[0]
      const data = { ...res, status }
      state.jobList.splice(index, 1, data)
    }
    // 公告有效期不能早于当前日也不能晚于一年时间
    const handleDisableDate = (time: any) => {
      return (
        time.getTime() < Date.now() - 24 * 60 * 60 * 1000 ||
        time.getTime() > Date.now() + 365 * 2 * 60 * 60 * 24 * 1000
      )
    }

    const changeCoopertaion = (val: string) => {
      if (val) {
        state.form.companyTxt = ''
        state.natureTxt = ''
        state.typeTxt = ''
        state.companyDeliveryTypeTxt = ''
        state.companyDeliveryType = null
      }
    }

    const openJobFileUpload = () => {
      jobFile.value.openJobFileUpload()
    }

    return {
      ...toRefs(state),
      getHwActivityList,
      handleChange,
      fromData,
      showJobAdd,
      remoteMethod,
      batchUpload,
      batchUploadMessage,
      openJobAdd,
      testHtml,
      submitAnnounce,
      beforeAvatarUpload,
      handleAvatarSuccess,
      addWaitCooperation,
      submitCompany,
      getJobTempData,
      deleteJob,
      editJob,
      copyJob,
      offline,
      editorRef,
      handleDisableDate,
      changeCoopertaion,
      openJobFileUpload,
      jobFile
    } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 5px;
  padding: 20px;

  :deep(.information) {
    .cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      max-height: calc(2 * 1.5);
      line-height: 1.5;
      word-break: break-word;
      white-space: normal;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.job-card {
  margin-top: 30px;
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .jc {
    justify-content: normal;
    button {
      margin-left: 20px;
    }
  }
}

.button-box,
.submit {
  margin-top: 20px;
}
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>

<style lang="scss">
.information-class {
  width: 300px;
}
</style>
