<template>
  <div class="main">
    <div class="form-big-box">
      <div class="form-box">
        <el-form label-width="90px" :model="formData" ref="announcementForm">
          <el-row>
            <el-col :span="4">
              <el-form-item label="公告检索" prop="announcementTitleNum">
                <el-input
                  v-model="formData.announcementTitleNum"
                  placeholder="请输入公告标题或编号"
                  clearable
                  @keyup.enter="search"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="职位检索" prop="jobNameNum">
                <el-input
                  v-model="formData.jobNameNum"
                  placeholder="请输入职位标题或编号"
                  clearable
                  @keyup.enter="search"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="单位检索" prop="companyNameNum">
                <el-input
                  v-model="formData.companyNameNum"
                  placeholder="请输入单位名称或编号"
                  clearable
                  @keyup.enter="search"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="所属栏目" prop="homeColumnId">
                <Colunm v-model="formData.homeColumnId" :columnList="columnList" />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="申请人" prop="username">
                <el-input
                  v-model="formData.username"
                  placeholder="请输入申请人信息"
                  clearable
                  @keyup.enter="search"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <div class="nowrap ml-15">
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="default" @click="resetForm">重置</el-button>
                <el-link
                  :underline="false"
                  type="primary"
                  size="small"
                  class="ml-12"
                  @click="showMore = !showMore"
                  >展开更多</el-link
                >
              </div>
            </el-col>
          </el-row>

          <div v-show="showMore">
            <el-row>
              <el-col :span="4">
                <el-form-item label="公告属性" prop="attribute">
                  <el-select v-model="formData.attribute" placeholder="不限" clearable filterable>
                    <el-option
                      v-for="item in attributeDocument"
                      :key="item.k"
                      :label="item.v"
                      :value="item.k"
                    ></el-option> </el-select
                ></el-form-item>
              </el-col>

              <el-col :span="4">
                <el-form-item label="申请时间" prop="applyAuditTimeStart">
                  <DatePickerRange
                    v-model:start="formData.applyAuditTimeStart"
                    v-model:end="formData.applyAuditTimeEnd"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="4">
                <el-form-item label="发布时间" prop="firstReleaseTimeStart">
                  <DatePickerRange
                    v-model:start="formData.firstReleaseTimeStart"
                    v-model:end="formData.firstReleaseTimeEnd"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
    <div class="jc-between amount">
      <div>
        共计:
        <span class="danger">{{ total }}</span>
        则公告；
      </div>
      <div v-if="selectedRows.length">
        <el-button type="primary" @click="batchAudit"
          >批量审核({{ selectedRows.length }})</el-button
        >
      </div>
    </div>
    <div class="table-list">
      <el-table
        :data="announcementAduitList"
        border
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column align="center" prop="announcementUid" label="公告id"> </el-table-column>
        <el-table-column align="center" prop="title" label="公告标题" width="300px">
          <template #default="{ row }">
            <router-link
              class="bg-primary td-none"
              :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
              >{{ row.title }}</router-link
            >
            <p class="danger">
              {{ row.tip }}
            </p>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="fullName" label="所属单位">
          <template #default="{ row }">
            <el-button type="primary" link @click="toCompanyDetail(row.companyId)">{{
              row.fullName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="sortOnlineJobCount"
          label="在线职位"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.onlineJobCount }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="sortWaitAuditJobCount"
          label="待审核职位"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.waitAuditJobCount }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="recruitStatusTxt" label="招聘状态"> </el-table-column>
        <el-table-column align="center" prop="creatorName" label="申请人"> </el-table-column>
        <el-table-column
          align="center"
          prop="sortFirstReleaseTime"
          label="发布时间"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.firstReleaseTime }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="sortApplyAuditTime"
          label="申请审核时间"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.applyAuditTime }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="success">
              <router-link
                class="td-none color-white"
                :to="`/cms/announcementAuditDetail/${row.aid}`"
                >审核</router-link
              >
            </el-button></template
          >
        </el-table-column>
      </el-table>
      <Paging class="mt-15" :total="total" @change="handlePaginationChange" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onActivated, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { getAnnounceSearchParams, getCmsAnnouncementAuditList } from '/@/api/announcement'
import Colunm from '/@/components/base/colunm.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

export default defineComponent({
  name: 'cmsAnnouncementAuditList',
  components: { Colunm, DatePickerRange, Paging },
  setup() {
    const state = reactive({
      formData: {
        announcementTitleNum: '',
        jobNameNum: '',
        companyNameNum: '',
        homeColumnId: '',
        username: '',
        attribute: '',
        firstReleaseTimeStart: '',
        releaseTimeEnd: '',
        page: Number,
        pageSize: Number
      },
      announcementAduitList: [],
      columnList: [],
      attributeDocument: [],
      total: 0,
      showMore: false,
      loading: true,
      selectedRows: [] as any[]
    })
    const router = useRouter()
    const announcementForm = ref()
    const search = async () => {
      state.loading = true
      const { list, pages } = await getCmsAnnouncementAuditList(state.formData)
      state.announcementAduitList = list
      state.total = pages.total
      state.loading = false
    }
    const getData = async () => {
      const { columnList, attributeDocument } = await getAnnounceSearchParams()
      state.columnList = columnList
      state.attributeDocument = attributeDocument
      search()
    }
    getData()
    const toCompanyDetail = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }
    const handleSortChange = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortFirstReleaseTime')
      Reflect.deleteProperty(state.formData, 'sortApplyAuditTime')
      Reflect.deleteProperty(state.formData, 'sortWaitAuditJobCount')
      Reflect.deleteProperty(state.formData, 'sortOnlineJobCount')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      search()
    }
    const resetForm = () => {
      announcementForm.value.resetFields()
      getData()
    }
    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      search()
    }
    const handleSelectionChange = (rows: any[]) => {
      state.selectedRows = rows
    }
    const batchAudit = () => {
      const aids = state.selectedRows.map((row) => row.aid)
      const aid = aids[0]
      // 剩余的id
      const aids2 = aids.slice(1)
      const url = `/cms/announcementAuditDetail/${aid}`

      if (aids2.length) {
        router.push({
          path: url,
          query: {
            nextIds: aids2.join(',')
          }
        })
      } else {
        router.push(url)
      }
    }
    onActivated(() => {
      search()
    })
    return {
      ...toRefs(state),
      getData,
      search,
      toCompanyDetail,
      handleSortChange,
      resetForm,
      announcementForm,
      handlePaginationChange,
      handleSelectionChange,
      batchAudit
    }
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
</style>
