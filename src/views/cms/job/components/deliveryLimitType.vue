<template>
  <div>
    <el-form-item label="投递限制" prop="deliveryLimitType">
      <el-checkbox-group v-model="deliveryLimitType">
        <el-checkbox v-for="item in deliveryLimitList" :key="item.v" :label="item.k">{{
          item.v
        }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue'
import { getDeliveryLimit } from '/@/api/config'

export default defineComponent({
  name: 'deliveryLimitType',

  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      deliveryLimitType: computed({
        get() {
          const value = props.modelValue
          return value.length ? value.split(',') : []
        },
        set(val: any[]) {
          emit('update:modelValue', val.join(','))
        }
      }),
      deliveryLimitList: []
    })

    const getDeliveryLimitList = async () => {
      state.deliveryLimitList = await getDeliveryLimit()
    }

    getDeliveryLimitList()

    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
