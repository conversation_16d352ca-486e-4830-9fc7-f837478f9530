<template>
  <el-dialog v-model="visible" title="投递列表" width="850px" :before-close="handleClose">
    <el-form ref="form" :model="formData" class="mt-10" :inline="true">
      <el-form-item prop="resumeName">
        <el-input
          v-model="formData.resumeName"
          placeholder="人才姓名/编号"
          filterable
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="addTimeStart">
        <DatePickerRange
          v-model:start="formData.addTimeStart"
          v-model:end="formData.addTimeEnd"
          placeholder="投递时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border size="small" v-loading="loading">
      <el-table-column
        prop="id"
        align="center"
        header-align="center"
        label="人才编号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="resumeName"
        align="center"
        header-align="center"
        label="人才姓名"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="resumeAttachmentTitle"
        align="center"
        header-align="center"
        label="附件简历"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="addTime"
        align="center"
        header-align="center"
        label="投递时间"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="1"
        align="center"
        header-align="center"
        label="投递进度"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-popover placement="top" width="650px" trigger="click">
            <template #reference>
              <el-link class="fs-13" :underline="false" type="primary"
                >{{ row.handleTypeTitle || '查看进度' }}
                <i class="el-icon-caret-bottom"></i>
              </el-link>
            </template>
            <el-steps
              :active="row.steps"
              finish-status="success"
              process-status="wait"
              align-center
            >
              <el-step title="已投递" :description="row.jobApplyHandleList[0].addTime"></el-step>
              <el-step title="被查看" :description="row.jobApplyHandleList[1].addTime"></el-step>
              <el-step title="通过初筛" :description="row.jobApplyHandleList[2].addTime"></el-step>
              <el-step title="面试" :description="row.jobApplyHandleList[3].addTime"></el-step>
            </el-steps>
          </el-popover>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-if="pagination.total > 0"
      @change="receivedPaginationChange"
      class="mt-15"
      :total="pagination.total"
    />
  </el-dialog>
</template>

<script lang="ts">
import { nextTick, reactive, ref, toRefs } from 'vue'
// import { ElMessage } from 'element-plus'
import Pagination from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

import { getJobApplyList } from '/@/api/cmsJob'

export default {
  name: 'applyListDailog',
  props: {},
  components: { Pagination, DatePickerRange },
  emits: ['update'],
  setup() {
    const form = ref()

    const state = reactive({
      visible: false,
      loading: false,
      dialogTitle: '添加话题',

      formData: {
        jobId: '',
        resumeName: '',
        addTimeStart: '',
        addTimeEnd: '',

        limit: 20,
        page: 1
      },
      pagination: {
        total: 0
      },
      list: []
    })
    const getList = () => {
      state.loading = true
      getJobApplyList(state.formData).then((resp: any) => {
        state.list = resp.list.map((item: any) => {
          const { length } = item.jobApplyHandleList
          let { jobApplyHandleList } = item
          if (item.jobApplyHandleList.length < 4) {
            jobApplyHandleList.length = 4
            jobApplyHandleList.fill(
              {
                addTime: '-',
                handleType: '',
                id: ''
              },
              length,
              4
            )
          }
          jobApplyHandleList = jobApplyHandleList.map((i: any) => {
            return {
              ...i,
              addTime: i.addTime.substring(5)
            }
          })
          return {
            ...item,
            jobApplyHandleList,
            steps: length
          }
        })
        state.loading = false
      })
    }

    const open = (id: any) => {
      if (!id) return
      state.formData.jobId = id
      state.visible = true
      getList()
    }

    const handleReset = () => {
      form.value.resetFields()
      nextTick(() => {
        form.value.clearValidate()
      })
    }

    const handleClose = (done) => {
      handleReset()
      done()
    }

    return {
      form,
      open,
      handleClose,
      getList,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.right {
  position: absolute;
  left: calc(100% + 10px);
  white-space: nowrap;
}
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
