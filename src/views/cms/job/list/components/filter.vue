<template>
  <el-form ref="form" label-width="70px" :model="formData">
    <div class="flex">
      <el-form-item class="span-6" label="职位检索" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请填写职位名称或ID"
          clearable
          @keyup.enter="handleSearch"
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label="单位检索" prop="company">
        <el-input
          v-model="formData.companyName"
          placeholder="请填写单位名称或ID"
          clearable
          @keyup.enter="handleSearch"
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label="职位类型" prop="jobCategoryId">
        <JobCategory multiple v-model="formData.jobCategoryId" placeholder="全部" />
      </el-form-item>
      <el-form-item class="span-6" label="审核状态" prop="auditStatus">
        <Audit v-model="formData.auditStatus" placeholder="全部" />
      </el-form-item>
      <el-form-item class="span-6" label="需求专业" prop="majorId">
        <MajorCategory :deep="2" :multiple="false" v-model="formData.majorId" placeholder="全部" />
      </el-form-item>
      <el-form-item class="span-6" label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetField">重置</el-button>
          <el-link :underline="false" class="ml-10" @click="showMore = !showMore">展开</el-link>
        </div>
      </el-form-item>
    </div>
    <div v-show="showMore">
      <div class="flex">
        <el-form-item class="span-6" label="招聘状态" prop="status">
          <Recruitment v-model="formData.status" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-6" label="学历要求" prop="educationType">
          <Education v-model="formData.educationType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-6" label="工作城市" prop="cityId">
          <Region multiple collapse-tags v-model="formData.cityId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-6" label="用人部门" prop="department">
          <el-input
            v-model="formData.department"
            placeholder="请填写用人部门"
            clearable
            @keyup.enter="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-6" label="海外经历" prop="abroadType">
          <AbroadExperience v-model="formData.abroadType" placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item class="span-6" label="工作性质" prop="natureType">
          <WorkNature v-model="formData.natureType" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-6" label="显示状态" prop="isShow">
          <el-select v-model="formData.isShow" placeholder="不限" filterable clearable>
            <el-option
              v-for="(item, index) in showOptionList"
              :key="index"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-6" label="发 布 人" prop="creator">
          <el-input
            v-model="formData.creator"
            placeholder="请填写创建人"
            clearable
            @keyup.enter="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-6" label="发布时间" prop="releaseTimeStart">
          <DatePickerRange
            v-model:start="formData.releaseTimeStart"
            v-model:end="formData.releaseTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-6" label="刷新时间" prop="refreshTimeStart">
          <DatePickerRange
            v-model:start="formData.refreshTimeStart"
            v-model:end="formData.refreshTimeEnd"
          />
        </el-form-item>
        <el-col :span="4">
          <el-form-item label="是否小程序" prop="isMiniapp">
            <IsMiniapp v-model="formData.isMiniapp" />
          </el-form-item>
        </el-col>
        <el-form-item class="span-6" label="编制类型" prop="establishmentType">
          <EstablishmentType v-model="formData.establishmentType" />
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script lang="ts">
import { reactive, toRefs, ref, nextTick } from 'vue'
import MajorCategory from '/@select/majorCategory.vue'
import Education from '/@select/education.vue'
import Region from '/@select/region.vue'
import JobCategory from '/@select/jobCategory.vue'
import WorkNature from '/@select/workNature.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import Audit from '/@select/audit.vue'
import Recruitment from '/@select/jobStatus.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import EstablishmentType from '/@/components/base/select/establishmentType.vue'

export default {
  name: 'cmsJobQueryFilter',
  components: {
    EstablishmentType,
    MajorCategory,
    Education,
    Region,
    JobCategory,
    WorkNature,
    DatePickerRange,
    AbroadExperience,
    Audit,
    Recruitment,
    IsMiniapp
  },
  emits: ['search', 'download'],
  setup(props, { emit }) {
    const form = ref()

    const state = reactive({
      loading: false,
      showMore: false,
      showOptionList: [
        {
          k: '1',
          v: '显示'
        },
        { k: '2', v: '隐藏' }
      ],
      formData: {
        name: '',
        companyName: '',
        jobCategoryId: [],
        auditStatus: '',
        majorId: [],
        status: '',
        educationType: '',
        cityId: [],
        department: '',
        abroadType: '',
        natureType: '',
        isShow: '',
        creator: '',
        releaseTimeStart: '',
        releaseTimeEnd: '',
        refreshTimeStart: '',
        refreshTimeEnd: '',
        isMiniapp: '',
        establishmentType: ''
      }
    })

    const arrayToStringData = (data: any) => {
      return {
        ...data,
        cityId: data.cityId.join(),
        jobCategoryId: data.jobCategoryId.join(),
        establishmentType: data.establishmentType.join(),
        majorId: data.majorId instanceof Array ? data.majorId.join() : data.majorId
      }
    }

    const handleResetField = () => {
      form.value.resetFields()
      nextTick(() => {
        emit('search', arrayToStringData(state.formData))
      })
    }
    const handleSearch = () => {
      emit('search', arrayToStringData(state.formData))
    }

    return {
      form,
      handleSearch,
      handleResetField,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
