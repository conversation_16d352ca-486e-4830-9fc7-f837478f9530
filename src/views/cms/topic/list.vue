<template>
  <div class="box">
    <el-form ref="form" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-5" label="话题检索" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请填写话题标题或编号"
            clearable
            @keyup.enter="getList"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="发布时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5" label-width="10px">
          <div class="nowrap">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="handleResetField">重置</el-button>
          </div>
        </el-form-item>
      </div>
      <div>
        <el-form-item class="span-5" label-width="10px">
          <el-button type="primary" @click="handleAddTopic()">+ 添加话题</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table :data="list" border size="small" ref="table" v-loading="loading">
      <el-table-column
        prop="id"
        label="话题编号"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="title"
        label="话题标题"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="subTitle"
        label="话题标题"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="sortApplyNum"
        label="审核状态"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.statusTitle }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="targetUrl"
        label="目标链接"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <a class="color-primary" :href="row.targetUrl" :underline="false">{{ row.targetUrl }}</a>
        </template>
      </el-table-column>
      <el-table-column
        prop="addTime"
        label="发布时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" align="center" header-align="center" min-width="150px">
        <template #default="{ row }">
          <div class="jc-end">
            <el-button
              v-if="['7'].includes(row.status)"
              @click="handleAudDialog(row.newsId)"
              type="primary"
              size="small"
              >审核</el-button
            >
            <el-button v-if="row.status != 7" @click="handleAddTopic(row.id)" size="small"
              >编辑</el-button
            >
            <el-button @click="handleDelete(row.id)" size="small">删除</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <Pagination
      v-show="list.length"
      class="mt-15"
      :total="pagination.total"
      @change="handlePaginationChange"
    />
    <AddDialog @update="getList" ref="addDialog" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import AddDialog from './component/addDialog.vue'

import { getStatusList } from '/@/api/news'
import { getTopicList, deleteTopic } from '/@/api/topic'

export default {
  name: 'cmsNewsList',
  components: { DatePickerRange, Pagination, AddDialog },
  setup() {
    const form = ref()
    const table = ref()
    const addDialog = ref()

    const state = reactive({
      aa: true,
      loading: false,
      submitLoading: false,
      // 审核状态
      statusList: [],
      homeColumnList: [],

      batchId: <any>'',
      checkedAll: false,

      visible: false,
      dialogTitle: '批量编辑属性',

      // 当前排序ID
      sortEditId: '',

      // 选中项
      multipleSelection: [],

      formData: {
        title: '',
        addTimeStart: '',
        addTimeEnd: '',
        limit: 20,
        page: 1
      },
      pagination: {
        total: 0
      },
      list: []
    })
    const getSelectList = async () => {
      state.statusList = <any>await getStatusList()
    }

    const getList = () => {
      state.loading = true
      getTopicList(state.formData).then((resp: any) => {
        state.pagination.total = resp.page.count
        state.list = resp.list
        state.loading = false
      })
    }

    onMounted(() => {
      getList()
      getSelectList()
    })

    const handleResetField = () => {
      form.value.resetFields()
      setTimeout(() => {
        getList()
      }, 100)
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    const handleAddTopic = (id: string = '') => {
      addDialog.value.open(id)
    }

    const handleDelete = (id: string) => {
      ElMessageBox({
        title: '提示',
        message: '此操作将永久删除选中的数据，是否继续？',
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // eslint-disable-next-line no-param-reassign
            instance.confirmButtonLoading = true
            deleteTopic({ ids: id })
              .then(() => {
                // eslint-disable-next-line no-param-reassign
                instance.confirmButtonLoading = false
                done()
                getList()
              })
              .catch(() => {
                // eslint-disable-next-line no-param-reassign
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
    }

    return {
      form,
      table,
      addDialog,
      handleResetField,
      getList,
      handlePaginationChange,
      handleAddTopic,
      handleDelete,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
  }
}
</style>
