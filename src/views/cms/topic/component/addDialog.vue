<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="600px" :before-close="handleClose">
    <el-form ref="form" :rules="rules" label-width="90px" :model="formData">
      <div class="pr-70 py-20">
        <el-form-item label="话题标题" prop="title">
          <el-input v-model="formData.title" placeholder="请填写标题"></el-input>
        </el-form-item>
        <el-form-item label="副 标 题" prop="subTitle">
          <el-input v-model="formData.subTitle" placeholder="请填写副标题"></el-input>
        </el-form-item>
        <el-form-item label="跳转链接" prop="targetUrl">
          <el-input v-model="formData.targetUrl" placeholder="请填写跳转链接"></el-input>
        </el-form-item>
        <el-form-item label="话题图片" prop="coverUrl">
          <div class="ai-center">
            <el-upload
              class="avatar-uploader jc-start"
              action="/upload/image"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.coverUrl" :src="formData.coverUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <el-link
              v-show="formData.coverUrl"
              @click="handleClearcoverUrl"
              type="primary"
              class="ml-10"
              :underline="false"
              >清除</el-link
            >
          </div>
        </el-form-item>

        <el-form-item required label="相关资讯">
          <div v-for="(item, index) in formData.newsListJson" :key="index" class="flex p-relative">
            <el-form-item
              :prop="'newsListJson.' + index + '.title'"
              :rules="{
                required: true,
                message: '请填写资讯标题',
                trigger: 'blur'
              }"
              class="flex-1 mr-5"
              label-width="0px"
            >
              <el-input v-model="item.title" placeholder="请填写资讯标题"></el-input>
            </el-form-item>
            <el-form-item
              :prop="'newsListJson.' + index + '.links'"
              :rules="{
                required: true,
                message: '请填写资讯标题',
                trigger: 'blur'
              }"
              class="flex-1 ml-5"
              label-width="0px"
            >
              <el-input v-model="item.links" placeholder="请填写资讯跳转链接"></el-input>
            </el-form-item>
            <div class="right mt-4">
              <i
                @click="handleReduceNews(index)"
                v-show="formData.newsListJson.length > 1"
                class="el-icon-remove fs-22 color-warning mr-10"
              ></i>
              <i
                @click="handleAddNews"
                v-show="formData.newsListJson.length - 1 === index"
                class="el-icon-circle-plus fs-22 color-warning"
              ></i>
            </div>
          </div>
        </el-form-item>

        <div class="center">
          <el-button @click="submit" class="w-100" type="primary" :loading="btnLoading">{{
            formData.id ? '确认编辑' : '确认提交'
          }}</el-button>
        </div>
      </div>
    </el-form>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, ref, toRefs } from 'vue'
import { ElMessage } from 'element-plus'

import { addTopic, getTopicDetails } from '/@/api/topic'

export default {
  name: 'addTopicDialog',
  props: {},
  components: {},
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()

    const state = reactive({
      visible: false,
      btnLoading: false,
      dialogTitle: '添加话题',

      formData: <any>{
        title: '',
        subTitle: '',
        targetUrl: '',
        coverUrl: '',
        newsListJson: <any>[
          {
            title: '',
            links: ''
          }
        ]
      },
      rules: {
        title: [
          {
            required: true,
            message: '请填写标题',
            trigger: 'blur'
          }
        ],
        subTitle: [
          {
            required: true,
            message: '请填写副标题',
            trigger: 'blur'
          }
        ],
        targetUrl: [
          {
            required: true,
            message: '请填写跳转链接',
            trigger: 'blur'
          }
        ],
        coverUrl: [
          {
            required: true,
            message: '上传话题图片',
            trigger: ['blur', 'change']
          }
        ]
      }
    })
    const getDetail = () => {
      getTopicDetails({ id: state.formData.id }).then((resp: any) => {
        state.formData = resp
      })
    }

    const open = (id: any) => {
      if (state.formData.id) {
        Reflect.deleteProperty(state.formData, 'id')
      }
      if (id) {
        state.formData.id = id
        state.dialogTitle = '编辑话题'
        getDetail()
      } else {
        state.dialogTitle = '添加话题'
      }
      state.visible = true
    }

    // 略缩图上传
    const beforeAvatarUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }

    // 上传正文头图
    const handleAvatarSuccess = ({ data }: any) => {
      state.formData.coverUrl = data.fullUrl
    }

    const handleReduceNews = (index) => {
      state.formData.newsListJson.splice(index, 1)
    }
    const handleAddNews = () => {
      state.formData.newsListJson.push({
        title: '',
        links: ''
      })
    }
    const handleClearcoverUrl = () => {
      state.formData.coverUrl = ''
    }

    const handleReset = () => {
      form.value.resetFields()
      state.formData.newsListJson = [
        {
          title: '',
          links: ''
        }
      ]
    }

    const handleClose = (done) => {
      handleReset()
      done()
    }

    const submit = async () => {
      form.value.validate((valid: boolean) => {
        if (!valid) return
        state.btnLoading = true
        addTopic(state.formData)
          .then(() => {
            handleReset()
            state.btnLoading = false
            state.visible = false
            emit('update')
          })
          .catch(() => {
            state.btnLoading = false
          })
      })
    }

    return {
      form,
      open,
      handleClose,
      submit,
      beforeAvatarUpload,
      handleAvatarSuccess,
      handleClearcoverUrl,
      handleAddNews,
      handleReduceNews,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.right {
  position: absolute;
  left: calc(100% + 10px);
  white-space: nowrap;
}
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
