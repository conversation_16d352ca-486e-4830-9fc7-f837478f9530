<template>
  <div>
    <el-card>
      <el-form label-width="90px" :model="form" ref="announcementForm">
        <el-row>
          <el-col :span="4">
            <el-form-item label="职位检索" prop="jobNameNum">
              <el-input
                v-model="form.jobNameNum"
                placeholder="请填写职位名称或编号"
                clearable
                @keyup.enter="getList"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="公告检索" prop="announcementTitleNum">
              <el-input
                v-model="form.announcementTitleNum"
                placeholder="请填写公告标题或编号"
                clearable
                @keyup.enter="getList"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="单位检索" prop="companyNameNum">
              <el-input
                v-model="form.companyNameNum"
                placeholder="请填写单位名称或编号"
                clearable
                @keyup.enter="getList"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-form-item label="发布时间" prop="firstReleaseTimeStart">
              <DatePickerRange
                v-model:start="form.firstReleaseTimeStart"
                v-model:end="form.firstReleaseTimeEnd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请时间" prop="applyAuditTimeStart">
              <DatePickerRange
                v-model:start="form.applyAuditTimeStart"
                v-model:end="form.applyAuditTimeEnd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请人" prop="username">
              <el-input
                v-model="form.username"
                placeholder="请填写申请人账号或姓名"
                clearable
                @keyup.enter="getList"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <div class="nowrap ml-15">
              <el-button type="primary" @click="getList">搜索</el-button>
              <el-button @click="reset">重置</el-button>
              <el-button @click="downloadExcel">下载</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ pagination.total }}</span>
          则公告； 运营：<span class="danger">{{ amount.adminAuditStatusWaitCount }}</span
          >则； 单位：<span class="danger">{{ amount.companyAuditStatusWaitCount }}</span
          >则；
        </div>
        <div v-if="selectedRows.length">
          <el-button type="primary" @click="batchAudit"
            >批量审核({{ selectedRows.length }})</el-button
          >
        </div>
      </div>
      <el-table
        v-loading="loading"
        border
        :data="auditList"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="announcementUid" align="center" label="公告ID" />
        <el-table-column prop="title" align="center" label="公告名称" width="300">
          <template #default="{ row }">
            <router-link
              class="bg-primary td-none"
              :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
              >{{ row.title }}</router-link
            >
          </template>
        </el-table-column>
        <el-table-column prop="fullName" align="center" label="所属单位">
          <template #default="{ row }">
            <el-button type="primary" link @click="toCompanyDetail(row.companyId)">{{
              row.fullName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="sortRecruitJobCount"
          align="center"
          label="招聘职位"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.jobNum }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sortOnlineJobCount"
          align="center"
          label="在线职位"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.onlineJobCount }}
          </template>
        </el-table-column>
        <el-table-column prop="waitAuditJobCount" align="center" label="待审核职位" />
        <el-table-column prop="auditStatusTxt" align="center" label="审核状态" />
        <el-table-column prop="recruitStatusTxt" align="center" label="招聘状态" />
        <el-table-column prop="creatorName" align="center" label="申请人" />
        <el-table-column
          prop="sortFirstReleaseTime"
          align="center"
          label="发布时间"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.firstReleaseTime }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sortApplyAuditTime"
          align="center"
          label="申请审核时间"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.applyAuditTime }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="success">
              <router-link
                class="td-none color-white"
                :to="`/cms/announcementAuditDetail/${row.aid}`"
                >审核</router-link
              >
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { getCooperaAuditList } from '/@/api/cooperaAnnouncement'
import Paging from '/@/components/base/paging.vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'announcementAuditList',

  components: { DatePickerRange, Paging },

  setup() {
    const state = reactive({
      form: {
        jobNameNum: '',
        announcementTitleNum: '',
        companyNameNum: '',
        auditStatus: '',
        applyAuditTimeStart: '',
        applyAuditTimeEnd: '',
        firstReleaseTimeStart: '',
        firstReleaseTimeEnd: '',
        username: '',
        export: '',
        page: 1
      },
      pagination: {
        total: 0,
        limit: 20,
        page: 1
      },
      auditList: [],
      amount: {},
      loading: true,
      selectedRows: [] as any[]
    })
    const router = useRouter()
    const announcementForm = ref()
    const getList = async () => {
      const { amount, list, pages } = await getCooperaAuditList(state.form)
      state.auditList = list
      state.amount = amount
      state.pagination.total = pages.total
      state.loading = false
    }
    getList()

    const handlePaginationChange = (data: any) => {
      state.form.page = data.page
      state.form.limit = data.limit
      getList()
    }
    const handleSortChange = ({ prop, order }) => {
      Reflect.deleteProperty(state.form, 'sortFirstReleaseTime')
      Reflect.deleteProperty(state.form, 'sortApplyAuditTime')
      Reflect.deleteProperty(state.form, 'sortRecruitJobCount')
      Reflect.deleteProperty(state.form, 'sortOnlineJobCount')
      if (order === 'ascending') {
        // 正序
        state.form[prop] = 2
      } else if (order === 'descending') {
        state.form[prop] = 1
      }
      getList()
    }
    const toCompanyDetail = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }
    const reset = () => {
      announcementForm.value.resetFields()
      getList()
    }

    const downloadExcel = async () => {
      await getCooperaAuditList({ ...state.form, export: 1 })
    }

    const handleSelectionChange = (rows: any[]) => {
      state.selectedRows = rows
    }

    const batchAudit = () => {
      const aids = state.selectedRows.map((row) => row.aid)
      const aid = aids[0]
      // 剩余的id
      const aids2 = aids.slice(1)
      const url = `/cms/announcementAuditDetail/${aid}`

      if (aids2.length) {
        router.push({
          path: url,
          query: {
            nextIds: aids2.join(',')
          }
        })
      } else {
        router.push(url)
      }
    }

    return {
      ...toRefs(state),
      handlePaginationChange,
      handleSortChange,
      getList,
      announcementForm,
      reset,
      downloadExcel,
      toCompanyDetail,
      handleSelectionChange,
      batchAudit
    }
  }
})
</script>

<style lang="scss" scoped>
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
</style>
