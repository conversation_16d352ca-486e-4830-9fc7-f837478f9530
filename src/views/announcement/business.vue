<template>
  <div class="box">
    <el-tabs type="card" v-model="active" @tab-change="handleTabChange">
      <el-tab-pane label="站内投递" name="1">
        <el-form
          ref="receivedForm"
          :model="receivedFormData"
          class="mt-10"
          size="small"
          :inline="true"
        >
          <el-form-item prop="resumeName">
            <el-input
              v-model="receivedFormData.resumeName"
              placeholder="人才姓名/编号/职位编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="receivedFormData.addTimeStart"
              v-model:end="receivedFormData.addTimeEnd"
              size="small"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="receivedFormData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="getApplyData">搜索</el-button>
          </el-form-item>
          <div>
            共计投递次{{ statistics.allJobApplyAmount }}； {{ statistics.allJobAmount }}个职位
          </div>
        </el-form>
        <el-table :data="onSiteList" border size="small" v-loading="loading">
          <el-table-column
            prop="jobUid"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeUid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmentTitle"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deliveryWayTxt"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          />
          <el-table-column
            prop="statusTitle"
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
          />
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="receivedPaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="站外投递" name="2">
        <el-form
          ref="outSiteForm"
          :model="outSiteFormData"
          class="mt-10"
          size="small"
          :inline="true"
        >
          <el-form-item prop="resumeName">
            <el-input
              v-model="outSiteFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="outSiteFormData.addTimeStart"
              v-model:end="outSiteFormData.addTimeEnd"
              size="small"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="outSiteFormData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="getOutsite">搜索</el-button>
          </el-form-item>
          <div>共计投递次{{ pagination.total }}； {{ jobTotal }}个职位</div>
        </el-form>
        <el-table :data="outSiteList" border size="small">
          <el-table-column
            prop="jobId"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeId"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fileName"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deliveryWayTxt"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
            prop="applyProgramTxt"
          >
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="outsitePaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="面试邀约" name="3">
        <el-form ref="inviteForm" :model="inviteFormData" class="mt-10" size="small" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="inviteFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="invitationTimeStart">
            <DatePickerRange
              v-model:start="inviteFormData.invitationTimeStart"
              v-model:end="inviteFormData.invitationTimeEnd"
              size="small"
              placeholder="面试时间"
            />
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="inviteFormData.addTimeStart"
              v-model:end="inviteFormData.addTimeEnd"
              size="small"
              placeholder="面试创建时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="getInviteData">搜索</el-button>
          </el-form-item>
          <div>
            共计面试次{{ statistics.allInterviewAmount }}； {{ statistics.allJobAmount }}个职位
          </div>
        </el-form>
        <el-table :data="inviteList" border size="small">
          <el-table-column
            prop="id"
            align="center"
            header-align="center"
            label="序号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobUid"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeId"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewTime"
            align="center"
            header-align="center"
            label="面试时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="applyTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="面试创建时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="面试详情"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-popover placement="top" width="400px" trigger="click">
                <template #reference>
                  <el-link class="fs-13" :underline="false" type="primary">查看</el-link>
                </template>
                <div class="px-15">
                  <div class="fw-bold my-15 flex">
                    面试信息
                    <span class="invite-amount">{{ row.interviewAmount }}</span>
                  </div>
                  <div class="mb-10">面试职位：{{ row.jobName }}</div>
                  <div class="mb-10">面试时间：{{ row.interviewTime }}</div>
                  <div class="mb-10">面试地址：{{ row.address }}</div>
                  <div class="mb-10">联 系 人：{{ row.contact }}</div>
                  <div class="mb-10">联系电话：{{ row.telephone }}</div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="invitePaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="下载的简历" name="4">
        <el-form
          ref="downloadForm"
          :model="downloadFormData"
          class="mt-10"
          size="small"
          :inline="true"
        >
          <el-form-item prop="resumeName">
            <el-input
              v-model="downloadFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="downloadTimeStart">
            <DatePickerRange
              v-model:start="downloadFormData.downloadTimeStart"
              v-model:end="downloadFormData.downloadTimeEnd"
              size="small"
              placeholder="下载时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="getDownloadData">搜索</el-button>
          </el-form-item>
          <div>
            共计下载次{{ statistics.allDownloadAmount }}； {{ statistics.allResumeAmount }}个人才
          </div>
        </el-form>
        <el-table :data="downloadList" border size="small">
          <el-table-column
            prop="id"
            align="center"
            header-align="center"
            label="序号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeId"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeTypeTitle"
            align="center"
            header-align="center"
            label="简历类型"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeId"
            align="center"
            header-align="center"
            label="简历编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="downloadTime"
            align="center"
            header-align="center"
            label="下载时间"
            show-overflow-tooltip
          ></el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="downloadPaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import ResumeSource from '/@select/resumeSource.vue'

import {
  getAnnouncementApplyList,
  getAnnouncementInterviewList,
  getOutsideApplyList,
  getResumeDownloadLog
} from '/@/api/cooperaAnnouncement'

export default {
  name: 'announcementBusiness',

  components: { DatePickerRange, Pagination, ResumeSource },

  emits: ['confirm'],

  setup() {
    const route = useRoute()
    const state = reactive({
      loading: false,
      active: '1',
      id: '',
      receivedFormData: {
        id: computed(() => state.id),
        resumeName: '',
        addTimeStart: '',
        addTimeEnd: '',
        deliveryWay: '',
        page: 1,
        limit: 20
      },
      outSiteFormData: {
        id: computed(() => state.id),
        name: '',
        addTimeStart: '',
        addTimeEnd: '',
        deliveryWay: '',
        page: 1,
        limit: 20
      },
      inviteFormData: {
        id: computed(() => state.id),
        resumeName: '',
        invitationTimeStart: '',
        invitationTimeEnd: '',
        addTimeStart: '',
        addTimeEnd: '',
        page: 1,
        limit: 20
      },
      // 面试信息
      interviewDetail: {},
      downloadFormData: {
        id: computed(() => state.id),
        resumeName: '',
        downloadTimeStart: '',
        downloadTimeEnd: '',
        page: 1,
        limit: 20
      },
      pagination: {
        total: 0
      },
      resumeList: [],
      outSiteList: [],
      onSiteList: [],
      inviteList: [],
      downloadList: [],
      statistics: {},
      jobTotal: ''
    })

    const getApplyData = () => {
      state.loading = true
      getAnnouncementApplyList(state.receivedFormData).then((resp: any) => {
        state.loading = false
        state.onSiteList = resp.list
        state.statistics = resp.statistics
        state.pagination.total = Number(resp.page.count)
      })
    }

    const getInviteData = () => {
      state.loading = true
      getAnnouncementInterviewList(state.inviteFormData).then((resp: any) => {
        state.loading = false
        state.inviteList = resp.list
        state.pagination.total = Number(resp.page.count)
        state.statistics = resp.statistics
      })
    }

    const getDownloadData = () => {
      state.loading = true
      getResumeDownloadLog(state.downloadFormData).then((resp: any) => {
        state.loading = false
        state.downloadList = resp.list
        state.pagination.total = Number(resp.page.count)
        state.statistics = resp.statistics
      })
    }

    const getOutsite = () => {
      state.loading = true
      getOutsideApplyList(state.outSiteFormData).then((res: any) => {
        state.loading = false
        state.outSiteList = res.list
        state.pagination.total = Number(res.page.count)
        state.jobTotal = res.jobTotal
      })
    }

    const handleTabChange = () => {
      switch (state.active) {
        case '1':
          getApplyData()
          break
        case '2':
          getOutsite()
          break
        case '3':
          getInviteData()
          break
        case '4':
          getDownloadData()
          break
        default:
          break
      }
    }
    onMounted(() => {
      const {
        query: { id, active }
      } = route

      if (id) {
        state.id = String(id)
        getApplyData()
      }
      if (active === '2') {
        state.active = active
        getInviteData()
      }
    })

    const receivedForm = ref()
    const inviteForm = ref()
    const downloadForm = ref()
    const outSiteForm = ref()

    const handleClose = () => {
      receivedForm.value.resetFields()
      inviteForm.value.resetFields()
      downloadForm.value.resetFields()
    }

    const receivedPaginationChange = (data: any) => {
      state.receivedFormData.limit = data.limit
      state.receivedFormData.page = data.page
      getApplyData()
    }
    const invitePaginationChange = (data: any) => {
      state.inviteFormData.limit = data.limit
      state.inviteFormData.page = data.page
      getInviteData()
    }
    const downloadPaginationChange = (data: any) => {
      state.downloadFormData.limit = data.limit
      state.downloadFormData.page = data.page
      getDownloadData()
    }

    const outsitePaginationChange = (data: any) => {
      state.outSiteFormData.limit = data.limit
      state.outSiteFormData.page = data.page
      getOutsite()
    }

    return {
      receivedForm,
      inviteForm,
      downloadForm,
      outSiteForm,
      getApplyData,
      getInviteData,
      getDownloadData,
      getOutsite,
      handleClose,
      handleTabChange,
      receivedPaginationChange,
      invitePaginationChange,
      downloadPaginationChange,
      outsitePaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.invite-amount {
  font-size: 10px;
  color: #02a7f0;
  background-color: #f2f2f2;
  align-self: center;
  padding: 1px 8px;
  border-radius: 2px;
  margin-left: 10px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
  }
}
</style>
