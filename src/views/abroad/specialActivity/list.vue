<template>
  <div class="main">
    <el-card>
      <div class="form-box">
        <el-form ref="formRef" :model="formData" label-width="100px" :inline="false">
          <div class="flex">
            <el-form-item class="span-4" label="专场名称" prop="name">
              <el-input
                @keyup.enter="getList"
                v-model="formData.name"
                placeholder="请填写名称或者ID"
                clearable
              />
              <!-- <el-select
                @change="getList"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteMethod"
                :loading="nameOptionsLoading"
              >
                <el-option
                  v-for="item in options.special"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select> -->
            </el-form-item>
            <el-form-item class="span-4" label="创建时间" prop="startAddTime">
              <DatePickerRange
                v-model:start="formData.startAddTime"
                v-model:end="formData.endAddTime"
              />
            </el-form-item>
            <el-form-item class="span-4" label="活动开始日期" prop="startStartDate">
              <DatePickerRange
                v-model:start="formData.startStartDate"
                v-model:end="formData.endStartDate"
              />
            </el-form-item>
            <el-form-item class="span-4" label="活动结束日期" prop="startEndDate">
              <DatePickerRange
                v-model:start="formData.startEndDate"
                v-model:end="formData.endEndDate"
              />
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item class="span-4" label="活动状态" prop="status">
              <el-select v-model="formData.status" placeholder="不限" clearable>
                <el-option
                  v-for="item in options.status"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="举办方式" prop="toHoldType">
              <el-select v-model="formData.toHoldType" placeholder="不限" clearable>
                <el-option
                  v-for="item in options.toHoldType"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="flex">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button type="default" @click="handleReset">重置</el-button>
          </div>

          <div class="flex mt-20">
            <el-button type="primary" @click="handleCreate">+创建专场</el-button>
          </div>
        </el-form>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        :border="true"
        size="small"
        style="width: 100%; margin-top: 20px"
        :align="'center'"
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        @sort-change="sortChange"
      >
        <el-table-column prop="id" label="专场ID" width="120" align="center" />
        <el-table-column prop="name" label="专场名称" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link type="primary" :href="row.specialLink" target="_blank">{{ row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="toHoldTypeText" label="举办方式" align="center" />
        <el-table-column prop="activityDateText" label="活动时间" align="center" width="210" />
        <el-table-column prop="statusTxt" label="活动状态" align="center" />
        <el-table-column prop="realParticipationActivityAmount" label="关联活动" align="center" />
        <el-table-column prop="realParticipationCompanyAmount" label="参会单位" align="center" />
        <el-table-column
          prop="addTime"
          label="创建时间"
          sortable="custom"
          align="center"
          width="210"
        />
        <el-table-column prop="statusCompany" label="操作" align="center" width="180px">
          <template #default="{ row }">
            <el-row :gutter="10" justify="center">
              <el-button type="primary" size="small" @click="handleEdit(row.id)">编辑</el-button>
              <el-button type="primary" size="small" link @click="handleCopy(row.specialLink)">
                页面链接</el-button
              >
            </el-row>
          </template>
        </el-table-column>
      </el-table>

      <el-row justify="center" style="margin-top: 20px">
        <Paging :total="total" @change="change"></Paging>
      </el-row>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'

import commonFunction from '/@/utils/commonFunction'

import { getSpecialActivityParams, getSpecialActivityList } from '/@/api/abroad'

const { copyText } = commonFunction()

const router = useRouter()

const formRef = ref()

const isFirst = ref(true)
const loading = ref(false)
const nameOptionsLoading = ref(false)
const formData = ref({
  name: '',
  startAddTime: '',
  endAddTime: '',
  startStartDate: '',
  endStartDate: '',
  startEndDate: '',
  endEndDate: '',
  status: '',
  toHoldType: '',
  sortAddTime: 1,
  pageSize: 20,
  page: 1
})

const tableData = ref([])
const total = ref(0)

const options = <any>reactive({
  status: [],
  toHoldType: [],

  special: []
})

const handleCreate = () => {
  router.push('/abroad/specialActivity/add')
}

const handleEdit = (id: string) => {
  router.push(`/abroad/specialActivity/edit/${id}`)
}

getSpecialActivityParams().then((resp: any) => {
  const { statusList, toHoldTypeList } = resp
  options.status = statusList
  options.toHoldType = toHoldTypeList
})

const getList = () => {
  loading.value = true
  getSpecialActivityList(formData.value).then((resp: any) => {
    tableData.value = resp.list
    total.value = Number(resp.pages.count)
    if (isFirst.value) {
      isFirst.value = false
      options.special = resp.list
    }
    loading.value = false
  })
}

getList()

const remoteMethod = (query: string) => {
  if (query) {
    nameOptionsLoading.value = true
    setTimeout(() => {
      nameOptionsLoading.value = false
      getSpecialActivityList({ name: query }).then((resp: any) => {
        options.special = resp.list
      })
    }, 200)
  }
}

const sortChange = ({ prop, order }) => {
  switch (prop) {
    case 'addTime':
      formData.value.sortAddTime = order === 'ascending' ? 2 : 1
      break
    default:
      break
  }
  getList()
}

const change = (pageInfo) => {
  formData.value.page = pageInfo.page
  formData.value.pageSize = pageInfo.limit
  getList()
}

const handleReset = () => {
  formRef.value.resetFields()
  nextTick(() => {
    getList()
  })
}

const handleCopy = (link) => {
  const message = `<a href='${link}' target='_blank' style='text-align:center;color:#409eff;display:block;text-decoration:none'>${link}</a>`
  ElMessageBox.alert(message, '报名链接', {
    confirmButtonText: '复制',
    dangerouslyUseHTMLString: true,
    callback: (action) => {
      if (action === 'confirm') {
        copyText(link)
      }
    }
  })
}
</script>

<script lang="ts">
export default {
  name: 'specialActivity'
}
</script>

<style lang="scss" scoped>
.form-box {
  :deep() {
    .el-form-item__content {
      width: 100%;

      & > div {
        width: inherit;
      }
    }
  }
}
</style>
