<template>
  <el-dialog
    v-model="visible"
    title="关联单位"
    width="750px"
    :close-on-click-modal="false"
    @close="resetRef"
  >
    <div class="content">
      <div class="wrap">
        <div class="top-box">
          <div class="f-l">
            <el-button type="primary" @click="connectAn">公告关联</el-button>
            <el-button type="primary" @click="connectCompany">单位关联</el-button>
          </div>
          <div class="f-r">
            <el-form
              ref="form1"
              label-width=""
              :model="formData1"
              :rules="rules1"
              @submit.native.prevent
            >
              <el-form-item class="span-4" label="" prop="companyId">
                <el-input
                  style="width: 240px"
                  v-model="formData1.keyword"
                  filterable
                  placeholder="搜索"
                  clearable
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <el-table border max-height="500" :data="tableData" @sort-change="handleSortChange">
          <el-table-column align="center" label="单位排序" prop="sort">
            <template #default="{ row }">
              <el-input v-model="row.sort" @keyup.enter="handleChangeSort(row.id, row.sort)" />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="关联公告"
            prop="announcementName"
            sortable="custom"
          />
          <el-table-column align="center" label="关联单位" prop="companyName" />
          <el-table-column align="center" label="是否置顶" prop="isTop" sortable="custom">
            <template #default="{ row }">
              <el-select v-model="row.isTop" @change="handleChangeStatus(row.id, row.isTop)">
                <el-option
                  v-for="item in isTopList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="handleCancel(row.activityId, row.companyId)"
                :underline="false"
                >取消关联</el-link
              >
            </template>
          </el-table-column>
        </el-table>
        <Paging
          class="mt-20"
          layout="prev, pager, next"
          :total="formData1.count"
          @change="handlePageChange"
        />
      </div>
    </div>
  </el-dialog>
  <CompanyListDialog :activityId="activityId" ref="companyListDialogRef" @confirm="handleSearch" />
  <AnListDialog :activityId="activityId" ref="anListDialogRef" @confirm="handleSearch" />
</template>

<script lang="ts">
import { onMounted, reactive, toRefs, ref, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Paging from '/@/components/base/paging.vue'
import CompanyListDialog from './companyListDialog.vue'
import AnListDialog from './anListDialog.vue'
import {
  getAssociatedList,
  setAssociatedSort,
  setAssociatedTop,
  cancelAssociatedCompany
} from '/@/api/abroad'

export default {
  name: 'tagDialog',
  components: {
    Paging,
    CompanyListDialog,
    AnListDialog
  },
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    activityId: {
      type: String,
      required: true,
      default() {
        return ''
      }
    }
  },
  emits: ['confirm', 'resetTagList'],
  setup(props, { emit }) {
    const form1 = ref()
    const companyListDialogRef = ref()
    const anListDialogRef = ref()
    const state = reactive({
      visible: false,
      tableData: [],
      companyName: '',
      defaultList: [],
      isTopList: [
        { k: '1', v: '是' },
        { k: '2', v: '否' }
      ],
      formData1: {
        keyword: null,
        count: 0,
        page: 1,
        limit: 20,
        sortField: null,
        sortOrder: null as string | null
      },
      rules1: {
        tag: [{ required: false, message: '请选择标签', trigger: 'change' }]
      }
    })

    const getTableList = async () => {
      const {
        list = [],
        page: { count, limit }
      } = await getAssociatedList({
        ...state.formData1,
        activityId: props.activityId
      })

      state.tableData = list
      state.formData1.count = count
      state.formData1.limit = limit
    }

    watch(
      () => state.visible,
      (value) => {
        if (value) {
          getTableList()
        }
      },
      { deep: true }
    )

    const open = () => {
      state.visible = true
    }

    const resetRef = () => {
      state.formData1.keyword = null
      nextTick(() => {
        form1.value?.resetFields()
      })
      emit('confirm')
    }

    const handlePageChange = (r) => {
      const { formData1 } = state
      const { page } = r
      formData1.page = page

      getTableList()
    }

    const connectAn = () => {
      anListDialogRef.value.open()
    }

    const connectCompany = () => {
      companyListDialogRef.value.open()
    }

    const handleChangeSort = async (id, sort) => {
      if (!sort) {
        ElMessage.error('请输入排序')
      }
      await setAssociatedSort({ id, sort })
    }

    const handleChangeStatus = async (id, isTop) => {
      await setAssociatedTop({ id, isTop })
      // console.log(activityId)
    }

    const handleCancel = (activityId, companyId) => {
      // 先提示是否确认操作
      const txt = `确定要取消该关联吗？`
      ElMessageBox.confirm(txt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        const res = await cancelAssociatedCompany({ activityId, companyId })
        if (res) {
          getTableList()
        }
      })
    }

    const handleSearch = () => {
      getTableList()
    }

    const handleSortChange = ({ prop, order }) => {
      state.formData1.sortField = prop
      state.formData1.sortOrder =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null

      // 修改排序以后，分页要重置
      state.formData1.page = 1
      getTableList()
    }

    onMounted(() => {
      state.defaultList = JSON.parse(JSON.stringify((props as any).data)).map((item: any) => {
        return {
          k: item.k,
          v: item.v,
          select: !!item.default
        }
      })
    })

    return {
      form1,
      companyListDialogRef,
      anListDialogRef,
      connectAn,
      connectCompany,
      open,
      resetRef,
      handleChangeSort,
      handleChangeStatus,
      handlePageChange,
      handleCancel,
      handleSearch,
      handleSortChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.top-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
