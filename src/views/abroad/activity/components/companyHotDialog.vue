<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="单位关联"
    width="650px"
    @close="resetRef"
  >
    <div class="content">
      <el-select
        v-model="value"
        class="w100"
        :placeholder="placeholder"
        :multiple="multiple"
        :close-on-click-modal="false"
        filterable
        clearable
        remote
        @change="handleChange"
        reserve-keyword
        :remote-method="getList"
      >
        <el-option v-for="item in list" :key="item.id" :label="item.fullName" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="handleConfirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getCompanyList } from '/@/api/configuration'
import { ElMessage } from 'element-plus'

export default {
  name: 'companyTypeSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    activityId: {
      type: String,
      required: true,
      default: ''
    }
  },
  emits: ['confirm'],
  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      value: [],
      list: <any>[],
      selectedOptions: [] // 新增的数组，用于存储选中的 label 和 value
    })

    const getList = async (kw: string = '') => {
      await getCompanyList({
        activityId: props.activityId,
        isCooperation: 3,
        name: kw
      }).then((resp: any) => {
        state.list = resp
      })
    }

    const open = () => {
      state.visible = true
      getList()
    }

    const handleChange = (value) => {
      let selectedOption: any = {}
      state.selectedOptions = value.map((val) => {
        state.list.forEach((option) => {
          if (option.id === val) {
            selectedOption = option
          } else {
            state.selectedOptions.forEach((item: any) => {
              if (item.value === val) {
                selectedOption = item
              }
            })
          }
        })
        return {
          label: selectedOption.fullName || selectedOption.label,
          value: val
        }
      })
    }

    const handleClose = () => {
      state.visible = false
    }

    const handleConfirm = async () => {
      if (!state.value.length) {
        ElMessage.error('请先选择单位')
        return
      }
      emit('confirm', state.selectedOptions)
      handleClose()
    }

    const resetRef = () => {
      state.value = []
      state.selectedOptions = []
    }

    onMounted(() => {})

    return {
      open,
      getList,
      handleChange,
      handleClose,
      handleConfirm,
      resetRef,
      ...toRefs(state)
    }
  }
}
</script>
