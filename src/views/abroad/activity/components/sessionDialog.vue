<template>
  <el-dialog
    v-model="dialogVisible"
    width="90%"
    title="场次"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form :model="formData" ref="formRef" :rules="formRules">
      <div class="flex">
        <el-form-item label="场次名称" class="span-2" prop="name">
          <el-input v-model="formData.name" placeholder="请输入场次名称" />
        </el-form-item>

        <el-form-item label="场次编号" class="span-3 ml-10">
          <el-input v-model="formData.number" placeholder="请输入场次编号" />
        </el-form-item>
      </div>

      <el-form-item label="举办时间" prop="customTimeRules">
        <div class="flex">
          <el-select style="width: 190px" v-model="customTimeValue" @change="handleTimeChange">
            <el-option
              v-for="item in customList"
              :key="item.k"
              :value="item.k"
              :label="item.v"
            ></el-option>
          </el-select>
          &nbsp;
          <div class="flex" v-if="showTimeCustom">
            <DatePicker
              v-model="formData.startDate"
              style="width: 200px"
              :placeholder="'开始日期(必填)'"
            />
            &nbsp;
            <el-time-picker
              v-model="formData.startTime"
              value-format="HHmm"
              style="width: 200px"
              placeholder="开始时间(非必填)"
              ref="startTimeRef"
              @focus="handleStartTimeFocus"
            />
            ～
            <DatePicker
              v-model="formData.endDate"
              style="width: 200px"
              :placeholder="'结束日期(必填)'"
            />
            &nbsp;
            <el-time-picker
              v-model="formData.endTime"
              value-format="HHmm"
              style="width: 200px"
              placeholder="结束时间(非必填)"
              ref="endTimeRef"
              @focus="handleEndTimeFocus"
            />

            <el-checkbox-group
              style="width: 200px"
              v-model="formData.timeType"
              :max="1"
              class="ml-10"
            >
              <el-checkbox v-for="item in timeTypeList" :key="item.k" :label="item.k">{{
                item.v
              }}</el-checkbox>
            </el-checkbox-group>
          </div>

          <el-input
            v-model="formData.customTime"
            v-else
            clearable
            style="width: 400px"
            placeholder="请输入举办时间"
          />
        </div>
      </el-form-item>

      <el-form-item label="举办地点" prop="customLocalRules">
        <div class="flex">
          <el-select v-model="customLocalValue" @change="handleLocalChange">
            <el-option
              v-for="item in customLocalList"
              :key="item.k"
              :value="item.k"
              :label="item.v"
            ></el-option>
          </el-select>
          &nbsp;

          <div class="flex" v-if="showLocalCustom">
            <el-cascader
              :options="areaList"
              :props="{
                value: 'k',
                label: 'v',
                multiple: true,
                emitPath: false
              }"
              filterable
              placeholder="请选择国家/城市(必填)"
              style="width: 400px"
              v-model="formData.areaIds"
              collapse-tags
              clearable
            />
            &nbsp;

            <el-input v-model="formData.detailAddress" clearable placeholder="请输入详细地址" />
          </div>

          <el-input v-model="formData.customAddress" v-else clearable style="width: 400px" />
        </div>
      </el-form-item>
    </el-form>

    <div class="flex jc-center">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import DatePicker from '/@/components/base/datePicker.vue'
import { hwAreaList } from '/@/api/config'
import { saveTempData } from '/@/api/abroad'
import { ElMessageBox } from 'element-plus'

const emits = defineEmits(['update'])

const customList = ref([
  { k: '2', v: '选择时间' },
  { k: '1', v: '自定义录入' }
])

const customLocalList = ref([
  { k: '2', v: '选择地点' },
  { k: '1', v: '自定义录入' }
])

const timeTypeList = ref([
  { k: '1', v: '当地时间' },
  { k: '2', v: '北京时间' }
])

const formRef = ref()

const areaList = ref([])

const customTimeValue = ref('2')

const customLocalValue = ref('2')

const startTimeRef = ref()

const endTimeRef = ref()

const showTimeCustom = computed(() => customTimeValue.value === '2')

const showLocalCustom = computed(() => customLocalValue.value === '2')

const dialogVisible = ref(false)

const formData = ref({
  name: '',
  number: '',
  customTime: '',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  timeType: [],
  customAddress: '',
  areaIds: [],
  detailAddress: '',
  activityId: '',
  id: ''
})

const handleStartTimeFocus = () => {
  startTimeRef.value.handleOpen()
}

const handleEndTimeFocus = () => {
  endTimeRef.value.handleOpen()
}

const getParams = async () => {
  areaList.value = await hwAreaList()
}

const validateCustomTime = (rule, value, callback) => {
  const { startDate, endDate, customTime } = formData.value
  if (showTimeCustom.value) {
    if (startDate === '' || startDate === null) {
      callback('请选择开始日期')
    }

    if (endDate === '' || endDate === null) {
      callback('请选择结束日期')
    }
  } else if (customTime === '') {
    callback('请输入举办时间')
  }

  callback()
}

const validateCustomLocal = (rule, value, callback) => {
  const { customAddress, areaIds } = formData.value
  if (showLocalCustom.value) {
    if (areaIds.length === 0) {
      callback('请选择国家/城市')
    }
  } else if (customAddress === '') {
    callback('请输入举办地点')
  }

  callback()
}

const formRules = ref({
  name: [{ required: true, message: '场次名称不能为空', trigger: 'blur' }],
  customTimeRules: [{ required: true, validator: validateCustomTime, trigger: 'blur' }],
  customLocalRules: [{ required: true, validator: validateCustomLocal, trigger: 'blur' }]
})

const handleTimeChange = () => {
  formData.value.customTime = ''
  formData.value.startDate = ''
  formData.value.startTime = ''
  formData.value.endDate = ''
  formData.value.endTime = ''
}

const handleLocalChange = () => {
  formData.value.customAddress = ''
  formData.value.areaIds = []
  formData.value.detailAddress = ''
}

const close = () => {
  formRef.value.resetFields()
  Object.keys(formData.value).forEach((key) => {
    const isArray = formData.value[key] instanceof Array
    formData.value[key] = isArray ? [] : ''
  })

  customTimeValue.value = '2'
  customLocalValue.value = '2'

  dialogVisible.value = false
}

const handleSubmit = () => {
  formRef.value.validate(async (val: boolean) => {
    if (val) {
      const postData = {
        ...formData.value,
        areaIds: formData.value.areaIds?.join(),
        timeType: formData.value.timeType?.join()
      }
      // 如果这里已经有活动id，就要出现一个提示，提醒编辑人员，这个是会直接进表的
      if (postData.activityId !== '0' && postData.activityId !== '') {
        // 出提示 确定修改吗？修改后该场次信息将立即在前端生效～
        // 确定修改，取消
        ElMessageBox.confirm('确定修改吗？修改后该场次信息将立即在前端生效～', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            const res = await saveTempData(postData)
            emits('update', res[0])
            close()
          })
          .catch(() => {
            // 取消
          })
      } else {
        const res = await saveTempData(postData)
        emits('update', res[0])
        close()
      }
    }
  })
}

const handleOpenDialog = (data?: any) => {
  if (data.id) {
    formData.value = { ...data }
    customTimeValue.value = data.customTime === '' ? '2' : '1'
    customLocalValue.value = data.customAddress === '' ? '2' : '1'
    formData.value.areaIds = data.areaIds?.split(',')
    formData.value.timeType = data.timeType ? data.timeType.split(',') : []
  }

  dialogVisible.value = true
}

getParams()

defineExpose({ handleOpenDialog })
</script>

<style lang="scss" scoped></style>
