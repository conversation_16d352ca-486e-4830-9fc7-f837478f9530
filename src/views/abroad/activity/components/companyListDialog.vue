<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="单位关联"
    width="650px"
    @close="resetRef"
  >
    <div class="content">
      <el-select
        v-model="value"
        class="w100"
        :placeholder="placeholder"
        :multiple="multiple"
        filterable
        clearable
        remote
        reserve-keyword
        :remote-method="getList"
      >
        <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="handleConfirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getAssociatedCompanyList, addAssociatedCompany } from '/@/api/abroad'
import { ElMessage } from 'element-plus'

export default {
  name: 'companyTypeSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    activityId: {
      type: String,
      required: true,
      default: ''
    }
  },
  emits: ['confirm'],
  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      value: [],
      list: <any>[]
    })

    const getList = async (kw: string = '') => {
      await getAssociatedCompanyList({ activityId: props.activityId, companyName: kw }).then(
        (resp: any) => {
          state.list = resp
        }
      )
    }

    const open = () => {
      state.visible = true
      getList()
    }

    const handleClose = () => {
      state.visible = false
    }

    const handleConfirm = async () => {
      if (!state.value.length) {
        ElMessage.error('请先选择单位')
        return
      }
      const res = await addAssociatedCompany({
        activityId: props.activityId,
        companyId: state.value
      })
      if (res) {
        // ElMessage.success('添加成功！')
        handleClose()
        emit('confirm')
      }
    }

    const resetRef = () => {
      state.value = []
    }

    onMounted(() => {})

    return {
      open,
      getList,
      handleClose,
      handleConfirm,
      resetRef,
      ...toRefs(state)
    }
  }
}
</script>
