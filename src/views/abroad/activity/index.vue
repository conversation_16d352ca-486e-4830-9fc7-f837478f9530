<template>
  <div class="abroad-activity-container">
    <el-form>
      <div class="flex">
        <el-form-item class="span-5 common-select">
          <CheckLabel
            :options-list="queryActiveOptions"
            :form-data="formData"
            v-model="activityType"
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item class="span-5 common-select">
          <CheckLabel
            :options-list="queryCompanyOptions"
            :form-data="formData"
            v-model="companyType"
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item class="span-5" label="活动系列&活动类型">
          <commonCascader
            :list="paramsList.seriesList"
            v-model="formData.type"
            :checkStrictly="false"
          />
        </el-form-item>

        <el-form-item class="span-5" label="活动状态">
          <el-select v-model="formData.activityStatus" clearable="true">
            <el-option
              v-for="item in paramsList.statusList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="上架状态">
          <el-select v-model="formData.groundingStatus" clearable="true">
            <el-option
              v-for="item in paramsList.groundingStatus"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>

      <div class="flex">
        <el-form-item class="span-5" label="创建时间">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5 common-select ml-10">
          <el-select v-model="dateTimeType">
            <el-option
              v-for="item in queryDateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <DatePickerRange v-model:start="timeStart" v-model:end="timeEnd" />
        </el-form-item>
        <el-form-item class="span-5" label="推广位">
          <el-select v-model="formData.promotionPosition" clearable="true">
            <el-option
              v-for="item in paramsList.promotionPositionList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="活动标签">
          <el-select v-model="formData.tags" clearable="true">
            <el-option
              v-for="item in paramsList.tagsList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="举办方式">
          <el-select v-model="formData.toHoldType" clearable="true">
            <el-option
              v-for="item in paramsList.toHoldType"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-5">
          <el-button type="primary" @click="getList">搜索</el-button>
          <el-button type="default" @click="reset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <div class="mt-20 mb-20">
      <router-link class="add-button" to="/abroad/active/add">+ 创建活动</router-link>
    </div>

    <el-table border :data="tableData" v-loading="loading" @sort-change="handleSortChange">
      <el-table-column align="center" label="活动ID" prop="activityId" width="100" />
      <el-table-column align="center" label="活动标签" prop="tags" width="100">
        <template #default="{ row }">
          <span v-for="item in row.tagsList" key="{{item.k}}">
            <span class="btn-sm btn-m-flex bg-color-1" v-if="item.type === '1'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-2" v-else-if="item.type === '2'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-3" v-else-if="item.type === '3'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-4" v-else-if="item.type === '4'">{{
              item.name
            }}</span>
            <span class="btn-sm btn-m-flex bg-color-9" v-else-if="item.type === '9'">{{
              item.name
            }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="活动名称" prop="activityName">
        <template #default="{ row }">
          <el-link @click="handleOpen(row.activityNameUrl)" type="primary" :underline="false">{{
            row.activityName
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column align="center" label="基本信息" prop="combinationFields" min-width="200" />
      <el-table-column align="center" label="创建人" prop="adminName" />
      <el-table-column align="center" label="创建时间" prop="sortAddTime" sortable="custom">
        <template #default="{ row }">{{ row.addTime }}</template>
      </el-table-column>
      <!-- <el-table-column align="center" label="活动日期" prop="activityDate" />
      <el-table-column align="center" label="报名截止日期" prop="signEndDate" /> -->
      <el-table-column align="center" label="活动状态" prop="activityStatus" />
      <el-table-column align="center" label="活动排序" prop="sortSort" sortable="custom">
        <template #default="{ row }">
          <el-input
            :disabled="!abroadActivitySys.includes('sortAndGrounding')"
            v-model="row.sort"
            @keyup.enter="handleChangeSort(row.activityId, row.sort)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="上架状态">
        <template #default="{ row }">
          <el-select
            v-model="row.groundingStatus"
            :disabled="!abroadActivitySys.includes('sortAndGrounding')"
            @change="handleChangeStatus(row.activityId)"
          >
            <el-option
              v-for="item in paramsList.groundingStatus"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" label="关联单位" prop="companyName">
        <template #default="{ row }">
          <span v-if="row.isZhaoPinHui === 2">{{ row.companyName }}</span>
          <el-link v-else type="primary" @click="handleRelevancy(row)" :underline="false">{{
            row.participationCompanyAmount
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column align="center" label="推广位">
        <template #default="{ row }">
          <div class="flex promotion-list" v-for="item in row.promotionList" :key="item.id">
            <el-checkbox checked @change="handleChangeCheckbox(item.id)"></el-checkbox>
            <div class="txt">{{ item.positionTypeName }}</div>
            <el-input v-model="item.sort" @keyup.enter="handlePromotionSort(item.id, item.sort)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-link type="primary" @click="handleToEdit(row.activityId)" :underline="false"
            >编辑</el-link
          >&nbsp;
          <template v-if="row.isZhaoPinHui === 2">
            <el-link type="primary" @click="handleDelete(row.activityId)" :underline="false"
              >删除</el-link
            >
            &nbsp;
          </template>
          <template v-else>
            <el-link type="primary" @click="handleCopy(row.activityNameUrl)" :underline="false"
              >页面链接</el-link
            >
            &nbsp;
            <el-link type="primary" @click="handleRelevancy(row)" :underline="false"
              >关联单位</el-link
            >
          </template>
        </template>
      </el-table-column>
    </el-table>

    <Paging class="mt-20" :total="pageData.count" @change="handlePageChange" />
    <relevancyDialog ref="relevancyDialogRef" :activityId="activityId" @confirm="getList" />
  </div>
</template>

<script lang="ts" setup>
import { useStore } from '/@/store'
import { ref, computed, defineOptions } from 'vue'
import { useRouter } from 'vue-router'
import {
  delActivity,
  getAbroadActivityList,
  getAbroadParamsList,
  changeGroundingStatus,
  changeActiveSort,
  setPromotionSort,
  deletePromotion
} from '/@/api/abroad'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import CheckLabel from '/@/components/business/checkLabel.vue'
import commonCascader from '/@/components/base/commonCascader.vue'
import relevancyDialog from './components/relevancyDialog.vue'
import { ElMessageBox } from 'element-plus'
import commonFunction from '/@/utils/commonFunction.ts'

defineOptions({
  name: 'abroadActivity'
})

const router = useRouter()
const store = useStore()
const requestOldRoutesAction = <any>(
  computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
)
const abroadActivitySys = requestOldRoutesAction.value.abroadActivity ?? []
const { copyText } = commonFunction()
const relevancyDialogRef = ref()
const tableData = ref([])
const pageData = ref({ count: 0 })
const paramsList = ref({
  groundingStatus: [],
  promotionPositionList: [],
  seriesList: [],
  statusList: [],
  tagsList: [],
  toHoldType: []
})

const loading = ref(false)

const activityId = ref('')

const formData = ref({
  sortAddTimeSort: '',
  sortSort: '',
  activityId: '',
  activityName: '',
  companyId: '',
  companyName: '',
  activityStatus: '',
  groundingStatus: '',
  type: '',
  tags: '',
  addTimeStart: '',
  addTimeEnd: '',
  activityStartTimeStart: '',
  activityStartTimeEnd: '',
  activityEndTimeStart: '',
  activityEndTimeEnd: '',
  signTimeStart: '',
  signTimeEnd: '',
  promotionPosition: '',
  page: 1,
  pageSize: 20
})

const queryActiveOptions = [
  { label: '活动名称', value: '1', key: 'activityName', placeholder: '请输入活动名称' },
  { label: '活动ID', value: '2', key: 'activityId', placeholder: '请输入活动ID' }
]

const queryCompanyOptions = [
  { label: '单位名称', value: '1', key: 'companyName', placeholder: '请输入单位名称' },
  { label: '单位ID', value: '2', key: 'companyId', placeholder: '请输入单位ID' }
]

const queryDateOptions = [
  { label: '活动开始时间', value: '1' },
  { label: '活动结束时间', value: '2' },
  { label: '报名截止时间', value: '3' }
]

const dateTimeType = ref('1')
const activityType = ref('1')
const companyType = ref('1')
const timeStart = ref('')
const timeEnd = ref('')

const getQuery = () => {
  formData.value.activityName = activityType.value === '1' ? formData.value.activityName : ''
  formData.value.activityId = activityType.value === '2' ? formData.value.activityId : ''

  formData.value.companyName = companyType.value === '1' ? formData.value.companyName : ''
  formData.value.companyId = companyType.value === '2' ? formData.value.companyId : ''

  formData.value.activityStartTimeStart = dateTimeType.value === '1' ? timeStart.value : ''
  formData.value.activityStartTimeEnd = dateTimeType.value === '1' ? timeEnd.value : ''

  formData.value.activityEndTimeStart = dateTimeType.value === '2' ? timeStart.value : ''
  formData.value.activityEndTimeEnd = dateTimeType.value === '2' ? timeEnd.value : ''

  formData.value.signTimeStart = dateTimeType.value === '3' ? timeStart.value : ''
  formData.value.signTimeEnd = dateTimeType.value === '3' ? timeEnd.value : ''
}

const getParamsList = async () => {
  const resp = await getAbroadParamsList()
  paramsList.value = resp
}

const getList = async () => {
  getQuery()
  loading.value = true
  const { list, page } = await getAbroadActivityList(formData.value)

  tableData.value = list
  pageData.value = page
  loading.value = false
}

const reset = () => {
  Object.keys(formData.value).forEach((key) => {
    formData.value[key] = ''
  })

  dateTimeType.value = '1'
  activityType.value = '1'
  companyType.value = '1'
  timeEnd.value = ''
  timeStart.value = ''

  // 搜索
  getList()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  formData.value.sortAddTimeSort = ''
  formData.value.sortSort = ''

  if (order === 'ascending') {
    // 正序
    formData.value[prop] = 2
  } else if (order === 'descending') {
    formData.value[prop] = 1
  }
  getList()
}

const handleCopy = (link) => {
  const suffix = ''
  const copyLink = `${link + suffix}`
  const message = `<a href='${copyLink}' target='_blank' style='text-align:center;color:#409eff;display:block;text-decoration:none;word-break:break-all;'>${copyLink}</a>`
  ElMessageBox.alert(message, '活动详情页链接', {
    confirmButtonText: '复制',
    dangerouslyUseHTMLString: true,
    callback: (action) => {
      if (action === 'confirm') {
        copyText(copyLink)
      }
    }
  })
}

const handleRelevancy = (row) => {
  activityId.value = row.activityId
  relevancyDialogRef.value.open()
}

const handleToEdit = (id: string) => {
  router.push({
    path: `/abroad/active/edit/${id}`
  })
}

const handleDelete = async (id: string) => {
  // 二次确认
  ElMessageBox({
    title: '提示',
    message: '确认删除该活动吗？',
    center: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        // eslint-disable-next-line no-param-reassign
        instance.confirmButtonLoading = true
        await delActivity({ id })
        getList()
        done()
      } else {
        done()
      }
    }
  })
}

const handleOpen = (url: string) => {
  window.open(url)
}

const handleChangeStatus = async (id: string) => {
  await changeGroundingStatus({ id })
}

const handleChangeSort = async (id: string, sort: string) => {
  await changeActiveSort({ id, sort })
}

const handleChangeCheckbox = async (promotionId: string) => {
  const res = await deletePromotion({ promotionId })
  if (res) {
    getList()
  }
}

const handlePromotionSort = async (promotionId: string, promotionSort: string) => {
  await setPromotionSort({ promotionId, promotionSort })
}

const handlePageChange = (r) => {
  const { page, limit } = r
  formData.value.page = page
  formData.value.pageSize = limit

  getList()
}

getList()
getParamsList()
</script>

<style lang="scss" scoped>
.abroad-activity-container {
  padding: 20px;
  background-color: var(--color-whites);

  .promotion-list {
    border-bottom: 1px #ccc solid;
    padding-top: 6px;
    padding-bottom: 6px;
    align-items: center;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }

  .common-select {
    :deep(.el-form-item__content) {
      flex-wrap: nowrap;
    }
    margin-right: 10px;
  }

  .add-button {
    background-color: var(--color-primary);
    padding: 8px 15px;
    border-radius: 5px;
    color: var(--color-whites);
    font-weight: bold;
    text-decoration: none;
  }

  .edit-button {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: bold;
    margin-right: 5px;
  }

  .btn-sm {
    display: block;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    padding: 2px 5px;
    color: #000000;
    margin-right: 4px;
    font-size: 12px;
  }

  .bg-color-1 {
    background-color: #ffc6b3;
  }
  .bg-color-2 {
    background-color: #b3b3ff;
  }
  .bg-color-3 {
    background-color: #fe7f7f;
  }
  .bg-color-4 {
    background-color: #7ffe8e;
  }
  .bg-color-5 {
    background-color: #a0fded;
  }
  .bg-color-6 {
    background-color: #fe7ff8;
  }
  .bg-color-7 {
    background-color: #d4d4d4;
  }
  .bg-color-8 {
    background-color: #e2ffb3;
  }
  .bg-color-9 {
    background-color: #fffbb3;
  }
}
</style>
