<template>
  <div class="form-container" v-loading="loading">
    <div class="title">{{ title }}</div>

    <!-- 基本信息区域 -->
    <div class="common-title">基本信息</div>

    <!-- 活动表单 -->
    <el-form :model="formData" :rules="formRules" ref="formRef">
      <!-- 活动标签选择 -->
      <el-form-item prop="tags" label="活动标签">
        <el-checkbox-group v-model="formData.tags">
          <el-checkbox label="1" name="tags">付费</el-checkbox>
          <el-checkbox label="2" name="tags">项目推广</el-checkbox>
          <el-checkbox label="3" name="tags">内部推广</el-checkbox>
          <el-checkbox label="4" name="tags">额外推广</el-checkbox>
          <el-checkbox label="9" name="tags">其他</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="活动系列" prop="seriesType">
            <el-select v-model="formData.seriesType" @change="handleSeriesChange">
              <el-option
                v-for="item in seriesList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="活动类型" prop="type">
            <el-select v-model="formData.type">
              <el-option
                v-for="item in renderSeriesList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="pl-10" v-if="isZhaoPinHui">
          <el-form-item label="调用活动类型" prop="subType">
            <el-select clearable v-model="formData.subType">
              <el-option
                v-for="item in options.subTypeParams"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="举办方式" prop="toHoldType">
            <el-select multiple v-model="formData.toHoldType">
              <el-option
                v-for="item in options.toHoldType"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="特色标签" prop="featureTagId">
            <el-select multiple clearable v-model="formData.featureTagId">
              <el-option
                v-for="item in options.featureTagParams"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="pl-10">
          <el-input-tag
            v-model="formData.customFeatureTag"
            placeholder="请录入自定义标签，按Enter键确认"
            :max="10"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="活动名称" prop="name">
            <el-input
              v-model="formData.name"
              maxlength="50"
              style="width: 500px"
              placeholder="请输入活动名称，最多50字"
              show-word-limit
              type="text"
              clearable
              @blur="handleNameBlur"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <block v-if="isZhaoPinHui">
        <el-row>
          <el-col :span="5">
            <el-form-item prop="isCustomTime" label="活动时间" required>
              <el-select v-model="formData.isCustomTime" placeholder="请选择" clearable>
                <el-option label="选择时间" value="2" />
                <el-option label="自定义录入" value="1" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="19" class="flex">
            <template v-if="formData.isCustomTime == '1'">
              <el-form-item class="pl-10" prop="customTime">
                <el-input
                  style="width: 600px"
                  v-model="formData.customTime"
                  placeholder="请输入活动时间"
                  clearable
                />
              </el-form-item>
            </template>

            <template v-else>
              <el-form-item class="pl-10" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  placeholder="开始日期(必填)"
                  format="YYYY.MM.DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item class="pl-10" prop="startTime">
                <el-time-picker
                  v-model="formData.startTime"
                  value-format="HHmm"
                  placeholder="开始时间(非必填)"
                  clearable
                />
              </el-form-item>

              <div style="line-height: 32px; margin: 0 5px">~</div>

              <el-form-item prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  placeholder="结束日期(必填)"
                  format="YYYY.MM.DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item class="pl-10" prop="endTime">
                <el-time-picker
                  v-model="formData.endTime"
                  value-format="HHmm"
                  placeholder="结束时间(非必填)"
                  clearable
                />
              </el-form-item>
            </template>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="5">
            <el-form-item prop="isCustomAddress" label="活动地点" required>
              <el-select v-model="formData.isCustomAddress" placeholder="请选择" clearable>
                <el-option label="选择地点" value="2" />
                <el-option label="自定义录入" value="1" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="19" class="flex">
            <template v-if="formData.isCustomAddress == '1'">
              <el-form-item class="pl-10" prop="customAddress">
                <el-input
                  style="width: 400px"
                  v-model="formData.customAddress"
                  placeholder="请输入活动地点"
                  clearable
                />
              </el-form-item>
            </template>

            <template v-else>
              <el-form-item class="pl-10" prop="areaIds">
                <el-cascader
                  v-model="formData.areaIds"
                  :options="options.areaList"
                  :props="areaProps"
                  collapse-tags
                  placeholder="请选择国家城市(必填)"
                  clearable
                />
              </el-form-item>
              <el-form-item class="pl-10" prop="detailAddress">
                <el-input
                  style="width: 400px"
                  v-model="formData.detailAddress"
                  placeholder="请输入活动地点"
                  clearable
                />
              </el-form-item>
            </template>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="5">
            <el-form-item class="pl-10" label="纬度" prop="latitude">
              <el-input
                style="width: 400px"
                v-model="formData.latitude"
                placeholder="请录入纬度"
                maxLength="15"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item class="pl-10" label="经度" prop="longitude">
              <el-input
                style="width: 400px"
                v-model="formData.longitude"
                placeholder="请录入经度"
                maxLength="15"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item class="pl-10" label="需求人数" prop="activityNumber">
              <el-input
                style="width: 400px"
                v-model="formData.activityNumber"
                placeholder="请录入需求人数"
                maxLength="20"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item class="pl-10" label="活动组织" prop="activityOrganization">
          <el-input
            style="width: 400px"
            v-model="formData.activityOrganization"
            placeholder="请填写"
            maxLength="100"
            clearable
          />
        </el-form-item>

        <el-form-item prop="activityDetail" label="活动详情">
          <WangEditor
            style="width: 1054px"
            ref="activityDetailRef"
            v-model="formData.activityDetail"
            showCustomUploadFile
          />
        </el-form-item>

        <el-form-item prop="participationMethod" label="参会方式">
          <WangEditor
            style="width: 1054px"
            ref="participationMethodRef"
            v-model="formData.participationMethod"
            showCustomUploadFile
          />
        </el-form-item>

        <el-form-item prop="activityHighlights">
          <template #label>
            <div style="width: 56px" v-if="!isEditHighlightsTitle">
              {{ formData.activityHighlightsTitle }}
              <el-icon style="cursor: pointer" @click.stop="handleEditHighlightsTitle">
                <Edit />
              </el-icon>
            </div>
            <el-input
              v-else
              v-model="formData.activityHighlightsTitle"
              maxlength="6"
              type="textarea"
              autosize
              style="width: 56px"
              @blur="isEditHighlightsTitle = false"
              @keyup.enter="isEditHighlightsTitle = false"
              ref="activityHighlightsInputRef"
            />
          </template>
          <WangEditor
            style="width: 1054px"
            ref="activityHighlightsRef"
            v-model="formData.activityHighlights"
            showCustomUploadFile
            :placeholder="'请输入内容（仅在电脑端显示）'"
          />
        </el-form-item>

        <el-form-item label="参会福利" prop="activityBenefits">
          <el-input
            style="width: 1054px"
            v-model="formData.activityBenefits"
            placeholder="请输入参会福利概述，最多150字"
            clearable
          />
        </el-form-item>

        <el-form-item prop="activityBenefitsContent" label="福利详情">
          <WangEditor
            style="width: 1054px"
            ref="activityBenefitsContentRef"
            v-model="formData.activityBenefitsContent"
            showCustomUploadFile
          />
        </el-form-item>

        <el-form-item label="参会须知" prop="attendanceNotes">
          <el-input
            style="width: 1054px"
            v-model="formData.attendanceNotes"
            placeholder="请输入内容（仅在小程序端显示），最多150字"
            clearable
          />
        </el-form-item>

        <el-row>
          <el-form-item class="mr-30" prop="mainImgUrl" label="活动图片" required>
            <div class="upload-img-content">
              <SingleImage
                width="180px"
                uploadText="352px * 187px<br/>仅支持png、jpg、jpeg格式；"
                v-model="formData.mainImgUrl"
                v-model:id="formData.mainImgFileId"
              />
              <div class="tips"><span>*</span>活动主图</div>
            </div>
          </el-form-item>
          <el-form-item class="mr-30" prop="imagePcBannerUrl" required>
            <div class="upload-img-content">
              <SingleImage
                width="180px"
                uploadText="2560px * 350 px<br/>仅支持png、jpg、jpeg格式；"
                v-model="formData.imagePcBannerUrl"
                v-model:id="formData.imagePcBannerId"
              />
              <div class="tips"><span>*</span>PC-banner图</div>
            </div>
          </el-form-item>
          <el-form-item class="mr-30" prop="imageMiniMasterUrl">
            <div class="upload-img-content">
              <SingleImage
                width="180px"
                uploadText="690px * 310px<br/>仅支持png、jpg、jpeg格式；"
                v-model="formData.imageMiniMasterUrl"
                v-model:id="formData.imageMiniMasterId"
              />
              <div class="tips">小程序-主图</div>
            </div>
          </el-form-item>
          <el-form-item class="mr-30" prop="imageMiniBannerUrl" required>
            <div class="upload-img-content">
              <SingleImage
                width="180px"
                :max-size="5"
                uploadText="750px * 400px<br/>png、jpg、jpeg格式；<br/>限5M以内"
                v-model="formData.imageMiniBannerUrl"
                v-model:id="formData.imageMiniBannerId"
              />
              <div class="tips"><span>*</span>小程序-banner图</div>
            </div>
          </el-form-item>
          <el-form-item class="mr-30" prop="imageServiceCodeUrl">
            <div class="upload-img-content">
              <SingleImage
                width="180px"
                uploadText="仅支持png、jpg、jpeg格式；"
                v-model="formData.imageServiceCodeUrl"
                v-model:id="formData.imageServiceCodeId"
              />
              <div class="tips">客服二维码</div>
            </div>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="更多图片">
            <div class="flex">
              <div>
                <uploadSingleImg
                  style="height: 150px; width: 150px"
                  :uploadText="'仅支持png、jpg、jpge格式；'"
                  :max-size="5"
                  class="mr-20"
                  v-model:file-id="formData.otherImgOneFileId"
                  v-model:full-url="otherImgOneUrl"
                />
                <div>其他图片1</div>
              </div>

              <div>
                <uploadSingleImg
                  style="height: 150px; width: 150px"
                  :uploadText="'仅支持png、jpg、jpge格式；'"
                  :max-size="5"
                  class="mr-20"
                  v-model:file-id="formData.otherImgTwoFileId"
                  v-model:full-url="otherImgTwoUrl"
                />

                <div>其他图片2</div>
              </div>

              <div>
                <uploadSingleImg
                  style="height: 150px; width: 150px"
                  :uploadText="'仅支持png、jpg、jpge格式；'"
                  :max-size="5"
                  class="mr-20"
                  v-model:file-id="formData.otherImgThreeFileId"
                  v-model:full-url="otherImgThreeUrl"
                />

                <div>其他图片3</div>
              </div>

              <div>
                <uploadSingleImg
                  style="height: 150px; width: 150px"
                  :uploadText="'仅支持png、jpg、jpge格式；'"
                  :max-size="5"
                  v-model:file-id="formData.imageNoticeId"
                  v-model:full-url="imageNoticeUrl"
                />

                <div>提示图片</div>
              </div>
            </div>
          </el-form-item>
        </el-row>

        <el-row>
          <el-col :span="5">
            <el-form-item prop="applyLinkPersonType" label="人才报名链接">
              <el-select
                v-model="formData.applyLinkPersonType"
                @change="personApplyChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  :label="item.v"
                  :value="item.k"
                  :key="item.k"
                  v-for="item in options.applyLinkPersonTypeList"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="19" class="flex">
            <template
              v-if="formData.applyLinkPersonType == '1' || formData.applyLinkPersonType == '3'"
            >
              <el-form-item class="pl-10" prop="applyLinkPersonFormId">
                <el-select
                  style="width: 200px"
                  v-model="formData.applyLinkPersonFormId"
                  placeholder="请选择表单"
                  filterable
                  remote
                  :remote-method="searchFormList"
                  @change="handleFormListChange"
                  clearable
                >
                  <el-option
                    :label="item.v"
                    :value="item.k"
                    :key="item.k"
                    v-for="item in options.applyFormList"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="formData.applyLinkPersonType == '3'"
                class="pl-10"
                prop="applyLinkPersonFormOptionId"
              >
                <el-select
                  style="width: 200px"
                  v-model="formData.applyLinkPersonFormOptionId"
                  placeholder="请选择选项"
                  clearable
                >
                  <el-option
                    :label="item.v"
                    :value="item.k"
                    :key="item.k"
                    v-for="item in options.applyFormOptionsList"
                  />
                </el-select>
              </el-form-item>
            </template>

            <el-form-item v-else class="pl-10" prop="applyLinkPerson">
              <el-input
                style="width: 300px"
                v-model="formData.applyLinkPerson"
                placeholder="请输入链接"
              />
            </el-form-item>

            <el-form-item class="pl-10" prop="applyPersonTime" label="人才报名截止时间">
              <el-date-picker
                v-model="formData.applyPersonTime"
                type="date"
                placeholder="请选择日期(非必填)"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label=" 单位报名链接" prop="applyLinkCompany">
              <el-input
                style="width: 350px"
                v-model="formData.applyLinkCompany"
                placeholder="请录入单位报名链接"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="1" class="pl-10">
            <el-form-item label="单位报名截止日期" prop="applyCompanyTime">
              <el-date-picker
                v-model="formData.applyCompanyTime"
                type="date"
                placeholder="请选择日期(非必填)"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item prop="templateId" label="页面模板">
              <el-select v-model="formData.templateId" placeholder="请选择" clearable>
                <el-option
                  v-for="item in options.templateList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item prop="activityLink" label="页面链接">
          <el-row align="middle">
            {{ formData.activityDomain }}
            <el-input
              class="pl-10 pr-5"
              style="width: 350px"
              v-model="formData.activityLink"
              placeholder="请输入页面链接,最多50字"
              @input="replaceChinese"
              :maxlength="50"
              clearable
            />
            .html
          </el-row>
        </el-form-item>

        <el-form-item prop="wonderfulReview" label="精彩回顾">
          <WangEditor
            style="width: 1054px"
            ref="wonderfulReviewRef"
            v-model="formData.wonderfulReview"
            showCustomUploadFile
          />
        </el-form-item>
      </block>
      <el-form-item label="活动简介" prop="introduce">
        <el-input
          v-model="formData.introduce"
          maxlength="120"
          style="width: 500px"
          placeholder="请输入活动简介，最多120字"
          show-word-limit
          type="textarea"
          :rows="4"
        />
      </el-form-item>
      <template v-if="!isZhaoPinHui">
        <el-form-item label="活动场次" prop="sessionList">
          <el-button type="primary" @click="handleSession">+ 新增场次</el-button>
        </el-form-item>

        <el-table :data="sessionList" class="table m-20" border>
          <el-table-column label="场次ID" prop="id" />
          <el-table-column label="场次信息">
            <template #default="{ row }">
              <h3>{{ row.name }}</h3>
              <div class="mb-10">时间：{{ row.activityDate }}</div>
              <div>地点：{{ row.activityAddress }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row, $index }">
              <el-button link type="primary" @click="handleSession(row)">编辑</el-button>
              <el-button link type="primary" @click="handleDeleteSession(row.id)">删除</el-button>
              <el-button link type="primary" @click="handleUpList(row, $index)">上移</el-button>
              <el-button link type="primary" @click="handleDownList(row, $index)">下移</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-form-item label="报名截止日期">
          <div class="flex">
            <el-select v-model="customTimeValue" @change="handleTimeChange">
              <el-option
                v-for="item in customList"
                :key="item.k"
                :value="item.k"
                :label="item.v"
              ></el-option>
            </el-select>

            &nbsp;&nbsp;

            <DatePicker
              v-model="formData.signEndDate"
              v-if="showTimeCustom"
              :placeholder="'请选择日期'"
            />
            <el-input
              v-model="formData.signCustomEndDate"
              v-else
              placeholder="请输入报名截止日期"
              clearable
            />
          </div>
        </el-form-item>

        <el-form-item label="活动详情链接" prop="validateUrl">
          <el-input
            @blur="matchCompanyMethod"
            style="width: 500px"
            v-model="formData.detailUrl"
            placeholder="请输入活动详情链接"
            clearable
          />
          <el-checkbox
            class="ml-10"
            v-model="formData.isOutsideUrl"
            :true-label="'1'"
            :false-label="''"
            >站外链接</el-checkbox
          >
        </el-form-item>

        <el-form-item label="活动报名链接" prop="validateUrl">
          <el-input
            style="width: 500px"
            v-model="formData.signUpUrl"
            placeholder="请输入活动报名链接"
            clearable
          />
        </el-form-item>

        <div class="flex">
          <el-form-item label="活动主图">
            <CutOutUploadImg
              v-model:file-id="formData.mainImgFileId"
              :uploadText="'488 px* 258 px；5M以内；支持png、jpg、jpge格式'"
              width="100px"
              height="100px"
              v-model:full-url="mainImgUrl"
            />
          </el-form-item>

          <el-form-item label="活动logo">
            <uploadSingleImg
              style="height: 200px; width: 200px"
              :uploadText="'50 px* 50 px；5M以内；支持png、jpg、jpge格式'"
              :max-size="5"
              v-model:file-id="formData.logoFileId"
              v-model:full-url="logoImgUrl"
            />
          </el-form-item>
        </div>

        <el-form-item label="精彩回顾">
          <div class="img-box">
            <div>
              最多5张，建议尺寸 488 px* 258
              px，大小5M以内；支持png、jpg、jpge格式；图片排序默认为0，可输入0～999，数字越大排越前
            </div>
            <div class="flex">
              <div class="splendid" v-for="(item, index) in splendidImgList" :key="index">
                <CutOutUploadImg
                  v-model:file-id="item.fileId"
                  :uploadText="''"
                  width="100px"
                  height="50px"
                  v-model:full-url="item.imgUrl"
                />
                <el-input style="width: 100px" v-model="item.sort" :disabled="!item.fileId" />
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="更多图片">
          <div class="flex">
            <div>
              <uploadSingleImg
                style="height: 150px; width: 150px"
                :uploadText="'仅支持png、jpg、jpge格式；'"
                :max-size="5"
                class="mr-20"
                v-model:file-id="formData.otherImgOneFileId"
                v-model:full-url="otherImgOneUrl"
              />
              <div>其他图片1</div>
            </div>

            <div>
              <uploadSingleImg
                style="height: 150px; width: 150px"
                :uploadText="'仅支持png、jpg、jpge格式；'"
                :max-size="5"
                class="mr-20"
                v-model:file-id="formData.otherImgTwoFileId"
                v-model:full-url="otherImgTwoUrl"
              />

              <div>其他图片2</div>
            </div>

            <div>
              <uploadSingleImg
                style="height: 150px; width: 150px"
                :uploadText="'仅支持png、jpg、jpge格式；'"
                :max-size="5"
                v-model:file-id="formData.otherImgThreeFileId"
                v-model:full-url="otherImgThreeUrl"
              />

              <div>其他图片3</div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="其他说明1">
          <el-input
            style="width: 500px"
            v-model="formData.otherDescriptionOne"
            placeholder='如配置"首页-推荐活动"推广位，需填写活动简称'
          />
        </el-form-item>

        <el-form-item label="其他说明2">
          <el-input style="width: 500px" v-model="formData.otherDescriptionTwo" />
        </el-form-item>

        <el-form-item label="其他说明3">
          <el-input style="width: 500px" v-model="formData.otherDescriptionThree" />
        </el-form-item>
      </template>

      <div class="common-title">活动设置</div>

      <el-form-item label="是否上架活动" prop="groundingStatus">
        <el-radio-group
          :disabled="!abroadActivitySys.includes('sortAndGrounding')"
          v-model="formData.groundingStatus"
        >
          <el-radio v-for="item in activityGroundingList" :key="item.k" :label="item.k">{{
            item.v
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="活动排序">
            <el-input
              :disabled="!abroadActivitySys.includes('sortAndGrounding')"
              v-model="formData.sort"
              :step="1"
              :min="0"
              :maxlength="3"
              :max="999"
            />
            &nbsp;&nbsp;
            <span>0～999，数字越大排越前</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!isZhaoPinHui">
        <el-col :span="8">
          <el-form-item label="关联单位">
            <SelectCompany v-model="formData.companyId" :name="companyName" />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="common-title">推广设置</div>

      <el-table border class="table" :data="typeShowSetOption">
        <el-table-column align="center" label="推广位">
          <template #default="{ row }">
            <el-checkbox
              @change="handleChangeCheckbox(row)"
              v-model="row.checked"
              :label="row.label"
              :true-label="row.positionType"
              :false-label="''"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="推广排序">
          <template #default="{ row }">
            <el-input
              v-model="row.sort"
              :rows="1"
              type="number"
              :step="1"
              :min="0"
              :max="999"
              :disabled="!row.checked"
            />
          </template>
        </el-table-column>

        <el-table-column align="center" label="推广时间">
          <template #default="{ row }">
            <DatePickerRange
              :disabled="!row.checked"
              v-model:start="row.startDate"
              v-model:end="row.endDate"
              :disabled-date="handleDisabledDate"
            />
          </template>
        </el-table-column>

        <el-table-column align="center" label="显示图片">
          <template #default="{ row }">
            <el-select v-model="row.imgType" :disabled="!row.checked">
              <el-option
                v-for="item in imgCheckList"
                :key="item.k"
                :value="item.k"
                :label="item.v"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <block v-show="isZhaoPinHui">
        <div class="common-title">
          热门单位
          <span class="pl-10">
            <el-button type="primary" @click="handleCompanyAdd">+添加单位</el-button>
          </span>
        </div>

        <el-table border class="table" :data="companyHotTableData">
          <el-table-column align="center" label="单位ID" prop="companyId" />
          <el-table-column align="center" label="单位名称" prop="companyName" />
          <el-table-column align="center" label="排序号">
            <template #default="{ row }">
              <el-input v-model="row.sort" :rows="1" type="number" :step="1" :min="0" :max="999" />
            </template>
          </el-table-column>

          <el-table-column align="center" label="落地链接">
            <template #default="{ row }">
              <el-select v-model="row.linkType">
                <el-option
                  v-for="item in options.companyHotLinkTypeList"
                  :key="item.k"
                  :value="item.k"
                  :label="item.v"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column align="center" label="操作">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDeleteCompany(row.companyId)" :underline="false"
                >移除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </block>
    </el-form>

    <el-form-item>
      <el-row class="w100" justify="end">
        <el-affix position="bottom" :offset="20">
          <el-button>取消</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </el-affix>
      </el-row>
    </el-form-item>

    <companyHotDialog @confirm="companyHotConfirm" ref="companyHotRef" />
    <SessionDialog @update="handleUpdateSessionList" ref="sessionRef" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'abroadActiveAdd'
}
</script>

<script lang="ts" setup>
// 导入所需的组件和API
import { useStore } from '/@/store'
import { computed, ref, reactive, watch, nextTick } from 'vue'
import { hwAreaList, getFirstLetter } from '/@/api/config'
import {
  getAbroadTypeList,
  getShowImgTypeList,
  saveActiveData,
  getAbroadActivityInfo,
  getAddParamsList,
  getAnnouncementDetailCompanyInfo,
  getFormList,
  getFormOptionsList
} from '/@/api/abroad'

// 组件引入
import DatePicker from '/@/components/base/datePicker.vue'
import uploadSingleImg from '/@/components/upload/uploadSingleImg.vue'
import CutOutUploadImg from '/@/components/upload/cutOutUploadImg.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import companyHotDialog from './components/companyHotDialog.vue'
import SessionDialog from './components/sessionDialog.vue'
import SelectCompany from '../advertising/components/selectCompany.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import WangEditor from '/@/components/wangEditor/index.vue'
import SingleImage from '/@/components/upload/singleImage.vue'
import { verifyUrl } from '/@/utils/toolsValidate'
import router from '/@/router'
import { Edit } from '@element-plus/icons-vue'

// 获取路由实例
const route = useRoute()

// 获取状态管理
const store = useStore()

// 获取权限相关信息
const requestOldRoutesAction = <any>(
  computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
)

const abroadActivitySys = requestOldRoutesAction.value.abroadActivity ?? []

// 获取活动ID,用于判断是新建还是编辑
const id = computed(() => route.params?.id)

// 页面标题
const title = id.value ? '编辑活动' : '创建活动'

// 地区选择器配置
const areaProps = { value: 'k', label: 'v', multiple: true, emitPath: false }

// 组件引用
const companyHotRef = ref()
const sessionRef = ref()

// 活动类型列表
const talentList = ref([])
const activityList = ref([])
const moreActive = ref([])
const boshihouActive = ref([])
const offlineActive = ref([])
const onlineActive = ref([])
const seriesList = <any>ref([])

// 活动场次列表
const sessionList = <any>ref([])

// 表单引用
const formRef = ref()

// 富文本编辑器引用
const activityDetailRef = ref()
const participationMethodRef = ref()
const activityHighlightsRef = ref()
const activityBenefitsContentRef = ref()
const wonderfulReviewRef = ref()

// 图片类型列表
const imgCheckList = ref([])

// 自定义时间值
const customTimeValue = ref('2')

// 是否为招聘会标识
const isZhaoPinHui = ref(false)

// 是否手动输入标识
const hasInput = ref(false)

// 加载状态
const loading = ref(false)

// 活动上架状态选项
const activityGroundingList = ref([
  { k: '1', v: '上架' },
  { k: '2', v: '下架' }
])

// 自定义列表选项
const customList = ref([
  { k: '2', v: '选择时间' },
  { k: '1', v: '自定义录入' }
])

// 图片URL存储
const otherImgOneUrl = ref('')
const otherImgTwoUrl = ref('')
const otherImgThreeUrl = ref('')
const imageNoticeUrl = ref('')
const logoImgUrl = ref('')
const mainImgUrl = ref('')

// 选项数据对象
const options: any = reactive({
  featureTagParams: [], // 特色标签参数
  subTypeParams: [], // 子类型参数
  specialLinkParams: [], // 特殊链接参数
  toHoldType: [], // 举办方式
  companyHotLinkTypeList: [], // 热门公司链接类型列表
  areaList: [], // 地区列表
  templateList: [], // 模板列表
  applyLinkPersonTypeList: [] // 人员链接类型列表
})

// 热门公司选项接口定义
interface CompanyHotOption {
  companyId: number
  companyName: string
  sort: string
  linkType: string
}

// 推广选项接口定义
interface PromotionOption {
  checked: boolean
  positionType: string
  label: string
  startDate: string
  endDate: string
  sort: number
  imgType: string
}

// 推广设置选项
const typeShowSetOption = ref<PromotionOption[]>([])

// 热门公司表格数据
const companyHotTableData = ref<CompanyHotOption[]>([])

// 图片类型列表
const showImgTypeList = ref([])

// 公司名称
const companyName = ref('')

// 表单数据对象
const formData = ref({
  id: '',
  seriesType: 2,
  tags: [],
  type: '',
  subType: '',
  toHoldType: [],
  featureTagId: [],
  customFeatureTag: [],

  isCustomTime: '2',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  customTime: '',

  isCustomAddress: '2',
  customAddress: '',
  areaIds: [],
  detailAddress: '',
  latitude: '',
  longitude: '',
  activityNumber: '',

  activityOrganization: '',
  activityDetail: '',
  participationMethod: '',
  activityHighlightsTitle: '参会须知',
  activityHighlights: '',
  activityBenefits: '',
  activityBenefitsContent: '',
  attendanceNotes: '',
  retrospection: '',
  participationBenefit: '',
  participationBenefitDetail: '',

  imagePcBannerId: '1',
  imagePcBannerUrl: '',
  imageMiniBannerId: '',
  imageMiniBannerUrl: '',
  imageMiniMasterUrl: '',
  imageMiniMasterId: '',
  imageServiceCodeId: '',
  imageServiceCodeUrl: '',

  applyLinkPersonType: '',
  applyLinkPersonFormId: '',
  applyLinkPersonFormOptionId: '',
  applyLinkPerson: '',
  applyPersonTime: '',

  applyLinkCompany: '',
  applyCompanyTime: '',

  templateId: '2',

  activityDomain: '',
  activityLink: '',

  wonderfulReview: '',

  companyHotList: [],

  name: '',
  introduce: '',
  detailUrl: '',
  signUpUrl: '',
  otherImgOneFileId: '',
  otherImgTwoFileId: '',
  otherImgThreeFileId: '',
  imageNoticeId: '',
  otherDescriptionOne: '',
  otherDescriptionTwo: '',
  otherDescriptionThree: '',
  groundingStatus: '2',
  sort: '0',
  companyId: '',
  mainImgFileId: '',
  mainImgUrl: '',
  signEndDate: '',
  signCustomEndDate: '',
  isOutsideUrl: '',
  logoFileId: ''
})

const splendidImgList = ref([
  { fileId: '', sort: '0', imgUrl: '' },
  { fileId: '', sort: '0', imgUrl: '' },
  { fileId: '', sort: '0', imgUrl: '' },
  { fileId: '', sort: '0', imgUrl: '' },
  { fileId: '', sort: '0', imgUrl: '' }
])

// 用于监控是否全部合法
let isAllPromotionListMatch = true

const validateSession = (rule, value, callback) => {
  if (sessionList.value.length === 0) {
    callback('请添加活动场次')
  }

  callback()
}

const validateUrlMethod = (rule, value, callback) => {
  if (formData.value.detailUrl && !verifyUrl(formData.value.detailUrl)) {
    callback('网址不正确')
  }

  if (formData.value.signUpUrl && !verifyUrl(formData.value.signUpUrl)) {
    callback('网址不正确')
  }

  callback()
}

const formRules = ref({
  seriesType: [{ required: true, message: '请选择活动系列', trigger: 'change' }],
  type: [{ required: true, message: '请选择活动类型', trigger: 'blur' }],
  toHoldType: [{ required: true, message: '请选择举办方式', trigger: 'blur' }],
  isCustomTime: [{ required: true, message: '请选择', trigger: 'change' }],
  startDate: [
    { required: true, message: '开始日期必填', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        const { endDate } = formData.value
        if (!endDate) return callback()
        const isLessThan = new Date(endDate).getTime() < new Date(value).getTime()
        if (isLessThan) return callback(new Error('开始日期不能大于结束日期'))
        return callback()
      },
      trigger: 'change'
    }
  ],
  endDate: [
    { required: true, message: '结束日期必填', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        const { startDate } = formData.value
        if (!startDate) return callback()
        const isLessThan = new Date(value).getTime() < new Date(startDate).getTime()
        if (isLessThan) return callback(new Error('结束日期不能小于开始日期'))
        return callback()
      },
      trigger: 'change'
    }
  ],

  isCustomAddress: [{ required: true, message: '请选择', trigger: 'change' }],
  sessionAreaList: [{ required: true, message: '请选择国家城市(必填)', trigger: 'change' }],
  customAddress: [{ required: true, message: '请输入活动地点', trigger: 'blur' }],

  activityDetail: [{ required: false, message: '请输入', trigger: 'blur' }],

  mainImgUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  imagePcBannerUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  imageMiniBannerUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],

  applyLinkPersonFormId: [
    {
      required: computed(() => !!formData.value.applyLinkPersonType),
      message: '请选择',
      trigger: 'change'
    }
  ],
  applyLinkPersonFormOptionId: [
    {
      required: computed(() => !!formData.value.applyLinkPersonType),
      message: '请选择',
      trigger: 'change'
    }
  ],
  applyLinkPerson: [
    {
      required: computed(() => !!formData.value.applyLinkPersonType),
      message: '请输入',
      trigger: 'blur'
    }
  ],

  activityLink: [{ required: true, message: '请输入页面链接', trigger: 'blur' }],

  name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  groundingStatus: [{ required: true, message: '请选择上架活动' }],
  sessionList: [{ required: true, validator: validateSession }],
  validateUrl: [{ required: false, validator: validateUrlMethod }]
})

const showTimeCustom = computed(() => customTimeValue.value === '2')

const renderSeriesList = computed(() => {
  let value = []
  if (formData.value.seriesType === 1) {
    value = activityList.value
  }

  if (formData.value.seriesType === 2) {
    value = talentList.value
  }

  if (formData.value.seriesType === 3) {
    value = moreActive.value
  }

  if (formData.value.seriesType === 4) {
    value = boshihouActive.value
  }

  if (formData.value.seriesType === 5) {
    value = offlineActive.value
  }

  if (formData.value.seriesType === 6) {
    value = onlineActive.value
  }

  return value
})

const handleNameBlur = async () => {
  if (formData.value.name && !hasInput.value && !id.value) {
    const res = await getFirstLetter(formData.value.name)
    if (res.text) {
      formData.value.activityLink = res.text
    }
  }
}

const personApplyChange = () => {
  formData.value.applyLinkPersonFormId = ''
  formData.value.applyLinkPersonFormOptionId = ''
  formData.value.applyLinkPerson = ''
}

const handleSeriesChange = (init = true) => {
  if (init) {
    formData.value.type = ''
  }
  if (formData.value.seriesType === 5 || formData.value.seriesType === 6) {
    // 招聘会
    isZhaoPinHui.value = true
    options.specialLinkParams.forEach((element) => {
      element.k = element.k - 0
      if (element.k === formData.value.seriesType) {
        formData.value.activityDomain = element.v
      }
    })
  } else {
    // 非招聘会
    isZhaoPinHui.value = false
  }
}

const handleSwitchData = (data: any) => {
  let arr1 = [
    {
      checked: false,
      positionType: '1',
      label: '首页-推荐活动',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '2',
      label: '首页-出海引才',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '3',
      label: '首页-归国活动-上',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '4',
      label: '首页-归国活动-下',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '7',
      label: '小程序活动汇总页-热门场次',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '8',
      label: 'PC活动汇总页-热门场次',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    }
  ]
  let arr2 = [
    {
      checked: false,
      positionType: '1',
      label: '首页-推荐活动',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '2',
      label: '首页-出海引才',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '3',
      label: '首页-归国活动-上',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '4',
      label: '首页-归国活动-下',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '5',
      label: '博士后首页-博后活动',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '6',
      label: '博士后博后活动页-推荐活动',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '7',
      label: '小程序活动汇总页-热门场次',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    },
    {
      checked: false,
      positionType: '8',
      label: 'PC活动汇总页-热门场次',
      startDate: '',
      endDate: '',
      sort: 0,
      imgType: '1'
    }
  ]
  if (data) {
    typeShowSetOption.value = [...arr1]
    handlePromotionFilter(promotionChecked1.value)
    const fieldsToReset = [
      'activityDetail',
      'activityBenefitsContent',
      'participationMethod',
      'activityHighlights',
      'wonderfulReview'
    ]
    const originalValues = fieldsToReset.reduce((acc, field) => {
      acc[field] = JSON.parse(JSON.stringify(formData.value[field]))
      formData.value[field] = ''
      return acc
    }, {})

    setTimeout(() => {
      fieldsToReset.forEach((field) => {
        formData.value[field] = originalValues[field]
      })
    }, 100)
  } else {
    typeShowSetOption.value = [...arr2]
    handlePromotionFilter(promotionChecked2.value)
  }
  imgCheckList.value = showImgTypeList.value[formData.value.seriesType]
}

const promotionChecked1 = ref([])
const promotionChecked2 = ref([])

const handleChangeCheckbox = (item: PromotionOption) => {
  const targetArray: PromotionOption[] = isZhaoPinHui.value
    ? promotionChecked1.value
    : promotionChecked2.value
  if (item.checked) {
    targetArray.push(item)
  } else {
    const index = targetArray.findIndex((i) => i.positionType === item.positionType)
    if (index !== -1) targetArray.splice(index, 1)
  }
}

const newGetParams = async () => {
  const res = await getAddParamsList({})
  if (res) {
    options.featureTagParams = res.featureTagParams
    options.subTypeParams = res.subTypeParams
    options.specialLinkParams = res.specialLinkParams
    options.toHoldType = res.toHoldType
    options.companyHotLinkTypeList = res.companyHotLinkTypeList
    options.templateList = res.templateList
    options.applyLinkPersonTypeList = res.applyLinkPersonTypeList
    // typeShowSetOption.value = res.promotionPositionList
  }
}

const getParams = async () => {
  const resp = await getAbroadTypeList()
  const hw = await hwAreaList()
  const res = await getShowImgTypeList()
  showImgTypeList.value = res
  imgCheckList.value = res[formData.value.seriesType]
  options.areaList = hw

  resp.forEach((item) => {
    if (item.k === 2) {
      talentList.value = item.children
    }

    if (item.k === 1) {
      activityList.value = item.children
    }

    if (item.k === 3) {
      moreActive.value = item.children
    }

    if (item.k === 4) {
      boshihouActive.value = item.children
    }

    if (item.k === 5) {
      offlineActive.value = item.children
    }

    if (item.k === 6) {
      onlineActive.value = item.children
    }

    seriesList.value.push({ k: item.k, v: item.v, disabled: false })
  })
}

const companyHotConfirm = (data: any) => {
  const existingCompanyIds = new Set(companyHotTableData.value.map((item) => item.companyId))
  const newCompanies = data
    .filter((item: any) => !existingCompanyIds.has(item.value))
    .map((item: any) => {
      return {
        companyId: item.value,
        companyName: item.label,
        sort: 0,
        linkType: '1'
      }
    })
  companyHotTableData.value.push(...newCompanies)
}

const replaceChinese = (value) => {
  hasInput.value = true
  // 匹配非数字、非字母且非-_/的字符
  const nonAlphanumericRegex = /[^A-Za-z0-9-_\/]/g
  formData.value.activityLink = value.replace(nonAlphanumericRegex, '')
}

const getFormOptions = (query) => {
  getFormList(query).then((resp: any) => {
    options.applyFormList = resp.list
  })
}

const getFormOptionOptions = (id) => {
  getFormOptionsList({ activityFormId: id }).then((resp: any) => {
    options.applyFormOptionsList = resp.list
  })
}

const searchFormList = (kw) => {
  getFormOptions({ keyword: kw })
}

const handleFormListChange = (id) => {
  formData.value.applyLinkPersonFormOptionId = ''

  const { applyLinkPersonType } = formData.value
  if (applyLinkPersonType !== '3') return
  getFormOptionOptions(id)
}

const handleSession = (data?: any) => {
  sessionRef.value.handleOpenDialog(data)
}

const handleUpdateSessionList = (data: any) => {
  const index = sessionList.value.findIndex((item: any) => item.id === data.id)

  if (index === -1) {
    sessionList.value.push(data)
  } else {
    sessionList.value.splice(index, 1, data)
  }
}

const handleDeleteSession = (sessionId: string) => {
  ElMessageBox.confirm('确定要删除吗？', '提示')
    .then(() => {
      sessionList.value = sessionList.value.filter((item: any) => item.id !== sessionId)
    })
    .catch(() => {})
}

const handleDeleteCompany = (companyId: string) => {
  companyHotTableData.value = companyHotTableData.value.filter(
    (item: any) => item.companyId !== companyId
  )
  // ElMessageBox.confirm('确定要删除吗？', '提示')
  //   .then(() => {
  //     companyHotTableData.value = companyHotTableData.value.filter(
  //       (item: any) => item.companyId !== companyId
  //     )
  //   })
  //   .catch(() => {})
}

const handlePostData = () => {
  let reviewImgList = splendidImgList.value?.filter((item) => item.fileId !== '') as any
  let promotionList = typeShowSetOption.value.filter((item) => item.checked) as any

  reviewImgList = reviewImgList.map((item) => {
    const { imgUrl, ...other } = item
    return other
  })

  promotionList = promotionList.map((item) => {
    if (!isAllPromotionListMatch) {
      return
    }
    const { checked, sort, startDate, endDate, positionType, imgType } = item
    if (checked) {
      if (sort === '') {
        isAllPromotionListMatch = false
        ElMessage.error('推广设置的推广排序不能为空')
      }
      // 中断
      if (startDate === '') {
        isAllPromotionListMatch = false
        ElMessage.error('推广设置的推广开始时间不能为空')
      }
      if (endDate === '') {
        isAllPromotionListMatch = false
        ElMessage.error('推广设置的推广结束时间不能为空')
      }
    }
    return {
      sort,
      startDate,
      endDate,
      imgType,
      positionType
    }
  })

  const sessionIds = sessionList.value?.map((item) => item.id)?.join(',')
  const customFeatureTag = formData.value.customFeatureTag?.join(',')

  const postData = {
    ...formData.value,
    reviewImgList,
    promotionList,
    sessionIds,
    customFeatureTag,
    companyHotList: companyHotTableData.value
  }

  return postData
}

const handleCompanyAdd = () => {
  companyHotRef.value.open()
}

const handleClearForm = () => {
  formRef.value.resetFields()

  activityDetailRef.value?.clearEditor()
  participationMethodRef.value?.clearEditor()
  activityHighlightsRef.value?.clearEditor()
  activityBenefitsContentRef.value?.clearEditor()
  wonderfulReviewRef.value?.clearEditor()

  Object.keys(formData.value).forEach((key) => {
    formData.value[key] = ''
  })

  formData.value.seriesType = 2
  formData.value.tags = []
  formData.value.groundingStatus = '1'
  formData.value.sort = '0'
  otherImgOneUrl.value = ''
  otherImgTwoUrl.value = ''
  otherImgThreeUrl.value = ''
  logoImgUrl.value = ''
  mainImgUrl.value = ''
  sessionList.value = []
  companyHotTableData.value = []

  splendidImgList.value.forEach((item: any) => {
    item.fileId = ''
    item.sort = '0'
    item.imgUrl = ''
  })

  typeShowSetOption.value.forEach((item: any) => {
    item.checked = false
    item.sort = 0
    item.startDate = ''
    item.endDate = ''
    item.imgType = '1'
  })
}

const handleSubmit = () => {
  const postData = handlePostData()

  if (!isAllPromotionListMatch) {
    // 重置
    isAllPromotionListMatch = true
    return false
  }

  formRef.value.validate(async (val: boolean) => {
    if (val) {
      const res = await saveActiveData(postData)

      if (!formData.value.id) {
        // 重新加载路由
        handleClearForm()
        router.back()
      }

      // 提示保存成功
      ElMessage.success('操作成功！')
      // 重新加载数据
      // await init()
    }
  })
}

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

const handlePromotionFilter = (res) => {
  const positionTypes = typeShowSetOption.value.map((item) => item.positionType)

  res.forEach((item) => {
    const { positionType, imgType, sort, startDate, endDate } = item
    const index = positionTypes.indexOf(positionType)
    if (index >= 0) {
      typeShowSetOption.value[index].checked = positionType
      typeShowSetOption.value[index].sort = sort
      typeShowSetOption.value[index].imgType = imgType
      typeShowSetOption.value[index].startDate = startDate
      typeShowSetOption.value[index].endDate = endDate
    }
  })
}

const getEditData = async () => {
  loading.value = true
  const res = await getAbroadActivityInfo({ id: id.value })

  getFormOptions({ formId: res.applyLinkPersonFormId })
  getFormOptionOptions(res.applyLinkPersonFormId)
  loading.value = false

  Object.keys(res).forEach((key) => {
    formData.value[key] = res[key]
  })

  handleSeriesChange(false)

  // 编辑情况下禁止切换活动系列
  seriesList.value.forEach((element) => {
    if (id.value) {
      if (!isZhaoPinHui.value) {
        if (element.k === 5 || element.k === 6) {
          element.disabled = true
        }
      } else {
        if (element.k !== 5 && element.k !== 6) {
          element.disabled = true
        }
      }
    }
  })

  formData.value.customFeatureTag = !!res.customFeatureTag ? res.customFeatureTag.split(',') : []

  companyHotTableData.value = res.companyHotList

  companyName.value = res.companyName

  sessionList.value = res.sessionList

  splendidImgList.value.splice(0, res.reviewImgFileIds.length, ...res.reviewImgFileIds)

  otherImgOneUrl.value = res.otherImgOneUrl
  otherImgTwoUrl.value = res.otherImgTwoUrl
  otherImgThreeUrl.value = res.otherImgThreeUrl
  imageNoticeUrl.value = res.imageNoticeUrl
  mainImgUrl.value = res.mainImgUrl
  logoImgUrl.value = res.logoImgUrl
  customTimeValue.value = res.signCustomEndDate === '' ? '2' : '1'
  await sleep(1000)
  handlePromotionFilter(res.promotionList)
}

const handleDisabledDate = (time: any) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const handleUpList = (data: any, index: number) => {
  if (index === 0) return
  const list = sessionList.value[index - 1]

  sessionList.value.splice(index - 1, 1, data)
  sessionList.value.splice(index, 1, list)
}

const handleDownList = (data: any, index: number) => {
  if (index === sessionList.value.length - 1) return
  const list = sessionList.value[index + 1]

  sessionList.value.splice(index + 1, 1, data)
  sessionList.value.splice(index, 1, list)
}

const init = async () => {
  await getParams()
  await newGetParams()
  if (id.value) {
    await getEditData()
  }
}

init()

const handleTimeChange = () => {
  formData.value.signEndDate = ''
  formData.value.signCustomEndDate = ''
}

const matchCompanyMethod = async () => {
  if (formData.value.detailUrl) {
    const res = await getAnnouncementDetailCompanyInfo({ detailUrl: formData.value.detailUrl })
    if (res.id) {
      formData.value.companyId = res.id + ''
      companyName.value = res.fullName
    } else {
      formData.value.companyId = ''
      companyName.value = ''
    }
  }
}

watch(
  () => isZhaoPinHui.value,
  (val: any) => {
    handleSwitchData(val)
  },
  { immediate: true }
)

const isEditHighlightsTitle = ref(false)
const activityHighlightsInputRef = ref()
const handleEditHighlightsTitle = () => {
  isEditHighlightsTitle.value = true
  nextTick(() => {
    activityHighlightsInputRef.value &&
      activityHighlightsInputRef.value.focus &&
      activityHighlightsInputRef.value.focus()
  })
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 20px;
  background-color: var(--color-whites);
}

.common-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
}

.table {
  width: 800px;
}

.upload-img-content {
  text-align: center;

  .tips {
    span {
      color: var(--el-color-danger);
    }
  }
}

.img-box {
  :deep(.banner-img) {
    width: 100px;
    height: 100px;
  }

  .splendid {
    margin-right: 20px;
  }
}

.bottom-btn {
  display: flex;
  justify-content: flex-end;
}
</style>
