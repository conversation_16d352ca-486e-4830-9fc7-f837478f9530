<template>
  <el-dialog
    top="5vh"
    v-model="visible"
    :title="title"
    width="912px"
    @close="handleCancel"
    :close-on-click-modal="false"
  >
    <div v-loading="loading">
      <el-form
        ref="form"
        :model="formData"
        :rules="formRules"
        class="mt-15 pr-50"
        label-width="90px"
      >
        <el-form-item prop="packingId" label="关联维护" v-if="formData.packingId">
          -----------广告位关联维护-----------</el-form-item
        >
        <el-form-item prop="type" label="广告位类型">
          <el-checkbox-group v-model="formData.type">
            <el-checkbox label="1" name="type">付费</el-checkbox>
            <el-checkbox label="2" name="type">RPO</el-checkbox>
            <el-checkbox label="3" name="type">异议</el-checkbox>
            <el-checkbox label="4" name="type">客情</el-checkbox>
            <el-checkbox label="5" name="type">推广</el-checkbox>
            <el-checkbox label="6" name="type">高级</el-checkbox>
            <el-checkbox label="7" name="type">旧链</el-checkbox>
            <el-checkbox label="8" name="type">免费</el-checkbox>
            <el-checkbox label="9" name="type">其他</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="homePositionId" label="广告位置">
          <AdAbroadPosition
            v-model="formData.homePositionId"
            :multiple="true"
            :showAllLevels="false"
          />
        </el-form-item>

        <el-form-item prop="title" label="广告标题">
          <el-input v-model="formData.title" placeholder=""></el-input>
        </el-form-item>

        <el-form-item prop="subTitle" label="副标题">
          <el-input v-model="formData.subTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item prop="secondTitle" label="次标题">
          <el-input v-model="formData.secondTitle" placeholder=""></el-input>
        </el-form-item>
        <el-form-item class="ai-center" prop="imageUrl" label="广告图片">
          <el-input
            @blur="handleImageInputBlur('adImgPreview', formData.imageUrl)"
            class="flex-1"
            v-model="formData.imageUrl"
            clearable
            placeholder=""
          ></el-input>
          <el-image
            v-if="adImgPreview"
            class="preview"
            style="width: 100px; height: 100px"
            :src="formData.imageUrl"
            :preview-src-list="[formData.imageUrl]"
            :initial-index="4"
            fit="contain"
          />
        </el-form-item>
        <el-form-item prop="imageUrl" label="上传">
          <el-upload
            class="avatar-uploader"
            action="/upload/image"
            :show-file-list="false"
            :on-success="
              (res) => {
                handleImageSuccess('imageUrl', res)
              }
            "
            :before-upload="beforeImageUpload"
          >
            <el-button type="primary">上传</el-button>
            &nbsp;&nbsp;
            <p class="logo">支持JPG/PNG图片格式，文件小于5M</p>
          </el-upload>
        </el-form-item>
        <el-form-item prop="imageAlt" label="图片说明文字">
          <el-input v-model="formData.imageAlt" placeholder="请填写图片文字说明"></el-input>
        </el-form-item>
        <el-form-item prop="onlineTime" label="生效时间">
          <DatePicker v-model="formData.onlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>
        <el-form-item prop="offlineTime" label="失效时间">
          <DatePicker v-model="formData.offlineTime" :disabledDate="handleDisabledDate" />
        </el-form-item>
        <el-form-item class="ai-center" prop="otherImageUrl" label="其他图片">
          <el-input
            @blur="handleImageInputBlur('adOtherImgPreview', formData.otherImageUrl)"
            class="flex-1"
            v-model="formData.otherImageUrl"
            clearable
            placeholder=""
          ></el-input>
          <el-image
            v-if="adOtherImgPreview"
            class="preview"
            style="width: 100px; height: 100px"
            :src="formData.otherImageUrl"
            :preview-src-list="[formData.otherImageUrl]"
            :initial-index="4"
            fit="contain"
          />
        </el-form-item>
        <el-form-item prop="imageUrl" label="上传">
          <el-upload
            class="avatar-uploader"
            action="/upload/image"
            :show-file-list="false"
            :on-success="
              (res) => {
                handleImageSuccess('otherImageUrl', res)
              }
            "
            :before-upload="beforeImageUpload"
          >
            <el-button type="primary">上传</el-button>
            &nbsp;&nbsp;
            <p class="logo">支持JPG/PNG图片格式，文件小于5M</p>
          </el-upload>
        </el-form-item>
        <el-form-item prop="targetLink" label="跳转地址">
          <el-input v-model="formData.targetLink" placeholder="请填写广告落地页地址"></el-input>
        </el-form-item>
        <el-form-item prop="sort" label="广告排序">
          <el-input v-model="formData.sort" placeholder="数字越大越靠前，不填默认”0“"></el-input>
        </el-form-item>
        <el-form-item prop="describe" label="广告描述">
          <el-input
            v-model="formData.describe"
            type="textarea"
            rows="3"
            resize="none"
            placeholder=""
          ></el-input>
        </el-form-item>

        <el-form-item prop="companyId" label="关联单位">
          <SelectCompany v-model="formData.companyId" :name="companyName" />
        </el-form-item>
        <el-form-item>
          <el-button :loading="submitLoading" @click="submit" type="primary">保存</el-button>
          <el-button plain @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'

import { ElMessage } from 'element-plus'

import DatePicker from '/@/components/base/datePicker.vue'
import AdAbroadPosition from '/@select/adAbroadPosition.vue'
import SelectCompany from './selectCompany.vue'

import { addShowcase, getShowcaseDetails, editShowcase } from '/@/api/advertising'

const form = ref()
const visible = ref(false)
const loading = ref(false)
const title = ref('新增广告')
const adImgPreview = ref(false)
const adOtherImgPreview = ref(false)
const submitLoading = ref(false)
const formData = reactive({
  id: '',
  homePositionId: '',
  title: '',
  subTitle: '',
  secondTitle: '',
  imageUrl: '',
  imageLink: '',
  imageAlt: '',
  onlineTime: '',
  offlineTime: '',
  targetLink: '',
  contactHomePositionId: '',
  contactHomePositionSort: '',
  sort: '',
  packingId: '',
  describe: '',
  type: [],
  companyId: '',
  otherImageUrl: ''
})
const companyName = ref('')

const formRules = reactive({
  homePositionId: [{ required: true, message: '请选择广告位置', trigger: 'change' }],
  title: [{ required: true, message: '请输入广告标题', trigger: 'blur' }],
  onlineTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
  offlineTime: [{ required: true, message: '请选择失效时间', trigger: 'change' }]
})

const emit = defineEmits(['update'])

watch(
  () => formData.imageUrl,
  (value) => {
    formData.imageLink = value
  }
)

const getDetails = async () => {
  loading.value = true
  await getShowcaseDetails({ id: formData.id }).then((resp: any) => {
    const keys = Object.keys(formData)
    keys.forEach((key) => {
      formData[key] = resp[key]
    })

    const { imageUrl } = resp
    adImgPreview.value = !!imageUrl

    companyName.value = resp.companyName

    loading.value = false
  })
}

const open = async (id: string, string = '') => {
  visible.value = true
  title.value = id ? '编辑广告' : '新增广告'
  if (id) {
    formData.id = id

    await getDetails()
    if (string === 'copy') {
      title.value = '复制广告'
      formData.id = ''
      formData.homePositionId = ''
      formData.describe = ''
    }
  } else {
    companyName.value = ''
  }
}

const handleDisabledDate = (time: any) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const handleImageInputBlur = (key: any, value: string) => {
  switch (key) {
    case 'adImgPreview':
      adImgPreview.value = !!value
      break

    case 'adOtherImgPreview':
      adOtherImgPreview.value = !!value
      break

    default:
      break
  }
}

const beforeImageUpload = (file: any) => {
  const isImg = file.type === 'image/jpeg' || file.type === 'image/png'
  const limit = file.size / 1024 / 1024 < 5

  if (!isImg) {
    ElMessage.error('上传图片只能是 JPG或PNG 格式!')
  }
  if (!limit) {
    ElMessage.error('图片上传不能超过5M')
  }
  return isImg && limit
}

const handleImageSuccess = (key: string, res: any) => {
  formData[key] = res.data.fullUrl
  switch (key) {
    case 'imageUrl':
      adImgPreview.value = true
      break

    case 'otherImageUrl':
      adOtherImgPreview.value = true
      break
    default:
      break
  }
}

const resetForm = () => {
  formData.id = ''
  form.value.resetFields()
}

const handleCancel = () => {
  resetForm()
  visible.value = false
}

const submit = () => {
  form.value.validate(async (valid: any) => {
    if (valid) {
      submitLoading.value = true
      const { id, type, ...data } = formData
      const typeString = type ? type.join(',') : ''
      const postData = id ? { ...data, id, typeString } : { ...data, typeString }
      const fetchApi = id ? editShowcase : addShowcase

      fetchApi(postData)
        .then(() => {
          submitLoading.value = false
          emit('update', !!id)
          resetForm()
          visible.value = false
        })
        .catch(() => {
          submitLoading.value = false
        })
    }
  })
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.preview {
  width: 100px;
  height: 100px;
  margin-left: 15px;
  border-radius: 4px;
  border: var(--el-input-border, var(--el-border-base));
}
</style>
