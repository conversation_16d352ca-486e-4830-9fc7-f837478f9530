<template>
  <div class="order-container" v-loading="loading">
    <el-tabs v-model="currentTab" @tab-change="handleTabChange">
      <el-tab-pane label="求职者" name="person">
        <div class="filter-container">
          <el-row class="filter-row" :gutter="20">
            <el-col :span="6">
              <el-form-item>
                <el-input
                  v-model="queryNumberValue"
                  clearable
                  :placeholder="queryNumberPlaceholder"
                >
                  <template #prepend>
                    <el-select v-model="queryNumber" style="width: 100px">
                      <el-option
                        v-for="item in queryNumberOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <el-input v-model="queryNameValue" clearable :placeholder="queryNamePlaceholder">
                  <template #prepend>
                    <el-select v-model="queryNameOrId" style="width: 100px">
                      <el-option
                        v-for="item in queryNameOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <el-input v-model="queryUserValue" clearable :placeholder="queryUserPlaceholder">
                  >
                  <template #prepend>
                    <el-select v-model="queryUser" style="width: 100px">
                      <el-option
                        v-for="item in queryUserOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button @click="handleDownload">下载</el-button>
            </el-col>
          </el-row>

          <el-row class="filter-row" :gutter="20">
            <el-col :span="6">
              <el-form-item label="产品类型">
                <el-select
                  v-model="formData.equityPackageCategoryId"
                  clearable
                  placeholder="请选择产品类型"
                >
                  <el-option
                    v-for="item in queryTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="支付状态">
                <el-select v-model="formData.status" clearable placeholder="全部">
                  <el-option
                    v-for="item in queryPayStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="支付方式">
                <el-select v-model="formData.payway" clearable placeholder="全部">
                  <el-option
                    v-for="item in queryPayWayOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row class="filter-row" :gutter="20">
            <el-col :span="6">
              <el-form-item label="下单渠道">
                <el-select v-model="formData.platform" clearable placeholder="全部">
                  <el-option
                    v-for="item in querySourceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="下单时间">
                <el-date-picker
                  v-model="formData.addTime"
                  type="daterange"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="支付时间">
                <el-date-picker
                  v-model="formData.payTime"
                  type="daterange"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="order-stats">
          <span>总订单数：{{ statsData.count }}</span>
          <span>已支付订单数：{{ statsData.paidCount }}</span>
          <span>已支付订单金额：¥{{ statsData.paidAmount }}</span>
        </div>

        <div class="order-result">
          <el-table :data="resultList" border height="800">
            <el-table-column prop="orderNo" label="订单号" width="180" align="center" />
            <el-table-column prop="equityPackageId" label="产品ID" width="80" align="center" />
            <el-table-column prop="equityPackageName" label="产品名称" width="150" align="center" />
            <el-table-column
              prop="equityPackageCategoryName"
              label="产品类型"
              width="120"
              align="center"
            />
            <el-table-column prop="resumeId" label="用户ID" width="100" align="center" />
            <el-table-column prop="resumeName" label="在线简历姓名" width="120" align="center" />
            <el-table-column prop="realAmount" label="订单金额" width="100" align="center" />
            <el-table-column prop="discountsAmount" label="优惠金额" width="100" align="center" />
            <el-table-column prop="statusName" label="支付状态" width="100" align="center" />
            <el-table-column prop="paywayName" label="支付方式" width="100" align="center" />
            <el-table-column prop="addTime" label="下单时间" width="180" align="center" />
            <el-table-column prop="payTime" label="支付时间" width="180" align="center" />
            <el-table-column prop="tradeNo" label="流水号" width="180" align="center" />
            <el-table-column prop="platformName" label="下单渠道" width="100" align="center" />

            <el-table-column prop="address" label="操作" fixed="right" width="100" align="center">
              <template #default="{ row }">
                <el-button link type="primary" @click="() => handleViewDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <el-pagination
          v-model:current-page="formData.page"
          v-model:page-size="formData.pageSize"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-tab-pane>
      <el-tab-pane label="单位" name="company"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, unref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import {
  getPersonOrderList,
  getPersonOrderListFile,
  getPersonOrderListParams
} from '/@/api/configuration'
import { ElMessage } from 'element-plus'

const route = useRoute()

const {
  resumeId,
  equityPackageId: queryEquityPackageId,
  status: queryStatus,
  equityPackageCategoryId: queryEquityPackageCategoryId
} = route.query

const router = useRouter()

const currentTab = ref('person')

const handleTabChange = () => {}

const defaultFormData = {
  orderNo: '',
  tradeNo: '',
  equityPackageName: '',
  equityPackageId: '',
  equityPackageCategoryId: '',
  mobile: '',
  resumeName: '',
  resumeId: '',
  status: '',
  payway: '',
  platform: '',
  addTime: [],
  payTime: [],
  pageSize: 20,
  page: 1
}

const loading = ref(false)

const formData = reactive({ ...defaultFormData })

const total = ref(0)

const statsData = reactive({
  count: 0,
  paidCount: 0,
  paidAmount: 0
})

const resultList: any = ref([])

const findItemData = (target, value) => target.find((item) => item.value === value)

const queryNumber = ref('1')

const queryNumberOptions = [
  { label: '订单号', value: '1', key: 'orderNo', placeholder: '请输入订单编号' },
  { label: '流水号', value: '2', key: 'tradeNo', placeholder: '请输入交易流水号' }
]

const queryNumberData = computed(() => findItemData(queryNumberOptions, unref(queryNumber)))

const queryNumberValue = computed({
  get() {
    return formData[unref(queryNumberData).key]
  },

  set(value: string) {
    formData[unref(queryNumberData).key] = value
  }
})

const queryNumberPlaceholder = computed(() => unref(queryNumberData).placeholder)

const queryNameOrId = ref('1')

const queryNameOptions = [
  {
    label: '产品名称',
    value: '1',
    key: 'equityPackageName',
    placeholder: '产品检索-请输入产品名称'
  },
  {
    label: '产品ID',
    value: '2',
    key: 'equityPackageId',
    placeholder: '产品检索-请输入产品ID'
  }
]

const queryNameData = computed(() => findItemData(queryNameOptions, unref(queryNameOrId)))

const queryNameValue = computed({
  get() {
    return formData[unref(queryNameData).key]
  },

  set(value: string) {
    formData[unref(queryNameData).key] = value
  }
})

const queryNamePlaceholder = computed(() => unref(queryNameData).placeholder)

const queryUser = ref('1')

const queryUserOptions = [
  { label: '手机号', value: '1', key: 'mobile', placeholder: '用户检索-请输入手机号' },
  { label: '姓名', value: '2', key: 'resumeName', placeholder: '用户检索-请输入姓名' },
  { label: '用户ID', value: '3', key: 'resumeId', placeholder: '用户检索-请输入ID' }
]

const queryUserData = computed(() => findItemData(queryUserOptions, unref(queryUser)))

const queryUserValue = computed({
  get() {
    return formData[unref(queryUserData).key]
  },

  set(value: string) {
    formData[unref(queryUserData).key] = value
  }
})

const queryUserPlaceholder = computed(() => unref(queryUserData).placeholder)

const queryTypeOptions = ref([])

const queryPayStatusOptions = ref([])

const queryPayWayOptions = ref([])

const querySourceOptions = ref([])

const getQuery = () => {
  const { key: numberKey } = unref(queryNumberData)
  const { key: nameKey } = unref(queryNameData)
  const { key: userKey } = unref(queryUserData)

  // formData的addTime有可能是null
  formData.addTime = formData.addTime || []
  formData.payTime = formData.payTime || []

  const {
    status,
    payway,
    platform,
    equityPackageId,
    equityPackageCategoryId,
    addTime: [addTimeStart = '', addTimeEnd = ''],
    payTime: [payTimeStart = '', payTimeEnd = ''],
    page,
    pageSize
  } = formData

  return {
    [numberKey]: formData[numberKey],
    [nameKey]: formData[nameKey],
    [userKey]: formData[userKey],
    equityPackageId,
    equityPackageCategoryId,
    status,
    payway,
    platform,
    addTimeStart,
    addTimeEnd,
    payTimeStart,
    payTimeEnd,
    page,
    pageSize
  }
}

const downloadFile = (url: string) => {
  const link = document.createElement('a')

  link.setAttribute('download', '')
  link.setAttribute('href', url)
  link.click()
  link.remove()
}

const handleSearch = async () => {
  loading.value = true

  try {
    const { list, pages, total: data } = await getPersonOrderList(getQuery())
    const { page, limit, total: value } = pages

    formData.page = page
    formData.pageSize = limit
    total.value = value
    resultList.value = list
    statsData.paidAmount = data.payedAmount
    statsData.paidCount = data.payedCount
    statsData.count = value
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.keys(formData).forEach((key: string) => {
    formData[key] = defaultFormData[key]
  })
  handleSearch()
}

const handleDownload = async () => {
  const { page, pageSize, ...query } = getQuery()

  loading.value = true

  try {
    await getPersonOrderListFile(query)
    // const { excelUrl } = await getPersonOrderListFile(query)
    //
    // downloadFile(excelUrl)
  } finally {
    loading.value = false
  }
}

const handleViewDetail = (data: any) => {
  const { id } = data

  try {
    router.push({ name: 'configurationOrderDetail', params: { id } })
  } catch {
    ElMessage.warning('请添加查看订单详情路由')
  }
}

const handleSizeChange = (value: number) => {
  formData.page = 1
  formData.pageSize = value
  handleSearch()
}

const handleCurrentChange = (value: number) => {
  formData.page = value
  handleSearch()
}

const handelGetSearchParams = async () => {
  const rs = await getPersonOrderListParams()
  /**
   * // 下单渠道
   *             'platformOptions'  => BaseResumeOrder::PLATFORM_LIST,
   *             // 支付方式
   *             'paywayOptions'    => BaseResumeOrder::PAYWAY_LIST,
   *             // 支付状态
   *             'statusOptions'    => BaseResumeOrder::STATUS_LIST,
   */

  queryTypeOptions.value = rs.queryTypeOptions
  queryPayStatusOptions.value = rs.statusOptions
  queryPayWayOptions.value = rs.paywayOptions
  querySourceOptions.value = rs.platformOptions
}

if (resumeId) {
  queryUser.value = '3'
  formData.resumeId = <string>resumeId
}

if (queryEquityPackageId) {
  queryNameOrId.value = '2'
  formData.equityPackageId = <string>queryEquityPackageId
}

if (queryStatus) {
  formData.status = <string>queryStatus
}

if (queryEquityPackageCategoryId) {
  formData.equityPackageCategoryId = <string>queryEquityPackageCategoryId
}

handleSearch()
handelGetSearchParams()
</script>

<style lang="scss" scoped>
.order-container {
  padding: 20px;
  background-color: var(--color-whites);
  border-radius: 10px;

  .el-select {
    width: 100%;
  }

  .padding-left-reset {
    :deep(.el-input__wrapper) {
      padding-left: 1px;
    }

    .el-select {
      max-width: 100px;

      :deep(.el-input__wrapper) {
        padding-left: 11px;
      }
    }
  }

  .order-stats {
    margin-bottom: 10px;
    padding: 10px 15px;
    color: #3379d1;
    background-color: #d1e9f5;
    border-radius: 4px;

    span {
      margin-right: 30px;
    }
  }

  .order-result {
    :deep(th) {
      color: #333;
      background-color: #f2f2f2;
    }
  }

  .el-pagination {
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
