<template>
  <div class="main-container">
    <el-form :model="form" ref="formRef">
      <el-row>
        <el-col :span="6">
          <el-form-item label="埋点事件" prop="actionId">
            <el-cascader
              class="flex-1"
              v-model="form.actionId"
              :options="optionsList"
              :props="obj"
              clearable
              filterable
              collapse-tags
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="查询日期" class="ml-15" prop="startTime">
            <DatePickerRange v-model:start="form.startTime" v-model:end="form.endTime" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-button class="ml-15" @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleExport">导出数据</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { getAllActionList, exportLogList } from '/@/api/configuration'

const optionsList = ref([])

const formRef = ref()

const obj = { value: 'k', label: 'v', emitPath: false, multiple: true }

const form = ref({
  actionId: [],
  startTime: '',
  endTime: ''
})

const getActionList = async () => {
  optionsList.value = await getAllActionList()
}

const handleReset = () => {
  formRef.value.resetFields()
}

const handleExport = async () => {
  const postData = { ...form.value, actionId: form.value.actionId?.join(',') }
  await exportLogList(postData)
}

getActionList()
</script>

<style lang="scss" scoped>
.main-container {
  background-color: var(--color-whites);
  border-radius: 10px;
  padding: 10px 20px;
}
</style>
