<template>
  <div class="order-container" v-loading="loading">
    <div class="filter-container">
      <el-row class="filter-row" :gutter="20">
        <el-col :span="8">
          <el-form-item label="人才">
            <el-input v-model="queryResumeValue" clearable :placeholder="queryResumePlaceholder">
              <template #prepend>
                <el-select v-model="queryResume" style="width: 100px">
                  <el-option
                    v-for="item in queryResumeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="单位账号">
            <el-input
              v-model="queryCompanyMemberValue"
              clearable
              :placeholder="queryCompanyMemberPlaceholder"
            >
              <template #prepend>
                <el-select v-model="queryCompanyMember" style="width: 100px">
                  <el-option
                    v-for="item in queryCompanyMemberOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="单位">
            <el-input v-model="queryCompanyValue" clearable :placeholder="queryCompanyPlaceholder">
              <template #prepend>
                <el-select v-model="queryCompany" style="width: 100px">
                  <el-option
                    v-for="item in queryCompanyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="filter-row" :gutter="20">
        <el-col :span="8">
          <el-form-item label="职位">
            <el-input v-model="queryJobValue" clearable :placeholder="queryJobPlaceholder">
              <template #prepend>
                <el-select v-model="queryJob" style="width: 100px">
                  <el-option
                    v-for="item in queryJobOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="时间">
            <el-row :gutter="5">
              <el-col :span="8">
                <el-select v-model="queryTime">
                  <el-option
                    v-for="item in queryTimeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="16">
                <el-date-picker
                  v-model="queryTimeValue"
                  type="daterange"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  value-format="YYYY-MM-DD"
                />
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="filter-row" :gutter="20">
        <el-col :span="8">
          <el-form-item label="发起方">
            <el-select v-model="formData.creatorType" clearable placeholder="不限">
              <el-option
                v-for="item in queryCreatorTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="沟通进度">
            <el-select v-model="formData.talkProgress" clearable placeholder="不限">
              <el-option
                v-for="item in queryTalkProgressOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </div>
    <div class="jc-between amount">
      <div>
        共计:<span class="danger">{{ statChatRoom.roomCount }}</span
        >条会话&nbsp;&nbsp; 人才:<span class="danger">{{ statChatRoom.memberCount }}</span
        >位&nbsp;&nbsp; 单位账号:<span class="danger">{{ statChatRoom.companyMemberCount }}</span
        >个&nbsp;&nbsp; 所属单位:<span class="danger">{{ statChatRoom.companyCount }}</span
        >个&nbsp;&nbsp; 沟通职位:<span class="danger">{{ statChatRoom.jobCount }}</span
        >个
      </div>
    </div>
    <div class="order-result">
      <el-table :data="resultList" border height="800">
        <el-table-column prop="uuid" label="会话ID" align="center" />
        <el-table-column prop="resumeId" label="人才id" align="center" />
        <el-table-column prop="resumeName" label="人才姓名" align="center" />
        <el-table-column prop="companyMemberId" label="单位账号ID" align="center" />
        <el-table-column prop="companyMemberName" label="账号姓名" align="center" />
        <el-table-column prop="companyName" label="所属单位" align="center" />
        <el-table-column prop="creatorTypeTxt" label="聊天发起方" align="center" />
        <el-table-column prop="jobName" label="当前职位" align="center" />
        <el-table-column prop="addTime" label="聊天建立时间" align="center" />
        <el-table-column prop="lastTalkTime" label="最近一次聊天" align="center" />
        <el-table-column prop="talkProgressTxt" label="沟通进度" align="center" />
        <el-table-column label="累积沟通职位数" align="center">
          <template #default="{ row }">
            <el-popover placement="right" :width="600">
              <template #reference>
                <span>{{ row.talkJobAmount }} (详情)</span>
              </template>
              <el-table :data="row.historyJob" max-height="600">
                <el-table-column property="jobId" label="职位ID" />
                <el-table-column property="name" label="名称" />
                <el-table-column property="addTime" label="时间" />
                <el-table-column property="creatorTypeTxt" label="操作人" />
                <el-table-column property="typeTxt" label="变更类型" />
                <el-table-column property="isApplyTxt" label="是否已投递" />
              </el-table>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="{ row }">
            <!--    查看最近50条聊天 button  -->
            <el-button type="primary" link @click="showLastTalk(row.id)"> 查看聊天 </el-button>
          </template>
        </el-table-column>

        <!--        <el-table-column prop="address" label="操作" fixed="right" width="100" align="center">-->
        <!--          <template #default="{ row }">-->
        <!--            <el-button link type="primary" @click="() => handleViewDetail(row)">-->
        <!--              查看详情-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
    </div>

    <el-dialog v-model="messageListVisible" title="最近50条聊天内容">
      <el-table :data="messageList" max-height="800">
        <el-table-column property="addTime" label="时间" />
        <el-table-column property="resumeTalk" label="求职者" />
        <el-table-column property="companyTalk" label="单位" />
      </el-table>
    </el-dialog>

    <el-pagination
      v-model:current-page="formData.page"
      v-model:page-size="formData.pageSize"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, unref } from 'vue'

import { getChatRoomList, getChatLastMessage } from '/@/api/configuration'
import { useRoute } from 'vue-router'

const defaultFormData = {
  creatorType: '',
  talkProgress: '',
  resumeId: '',
  resumeName: '',
  resumeMobile: '',
  companyMemberId: '',
  companyMemberName: '',
  companyMemberMobile: '',
  companyMemberEmail: '',
  companyId: '',
  companyName: '',
  companyMobile: '',
  companyEmail: '',
  pageSize: 20,
  talkTimeStart: '',
  addTimeStart: '',
  lastTalkTimeStart: '',
  talkTimeEnd: '',
  addTimeEnd: '',
  lastTalkTimeEnd: '',
  page: 1
}

// 在这里接受页面参数
const statChatRoom = reactive({
  roomCount: 0,
  memberCount: 0,
  companyCount: 0,
  companyMemberCount: 0,
  jobCount: 0
})

const { resumeId, creatorType } = useRoute().query

const loading = ref(false)

const formData = reactive({ ...defaultFormData })

const total = ref(0)

const resultList: any = ref([])

const messageListVisible = ref(false)
const messageList: any = ref([])

const findItemData = (target, value) => target.find((item) => item.value === value)

// 这里开始是人才的条件
const queryResume = ref('1')

const queryResumeOptions = [
  { label: '姓名', value: '1', key: 'resumeName', placeholder: '请填写人才姓名' },
  { label: 'ID', value: '2', key: 'resumeId', placeholder: '请填写人才ID' },
  { label: '手机号', value: '3', key: 'resumeMobile', placeholder: '请填写人才手机号码' }
]

const queryResumeData = computed(() => findItemData(queryResumeOptions, unref(queryResume)))

const queryResumeValue = computed({
  get() {
    return formData[unref(queryResumeData).key]
  },

  set(value: string) {
    formData[unref(queryResumeData).key] = value
  }
})

const queryResumePlaceholder = computed(() => unref(queryResumeData).placeholder)

// 这里开始是单位账号的条件
const queryCompanyMember = ref('1')

const queryCompanyMemberOptions = [
  {
    label: '姓名',
    value: '1',
    key: 'companyMemberName',
    placeholder: '请填写账号姓名'
  },
  {
    label: 'ID',
    value: '2',
    key: 'companyMemberId',
    placeholder: '请填写账号ID'
  },
  {
    label: '手机号码',
    value: '3',
    key: 'companyMemberMobile',
    placeholder: '请填写手机号码'
  },
  {
    label: '邮箱',
    value: '4',
    key: 'companyMemberEmail',
    placeholder: '请填写邮箱'
  }
]

const queryCompanyMemberData = computed(() =>
  findItemData(queryCompanyMemberOptions, unref(queryCompanyMember))
)

const queryCompanyMemberValue = computed({
  get() {
    return formData[unref(queryCompanyMemberData).key]
  },

  set(value: string) {
    formData[unref(queryCompanyMemberData).key] = value
  }
})

const queryCompanyMemberPlaceholder = computed(() => unref(queryCompanyMemberData).placeholder)

// 这里开始是单位
const queryCompany = ref('1')

const queryCompanyOptions = [
  {
    label: '名称',
    value: '1',
    key: 'companyName',
    placeholder: '请填写单位名称'
  },
  {
    label: 'ID',
    value: '2',
    key: 'companyId',
    placeholder: '请填写单位ID'
  }
]

const queryCompanyData = computed(() => findItemData(queryCompanyOptions, unref(queryCompany)))

const queryCompanyValue = computed({
  get() {
    return formData[unref(queryCompanyData).key]
  },

  set(value: string) {
    formData[unref(queryCompanyData).key] = value
  }
})

const queryCompanyPlaceholder = computed(() => unref(queryCompanyData).placeholder)

// 这里开始是职位
const queryJob = ref('1')

const queryJobOptions = [
  { label: '职位名称', value: '1', key: 'jobName', placeholder: '请填写职位名称' },
  { label: '职位ID', value: '2', key: 'jobId', placeholder: '请填写职位ID' }
]

const queryJobData = computed(() => findItemData(queryJobOptions, unref(queryJob)))

const queryJobValue = computed({
  get() {
    return formData[unref(queryJobData).key]
  },

  set(value: string) {
    formData[unref(queryJobData).key] = value
  }
})

const queryJobPlaceholder = computed(() => unref(queryJobData).placeholder)

// 这里开始是时间
const queryTime = ref('1')

const queryTimeOptions = [
  {
    label: '日期查询',
    value: '1',
    key: 'talkTime',
    beginKey: 'talkTimeStart',
    endKey: 'talkTimeEnd'
  },
  { label: '建立时间', value: '2', key: 'addTime', beginKey: 'addTimeStart', endKey: 'addTimeEnd' },
  {
    label: '最近一次',
    value: '3',
    key: 'lastTalkTime',
    beginKey: 'lastTalkTimeStart',
    endKey: 'lastTalkTimeEnd'
  }
]

const queryTimeData = computed(() => findItemData(queryTimeOptions, unref(queryTime)))

const queryTimeValue = ref([])

const queryCreatorTypeOptions = ref([
  { label: '人才', value: '1' },
  { label: '单位', value: '2' }
])

const queryTalkProgressOptions = ref([
  { label: '单向沟通', value: '1' },
  { label: '双向沟通', value: '2' }
])

const getQuery = () => {
  const { key: resumeKey } = unref(queryResumeData)
  const { key: companyMemberKey } = unref(queryCompanyMemberData)
  const { key: companyKey } = unref(queryCompanyData)
  const { key: jobKey } = unref(queryJobData)
  // 拿出来time的key和对应的值
  const { beginKey, endKey } = unref(queryTimeData)
  // const { key: userKey } = unref(queryUserData)

  // queryTimeValue有可能是null
  queryTimeValue.value = queryTimeValue.value || []

  const { page, pageSize, creatorType, talkProgress } = formData

  return {
    [resumeKey]: formData[resumeKey],
    [companyMemberKey]: formData[companyMemberKey],
    [companyKey]: formData[companyKey],
    [jobKey]: formData[jobKey],
    [beginKey]: unref(queryTimeValue)[0],
    [endKey]: unref(queryTimeValue)[1],
    creatorType,
    talkProgress,
    page,
    pageSize
  }
}

const handleSearch = async () => {
  loading.value = true

  try {
    const { list, pages, statChatRoomData } = await getChatRoomList(getQuery())
    const { page, limit, count: value } = pages

    formData.page = page
    formData.pageSize = limit
    total.value = value
    resultList.value = list
    statChatRoom.roomCount = statChatRoomData.roomCount
    statChatRoom.memberCount = statChatRoomData.memberCount
    statChatRoom.companyCount = statChatRoomData.companyCount
    statChatRoom.companyMemberCount = statChatRoomData.companyMemberCount
    statChatRoom.jobCount = statChatRoomData.jobCount
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.keys(formData).forEach((key: string) => {
    formData[key] = defaultFormData[key]
    // 这里是时间的重置
    queryTimeValue.value = []
  })

  // 选项也重置为一开始的
  queryResume.value = '1'
  queryCompanyMember.value = '1'
  queryCompany.value = '1'
  queryJob.value = '1'
  queryTime.value = '1'

  handleSearch()
}

const handleSizeChange = (value: number) => {
  formData.page = 1
  formData.pageSize = value
  handleSearch()
}

const handleCurrentChange = (value: number) => {
  formData.page = value
  handleSearch()
}

const showLastTalk = (value: number) => {
  getChatLastMessage({ id: value }).then((res) => {
    messageList.value = res.list
    messageListVisible.value = true
  })
}

// const handelGetSearchParams = async () => {
//   const rs = await getPersonOrderListParams()
//   /**
//    * // 下单渠道
//    *             'platformOptions'  => BaseResumeOrder::PLATFORM_LIST,
//    *             // 支付方式
//    *             'paywayOptions'    => BaseResumeOrder::PAYWAY_LIST,
//    *             // 支付状态
//    *             'statusOptions'    => BaseResumeOrder::STATUS_LIST,
//    */
//
// }
//
if (resumeId) {
  queryResume.value = '2'
  formData.resumeId = <string>resumeId
}

if (creatorType) {
  formData.creatorType = <string>creatorType
}
//
// if (queryEquityPackageCategoryId) {
//   formData.equityPackageCategoryId = <string>queryEquityPackageCategoryId
// }

handleSearch()
// handelGetSearchParams()
</script>

<style lang="scss" scoped>
.order-container {
  padding: 20px;
  background-color: var(--color-whites);
  border-radius: 10px;

  .el-select {
    width: 100%;
  }

  .padding-left-reset {
    :deep(.el-input__wrapper) {
      padding-left: 1px;
    }

    .el-select {
      max-width: 100px;

      :deep(.el-input__wrapper) {
        padding-left: 11px;
      }
    }
  }

  .order-stats {
    margin-bottom: 10px;
    padding: 10px 15px;
    color: #3379d1;
    background-color: #d1e9f5;
    border-radius: 4px;

    span {
      margin-right: 30px;
    }
  }

  .order-result {
    margin-top: 10px;

    :deep(th) {
      color: #333;
      background-color: #f2f2f2;
    }
  }

  .el-pagination {
    justify-content: center;
    margin-top: 20px;
  }
  .amount {
    margin: 20px 0;
    height: 30px;
    padding: 0 10px;
    line-height: 30px;
    background-color: #edf9ff;
    .danger {
      color: #d9041a;
      font-weight: bold;
    }
  }
}
</style>
