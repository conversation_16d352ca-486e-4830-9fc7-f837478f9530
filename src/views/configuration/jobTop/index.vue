<template>
  <div class="main">
    <el-form :model="formData" label-width="70px">
      <div class="flex">
        <el-form-item class="span-5" label="职位信息" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请填写职位id或名称"
            filterable
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="置顶类型" prop="type">
          <JobTopType v-model="formData.type" />
        </el-form-item>
        <el-form-item class="span-5" label="置顶状态" prop="status">
          <JobTopStatus v-model="formData.status" />
        </el-form-item>
        <el-form-item class="span-5" label="置顶时间" prop="dateStart">
          <DatePickerRange v-model:start="formData.dateStart" v-model:end="formData.dateEnd" />
        </el-form-item>
        <el-form-item class="span-5" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-5" label="职位类型" prop="jobCategoryId">
          <JobCategory v-model="formData.jobCategoryId" />
        </el-form-item>
        <el-form-item class="span-5" label="工作地点" prop="areaId">
          <Region v-model="formData.areaId" />
        </el-form-item>
        <el-form-item class="span-5" label="创建人" prop="creator">
          <el-input
            v-model="formData.creator"
            placeholder="请填写创建人id或名称"
            clearable
          ></el-input>
        </el-form-item>
        <div class="span-5 ml-10">
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button type="primary" @click="openDialog">置顶配置</el-button>
        </div>
      </div>
    </el-form>

    <div class="count">
      <div>
        共计：<span class="danger">{{ amount.topCount }}</span
        >条置顶信息；<span class="danger">{{ amount.topEndCount }}</span
        >条置顶已结束；<span class="danger">{{ amount.topNotStartCount }}</span
        >条置顶未开始
      </div>
    </div>

    <el-table
      border
      ref="table"
      :data="list"
      @sort-change="handleSortable"
      v-loading="tableLoading"
    >
      <el-table-column align="center" label="职位信息">
        <template #default="{ row }"> {{ `${row.jobName}(${row.jobId})` }}</template>
      </el-table-column>

      <el-table-column align="center" prop="typeText" label="置顶类型" />
      <el-table-column align="center" prop="topItem" label="置顶项" />
      <el-table-column align="center" prop="topDetail" label="置顶详情" />

      <el-table-column align="center" prop="sort" label="置顶排序">
        <template #default="{ row }">
          <tableSort ref="sortTable" :row="row" />
        </template>
      </el-table-column>

      <el-table-column align="center" prop="statusText" label="置顶状态" />

      <el-table-column align="center" sortable="custom" prop="dateSort" label="置顶时间">
        <template #default="{ row }"> {{ row.date }} </template>
      </el-table-column>
      <el-table-column align="center" sortable="custom" prop="beginTimeSort" label="开始置顶时间">
        <template #default="{ row }"> {{ row.runTime }} </template>
      </el-table-column>
      <el-table-column align="center" sortable="custom" prop="addTimeSort" label="创建时间">
        <template #default="{ row }"> {{ row.addTime }} </template>
      </el-table-column>

      <el-table-column align="center" prop="creator" label="创建人" />

      <el-table-column align="center" width="180px" label="操作">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            :disabled="!row.isCanEdit"
            @click="openConfig(row.id, row.jobId)"
            >编辑</el-button
          >
          <el-button
            type="primary"
            link
            :disabled="row.topButtonDisabledStatus === 1"
            @click="handelJobConfigTop(row.id, row.topButtonType)"
            >{{ row.topButtonType === 1 ? '暂停' : '重启' }}置顶</el-button
          >
          <el-button
            type="primary"
            link
            :disabled="row.isCanRemove === 2"
            @click="removeJobTop(row.id)"
            >移除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <Paging
      ref="pageFef"
      class="mt-20"
      :total="pages.total"
      :page="pages.current"
      @change="handlePageChange"
    />

    <JobTopDialog ref="jobTopDialog" @getList="getList" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import JobTopDialog from './components/jobTopDialog.vue'
import {
  getJobTopConfigList,
  jobTopConfigChangeStatus,
  jobTopConfigRemove
} from '/@/api/configuration'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import tableSort from './components/tableSort.vue'
import Paging from '/@/components/base/paging.vue'
import JobCategory from '/@/components/base/select/jobCategory.vue'
import JobTopStatus from '/@/components/base/select/jobTopStatus.vue'
import JobTopType from '/@/components/base/select/jobTopType.vue'
import Region from '/@/components/base/select/region.vue'

export default defineComponent({
  name: 'topList',

  components: {
    JobTopType,
    JobTopStatus,
    DatePickerRange,
    JobCategory,
    Region,
    JobTopDialog,
    Paging,
    tableSort
  },

  setup() {
    const state = reactive({
      formData: <any>{
        name: '',
        type: '',
        status: '',
        dateStart: '',
        dateEnd: '',
        addTimeStart: '',
        addTimeEnd: '',
        jobCategoryId: [],
        areaId: [],
        creator: '',
        page: 1,
        pageSize: 20,
        dateSort: '',
        beginTimeSort: '',
        addTimeSort: ''
      },
      pages: {
        total: 0,
        current: 1,
        pageSize: 20
      },
      amount: { topCount: '', topEndCount: '', topNotStartCount: '' },
      list: [],
      tableLoading: false
    })
    const jobTopDialog = ref()
    const table = ref()
    const pageFef = ref()
    const sortTable = ref()

    const openDialog = () => {
      jobTopDialog.value.open()
    }

    const getList = async () => {
      state.tableLoading = true
      const { jobCategoryId, areaId } = state.formData
      const postData = {
        ...state.formData,
        jobCategoryId: jobCategoryId ? jobCategoryId.join() : '',
        areaId: areaId ? areaId.join() : ''
      }
      const { amount, list, pages } = await getJobTopConfigList(postData)
      state.amount = amount
      state.pages = pages
      state.list = list
      state.tableLoading = false
    }

    const handleSortable = ({ prop, order }) => {
      const { dateSort, beginTimeSort, addTimeSort, ...other } = state.formData
      let sort = <any>''
      if (order === 'ascending') {
        // 正序
        sort = 2
      } else if (order === 'descending') {
        sort = 1
      }
      state.formData = <any>{
        ...other
      }
      if (prop !== null) {
        state.formData[prop] = sort
      }
      getList()
    }

    const search = () => {
      table.value.clearSort()
      if (pageFef.value !== null) {
        pageFef.value.currentPage = 1
        state.formData.page = 1
      }
      const { dateSort, beginTimeSort, addTimeSort, ...other } = state.formData
      state.formData = <any>other
      getList()
    }

    const openConfig = (id: string, jobId: string) => {
      jobTopDialog.value.editOpen(id, jobId)
    }

    const handlePageChange = (val: any) => {
      state.formData.page = val.page
      state.formData.pageSize = val.limit
      getList()
    }

    const handelJobConfigTop = (id: string, status: number) => {
      ElMessageBox.confirm(`确定要${status === 1 ? '暂停' : '重启'}置顶吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          await jobTopConfigChangeStatus({ id })
          getList()
        })
        .catch(() => {})
    }

    const removeJobTop = async (id: string) => {
      ElMessageBox.confirm('确定要移除该置顶配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          await jobTopConfigRemove({ id })
          getList()
        })
        .catch(() => {})
    }

    getList()

    return {
      ...toRefs(state),
      openDialog,
      pageFef,
      table,
      jobTopDialog,
      getList,
      handleSortable,
      search,
      sortTable,
      openConfig,
      handlePageChange,
      handelJobConfigTop,
      removeJobTop
    } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}
.count {
  padding: 0 15px;
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
}
.danger {
  color: #f56c6c;
}
</style>
