<template>
  <div>
    <el-input
      :input-style="{ 'text-align': 'center' }"
      v-model="row.sort"
      @keyup.enter="handleSort(row.id, row.sort)"
    ></el-input>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { jobTopConfigChangeSort } from '/@/api/configuration'

export default defineComponent({
  name: 'tableSort',

  props: {
    row: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['getList'],
  setup(props, { emit }) {
    // 修改排序
    const handleSort = (id, sort) => {
      jobTopConfigChangeSort({ id, sort })
      emit('getList')
    }
    return { handleSort }
  }
})
</script>

<style lang="scss" scoped></style>
