<template>
  <el-dialog title="置顶配置" v-model="topVisable" @close="close">
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item prop="jobId" label="职位信息">
        <el-select
          filterable
          remote
          clearable
          :remote-method="remoteMethod"
          v-model="formData.jobId"
          @change="handelChange"
          placeholder="请选择职位信息"
          no-data-text="职位信息有误"
        >
          <el-option
            v-for="item in jobList"
            :key="item.jobId"
            :label="`${item.jobName}（${item.jobId}）`"
            :value="item.jobId"
            >{{ `${item.jobName}（${item.jobId}）` }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item prop="date" label="置顶时间">
        <DatesPicker v-model="formData.date" />
      </el-form-item>
      <el-form-item prop="routineTopId" label="置顶类型" :rules="validateTopType">
        <CheckBox
          v-if="checkList[0]?.routineTopId !== ''"
          v-model="formData.routineTopId"
          label="常规置顶"
        />
        <div>搜索置顶</div>
        <div class="config">
          按职位类型：
          <CheckBox
            v-if="checkList[0]?.jobCategoryText"
            v-model="formData.jobCategoryId"
            :trueLabel="checkList[0]?.jobCategoryId"
            :label="checkList[0]?.jobCategoryText"
          />
        </div>
        <div class="config">
          按工作地点：
          <CheckBox
            v-if="checkList[0]?.provinceText"
            v-model="formData.provinceId"
            :trueLabel="checkList[0]?.provinceId"
            :label="checkList[0]?.provinceText"
          />
          <CheckBox
            v-if="checkList[0]?.cityText"
            v-model="formData.cityId"
            :trueLabel="checkList[0]?.cityId"
            :label="checkList[0]?.cityText"
          />
        </div>
      </el-form-item>
      <el-form-item label="置顶排序">
        <el-input
          placeholder="请输入置顶排序"
          v-model="formData.sort"
          filterable
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          placeholder="请输入备注"
          type="textarea"
          clearable
          :autosize="{ minRows: 2, maxRows: 4 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :disabled="isCanEdit" @click="submit">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, computed } from 'vue'
import CheckBox from '/@/components/base/checkBox.vue'
import DatesPicker from '/@/components/base/datesPicker.vue'
import { getSearchJobInfoList, saveJobTopConfig, getJobTopDetail } from '/@/api/configuration'

export default defineComponent({
  name: 'jobTopDialog',

  components: { DatesPicker, CheckBox },
  emits: ['getList'],

  setup(props, { emit }) {
    const state = reactive({
      topVisable: false,
      formData: {
        jobId: '',
        date: '',
        routineTopId: '',
        jobCategoryId: '',
        provinceId: '',
        cityId: '',
        sort: '',
        remark: ''
      },
      jobCategoryText: '',
      jobList: [],
      checkList: [],
      rules: {
        jobId: [{ required: true, message: '请选择职位信息', trigger: 'change' }],
        date: [{ required: true, message: '请选择置顶时间', trigger: 'change' }]
      },
      isCanEdit: computed(() => state.formData.isCanEdit === '2')
    })

    const form = ref()

    const open = () => {
      state.topVisable = true
    }

    const close = () => {
      state.topVisable = false
      state.jobList = []
      state.checkList = []
      form.value.resetFields()
      Object.keys(state.formData).forEach((key) => {
        state.formData[key] = ''
      })
    }

    const remoteMethod = async (query: string) => {
      if (!query) return
      state.jobList = await getSearchJobInfoList({ name: query })
    }

    const handelChange = (val: string) => {
      state.checkList = state.jobList.filter((item: any) => item.jobId === val)
    }

    const submit = async () => {
      const valid = await form.value.validate()
      if (!valid) return
      await saveJobTopConfig(state.formData)
      emit('getList')
      close()
    }

    const editOpen = async (id: string, jobId: string) => {
      state.jobList = await getSearchJobInfoList({ name: jobId })
      const res = await getJobTopDetail({ id })
      state.formData = res
      state.checkList.push(res)
      state.formData.id = id
      open()
    }

    const topTypeValidate = (rule, value, callback) => {
      if (
        state.formData.routineTopId ||
        state.formData.jobCategoryId ||
        state.formData.provinceId ||
        state.formData.cityId
      ) {
        callback()
      } else {
        callback('请选择置顶类型')
      }
    }

    const validateTopType = ref({
      required: true,
      validator: topTypeValidate,
      trigger: 'change'
    })

    return {
      ...toRefs(state),
      form,
      open,
      close,
      remoteMethod,
      handelChange,
      submit,
      editOpen,
      validateTopType
    } as any
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  display: inline;
}

.config {
  display: flex;
  align-items: center;
}
</style>
