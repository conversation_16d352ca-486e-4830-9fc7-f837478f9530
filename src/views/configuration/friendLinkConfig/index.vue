<template>
  <div class="main">
    <el-form ref="formVmSearch" :model="formDataSearch" label-width="70px">
      <div class="flex">
        <el-col :span="4">
          <el-form-item label="链接标题" prop="isShow">
            <el-input v-model="formDataSearch.title" clearable placeholder="请输链接标题关键字" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="显示状态" prop="isShow">
            <el-select v-model="formDataSearch.status" clearable placeholder="请选择显示状态">
              <el-option label="显示" value="1" />
              <el-option label="隐藏" value="2" />
              <el-option label="删除" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" style="line-height: 100%;">
          <div class="nowrap ml-15 btn-search-group">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </div>
    </el-form>
    <div class="btn-group">
      <el-button type="primary"  class="el-icon-plus" @click="formSubmitAdd()">
        新增友情链接</el-button>
    </div>
    <el-table
      :data="list"
      border
      v-loading="loading"
      @sort-change="handleSortable"
      ref="friendLinkConfigTable"
    >
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="id"
          align="center"
          width="70"
        />

        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="title"
          align="center"
          width="280"
          show-overflow-tooltip
        />

        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="linkUrl"
          align="center"

        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="sortNumber"
          width="110"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.sortNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="status"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag class="tag" effect="plain" v-if="scope.row.status === '1'" type="primary" size="large">{{scope.row.statusText}}</el-tag>
            <el-tag class="tag" effect="plain" v-if="scope.row.status === '2'" type="info" size="large">{{scope.row.statusText}}</el-tag>
            <el-tag class="tag" effect="plain" v-if="scope.row.status === '3'" type="danger" size="large">{{scope.row.statusText}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          prop="addTime"
          width="180"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          prop="isNofollow"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag class="tag" effect="plain" v-if="scope.row.isNofollow === '1'" type="danger" size="large">{{scope.row.isNofollowText}}</el-tag>
            <el-tag class="tag" effect="plain" v-if="scope.row.isNofollow === '2'" type="primary" size="large">{{scope.row.isNofollowText}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="formSubmitEdit(row)"> 编辑 </el-button>
            <el-button type="warning" v-if="row.status !== '1'" size="small" @click="formSubmitStatus(row.id,1)"> 显示 </el-button>
            <el-button type="info" v-if="row.status !== '2'" size="small" @click="formSubmitStatus(row.id,2)"> 隐藏 </el-button>
            <el-button type="danger" v-if="row.status !== '3'" size="small" @click="formSubmitStatus(row.id,3)"> 删除 </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
  </div>
  <el-dialog width="45%" :title="title" v-model="visible" @close="cancelButton">
    <el-form ref="formVm" :model="formData" :rules="formRules" class="form-data">
      <el-form-item
        label="链接标题:"
        :label-width="formLabelWidth"
        prop="title"
      >
        <el-input v-model="formData.title" class="select-width" placeholder="请输入网站标题" type="text" />
      </el-form-item>
      <el-form-item
        label="链接地址:"
        :label-width="formLabelWidth"
        prop="linkUrl"
      >
        <el-input v-model="formData.linkUrl" class="select-width"  placeholder="请输入链接地址" type="text" />
      </el-form-item>
      <el-form-item
        label="排序:"
        :label-width="formLabelWidth"
        prop="sortNumber"
      >
        <el-input v-model="formData.sortNumber" class="select-width" placeholder="请输入排序,默认为0,数字越大越靠前" type="text" />
      </el-form-item>

      <el-form-item
        label="是否nofollow:"
        :label-width="formLabelWidth"
        prop="isNofollow"
      >
          <el-radio-group v-model="formData.isNofollow">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import {friendLinkIndex,friendLinkAdd,friendLinkEdit,friendLinkEditInit,friendLinkStatus} from "../../../api/friendLinkConfig";
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import {ElMessage, ElMessageBox} from "element-plus";

export default {
  name: 'friendLinkConfig',
  components: {
    InputAutocomplete,
    Paging
  },
  setup() {
    const formVmSearch = ref()
    const formVm = ref()
    const listHeader = ref([
      {
        k: 1,
        v: 'ID',
        name: 'id',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '链接标题',
        name: 'title',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '链接',
        name: 'linkUrl',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '排序',
        name: 'sortNumber',
        select: true,
        default: false
      },
      {
        k: 5,
        v: '状态',
        name: 'status',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '添加时间',
        name: 'addTime',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '是否nofollow',
        name: 'isNofollow',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      formLabelWidth: '120px',
      loading: false,
      visible: false,
      title: '',
      list: [],
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      formDataSearch: {
        title:'',
        status:'',
      },
      fileList:[],
      submitType:0,
      rowId:0,
      formData:{
        title: '',
        linkUrl: '',
        sortNumber: '',
        isNofollow: 2
      }
    })

    /**
     * 列表
     * @returns {Promise<void>}
     */
    const getList = async () => {
      state.loading = true
      const { data, pages } = await friendLinkIndex(state.formDataSearch)
      state.list = data
      state.pagination.total = pages.total
      state.loading = false
    }

    /**
     * 初始化调用
     */
    getList()

    /**
     * 分页
     * @param data
     */
    const handlePaginationChange = (data) => {
      state.formDataSearch.page = data.page
      state.formDataSearch.pageSize = data.limit
      getList()
    }

    /**
     * 重置搜索表单
     */
    const reset = () => {
      formVmSearch.value.resetFields()
      getList()
    }

    /**
     * 添加模板
     */
    const formSubmitAdd = () => {
      state.title='新增'
      state.submitType = 1
      state.visible = true
    }

    /**
     * 编辑模板初始化
     * @param row
     */
    const formSubmitEdit = (row) => {
      state.title='编辑'
      state.rowId = row.id
      state.submitType = 2
      //初始化
      friendLinkEditInit({id:row.id}).then(res=>{
        state.formData.title = res.title
        state.formData.linkUrl = res.linkUrl
        state.formData.sortNumber = res.sortNumber
        state.formData.isShow = res.isShow
        state.formData.isNofollow = res.isNofollow
      })
      state.visible = true
    }

    /**
     * 确认提交表单
     */
    const confirmButton = () => {
      formVm.value.validate((validate_result,data)=> {
        if(!validate_result){
          return;
        }
        if (state.submitType === 1) {
          friendLinkAdd(state.formData).then(res => {
            state.visible = false
            formVm.value.resetFields()
            getList()
          })
        } else if (state.submitType === 2) {
          state.formData.id = state.rowId
          friendLinkEdit(state.formData).then(res => {
            state.visible = false
            formVm.value.resetFields()
            getList()
          })
        }
      })
    }

    /**
     * 取消提交表单
     */
    const cancelButton = () => {
      formVm.value.resetFields()
      state.fileList = []
      state.visible = false
    }

    /**
     * 验证表单
     * @type {Ref<UnwrapRef<{personActiveDayNumber: [{trigger: string[], message: string, required: boolean}], invitePersonResumeIds: [{trigger: string[], message: string, required: boolean}], inviteDeliveryWay: [{required: boolean}], inviteSelectText: [{trigger: string[], message: string, required: boolean}], inviteSelect: [{required: boolean}], inviteNumber: [{trigger: string[], message: string, required: boolean}], inviteTime: [{trigger: string[], message: string, required: boolean}]}>>}
     */
    const formRules = ref({
      title: [{ required: true , message: '链接标题不允许为空', trigger: ['blur', 'change'] }],
      linkUrl: [{ required: true , message: '链接地址不允许为空', trigger: ['blur', 'change'] }],
    })

    /**
     * 状态按钮
     */
    const formSubmitStatus = (id,status) => {
      ElMessageBox.confirm('确认变更ID为:'+id+'的状态吗？变更后将会立即生效。', '修改状态', {
        cancelButtonText: '取消',
        confirmButtonText: '确认',
        type: 'warning',
      }).then(() => {
        friendLinkStatus({id:id,status:status}).then((resp) => {
          getList()
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })
      })
    }

    return {
      ...toRefs(state),
      getList,
      formVmSearch,
      formVm,
      listHeader,
      handlePaginationChange,
      reset,
      formRules,
      formSubmitAdd,
      formSubmitEdit,
      confirmButton,
      cancelButton,
      formSubmitStatus,
    }
  }
}
</script>

<style scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.el-select .el-input {
  width: 130px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.flex{
  margin-top: 15px;
}

.btn-group{
  margin: 15px 0;
}

.select-width{
  width: 80% !important;
}

.radio-width{
  width: 40px !important;
}

.tip-color{
  font-weight: bold;
  color: #ff4d4f;
}

.tag{
  font-weight: bold;
}
</style>
