<template>
  <div class="box">
    <el-tabs v-model="activeName" class="equity-tabs" @tab-click="handleClick">
      <el-tab-pane label="单位" name="company">
        <el-form ref="form" label-width="90px" :model="formData">
          <div class="flex">
            <el-form-item class="span-4" label="用户信息" prop="name">
              <InputAutocomplete
                :value="companyInfo.fullName"
                value-key="fullName"
                class="flex-1"
                @change="handleCompanyIdChange"
                @select="handleCompanyIdSelect"
                placeholder="请填写单位ID或名称"
              >
                <template #default="{ row }">
                  <div>{{ row.fullName }}</div>
                </template>
              </InputAutocomplete>
            </el-form-item>
            <el-form-item class="span-4" label="服务项目" prop="type">
              <el-select
                v-model="formData.type"
                class="flex-1"
                placeholder="请选择类型"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in serviceOption"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="操作类型" prop="handleType">
              <el-select
                v-model="formData.handleType"
                class="flex-1"
                placeholder="请选择类型"
                clearable
              >
                <el-option
                  v-for="(item, index) in handleTypeOption"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="权益标识" prop="identify">
              <el-select
                v-model="formData.identify"
                class="flex-1"
                placeholder="请选择权益标识"
                clearable
              >
                <el-option
                  v-for="(item, index) in identifyList"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item class="span-4" label="操作人" prop="handler">
              <el-input
                v-model="formData.handler"
                class="flex-1"
                placeholder="请填写操作人账号"
                clearable
                @keyup.enter="getList"
              ></el-input>
            </el-form-item>
            <el-form-item class="span-4" label="操作时间" prop="addTimeStart">
              <DatePickerRange
                class="w-100"
                v-model:start="formData.addTimeStart"
                v-model:end="formData.addTimeEnd"
              />
            </el-form-item>
            <el-form-item class="pr-30" label-width="20px">
              <el-button type="primary" @click="getList">搜 索</el-button>
              <el-button @click="handleResetField">重 置</el-button>
              <el-button @click="handleDownload">下 载</el-button>
            </el-form-item>
          </div>
        </el-form>
        <!-- <div class="flex amount">
          共计：
          <span class="color-danger">6666</span>
          条记录
        </div> -->
        <el-table v-loading="loading" class="mt-5" :data="list" border size="small">
          <el-table-column
            prop="companyUid"
            align="center"
            header-align="center"
            label="单位ID"
            show-overflow-tooltip
          />
          <el-table-column
            prop="companyName"
            align="center"
            header-align="center"
            label="单位名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="typeTitle"
            align="center"
            header-align="center"
            label="服务项目"
            show-overflow-tooltip
          />
          <el-table-column
            prop="identifyTitle"
            align="center"
            header-align="center"
            label="权益标识"
            min-width="120px"
            show-overflow-tooltip
          />
          <el-table-column
            prop="changeAmount"
            align="center"
            header-align="center"
            label="本期发生数量"
            min-width="130px"
            show-overflow-tooltip
          />
          <el-table-column
            prop="handleAfter"
            align="center"
            header-align="center"
            label="期末余量"
            min-width="130px"
            show-overflow-tooltip
          />
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="操作时间"
            min-width="140px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row.addTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="sortEffectTime"
            align="center"
            header-align="center"
            label="操作人"
            min-width="130px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row.handler }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            align="center"
            header-align="center"
            label="操作类型"
            min-width="130px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row.content }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            align="center"
            header-align="center"
            label="备注"
            min-width="130px"
            show-overflow-tooltip
          />
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-show="list.length"
          @change="handlePaginationChange"
          :total="pagination.total"
          class="mt-15"
        />
      </el-tab-pane>
      <el-tab-pane label="求职者" name="person">
        <el-form ref="personForm" label-width="90px" :model="formDataPersonSearch">
          <div class="flex">
            <el-col :span="6">
              <el-input
                v-model="filterValue"
                prop="filterValue"
                placeholder="请输入关键字"
                class="input-with-select"
              >
                <template #prepend>
                  <el-select
                    v-model="filterKey"
                    property="filterKey"
                    clearable
                    placeholder="请选择字段"
                    style="width: 115px"
                  >
                    <el-option label="姓名" value="resumeName" />
                    <el-option label="用户ID" value="resumeId" />
                    <el-option label="手机号" value="mobile" />
                  </el-select>
                </template>
              </el-input>
            </el-col>
            <el-form-item class="span-4" label="权益类型" prop="equityId">
              <el-select
                v-model="formDataPersonSearch.equityId"
                class="flex-1"
                placeholder="请选择权益"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in personSearchEquityFilter"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="操作类型" prop="actionType">
              <el-select
                v-model="formDataPersonSearch.actionType"
                class="flex-1"
                placeholder="请选择类型"
                clearable
              >
                <el-option
                  v-for="(item, index) in personSearchOperationFilter"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="span-4" label="权益标识" prop="equityType">
              <el-select
                v-model="formDataPersonSearch.equityType"
                class="flex-1"
                placeholder="请选择权益标识"
                clearable
              >
                <el-option
                  v-for="(item, index) in identifyList"
                  :label="item.v"
                  :value="item.k"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item class="span-4" label="操作时间" prop="addTimeStart">
              <DatePickerRange
                class="w-100"
                v-model:start="formDataPersonSearch.addTimeStart"
                v-model:end="formDataPersonSearch.addTimeEnd"
              />
            </el-form-item>
            <el-form-item class="pr-30" label-width="20px">
              <el-button type="primary" @click="getPersonList">搜 索</el-button>
              <el-button @click="handlePersonResetField">重 置</el-button>
              <el-button @click="handlePersonDownload">下 载</el-button>
            </el-form-item>
          </div>
        </el-form>
        <el-table v-loading="loading" class="mt-5" :data="personList" border size="small">
          <el-table-column
            prop="encryptResumeId"
            align="center"
            header-align="center"
            label="用户ID"
            show-overflow-tooltip
          />
          <el-table-column
            prop="name"
            align="center"
            header-align="center"
            label="姓名"
            show-overflow-tooltip
          />
          <el-table-column
            prop="equityName"
            align="center"
            header-align="center"
            label="权益类型"
            show-overflow-tooltip
          />

          <el-table-column
            prop="equityTypeName"
            align="center"
            header-align="center"
            label="权益标识"
            show-overflow-tooltip
          />
          <el-table-column
            prop="changeAmount"
            align="center"
            header-align="center"
            label="本期发生数量"
            show-overflow-tooltip
          />
          <el-table-column
            prop="afterAmount"
            align="center"
            header-align="center"
            label="期末余量"
            show-overflow-tooltip
          />
          <el-table-column
            prop="actionTypeName"
            align="center"
            header-align="center"
            label="操作类型"
            min-width="120px"
            show-overflow-tooltip
          />
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="操作时间"
            min-width="140px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row.addTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="operationName"
            align="center"
            header-align="center"
            label="操作人"
            min-width="130px"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row.operationName }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="relationRemark"
            align="center"
            header-align="center"
            label="备注"
            min-width="130px"
            show-overflow-tooltip
          />
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-show="personList.length"
          @change="handlePersonPaginationChange"
          :total="personPagination.personTotal"
          class="mt-15"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, onMounted, nextTick } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

import {
  getCompanyList,
  getCompanyPackageChangeLog,
  getServiceOption,
  getHandleTypeOption,
  getPersonEquityList,
  getPersonEquityExport,
  getPersonEquityFilter
} from '/@/api/configuration'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  components: { DatePickerRange, Pagination, InputAutocomplete },
  emits: [],
  setup() {
    const form = ref()
    const personForm = ref()
    const packageDialog = ref()
    const activeName = ref('company')

    const handleClick = (tab: TabsPaneContext, event: Event) => {
      console.log(tab, event)
    }
    const state = reactive({
      loading: false,
      effectiveTimeList: [],
      companyInfo: <any>{
        fullName: ''
      },
      formData: {
        name: '',
        type: '',
        handleType: '',
        identify: '',
        handler: '',
        addTimeStart: '',
        addTimeEnd: '',
        export: 2, // 1是 2否
        page: 1,
        limit: 20
      },
      personSearchEquityFilter: [],
      personSearchOperationFilter: [],
      filterKey: 'mobile',
      filterValue: '',
      filterItem: ['mobile', 'resumeId', 'resumeName'],
      formDataPersonSearch: {
        actionType: '',
        equityType: '',
        equityId: '',
        addTimeStart: '',
        addTimeEnd: ''
      },
      personList: [],
      personPagination: {
        personTotal: 0
      },
      pagination: {
        total: 0
      },
      identifyList: [
        { k: 1, v: '增加' },
        { k: 2, v: '减少' }
      ],
      serviceOption: [],
      handleTypeOption: [],
      list: []
    })

    const getOption = async () => {
      state.serviceOption = await getServiceOption()
      state.handleTypeOption = await getHandleTypeOption()
      const { equityFilter, operationFilter } = await getPersonEquityFilter()
      state.personSearchEquityFilter = equityFilter
      state.personSearchOperationFilter = operationFilter
    }

    const getList = () => {
      state.loading = true
      getCompanyPackageChangeLog(state.formData).then((resp: any) => {
        state.loading = false
        if (state.formData.export === 1) {
          state.formData.export = 2
          const aEl = document.createElement('a')
          aEl.setAttribute('href', resp.excelUrl)
          aEl.click()
          return
        }
        state.list = resp.list
        state.pagination.total = resp.page.count
      })
    }

    const getPersonList = () => {
      state.loading = true
      if (state.filterKey && state.filterValue) {
        // 移除filterItem里面与filterKey不相等的key
        state.filterItem.forEach((item) => {
          if (item !== state.filterKey) {
            delete state.formDataPersonSearch[item]
          }
        })
        state.formDataPersonSearch[state.filterKey] = state.filterValue
      } else {
        state.filterItem.forEach((item) => {
          delete state.formDataPersonSearch[item]
        })
      }
      getPersonEquityList(state.formDataPersonSearch).then((resp: any) => {
        state.loading = false
        state.personList = resp.list
        state.personPagination.personTotal = resp.pages.count
      })
    }

    onMounted(() => {
      getList()
      getOption()
      getPersonList()
    })

    const handleResetField = () => {
      form.value.resetFields()
      state.companyInfo.fullName = ''
      nextTick(() => {
        getList()
      })
    }

    const handlePersonResetField = () => {
      personForm.value.resetFields()
      state.filterItem.forEach((item) => {
        delete state.formDataPersonSearch[item]
      })
      state.filterKey = 'mobile'
      state.filterValue = ''
      nextTick(() => {
        getPersonList()
      })
    }

    const handleOpen = () => {
      packageDialog.value.open()
    }

    const handleDownload = () => {
      state.formData.export = 1
      getList()
    }

    const handlePersonDownload = () => {
      getPersonEquityExport(state.formDataPersonSearch)
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (val: any, callback) => {
      state.formData.name = val
      state.companyInfo.fullName = val
      getCompanyList({ name: val }).then((resp: any) => {
        callback(resp)
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (!val) return
      state.formData.name = val.fullName
      state.companyInfo.fullName = val.fullName
    }
    // 单位ID模糊搜索 end

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    const handlePersonPaginationChange = (data: any) => {
      state.formDataPersonSearch.page = data.page
      state.formDataPersonSearch.limit = data.limit
      getPersonList()
    }

    return {
      form,
      personForm,
      packageDialog,
      getList,
      getPersonList,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleResetField,
      handlePersonResetField,
      handleOpen,
      handlePaginationChange,
      handleDownload,
      handlePersonDownload,
      activeName,
      handleClick,
      handlePersonPaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  .amount {
    margin: 0 0 5px;
    height: 30px;
    padding: 0 10px;
    line-height: 30px;
    background-color: #edf9ff;
  }
}
</style>
