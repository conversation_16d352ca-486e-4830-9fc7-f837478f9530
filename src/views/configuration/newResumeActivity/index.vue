<template>
  <div class="main">
    <el-form :model="formData" label-width="100px" ref="form">
      <div class="flex">
        <el-form-item class="span-4" label="人才检索" prop="keyword">
          <el-input
            v-model="formData.keyword"
            placeholder="请输入用户姓名/ID"
            filterable
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="发放状态" prop="issueStatus">
          <el-select
            class="w100"
            v-model="formData.issueStatus"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="item in issueStatusList as any"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="span-4" label="注册时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-4" label="奖励发放时间" prop="successTimeStart">
          <DatePickerRange
            v-model:start="formData.successTimeStart"
            v-model:end="formData.successTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="邀请进度" prop="status">
          <el-select
            class="w100"
            v-model="formData.status"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="item in statusList as any"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
        <div class="span-4 ml-10">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleDownload">下载</el-button>
        </div>
      </div>
    </el-form>

    <el-table border ref="table" :data="list">
      <el-table-column align="center" prop="shareUuid" label=" 邀请人ID" />
      <el-table-column align="center" prop="shareName" label="邀请人姓名" />
      <el-table-column align="center" prop="shareMobile" label="邀请人手机号" />
      <el-table-column align="center" prop="acceptUuid" label="受邀人ID" />
      <el-table-column align="center" prop="acceptMobile" label="受邀人手机号" />
      <el-table-column align="center" prop="acceptName" label="受邀人姓名" />
      <el-table-column align="center" prop="addTime" label="受邀人注册时间" />
      <el-table-column align="center" prop="statusTxt" label="邀请进度" />
      <el-table-column align="center" prop="successTime" label="达标时间" />
      <el-table-column align="center" prop="shareIssueStatusTxt" label="奖励发放状态（邀请人）" />
      <el-table-column align="center" prop="acceptIssueStatusTxt" label="奖励发放状态（受邀人）" />
    </el-table>

    <Paging class="mt-20" :total="pages.total" :page="pages.current" @change="handlePageChange" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

import { getNewResumeLoadParams, getNewResumeActivityList } from '/@/api/newResumeActivity'

const formData = reactive({
  keyword: '',
  addTimeStart: '',
  addTimeEnd: '',
  successTimeStart: '',
  successTimeEnd: '',
  status: '',
  issueStatus: '',
  page: 1,
  pageSize: 20,
  export: ''
})
const pages = reactive({
  total: 0,
  current: 1,
  pageSize: 20
})

const issueStatusList = ref([])
const statusList = ref([])
const list = ref([])

const form = ref()

const getParams = async () => {
  const resp = await (<any>getNewResumeLoadParams())
  issueStatusList.value = resp.issueStatusList
  statusList.value = resp.statusList
}

const getList = async () => {
  const resp = await (<any>getNewResumeActivityList(formData))
  const { export: isExport } = formData
  if (!isExport) {
    list.value = resp.list
    pages.total = resp.pages?.count
  }
}

const handleSearch = () => {
  formData.export = ''

  getList()
}

const handleReset = () => {
  form.value.resetFields()
  formData.export = ''
  nextTick(() => {
    getList()
  })
}

const handleDownload = () => {
  formData.export = '1'
  getList()
}

const handlePageChange = (val: any) => {
  formData.page = val.page
  formData.pageSize = val.limit
  getList()
}

getParams()
getList()
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}
</style>
