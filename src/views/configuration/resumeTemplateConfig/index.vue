<template>
  <div class="main">
    <el-form ref="formVmSearch" :model="formDataSearch" label-width="70px">
      <div class="flex">
        <el-col :span="4">
          <el-input
            v-model="formDataSearch.filterValue"
            placeholder="请输入关键字"
            class="input-with-select"
          >
            <template #prepend>
              <el-select v-model="formDataSearch.filterKey" clearable placeholder="请选择字段" style="width: 115px">
                <el-option label="模板ID" value="1" />
                <el-option label="模板名称" value="2" />
                <el-option label="模板编号" value="3" />
              </el-select>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-form-item label="显示状态" prop="isShow">
            <el-select v-model="formDataSearch.isShow" clearable placeholder="请选择显示状态">
              <el-option label="显示" value="1" />
              <el-option label=隐藏 value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="会员状态" prop="isVip">
            <el-select v-model="formDataSearch.isVip" clearable placeholder="请选择会员状态">
              <el-option label="VIP" value="1" />
              <el-option label="普通" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
<!--        <el-col :span="4">-->
<!--          <el-form-item label="删除状态" prop="isDelete">-->
<!--            <el-select v-model="formDataSearch.isDelete" clearable placeholder="请选择删除状态">-->
<!--              <el-option label="删除" value="1" />-->
<!--              <el-option label="正常" value="2" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="4">
          <el-form-item label="添加时间" prop="addTimeStart">
            <DatePickerRange
              v-model:start="formDataSearch.addTimeStart"
              v-model:end="formDataSearch.addTimeEnd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" style="line-height: 100%;">
          <div class="nowrap ml-15 btn-search-group">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </div>
    </el-form>
    <div class="btn-group">
      <el-button type="primary"  class="el-icon-plus" @click="formSubmitAdd()">
        新增简历模板</el-button>
    </div>
    <el-table
      :data="list"
      border
      v-loading="loading"
      @sort-change="handleSortable"
      ref="resumeTemplateConfigTable"
    >
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="id"
          align="center"
          width="70"
        />

        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="name"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="code"
          align="center"
          width="150"
        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="pdfDownloadNumberSort"
          sortable="custom"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.pdfDownloadNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="docDownloadNumberSort"
          sortable="custom"
          align="center"
          width="150"
        >
          <template #default="{ row }">
            <div>{{ row.docDownloadNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          sortable="custom"
          prop="sortNumberSort"
          width="110"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.sortNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          sortable="custom"
          prop="isShowSort"
          width="110"
          align="center"
        >
          <template #default="scope">
            <el-tag class="tag" effect="plain" v-if="scope.row.isShow === '1'" type="warning" size="large">{{scope.row.isShowText}}</el-tag>
            <el-tag class="tag" effect="plain" v-if="scope.row.isShow === '2'" type="info" size="large">{{scope.row.isShowText}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          sortable="custom"
          prop="isVipSort"
          width="130"
          align="center"
        >
          <template #default="scope">
            <el-tag class="tag" effect="plain" v-if="scope.row.isVip === '1'" type="warning" size="large">{{scope.row.isVipText}}</el-tag>
            <el-tag class="tag" effect="plain" v-if="scope.row.isVip === '2'" type="info" size="large">{{scope.row.isVipText}}</el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column-->
<!--          v-if="item.select && item.k === 9"-->
<!--          :key="index"-->
<!--          :label="item.v"-->
<!--          prop="isDeleteSort"-->
<!--          sortable="custom"-->
<!--          width="110"-->
<!--          align="center"-->
<!--        >-->
<!--          <template #default="scope">-->
<!--            <el-tag class="tag" effect="plain" v-if="scope.row.isDelete === '1'" type="danger" size="large">{{scope.row.isDeleteText}}</el-tag>-->
<!--            <el-tag class="tag" effect="plain" v-if="scope.row.isDelete === '2'" type="success" size="large">{{scope.row.isDeleteText}}</el-tag>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
          v-if="item.select && item.k === 10"
          :key="index"
          :label="item.v"
          prop="addTimeSort"
          sortable="custom"
          width="200"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.addTime }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 11"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="formSubmitEdit(row)"> 编辑 </el-button>
<!--            <el-button type="success" v-if="row.isDelete === '1'" size="small" @click="formSubmitDelete(row.id)"> 正常 </el-button>-->
<!--            <el-button type="danger" v-else-if="row.isDelete === '2'" size="small" @click="formSubmitDelete(row.id)"> 删除 </el-button>-->
            <el-button type="info" v-if="row.isShow === '1'" size="small" @click="formSubmitShow(row.id)"> 隐藏 </el-button>
            <el-button type="warning" v-else-if="row.isShow === '2'" size="small" @click="formSubmitShow(row.id)"> 显示 </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
  </div>
  <el-dialog width="45%" :title="title" v-model="visible" @close="cancelButton">
    <el-form ref="formVm" :model="formData" :rules="formRules" class="form-data">
      <el-form-item
        label="模板名称:"
        :label-width="formLabelWidth"
        prop="templateName"
      >
        <el-input v-model="formData.templateName" class="select-width" placeholder="请输入模板名称,最多10个字符" type="text" />
      </el-form-item>
      <el-form-item
        label="模板简介:"
        :label-width="formLabelWidth"
        prop="templateDescription"
      >
        <el-input v-model="formData.templateDescription"  placeholder="请输入模板简介,最多20个字符" class="select-width" type="text" />
      </el-form-item>
      <el-form-item
        label="模板编号:"
        :label-width="formLabelWidth"
        prop="templateCode"
      >
        <el-input v-model="formData.templateCode" class="select-width"  placeholder="请输入模板编号,编号必须唯一" type="text" />
      </el-form-item>
      <el-form-item
        label="排序:"
        :label-width="formLabelWidth"
        prop="sortNumber"
      >
        <el-input v-model="formData.sortNumber" class="select-width" placeholder="请输入排序,默认为0,数字越大越靠前" type="text" />
      </el-form-item>
      <el-form-item
        label="是否显示:"
        :label-width="formLabelWidth"
        prop="isShow"
      >
        <el-radio-group v-model="formData.isShow">
          <el-radio class="radio-width" label="1">是</el-radio>
          <el-radio class="radio-width" label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="VIP标识:"
        :label-width="formLabelWidth"
        prop="isVip"
      >
        <el-radio-group v-model="formData.isVip">
          <el-radio class="radio-width" label="1">VIP</el-radio>
          <el-radio class="radio-width" label="2">普通</el-radio>
        </el-radio-group>
      </el-form-item>
<!--      <el-form-item-->
<!--        label="状态:"-->
<!--        :label-width="formLabelWidth"-->
<!--        prop="isDelete"-->
<!--        v-if="submitType===2"-->
<!--      >-->
<!--        <el-radio-group v-model="formData.isDelete">-->
<!--          <el-radio class="radio-width" label="2">正常</el-radio>-->
<!--          <el-radio class="radio-width" label="1">删除</el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->
      <el-form-item
        label="模板示例图:"
        :label-width="formLabelWidth"
        prop="templateImage"
      >
        <el-upload
          v-model:file-list="fileList"
          class="upload-demo"
          action="/upload/image"
          limit="1"
          list-type="picture"
          :before-upload="beforeUpload"
          :on-success="successUpload"
          :on-error="errorUpload"
          :on-remove="removeUpload"
        >
          <el-button type="primary">上传图片</el-button>
          <template #tip>
            <div class="el-upload__tip tip-color" >
              图片尺寸：344px * 478px；大小：不超过2MB；支持格式：jpg/png/jpeg!
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import {indexTemplate,editTemplate,addTemplate,editInitTemplate,deleteStatusTemplate,showStatusTemplate} from "../../../api/resumeTemplateConfig";
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import {ElMessage, ElMessageBox} from "element-plus";

export default {
  name: 'resumeTemplateConfig',
  components: {
    DatePickerRange,
    InputAutocomplete,
    Paging
  },
  setup() {
    const formVmSearch = ref()
    const formVm = ref()
    const listHeader = ref([
      {
        k: 1,
        v: '模板ID',
        name: 'id',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '模板名称',
        name: 'name',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '模板编号',
        name: 'code',
        select: true,
        default: true
      },
      {
        k: 4,
        v: 'PDF下载次数',
        name: 'pdfDownloadNumber',
        select: true,
        default: false
      },
      {
        k: 5,
        v: 'DOC下载次数',
        name: 'docDownloadNumber',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '排序',
        name: 'sortNumber',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '显示状态',
        name: 'isShow',
        select: true,
        default: false
      },
      {
        k: 8,
        v: 'VIP标识状态',
        name: 'isVip',
        select: true,
        default: true
      },
      // {
      //   k: 9,
      //   v: '删除状态',
      //   name: 'isDelete',
      //   select: true,
      //   default: true
      // },
      {
        k: 10,
        v: '添加时间',
        name: 'addTime',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      formLabelWidth: '120px',
      loading: false,
      visible: false,
      title: '',
      list: [],
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      formDataSearch: {
        filterValue:'',
        filterKey:'2',
        isShow:'',
        // isDelete:'',
        isVip:'',
        addStartTime:'',
        addEndTime:'',
        sortNumberSort:'',
        pdfDownloadNumberSort:'',
        docDownloadNumberSort:'',
        addTimeSort:'',
        isShowSort:'',
        isVipSort:'',
        // isDeleteSort:'',
      },
      fileList:[],
      submitType:0,
      rowId:0,
      formData:{
        templateName: '',
        templateDescription: '',
        templateCode: '',
        isShow: '1',
        isVip:'1',
        // isDelete:'2',
        sortNumber:'',
        templateImage:'',
        fileId:''
      }
    })

    /**
     * 列表
     * @returns {Promise<void>}
     */
    const getList = async () => {
      state.loading = true
      const { data, pages } = await indexTemplate(state.formDataSearch)
      state.list = data
      state.pagination.total = pages.total
      state.loading = false
    }

    /**
     * 初始化调用
     */
    getList()

    /**
     * 分页
     * @param data
     */
    const handlePaginationChange = (data) => {
      state.formDataSearch.page = data.page
      state.formDataSearch.pageSize = data.limit
      getList()
    }

    /**
     * 重置搜索表单
     */
    const reset = () => {
      formVmSearch.value.resetFields()
      state.formDataSearch.filterKey = '2'
      state.formDataSearch.filterValue = ''
      state.formDataSearch.addTimeEnd = ''
      state.formDataSearch.addTimeStart = ''
      getList()
    }

    /**
     * 添加模板
     */
    const formSubmitAdd = () => {
      state.title='新增'
      state.submitType = 1
      state.visible = true
    }

    /**
     * 编辑模板初始化
     * @param row
     */
    const formSubmitEdit = (row) => {
      state.title='编辑'
      state.rowId = row.id
      state.submitType = 2
      //初始化
      editInitTemplate({id:row.id}).then(res=>{
        state.formData.templateName = res.name
        state.formData.templateDescription = res.description
        state.formData.templateCode = res.code
        state.formData.isShow = res.isShow
        state.formData.isVip = res.isVip
        // state.formData.isDelete = res.isDelete
        state.formData.sortNumber = res.sortNumber
        state.formData.templateImage = res.templateImage
        state.formData.fileId = res.fileId
        state.fileList = [
          {
            name: res.fileInfo.name,
            url: res.fileInfo.path,
          }
        ]
      })
      state.visible = true
    }

    /**
     * 确认提交表单
     */
    const confirmButton = () => {
        if(state.submitType === 1){
          addTemplate(state.formData).then(res => {
              state.visible = false
              getList()
          })
        }else if(state.submitType === 2){
          state.formData.id = state.rowId
          editTemplate(state.formData).then(res => {
              state.visible = false
              getList()
          })
        }
    }

    /**
     * 取消提交表单
     */
    const cancelButton = () => {
      formVm.value.resetFields()
      state.formData.fileId = ''
      state.fileList = []
      state.visible = false
    }

    /**
     * 成功上传示例图回调
     * @param res
     * @param file
     */
    const successUpload = (res,file) => {
      state.formData.templateImage=res.data.url
      state.formData.fileId=res.data.id
    }

    /**
     * 验证表单
     * @type {Ref<UnwrapRef<{personActiveDayNumber: [{trigger: string[], message: string, required: boolean}], invitePersonResumeIds: [{trigger: string[], message: string, required: boolean}], inviteDeliveryWay: [{required: boolean}], inviteSelectText: [{trigger: string[], message: string, required: boolean}], inviteSelect: [{required: boolean}], inviteNumber: [{trigger: string[], message: string, required: boolean}], inviteTime: [{trigger: string[], message: string, required: boolean}]}>>}
     */
    const formRules = ref({
      templateName: [{ required: true , message: '模板名称不允许为空', trigger: ['blur', 'change'] }],
      templateCode: [{ required: true , message: '模板编号不允许为空', trigger: ['blur', 'change'] }],
      isShow: [{ required: true }],
      isVip: [{ required: true }],
      // isDelete: [{ required: true }],
      fileList: [{ required: true }],
    })

    /**
     * 上传前的钩子函数
     * @param res
     * @returns {boolean}
     */
    const beforeUpload = (res) => {
      //限制一下上传的图片格式和大小
      const isJPG = res.type === 'image/jpeg';
      const isPNG = res.type === 'image/png';
      const isGIF = res.type === 'image/gif';
      const isLt2M = res.size / 1024 / 1024 < 2;
      if (!isJPG && !isPNG && !isGIF) {
        ElMessage.error('上传头像图片只能是 JPG/PNG/GIF 格式!');
        return false
      }
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 2MB!');
        return false
      }
    }

    /**
     * 上传失败回调
     * @param res
     * @returns {boolean}
     */
    const errorUpload = (res) => {
      ElMessage.error('上传失败');
      return false
    }

    /**
     * 移除上传回调
     * @param res
     */
    const removeUpload = (res) => {
        state.fileList= []
        state.formData.templateImage = ''
        state.formData.fileId = ''
    }

    /**
     * 删除状态按钮
     */
    const formSubmitDelete = (id) => {
      ElMessageBox.confirm('确认变更模板ID为:'+id+'的删除状态吗？变更后将会立即生效。', '删除状态', {
        cancelButtonText: '取消',
        confirmButtonText: '确认',
        type: 'warning',
      }).then(() => {
        deleteStatusTemplate({id:id}).then((resp) => {
          getList()
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })
      })
    }

    /**
     * 显示状态按钮
     */
    const formSubmitShow = (id) => {
      ElMessageBox.confirm('确认变更模板ID为:'+id+'的显示状态吗？变更后将会立即生效。', '显示状态', {
        cancelButtonText: '取消',
        confirmButtonText: '确认',
        type: 'warning',
      }).then(() => {
        showStatusTemplate({id:id}).then((resp) => {
          getList()
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })
      })
    }

    /**
     * 排序
     * @param prop
     * @param order
     */
    const handleSortable = ({ prop, order }) => {
      console.log(prop)
      console.log(order)
      const sortFormData= {
        sortNumberSort: '',
        pdfDownloadNumberSort: '',
        docDownloadNumberSort: '',
        addTimeSort: '',
        isShowSort: '',
        isVipSort: '',
        // isDeleteSort: '',
      }
      //清空其他排序
      for (let key in sortFormData) {
        if (key !== prop) {
          state.formDataSearch[key] = ''
        }
      }
      if (order === 'ascending') {
        // 正序
        state.formDataSearch[prop] = 2
      } else if (order === 'descending') {
        state.formDataSearch[prop] = 1
      }
      getList()
    }

    return {
      ...toRefs(state),
      getList,
      formVmSearch,
      formVm,
      listHeader,
      handlePaginationChange,
      reset,
      handleSortable,
      formRules,
      formSubmitAdd,
      formSubmitEdit,
      confirmButton,
      cancelButton,
      successUpload,
      beforeUpload,
      errorUpload,
      removeUpload,
      formSubmitDelete,
      formSubmitShow
    }
  }
}
</script>

<style scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.el-select .el-input {
  width: 130px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.flex{
  margin-top: 15px;
}

.btn-group{
  margin: 15px 0;
}

.select-width{
  width: 80% !important;
}

.radio-width{
  width: 40px !important;
}

.tip-color{
  font-weight: bold;
  color: #ff4d4f;
}

.tag{
  font-weight: bold;
}
</style>
