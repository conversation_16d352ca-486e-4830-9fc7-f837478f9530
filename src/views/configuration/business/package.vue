<template>
  <div>
    <el-form ref="form" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-5" label="单位信息" prop="name">
          <InputAutocomplete
            :value="companyInfo.fullName"
            value-key="fullName"
            class="flex-1"
            @change="handleCompanyIdChange"
            @select="handleCompanyIdSelect"
            placeholder="请填写单位ID或名称"
          >
            <template #default="{ row }">
              <div>{{ row.fullName }}</div>
            </template>
          </InputAutocomplete>
        </el-form-item>
        <el-form-item class="span-5" label="操作人" prop="handler">
          <el-input
            v-model="formData.handler"
            placeholder="请填写操作人账号"
            clearable
            @keyup.enter="getList"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label-width="95px" label="权益包有效期" prop="packageAmount">
          <el-select
            v-model="formData.packageAmount"
            placeholder="请选择有效期"
            filterable
            clearable
          >
            <el-option
              v-for="(item, index) in packageValidityList"
              :label="item.packageAmount"
              :value="item.packageAmount"
              :key="index"
            >
              {{ item.packageAmount }}个月
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="操作时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5" label="生效时间" prop="effectTimeStart">
          <DatePickerRange
            v-model:start="formData.effectTimeStart"
            v-model:end="formData.effectTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-5" label="失效时间" prop="expireTimeStart">
          <DatePickerRange
            v-model:start="formData.expireTimeStart"
            v-model:end="formData.expireTimeEnd"
          />
        </el-form-item>
        <el-form-item class="flex-1" label-width="10px">
          <el-button type="primary" @click="getList">搜 索</el-button>
          <el-button @click="handleResetField">重 置</el-button>
          <el-button @click="handleDownload">下 载</el-button>
          <el-button @click="handleOpen" type="primary">业务配置</el-button>
          <el-button
            v-if="buttonPermissionsList.includes('resumeDownloadAmountConfig')"
            @click="handleOpenResumeDown"
            type="primary"
            >资源配置</el-button
          >
        </el-form-item>
      </div>
    </el-form>
    <el-table
      v-loading="loading"
      class="mt-5"
      :data="list"
      border
      size="small"
      @sort-change="handleSort"
    >
      <el-table-column
        prop="companyUid"
        align="center"
        header-align="center"
        label="单位ID"
        show-overflow-tooltip
      />
      <el-table-column
        prop="companyName"
        align="center"
        header-align="center"
        label="单位名称"
        min-width="130px"
        show-overflow-tooltip
      />
      <el-table-column
        prop="style"
        align="center"
        header-align="center"
        label="配置类型"
        show-overflow-tooltip
      />
      <el-table-column
        prop="name"
        align="center"
        header-align="center"
        label="权益包类型"
        min-width="120px"
        show-overflow-tooltip
      />
      <el-table-column
        prop="handleBefore"
        align="center"
        header-align="center"
        label="配置前会员类型"
        min-width="130px"
        show-overflow-tooltip
      />
      <el-table-column
        prop="handleAfter"
        align="center"
        header-align="center"
        label="配置后会员类型"
        min-width="130px"
        show-overflow-tooltip
      />
      <el-table-column
        prop="sortPackageAmount"
        align="center"
        header-align="center"
        label="权益包有效期(月)"
        sortable="custom"
        min-width="140px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>{{ row.packageAmount }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortEffectTime"
        align="center"
        header-align="center"
        label="生效时间"
        sortable="custom"
        min-width="130px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>{{ row.effectTime }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortExpireTime"
        align="center"
        header-align="center"
        label="失效时间"
        sortable="custom"
        min-width="130px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>{{ row.expireTime }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortAddTime"
        align="center"
        header-align="center"
        label="操作时间"
        sortable="custom"
        min-width="130px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>{{ row.addTime }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="handler"
        align="center"
        header-align="center"
        label="操作人"
        show-overflow-tooltip
      />
      <el-table-column prop="" align="center" header-align="center" label="操作">
        <template #default="{ row }">
          <el-popover width="500px" trigger="hover" placement="left">
            <template #reference>
              <el-link type="primary" class="fs-12" :underline="false">查看</el-link>
            </template>
            <div class="pt-10">
              <el-row class="mb-15" :gutter="10">
                <el-col class="ta-right" :span="5">用户名称：</el-col>
                <el-col class="opacity-70" :span="19">{{ row.detail.companyName }}</el-col>
              </el-row>
              <el-row class="mb-15" :gutter="10">
                <el-col class="ta-right" :span="5">权益明细：</el-col>
                <el-col :span="19" class="opacity-70">
                  <el-row
                    v-for="(item, index) in row.detail.detailList"
                    :key="index"
                    :class="[row.detail.detailList.length === index + 1 ? '' : 'mb-10']"
                  >
                    <el-col :span="14">{{ item.name }}</el-col>
                    <el-col :span="10">{{ item.value }}</el-col>
                  </el-row>
                </el-col>
              </el-row>
              <el-row class="mb-15" :gutter="10">
                <el-col class="ta-right" :span="5">生效时间：</el-col>
                <el-col :span="19" class="opacity-70">{{ row.effectTime }}</el-col>
              </el-row>
              <el-row class="mb-15" :gutter="10">
                <el-col class="ta-right" :span="5">有效期：</el-col>
                <el-col :span="19" class="opacity-70">{{ row.detail.expireTimeTitle }}</el-col>
              </el-row>
              <el-row class="mb-15" :gutter="10">
                <el-col class="ta-right" :span="5">备注：</el-col>
                <el-col class="opacity-70" :span="19">{{ row.remark }}</el-col>
              </el-row>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-show="list.length"
      @change="handlePaginationChange"
      :total="pagination.total"
      class="mt-15"
    />
    <PackageDialog @update="getList" ref="packageDialog" />
    <PackageResumeDownDialog @update="getList" ref="packageResumeDownDialog" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, onMounted, nextTick, computed } from 'vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import PackageDialog from './components/packageDialog.vue'
import PackageResumeDownDialog from './components/packageResumeDownDialog.vue'
import {
  getPackageAmountList,
  getCompanyList,
  getCompanyPackageConfigLog
} from '/@/api/configuration'
import { useStore } from '/@/store'

export default {
  components: {
    DatePickerRange,
    Pagination,
    PackageDialog,
    InputAutocomplete,
    PackageResumeDownDialog
  },
  emits: [],
  setup() {
    const form = ref()
    const packageDialog = ref()
    const packageResumeDownDialog = ref()
    const store = useStore()
    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )

    const state = reactive({
      loading: false,
      companyInfo: <any>{
        fullName: ''
      },
      formData: {
        name: '',
        handler: '',
        packageAmount: '',
        addTimeStart: '',
        addTimeEnd: '',
        effectTimeStart: '',
        effectTimeEnd: '',
        expireTimeStart: '',
        expireTimeEnd: '',
        sortPackageAmount: '',
        sortEffectTime: '',
        sortExpireTime: '',
        sortAddTime: '',

        export: 2, // 1是 2否
        page: 1,
        limit: 20
      },
      pagination: {
        total: 0
      },
      list: [],
      packageValidityList: [],
      buttonPermissionsList: []
    })

    // 按钮权限

    state.buttonPermissionsList = requestOldRoutesAction.value.configurationBusiness ?? []
    const getPackageValidity = () => {
      getPackageAmountList().then((resp: any) => {
        state.packageValidityList = resp
      })
    }

    const getList = () => {
      state.loading = true
      getCompanyPackageConfigLog(state.formData).then((resp: any) => {
        state.loading = false
        if (state.formData.export === 1) {
          state.formData.export = 2
          const aEl = document.createElement('a')
          aEl.setAttribute('href', resp.excelUrl)
          aEl.click()
          return
        }
        state.list = resp.list
        state.pagination.total = resp.page.count
      })
    }

    onMounted(() => {
      getList()
      getPackageValidity()
    })
    const handleOpen = () => {
      packageDialog.value.open()
    }

    const handleOpenResumeDown = () => {
      packageResumeDownDialog.value.open()
    }

    const handleDownload = () => {
      state.formData.export = 1
      getList()
    }
    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (val: any, callback) => {
      state.formData.name = val
      state.companyInfo.fullName = val
      getCompanyList({ name: val }).then((resp: any) => {
        callback(resp)
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (!val) return
      state.formData.name = val.id
      state.companyInfo = val
    }
    // 单位ID模糊搜索 end

    const handleSort = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortPackageAmount')
      Reflect.deleteProperty(state.formData, 'sortEffectTime')
      Reflect.deleteProperty(state.formData, 'sortExpireTime')
      Reflect.deleteProperty(state.formData, 'sortAddTime')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getList()
    }

    const handleResetField = () => {
      form.value.resetFields()
      state.companyInfo.fullName = ''
      nextTick(() => {
        getList()
      })
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    return {
      form,
      packageDialog,
      packageResumeDownDialog,
      getList,
      handleResetField,
      handleDownload,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleOpen,
      handleSort,
      handlePaginationChange,
      handleOpenResumeDown,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
