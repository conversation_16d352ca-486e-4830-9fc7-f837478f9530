<template>
  <el-dialog
    title="业务配置管理"
    v-model="visible"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      class="pt-10 pr-100 mr-20"
      :model="formData"
      :rules="formRules"
      ref="form"
      label-width="95px"
    >
      <el-form-item label="单位ID：" prop="companyId">
        <div class="ai-center p-relative">
          <InputAutocomplete
            :value="companyInfo.fullName"
            value-key="fullName"
            class="flex-1"
            @change="handleCompanyIdChange"
            @select="handleCompanyIdSelect"
            placeholder="请填写单位ID"
          >
            <template #default="{ row }">
              <div>{{ row.fullName }}</div>
            </template>
          </InputAutocomplete>
          <div class="name p-absolute opacity-80 fs-13">{{ companyInfo.companyRoleName }}</div>
        </div>
      </el-form-item>
      <el-form-item label="新业务员：" prop="adminId">
        <InputAutocomplete
          :value="adminInfo.username"
          value-key="username"
          class="flex-1"
          @change="handleAdminChange"
          @select="handleAdminSelect"
          placeholder="请填写新业务员姓名或账号搜索"
        >
          <template #default="{ row }">
            <div>{{ row.username }}</div>
          </template>
        </InputAutocomplete>
      </el-form-item>
      <el-form-item label="备注：" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          rows="4"
          resize="none"
          placeholder="备注"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :loading="loading" type="primary" @click="submit">确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, ref, nextTick } from 'vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

import { getCompanyList, getAdminList, companyAdminChange } from '/@/api/configuration'

export default defineComponent({
  name: '',
  components: { InputAutocomplete },
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      loading: false,
      formData: {
        companyId: <string | number>'',
        adminId: <string | number>'',
        remark: ''
      },
      companyInfo: <any>{
        fullName: ''
      },
      adminInfo: <any>{
        username: ''
      }
    })

    const validateCompanyId = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写单位'))
        return
      }
      const { companyRole } = <any>state.companyInfo
      if (companyRole === 2) {
        callback(new Error('当前企业套餐还在使用中，不可配置'))
        return
      }
      if (value === -1) {
        callback(new Error('该单位不存在，请重新选择'))
      } else {
        callback()
      }
    }

    const validateAdminId = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写新业务员'))
        return
      }
      if (value === -1) {
        callback(new Error('该业务员不存在，请重新选择'))
      } else {
        callback()
      }
    }

    const formRules = ref({
      companyId: [{ validator: validateCompanyId, trigger: 'blur' }],
      adminId: [{ validator: validateAdminId, trigger: 'blur' }]
    })

    const open = () => {
      state.visible = true
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (kw: any, callback) => {
      state.formData.companyId = -1
      state.companyInfo.fullName = kw
      getCompanyList({ name: kw }).then((resp: any) => {
        callback(resp)
        if (!state.companyInfo.fullName.includes(kw) && !resp.length) {
          state.formData.companyId = -1
          setTimeout(() => {
            form.value.validateField('companyId', () => {})
          }, 210)
        }
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (!val) {
        state.formData.companyId = ''
        return
      }
      state.formData.companyId = val.id
      state.companyInfo = val
      form.value.validateField('companyId', () => {})
    }
    // 单位ID模糊搜索 end

    // 业务员模糊搜索 start
    const handleAdminChange = (kw: any, callback) => {
      state.formData.adminId = -1
      state.adminInfo.username = kw
      getAdminList({ name: kw }).then((resp: any) => {
        callback(resp)
        if (!state.adminInfo.username.includes(kw) && !resp.length) {
          state.formData.adminId = -1
          setTimeout(() => {
            form.value.validateField('adminId', () => {})
          }, 210)
        }
      })
    }
    const handleAdminSelect = (val: any) => {
      if (!val) {
        state.formData.adminId = ''
        return
      }
      state.formData.adminId = val.id
      state.adminInfo = val
      form.value.validateField('adminId', () => {})
    }
    // 业务员模糊搜索 end

    const handleClose = () => {
      form.value.resetFields()
      nextTick(() => {
        form.value.clearValidate()
      })
      state.companyInfo = {
        fullName: ''
      }
      state.adminInfo = {
        username: ''
      }
      state.visible = false
    }

    const submit = () => {
      form.value.validate((valid: boolean) => {
        if (!valid) return
        state.loading = true
        companyAdminChange(state.formData)
          .then(() => {
            state.loading = false
            handleClose()
            emit('update')
          })
          .catch(() => {
            state.loading = false
          })
      })
    }

    return {
      form,
      open,
      submit,
      handleClose,
      formRules,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleAdminChange,
      handleAdminSelect,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.name {
  left: calc(100% + 10px);
  white-space: nowrap;
}
</style>
