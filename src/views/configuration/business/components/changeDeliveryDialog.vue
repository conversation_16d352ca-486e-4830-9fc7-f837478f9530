<template>
  <div>
    <el-dialog v-model="visible" title="单位投递配置" width="520px" @close="handleClose">
      <el-form :model="formData" :rules="formRules" ref="form">
        <el-form-item label="单位信息" prop="companyId">
          <div class="ai-center p-relative">
            <InputAutocomplete
              :value="companyInfo.companyName"
              value-key="companyName"
              placeholder="请填写单位ID或名称"
              @change="handleCompanyIdChange"
              @select="handleCompanyIdSelect"
            >
              <template #default="{ row }">
                <div>{{ row.fullName }}</div>
              </template>
            </InputAutocomplete>
            <div class="name p-absolute opacity-80 fs-13">{{ companyInfo.deliveryTypeTxt }}</div>
          </div>
        </el-form-item>
        <el-form-item label="投递类型" prop="type">
          <el-radio-group v-model="formData.type" class="ml-4">
            <el-radio v-for="item in list" :key="item.k" :label="item.k" size="large">{{
              item.v
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-radio-group class="ml-4">
            <el-input v-model="formData.remark" maxlength="30" />
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="flex center">
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ElMessageBox } from 'element-plus'
import { defineComponent, nextTick, reactive, ref, toRefs } from 'vue'
import {
  changeCompanyDelivery,
  getCompanyDeliverySearch,
  getCompanyList
} from '/@/api/configuration'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

export default defineComponent({
  name: 'changeDeliveryDialog',
  emits: ['update'],
  components: { InputAutocomplete },

  setup(props, { emit }) {
    const validateCompany = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写单位信息'))
        return
      }
      if (value === -1) {
        callback(new Error('单位信息有误'))
      } else {
        callback()
      }
    }

    const state = reactive({
      formData: {
        companyId: <string | number>'',
        type: <string>'',
        remark: ''
      },
      companyInfo: {
        companyName: '',
        deliveryTypeTxt: ''
      },
      visible: false,
      list: [],
      formRules: {
        companyId: [{ validator: validateCompany, trigger: 'blur' }]
      }
    })

    const form = ref()

    const handleClose = () => {
      form.value.resetFields()
      nextTick(() => {
        form.value.clearValidate()
      })
      state.companyInfo.companyName = ''
      state.companyInfo.deliveryTypeTxt = ''
      state.visible = false
    }

    const submit = () => {
      ElMessageBox.confirm('配置变更成功将立即生效，确定要变更吗？')
        .then(async () => {
          await changeCompanyDelivery(state.formData)
          emit('update')
          handleClose()
        })
        .catch(() => {})
    }

    const open = () => {
      state.visible = true
    }

    const handleCompanyIdChange = (val: string, callback) => {
      state.companyInfo.companyName = val
      getCompanyList({ name: val }).then((resp: any) => {
        if (!resp.length) {
          state.formData.companyId = -1
          setTimeout(() => {
            form.value.validateField('companyId', () => {})
          }, 210)
        }
        callback(resp)
      })
    }

    const getCheck = async () => {
      const { deliveryList } = await getCompanyDeliverySearch()
      state.list = deliveryList
    }

    getCheck()

    const handleCompanyIdSelect = (val: any) => {
      if (!val) {
        state.formData.companyId = ''
        return
      }
      state.formData.companyId = val.id
      state.companyInfo.companyName = val.fullName
      state.companyInfo.deliveryTypeTxt = val.deliveryTypeTxt
      form.value.validateField('companyId', () => {})
    }

    return {
      ...toRefs(state),
      handleClose,
      form,
      open,
      submit,
      handleCompanyIdChange,
      handleCompanyIdSelect
    }
  }
})
</script>

<style lang="scss" scoped>
.name {
  left: calc(100% + 10px);
  white-space: nowrap;
}
</style>
