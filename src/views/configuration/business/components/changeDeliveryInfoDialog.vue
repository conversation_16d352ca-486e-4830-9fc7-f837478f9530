<template>
  <div>
    <el-dialog title="配置变更详情" v-model="infoVisible" width="500px">
      <div class="mt-20">单位信息：{{ companyInfo.name }}（id：{{ companyInfo.companyId }}）</div>
      <div class="mt-20">投递配置：{{ companyInfo.description }}</div>
      <div class="mt-20">备注：{{ companyInfo.remark }}</div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'changeDeliveryInfoDialog',

  components: {},

  setup() {
    const state = reactive({
      infoVisible: false,
      companyInfo: {
        name: '',
        description: '',
        remark: '',
        companyId: ''
      },
      open(info: any) {
        state.companyInfo.name = info.companyName
        state.companyInfo.description = info.descriptionAfter
        state.companyInfo.remark = info.remark
        state.companyInfo.companyId = info.companyId
        state.infoVisible = true
      }
    })
    return { ...toRefs(state) }
  }
})
</script>

<style lang="scss" scoped></style>
