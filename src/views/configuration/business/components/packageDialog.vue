<template>
  <div>
    <el-dialog title="业务配置管理" v-model="visible" width="650px" :close-on-click-modal="false">
      <el-form
        class="pt-10 pr-100 mr-20"
        :model="formData"
        :rules="formRules"
        ref="form"
        label-width="110px"
      >
        <el-form-item label="单位信息：" prop="companyId">
          <div class="ai-center p-relative">
            <InputAutocomplete
              :value="companyInfo.fullName"
              value-key="fullName"
              class="flex-1"
              @change="handleCompanyIdChange"
              @select="handleCompanyIdSelect"
              placeholder="请填写单位ID或名称"
            >
              <template #default="{ row }">
                <div>{{ row.fullName }}</div>
              </template>
            </InputAutocomplete>
            <div class="right p-absolute opacity-80 fs-13">{{ companyInfo.packageTypeName }}</div>
          </div>
        </el-form-item>
        <el-form-item label="会员类型：" prop="isSenior">
          <div class="ai-center p-relative">
            <el-radio-group v-model="formData.isSenior" class="ml-4">
              <el-radio label="1">高级会员</el-radio>
              <el-radio label="2">试用会员</el-radio>
            </el-radio-group>
            <el-popover trigger="hover" placement="left" width="300px">
              <template #reference>
                <div
                  v-show="formData.isSenior == 2"
                  class="right p-absolute opacity-80 fs-13 cursor-pointer color-warning"
                >
                  明细
                </div>
              </template>
              <div class="pl-10 pr-30 pt-10 opacity-80">
                <div class="jc-between mb-10">
                  <span>职位发布个数(条)</span>{{ trialInfo.jobAmount }}
                </div>
                <div class="jc-between mb-10">
                  <span>公告&简章发布数(条)</span>{{ trialInfo.announcementAmount }}
                </div>
                <div class="jc-between mb-10">
                  <span>职位刷新次数(次)</span>{{ trialInfo.jobRefreshAmount }}
                </div>
                <div class="jc-between mb-10">
                  <span>公告&简章刷新次数(次)</span>{{ trialInfo.announcementRefreshAmount }}
                </div>
                <div class="jc-between mb-10">
                  <span>简历下载点数(点)</span>{{ trialInfo.baseResumeDownloadAmount }}
                </div>
                <div class="jc-between mb-10">
                  <span>直聊沟通(点)</span>{{ trialInfo.baseResumeDownloadAmount }}
                </div>
                <div class="jc-between mb-10"><span>人才库</span>无</div>
              </div>
            </el-popover>
          </div>
        </el-form-item>

        <template v-if="formData.isSenior == 1">
          <el-form-item label="权益包类型：" prop="packageId">
            <div class="ai-center p-relative">
              <el-select class="flex-1" v-model="formData.packageId" filterable clearable>
                <el-option
                  v-for="(item, index) in packageList"
                  :label="item.name"
                  :value="item.id"
                  :key="index"
                  >{{ item.name }}</el-option
                >
              </el-select>
              <el-popover trigger="hover" placement="right" width="300px">
                <template #reference>
                  <div
                    v-show="formData.packageId"
                    class="right p-absolute opacity-80 fs-13 cursor-pointer color-warning"
                  >
                    明细
                  </div>
                </template>
                <div class="pl-10 pr-30 pt-10 opacity-80">
                  <div class="jc-between mb-10">
                    <span>职位发布个数(条)</span>{{ packageInfo.jobAmount }}
                  </div>
                  <div class="jc-between mb-10">
                    <span>公告&简章发布数(条)</span>{{ packageInfo.announcementAmount }}
                  </div>
                  <div class="jc-between mb-10">
                    <span>职位刷新次数(次)</span>{{ packageInfo.jobRefreshAmount }}
                  </div>
                  <div class="jc-between mb-10">
                    <span>公告&简章刷新次数(次)</span>{{ packageInfo.announcementRefreshAmount }}
                  </div>
                  <div class="jc-between mb-10">
                    <span>简历下载点数(点)</span>{{ packageInfo.resumeDownloadAmount }}
                  </div>
                  <div class="jc-between mb-10">
                    <span>直聊沟通(点)</span>{{ packageInfo.chatAmount }}
                  </div>
                  <div class="jc-between mb-10"><span>人才库</span>有</div>
                  <div class="jc-between mb-10">
                    <span>短信条数</span>{{ packageInfo.smsAmount }}
                  </div>
                </div>
              </el-popover>
            </div>
          </el-form-item>
          <el-form-item label="权益包数：">
            <div class="ai-center p-relative">
              <el-input
                disabled
                v-model="packageAmount"
                placeholder="权益包数"
                clearable
              ></el-input>
              <div class="right p-absolute opacity-80 fs-13">个</div>
            </div>
          </el-form-item>
          <el-form-item label="有效期：" prop="packageAmount">
            <div class="ai-center p-relative">
              <el-input
                v-model.trim="formData.packageAmount"
                placeholder="请填写有效期"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                clearable
              ></el-input>
              <div class="right p-absolute opacity-80 fs-13">月</div>
            </div>
          </el-form-item>
        </template>
        <template v-if="formData.isSenior == 2">
          <el-form-item label="有效期：" prop="packageAmount"> 15天 </el-form-item>
        </template>

        <el-form-item label="生效时间：" prop="effectTime">
          <DatePicker
            :disabledDate="handleDisabledDate"
            placeholder="生效时间"
            v-model="formData.effectTime"
          />
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            rows="4"
            resize="none"
            placeholder="备注"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleComfirm">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 再次弹框确认 -->
    <el-dialog
      title="业务配置管理"
      v-model="confirmVisible"
      width="650px"
      :close-on-click-modal="false"
    >
      <div class="color-warning ta-center pb-20 pt-10 fw-bold">
        *配置成功后将不可撤回，请谨慎配置
      </div>
      <div class="px-20">
        <el-row class="mb-15" :gutter="10">
          <el-col class="ta-right" :span="4">用户名称：</el-col>
          <el-col class="opacity-70" :span="20">{{ companyInfo.fullName }}</el-col>
        </el-row>
        <el-row class="mb-15" :gutter="10">
          <el-col class="ta-right" :span="4">会员类型：</el-col>
          <el-col class="opacity-70" :span="20">{{
            formData.isSenior == 1 ? `高级会员(${reviewPackageInfo.name})` : '试用会员'
          }}</el-col>
        </el-row>
        <el-row class="mb-5" :gutter="10">
          <el-col class="ta-right" :span="4">权益明细：</el-col>
          <el-col :span="20" class="opacity-70">
            <el-row class="mb-10">
              <el-col :span="14">职位发布个数(条)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(reviewPackageInfo.baseJobAmount, reviewPackageInfo.jobAmount)
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">公告&简章发布数(条)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(
                    reviewPackageInfo.baseAnnouncementAmount,
                    reviewPackageInfo.announcementAmount
                  )
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">职位刷新次数(次)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(
                    reviewPackageInfo.baseJobRefreshAmount,
                    reviewPackageInfo.jobRefreshAmount
                  )
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">公告&简章刷新次数(次)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(
                    reviewPackageInfo.baseAnnouncementRefreshAmount,
                    reviewPackageInfo.announcementRefreshAmount
                  )
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">简历下载点数(点)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(
                    reviewPackageInfo.baseResumeDownloadAmount,
                    reviewPackageInfo.resumeDownloadAmount
                  )
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">直聊沟通(点)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(
                    reviewPackageInfo.baseChatAmount,
                    reviewPackageInfo.chatAmount
                  )
                "
              ></el-col
            ></el-row>
            <el-row class="mb-10">
              <el-col :span="14">短信条数(条)</el-col>
              <el-col
                :span="10"
                v-html="
                  handlePackageDetail(reviewPackageInfo.baseSmsAmount, reviewPackageInfo.smsAmount)
                "
              ></el-col>
            </el-row>
            <el-row class="mb-10">
              <el-col :span="14">人才库</el-col>
              <el-col :span="10">{{ formData.isSenior == 1 ? '有' : '无' }}</el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row class="mb-15" :gutter="10">
          <el-col class="ta-right" :span="4">生效时间：</el-col>
          <el-col :span="20" class="opacity-70">{{ formData.effectTime }}</el-col>
        </el-row>
        <el-row v-show="formData.isSenior == 1" class="mb-15" :gutter="10">
          <el-col class="ta-right" :span="4">权益包数：</el-col>
          <el-col :span="20" class="opacity-70">{{ formData.packageAmount }} 个</el-col>
        </el-row>
        <el-row class="mb-15" :gutter="10">
          <el-col class="ta-right" :span="4">有效期：</el-col>
          <el-col :span="20" class="opacity-70">{{
            formData.isSenior == 1 ? `${formData.packageAmount}个月` : '15天'
          }}</el-col>
        </el-row>
        <el-row class="py-10">
          <el-col :offset="4">
            <el-button :loading="loading" type="primary" @click="submit">确定配置</el-button>
            <el-button @click=";(visible = true), (confirmVisible = false)">返回</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, ref, onMounted, computed, nextTick } from 'vue'
import DatePicker from '/@/components/base/datePicker.vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

import {
  getCompanyList,
  getCompanyPackageList,
  addConfigurationPackage
} from '/@/api/configuration'

export default defineComponent({
  name: '',
  components: { DatePicker, InputAutocomplete },
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      confirmVisible: false,
      loading: false,
      formData: {
        companyId: '',
        isSenior: '1',
        packageId: '',
        packageAmount: 1,
        effectTime: '',
        remark: ''
      },
      // 权益包数
      packageAmount: computed(() => {
        return state.formData.packageAmount > 0 ? state.formData.packageAmount : 1
      }),
      // 权益包列表
      packageList: [],
      // 试用会员明细
      trialInfo: {},
      // 权益包某套餐信息
      packageInfo: computed(() => {
        const [data = {}] = state.packageList.filter(
          (item: any) => item.id === state.formData.packageId
        )
        const { detail = {} } = data
        return detail
      }),
      // 单位信息
      companyInfo: <any>{
        fullName: ''
      },
      reviewPackageInfo: computed(() => {
        const { packageInfo, trialInfo } = state
        const { isSenior } = state.formData
        return /1/.test(isSenior) ? packageInfo : trialInfo
      })
    })

    const validateCompanyId = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写单位'))
        return
      }
      const { packageType } = <any>state.companyInfo
      /**
       * packageType 1 免费会员、2 高级会员、3过期会员、4 试用会员
       */
      const notConfiguration = [2, 4]
      if (notConfiguration.includes(packageType)) {
        callback(new Error('当前企业套餐还在使用中，不可配置'))
        return
      }
      if (value === -1) {
        callback(new Error('该单位不存在，请重新选择'))
      } else {
        callback()
      }
    }

    const formRules = ref({
      companyId: [{ validator: validateCompanyId, trigger: 'blur' }],
      packageId: [{ required: true, message: '请选择权益包类型', trigger: 'change' }],
      packageAmount: [
        {
          required: true,
          pattern: /^[1-9]\d*$/,
          message: '请填写正整数有效期',
          trigger: ['blur', 'change']
        }
      ],
      effectTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }]
    })

    const getPackageList = () => {
      getCompanyPackageList().then((resp: any) => {
        const [packageList = [], trial = []] = resp
        const [trialInfo = {}] = trial
        const { detail } = trialInfo
        state.packageList = packageList
        state.trialInfo = detail
      })
    }

    onMounted(() => {
      getPackageList()
    })

    const open = () => {
      state.visible = true
      state.companyInfo = {
        fullName: ''
      }
      nextTick(() => {
        form.value.resetFields()
        nextTick(() => {
          form.value.clearValidate()
        })
      })
    }

    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (kw: any, callback) => {
      state.formData.companyId = -1
      state.companyInfo.fullName = kw
      getCompanyList({ name: kw }).then((resp: any) => {
        callback(resp)
        if (!state.companyInfo.fullName.includes(kw) && !resp.length) {
          state.formData.companyId = -1
          setTimeout(() => {
            form.value.validateField('companyId', () => {})
          }, 210)
        }
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (val) {
        state.companyInfo = val
        state.formData.companyId = val.id
      }
      form.value.validateField('companyId', () => {})
    }
    // 单位ID模糊搜索 end

    const handlePackageDetail = (baseAmount, amount) => {
      const { isSenior, packageAmount } = state.formData
      const str = /1/.test(isSenior) ? `${baseAmount}+${amount}*${packageAmount}` : amount
      return str
    }

    const handleClose = () => {
      state.visible = false
      state.confirmVisible = false
    }

    const handleComfirm = () => {
      form.value.validate((valid: boolean) => {
        if (!valid) return
        state.confirmVisible = true
        state.visible = false
      })
    }

    const submit = () => {
      state.loading = true
      /**
       * isSenior 1 高级会员，2试用会员
       */
      const { isSenior, companyId, effectTime, remark, ...other } = state.formData
      const formData = <any>{ isSenior, companyId, effectTime, remark }

      if (/1/.test(isSenior)) {
        Object.keys(other).forEach((key: string) => {
          const value = other[key]
          formData[key] = value
          if (/packageAmount/.test(key)) {
            formData.packageAmountDay = value * 31
          }
        })
      }
      addConfigurationPackage(formData)
        .then(() => {
          state.loading = false
          handleClose()
          emit('update')
        })
        .catch(() => {
          state.loading = false
        })
    }

    return {
      form,
      open,
      handleDisabledDate,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleComfirm,
      submit,
      handleClose,
      formRules,
      handlePackageDetail,
      ...toRefs(state)
    } as any
  }
})
</script>

<style scoped lang="scss">
.right {
  left: calc(100% + 10px);
  white-space: nowrap;
}
</style>
