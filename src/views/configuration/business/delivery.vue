<template>
  <div>
    <el-form ref="form" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-6 center" label="单位Id" prop="companyId">
          <el-input v-model="formData.companyId" class="flex-1" placeholder="请填写单位ID">
          </el-input>
        </el-form-item>
        <el-form-item class="span-6 center" label="单位名称" prop="companyName">
          <el-input
            v-model="formData.companyName"
            class="flex-1"
            placeholder="请填写单位名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="span-6 center"
          label="变更后配置"
          label-width="95px"
          prop="descriptionAfter"
        >
          <Description v-model="formData.descriptionAfter" />
        </el-form-item>
        <el-form-item class="span-6 center" label="操作人" prop="userName">
          <el-input placeholder="请填写操作人姓名" clearable v-model="formData.userName"></el-input>
        </el-form-item>
        <el-form-item class="span-6 center" label="操作时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-4 center" label-width="20px">
          <el-button type="primary" @click="getList">搜 索</el-button>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="openDialog">投递配置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div class="jc-between amount">
      <div>
        共计:
        <span class="danger">{{ pages.total }}</span>
        条记录
      </div>
    </div>
    <el-table border :data="tableData" v-loading="tableLoading">
      <el-table-column align="center" label="单位ID" prop="companyId"></el-table-column>
      <el-table-column align="center" label="单位名称" prop="companyName"></el-table-column>
      <el-table-column align="center" label="变更前配置" prop="descriptionBefore"></el-table-column>
      <el-table-column align="center" label="变更后配置" prop="descriptionAfter"></el-table-column>
      <el-table-column align="center" label="操作时间" prop="addTime"></el-table-column>
      <el-table-column align="center" label="操作人" prop="userName"></el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="primary" @click="openInfoDialog(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Paging class="mt-20" :total="pages.total" @change="handlePaginationChange" />
    <ChangeDeliveryDialog ref="dialogVisible" @update="getList" />
    <ChangeDeliveryInfoDialog ref="changeDeliveryInfoDialog" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue'
import ChangeDeliveryDialog from './components/changeDeliveryDialog.vue'
import ChangeDeliveryInfoDialog from './components/changeDeliveryInfoDialog.vue'
import { getCompanyDeliveryChangeLogList } from '/@/api/configuration'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import Description from '/@/components/base/select/description.vue'

export default defineComponent({
  name: 'delivery',

  components: {
    DatePickerRange,
    Paging,
    ChangeDeliveryDialog,
    ChangeDeliveryInfoDialog,
    Description
  },

  setup() {
    const state = reactive({
      formData: {
        companyId: '',
        companyName: '',
        descriptionAfter: '',
        userName: '',
        addTimeStart: '',
        addTimeEnd: '',
        page: 1,
        pageSize: 20
      },
      tableData: [],
      pages: {
        total: null
      },
      tableLoading: false
    })
    const dialogVisible = ref()
    const changeDeliveryInfoDialog = ref()
    const form = ref()

    const getList = async () => {
      state.tableLoading = true
      const { list, pages } = await getCompanyDeliveryChangeLogList(state.formData)
      state.tableData = list
      state.pages.total = pages.total
      state.tableLoading = false
    }

    getList()
    const openDialog = () => {
      dialogVisible.value.open()
    }
    const openInfoDialog = (info: any) => {
      changeDeliveryInfoDialog.value.open(info)
    }

    const reset = () => {
      form.value.resetFields()
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getList()
    }

    return {
      ...toRefs(state),
      dialogVisible,
      openDialog,
      getList,
      reset,
      form,
      openInfoDialog,
      changeDeliveryInfoDialog,
      handlePaginationChange
    }
  }
})
</script>

<style lang="scss" scoped>
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: red;
  }
}
</style>
