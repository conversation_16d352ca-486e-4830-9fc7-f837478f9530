<template>
  <div class="box">
    <el-tabs v-model="activeName" class="mb-10">
      <el-tab-pane name="Salesman" label="单位归属业务员变更"></el-tab-pane>
      <el-tab-pane name="Package" label="单位业务权益配置"></el-tab-pane>
      <el-tab-pane name="delivery" label="单位投递配置"></el-tab-pane>
    </el-tabs>
    <component :is="activeName"></component>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs } from 'vue'
import Delivery from './delivery.vue'
import Salesman from './salesman.vue'
import Package from './package.vue'

export default {
  name: 'configurationBusinessIndex',
  components: { Salesman, Package, Delivery },
  setup() {
    const state = reactive({
      activeName: 'Salesman'
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 5px 15px 20px;
}
</style>
