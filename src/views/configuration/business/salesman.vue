<template>
  <div>
    <el-form ref="form" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-5" label="单位信息" prop="name">
          <InputAutocomplete
            :value="companyInfo.fullName"
            value-key="fullName"
            class="flex-1"
            @change="handleCompanyIdChange"
            @select="handleCompanyIdSelect"
            placeholder="请填写单位ID或名称"
          >
            <template #default="{ row }">
              <div>{{ row.fullName }}</div>
            </template>
          </InputAutocomplete>
        </el-form-item>
        <el-form-item class="span-5" label="操作人" prop="handler">
          <el-input
            v-model="formData.handler"
            placeholder="请填写操作人账号"
            clearable
            @keyup.enter="getList"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="操作时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="flex-1" label-width="10px">
          <el-button type="primary" @click="getList">搜 索</el-button>
          <el-button @click="handleResetField">重 置</el-button>
          <el-button @click="handleDownload">下 载</el-button>
          <el-button @click="handleOpen" type="primary">业务配置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table class="mt-5" :data="list" border size="small" v-loading="loading">
      <el-table-column
        prop="companyUid"
        align="center"
        header-align="center"
        label="单位ID"
        show-overflow-tooltip
      />
      <el-table-column
        prop="companyName"
        align="center"
        header-align="center"
        label="单位名称"
        show-overflow-tooltip
      />
      <el-table-column
        prop="style"
        align="center"
        header-align="center"
        label="配置类型"
        show-overflow-tooltip
      />
      <el-table-column
        prop="handleBefore"
        align="center"
        header-align="center"
        label="变更前"
        show-overflow-tooltip
      />
      <el-table-column
        prop="handleAfter"
        align="center"
        header-align="center"
        label="变更后"
        show-overflow-tooltip
      />
      <el-table-column
        prop="addTime"
        align="center"
        header-align="center"
        label="操作时间"
        show-overflow-tooltip
      />
      <el-table-column
        prop="handler"
        align="center"
        header-align="center"
        label="操作人"
        show-overflow-tooltip
      />
      <el-table-column
        prop="remark"
        align="center"
        header-align="center"
        label="备注"
        show-overflow-tooltip
      />
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-show="list.length"
      @change="handlePaginationChange"
      :total="pagination.total"
      class="mt-15"
    />
    <SalesmanChangeDialog @update="getList()" ref="salesmanChangeDialog" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, onMounted, nextTick } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import Pagination from '/@/components/base/paging.vue'
import SalesmanChangeDialog from './components/salesmanChangeDialog.vue'
import { getCompanyList, getCompanyAdminChangeLog } from '/@/api/configuration'

export default {
  components: { DatePickerRange, InputAutocomplete, SalesmanChangeDialog, Pagination },
  emits: [],
  setup() {
    const form = ref()
    const salesmanChangeDialog = ref()

    const state = reactive({
      loading: false,
      companyInfo: <any>{
        fullName: ''
      },
      formData: {
        name: '',
        handler: '',
        addTimeStart: '',
        addTimeEnd: '',
        export: 2, // 1是 2否
        page: 1,
        limit: 20
      },
      pagination: {
        total: 0
      },
      list: []
    })

    const getList = () => {
      state.loading = true
      getCompanyAdminChangeLog(state.formData).then((resp: any) => {
        state.loading = false
        if (state.formData.export === 1) {
          state.formData.export = 2
          const aEl = document.createElement('a')
          aEl.setAttribute('href', resp.excelUrl)
          aEl.click()
          return
        }
        state.list = resp.list
        state.pagination.total = resp.page.count
      })
    }

    onMounted(() => {
      getList()
    })

    const handleResetField = () => {
      form.value.resetFields()
      state.companyInfo.fullName = ''
      nextTick(() => {
        getList()
      })
    }

    const handleOpen = () => {
      salesmanChangeDialog.value.open()
    }

    const handleDownload = () => {
      state.formData.export = 1
      getList()
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (val: any, callback) => {
      state.formData.name = val
      state.companyInfo.fullName = val
      getCompanyList({ name: val }).then((resp: any) => {
        callback(resp)
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (!val) return
      state.formData.name = val.id
      state.companyInfo = val
    }
    // 单位ID模糊搜索 end

    return {
      form,
      salesmanChangeDialog,
      getList,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleResetField,
      handleDownload,
      handleOpen,
      handlePaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
