<template>
  <div class="list-container">
    <el-form label-width="100px" :model="formData" ref="formRef">
      <div class="flex">
        <el-form-item class="span-6" label-width="0px">
          <mobile v-model="mobileData" />
        </el-form-item>

        <el-form-item class="span-6" label="用户检索" prop="user">
          <el-input v-model="formData.user" placeholder="请输入用户姓名/ID" clearable />
        </el-form-item>

        <el-form-item class="span-4" label="创建时间" prop="addTimeStart">
          <datePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>

        <el-form-item class="span-6" label="审核状态" prop="status">
          <el-select v-model="formData.status" filterable clearable>
            <el-option v-for="item in auditList" :key="item.k" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>

        <div class="button-group">
          <el-button type="primary" @click="getList">搜索 </el-button>
          <el-button @click="handleReset">重置 </el-button>
          <el-button @click="handleDownload">下载 </el-button>
          <el-button type="primary" @click="handleOpenSetMealDialog">套餐配置 </el-button>
        </div>
      </div>

      <div class="flex">
        <el-form-item class="span-6" label="产品名称" prop="equityPackageId">
          <el-select v-model="formData.equityPackageId" filterable clearable>
            <el-option v-for="item in productList" :key="item.k" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="span-6" label="订单检索" prop="tradeNo">
          <el-input v-model="formData.tradeNo" placeholder="请输入订单号" clearable />
        </el-form-item>

        <el-form-item class="span-4" label="开通时间" prop="openTimeStart">
          <datePickerRange
            v-model:start="formData.openTimeStart"
            v-model:end="formData.openTimeEnd"
          />
        </el-form-item>

        <el-form-item class="span-6" label="是否退款" prop="isRefund">
          <el-select v-model="formData.isRefund" filterable clearable>
            <el-option v-for="item in resumeStatus" :key="item.k" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <el-table class="mt-20" border :data="tableData" v-loading="loading">
      <el-table-column label="订单号" prop="tradeNo" />
      <el-table-column label="产品ID" prop="equityPackageId" />
      <el-table-column label="产品名称" prop="equityPackageName" />
      <el-table-column label="订单金额" prop="realAmount" />
      <el-table-column label="退款金额" prop="refundAmount" />
      <el-table-column label="实付金额" prop="realityAmount" />
      <el-table-column label="用户ID" prop="resumeUuid" />
      <el-table-column label="姓名" prop="name" />
      <el-table-column label="手机号" prop="mobileText" />
      <el-table-column label="创建时间" prop="addTime" />
      <el-table-column label="开通时间" prop="openTime" />
      <el-table-column label="审核状态" prop="statusText" />
      <el-table-column label="是否退款" prop="isRefundText" />
      <el-table-column label="操作人" prop="submitAdminName" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-link
            class="ml-10"
            v-for="item in handleButtonGroup[`group_${row.status}`]"
            :key="item.key"
            @click="item.event(row.id)"
            v-show="item.show"
            :underline="false"
            type="primary"
            >{{ item.label }}</el-link
          >
        </template>
      </el-table-column>
    </el-table>

    <Paging class="mt-20 center" :total="total" :page="formData.page" @change="handlePageChange" />

    <setMealDialog v-model="setMealDialogVisible" :id="mealId" @update="getList" />
    <setMealDetail
      v-model="setMealDetailVisible"
      :id="mealId"
      :audit="mealAudit"
      @update="getList"
    />
    <setMealRefundDialog
      v-model="setMealRefundVisible"
      :id="mealId"
      @update="getList"
      :audit="mealAudit"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import {
  getSetMealParamsList,
  getSetMealList,
  getSetMealListFile,
  delMealSetting
} from '/@/api/configuration'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import mobile from '/@select/mobile.vue'
import { ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store'
import setMealDialog from './components/setMealDialog.vue'
import setMealDetail from './components/setMealDetail.vue'
import setMealRefundDialog from './components/setMealRefundDialog.vue'

const route = <any>useRoute()

const store = useStore()

const mobileData = ref({
  mobile: '',
  mobileCode: '+86'
})

const formRef = ref()

const requestOldRoutesAction = <any>(
  computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
)

const buttonLimit = ref(!!requestOldRoutesAction.value.setMeal)

const setMealDialogVisible = ref(false)
const setMealDetailVisible = ref(false)
const setMealRefundVisible = ref(false)
const loading = ref(false)
const mealAudit = ref(false)

const auditList = ref([{ k: '', v: '' }])
const productList = ref([{ k: '', v: '' }])
const resumeStatus = ref([{ k: '', v: '' }])

const tableData = ref([])

const total = ref(0)

const mealId = ref(null || '')

const formData = ref({
  mobile: computed(() => mobileData.value.mobile),
  mobileCode: computed(() => mobileData.value.mobileCode),
  user: '',
  status: '',
  isRefund: '',
  equityPackageId: '',
  tradeNo: '',
  addTimeStart: '',
  addTimeEnd: '',
  openTimeStart: '',
  openTimeEnd: '',
  page: 1,
  pageSize: 20
})

const getList = async () => {
  loading.value = true

  const { count, list } = await getSetMealList(formData.value)
  tableData.value = list
  total.value = count

  loading.value = false
}

const handleAudit = (id: string) => {
  mealId.value = id
  mealAudit.value = true
  setMealDetailVisible.value = true
}

const handleEdit = (id: string) => {
  mealId.value = id
  setMealDialogVisible.value = true
}

const handleDelete = (id: string) => {
  ElMessageBox.confirm('删除后将不可撤销，确定要删除配置吗？', '提示')
    .then(async () => {
      await delMealSetting({ id })
      getList()
    })
    .catch(() => {})
}

const handleCheck = (id: string) => {
  mealId.value = id
  mealAudit.value = false
  setMealDetailVisible.value = true
}

const handleRefund = (id: string) => {
  mealId.value = id
  mealAudit.value = false
  setMealRefundVisible.value = true
}

const handleRefundAudit = (id: string) => {
  mealId.value = id
  mealAudit.value = true
  setMealRefundVisible.value = true
}

const handleButtonGroup = ref({
  // 配置待审核
  group_1: [
    {
      label: '审核',
      key: 'audit',
      event: handleAudit,
      show: buttonLimit.value
    }
  ],
  // 退款待审核
  group_2: [
    {
      label: '查看',
      key: 'check',
      event: handleCheck,
      show: true
    },
    {
      label: '审核',
      key: 'audit',
      event: handleRefundAudit,
      show: buttonLimit.value
    }
  ],
  // 审核通过
  group_3: [
    {
      label: '查看',
      key: 'check',
      event: handleCheck,
      show: true
    },
    {
      label: '退款',
      key: 'refund',
      event: handleRefund,
      show: true
    }
  ],
  // 配置驳回
  group_4: [
    {
      label: '编辑',
      key: 'edit',
      event: handleEdit,
      show: true
    },
    {
      label: '删除',
      key: 'delete',
      event: handleDelete,
      show: true
    }
  ],
  // 退款驳回
  group_5: [
    {
      label: '查看',
      key: 'check',
      event: handleCheck,
      show: true
    },
    {
      label: '退款',
      key: 'refund',
      event: handleRefund,
      show: true
    }
  ]
})

const getParamsList = async () => {
  const { auditStatusList, refundList, resumeEquityPackage } = await getSetMealParamsList()

  auditList.value = auditStatusList
  resumeStatus.value = refundList
  productList.value = resumeEquityPackage
}

onMounted(() => {
  if (route.query.id) {
    formData.value.user = route.query.id
  }
  getList()
})

const handleReset = () => {
  formRef.value.resetFields()
  mobileData.value = { mobile: '', mobileCode: '+86' }
  getList()
}

const handleOpenSetMealDialog = () => {
  mealId.value = null
  setMealDialogVisible.value = true
}

const handlePageChange = (val) => {
  formData.value.page = val.page
  formData.value.pageSize = val.limit
  getList()
}

const handleDownload = async () => {
  loading.value = true

  try {
    await getSetMealListFile(formData.value)
  } finally {
    loading.value = false
  }
}

getParamsList()
</script>

<style lang="scss" scoped>
.list-container {
  background-color: #fff;
  padding: 20px;
}

.button-group {
  margin-left: 20px;
}
</style>
