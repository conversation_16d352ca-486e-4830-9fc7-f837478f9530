<template>
  <div class="rufund">
    <el-dialog :title="title" v-model="dialogVisible" width="30%" @close="close">
      <el-form :model="formData" :rules="formRules" ref="formRef">
        <div class="color-danger mb-10" v-if="formData.refundRejectRemark && !audit">
          审核驳回原因： {{ formData.refundRejectRemark }}
        </div>
        <el-form-item label="退款金额" prop="auditRefundAmount">
          <el-input
            placeholder="请输入订单金额"
            @input="validateMount"
            v-model="formData.auditRefundAmount"
            :disabled="audit"
          />
        </el-form-item>

        <el-form-item label="退款原因" prop="refundRemark">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="200"
            v-model="formData.refundRemark"
            :disabled="audit"
          />
        </el-form-item>

        <el-form-item label="退款凭证" prop="refundFileIds">
          <el-upload
            class="upload-demo"
            action="/upload/image"
            :file-list="fileList"
            :limit="5"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :disabled="audit"
          >
            <el-button type="primary" :disabled="audit">+ 上传</el-button>
            <template #tip>
              <div class="el-upload__tip">支持jpg、png、jpeg格式</div>
            </template>
          </el-upload>
        </el-form-item>

        <div class="file-list">
          <div class="file" v-for="item in fileList" :key="item.id">
            <div @click="handlePriview(item.fullUrl)">{{ item.name }}</div>
            <span @click="handleUploadRemove(item.id)" v-if="!audit">X</span>
          </div>
        </div>

        <el-form-item>
          <el-button type="primary" @click="handleSumbit">{{ confirmText }}</el-button>
          <el-button @click="cancel">{{ cancelText }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <div class="mask">
      <div class="mask-cover" v-if="licenseShow" @click="licenseShow = false"></div>

      <div class="mask-content" v-if="licenseShow">
        <img class="preImg" :src="priviewUrl" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { verifyNumberIntegerAndFloat } from '/@/utils/toolsValidate'
import { auditRefund, getRefundMessage, submitRefund } from '/@/api/configuration'
import { ElMessageBox } from 'element-plus'

const emits = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  id: {
    type: String,
    default: null
  },

  audit: {
    type: Boolean,
    default: false
  }
})

const formRef = ref()

const fileList = <any>ref([])

const id = <any>computed(() => props.id)

const licenseShow = ref(false)

const priviewUrl = ref('')

const title = computed(() => (props.audit ? '退款审核' : '退款录入'))

const confirmText = computed(() => (props.audit ? '审核通过' : '确定'))
const cancelText = computed(() => (props.audit ? '审核拒绝' : '取消'))

const formData = <any>ref({
  id: computed(() => id.value),
  auditRefundAmount: '',
  refundFileIds: '',
  refundRemark: ''
})

const amontValidate = (rule, value, callback) => {
  const { auditRefundAmount, realAmount } = formData.value
  if (auditRefundAmount === '') {
    callback('请录入退款金额')
    return
  }

  if (Number(auditRefundAmount) > Number(realAmount)) {
    callback('退款金额不得高于订单金额')
  }

  callback()
}

const formRules = ref({
  auditRefundAmount: [{ required: true, validator: amontValidate, trigger: 'blur' }],
  refundRemark: [{ required: true, message: '请输入退款原因', trigger: 'blur' }],
  refundFileIds: [{ required: true, message: '请录入退款凭证', trigger: 'change' }]
})

const getDetail = async () => {
  formData.value = await getRefundMessage({ id: id.value })
  fileList.value = formData.value.refundFileList
}

const dialogVisible = computed({
  get() {
    if (props.modelValue) {
      getDetail()
    }
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const close = () => {
  formRef.value.resetFields()
  fileList.value = []
  dialogVisible.value = false
}

const cancel = () => {
  if (props.audit) {
    ElMessageBox.prompt('', '请录入驳回原因', {
      inputPattern: /^(.+)$/,
      inputErrorMessage: '请录入驳回原因'
    })
      .then(async ({ value }) => {
        await auditRefund({ id: id.value, status: 5, refundRejectRemark: value })
        close()
        emits('update')
      })
      .catch(() => {})
    return
  }

  close()
}

const validateMount = () => {
  formData.value.auditRefundAmount = verifyNumberIntegerAndFloat(formData.value.auditRefundAmount)
}

const handlePriview = (url: string) => {
  priviewUrl.value = url
  licenseShow.value = true
}

const handleUploadRemove = (uid: string) => {
  fileList.value = fileList.value.filter((item: any) => item.id !== uid)
}

const handleUploadSuccess = (res) => {
  fileList.value = [
    { name: res.data.name, id: res.data.id, fullUrl: res.data.fullUrl },
    ...fileList.value
  ]
  formData.value.refundFileIds = fileList.value.map((item: any) => item.id).join()
}

const handleSumbit = () => {
  formRef.value.validate().then(async () => {
    const api = props.audit ? auditRefund : submitRefund
    const postData = props.audit
      ? { id: id.value, status: 3 }
      : {
          id: id.value,
          auditRefundAmount: formData.value.auditRefundAmount,
          refundFileIds: fileList.value.map((item: any) => item.id).join(),
          refundRemark: formData.value.refundRemark
        }

    await api(postData)
    emits('update')
    close()
  })
}
</script>

<style lang="scss" scoped>
.file-list {
  padding: 0 80px;
  .file {
    display: flex;
    justify-content: space-between;
    text-decoration: none;
    cursor: pointer;
    margin-bottom: 10px;
    color: #409eff;
    span {
      cursor: pointer;
    }
  }
}

.tips {
  color: var(--el-color-danger);
  font-size: 12px;
}

.mask {
  position: relative;
  color: #2e2c2d;
  font-size: 16px;
}

.mask-cover {
  background: rgba($color: #000000, $alpha: 0.5);
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.mask-content {
  position: fixed;
  top: 30%;
  left: 40%;
  height: 70%;
  z-index: 10000;
  .preImg {
    width: 500px;
    height: 500px;
  }
}
</style>
