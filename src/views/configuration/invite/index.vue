<template>
  <div class="main">
    <el-form ref="formVm" :model="formData" label-width="70px">
      <div class="flex">
        <el-col :span="4">
          <el-form-item label="邀请端口" prop="portType">
            <el-select v-model="formData.portType">
              <el-option label="运营端" value="1" />
              <el-option label="单位端" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="人才ID" prop="resumeId">
            <el-input v-model="formData.resumeId" clearable placeholder="请输入人才ID" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="人才姓名" prop="resumeName">
            <el-input v-model="formData.resumeName" clearable placeholder="模糊搜索人才姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="职位ID" prop="jobId">
            <el-input v-model="formData.jobId" clearable placeholder="请输入职位ID" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="职位名称" prop="jobName">
            <el-input v-model="formData.jobName" clearable placeholder="模糊搜索职位名称" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="是否投递" prop="isDelivery">
            <el-select v-model="formData.isDelivery">
              <el-option label="不限" value="-1" />
              <el-option label="是" value="1" />
              <el-option label="否" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
      <div class="flex">
        <!--        过于影响性能，已处理-->
        <!--        <el-col :span="4">-->
        <!--          <el-form-item label="应聘状态" prop="applyStatus">-->
        <!--            <el-select v-model="formData.applyStatus">-->
        <!--              <el-option label="不限" value="-1" />-->
        <!--              <el-option label="已投递" value="1" />-->
        <!--              <el-option label="通过初筛" value="2" />-->
        <!--              <el-option label="邀请面试" value="3" />-->
        <!--              <el-option label="不合适" value="4" />-->
        <!--              <el-option label="已录用" value="5" />-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="4">
          <el-form-item label="邀约时间" prop="inviteTime">
            <DatePickerRange
              v-model:start="formData.inviteTimeStart"
              v-model:end="formData.inviteTimeEnd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="邀约方式" prop="inviteWay">
            <el-select v-model="formData.inviteWay">
              <el-option label="不限" value="-1" />
              <el-option label="手动邀约" value="1" />
              <el-option label="智能邀约" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位检索" prop="inviteUserId">
            <el-input clearable v-model="companyValue" :placeholder="companyPlaceholder">
              <template #prepend>
                <el-select v-model="queryCompany" style="width: 100px">
                  <el-option
                    v-for="item in queryCompanyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <div class="nowrap ml-15">
            <el-button type="primary" size="small" @click="getList">搜索</el-button>
            <el-button size="small" @click="reset">重置</el-button>
            <el-button size="small" @click="downloadExcel">下载</el-button>
          </div>
        </el-col>
      </div>

      <div class="flex">
        <el-col :span="8">
          <el-form-item label="邀约人" prop="inviteUsername">
            <el-input v-model="queryNumberValue" :placeholder="queryNumberPlaceholder" clearable>
              <template #prepend>
                <el-select v-model="queryNumber" style="width: 100px">
                  <el-option
                    v-for="item in queryNumberOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="是否查看" prop="isRemindCheck">
            <el-select v-model="formData.isRemindCheck">
              <el-option label="不限" value="-1" />
              <el-option label="是" value="1" />
              <el-option label="否" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
    </el-form>
    <div class="count">
      <div>
        共计：
        <span class="danger">{{ amount.inviteTotal }}</span
        >条邀请信息； <span class="danger">{{ amount.inviteResumeTotal }}</span
        >位人才； <span class="danger">{{ amount.inviteApplyTotal }}</span
        >次投递；
      </div>
    </div>
    <el-table :data="list" border v-loading="loading" ref="announcementTable">
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="resumeId"
          align="center"
          width="85"
        />

        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="resumeName"
          align="center"
          width="100"
        />

        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="resumeInfo"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="sortRecruitCount"
          width="150"
          align="center"
        >
          <template #default="{ row }"> {{ row.jobName }}({{ row.jobId }}) </template>
        </el-table-column>

        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="inviteAddTime"
          align="center"
          width="180"
        />
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          prop="companyName"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          prop="inviteUsername"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          prop="wayTypeText"
          align="center"
          width="110"
        />
        <el-table-column
          v-if="item.select && item.k === 9"
          :key="index"
          :label="item.v"
          prop="deliveryText"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="item.select && item.k === 10"
          :key="index"
          :label="item.v"
          prop="remindCheckText"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="item.select && item.k === 11"
          :key="index"
          :label="item.v"
          prop="applyAddTime"
          align="center"
          width="180"
        />
        <!--        <el-table-column-->
        <!--          v-if="item.select && item.k === 11"-->
        <!--          :key="index"-->
        <!--          :label="item.v"-->
        <!--          prop="applyStatusText"-->
        <!--          align="center"-->
        <!--          width="110"-->
        <!--        />-->
        <el-table-column
          v-if="item.select && item.k === 12"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          width="100px"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="inviteDetail(row)"> 查看 </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
    <el-dialog width="40%" :title="title" v-model="visible">
      <el-col class="dialog-content-invite">
        <el-row>
          <el-row class="invite-label">邀约职位:</el-row>
          <el-row class="invite-content">{{ toastData.jobName }}({{ toastData.jobId }})</el-row>
        </el-row>
        <el-row>
          <el-row class="invite-label">邀约人才:</el-row>
          <el-row class="invite-content"
            >{{ toastData.resumeName }}({{ toastData.resumeId }})</el-row
          >
        </el-row>
        <el-row>
          <el-row class="invite-label">邀约时间:</el-row>
          <el-row class="invite-content">{{ toastData.inviteAddTime }}</el-row>
        </el-row>
        <el-row>
          <el-row class="invite-label">邀约方式:</el-row>
          <el-row class="invite-content">{{ toastData.wayTypeText }}</el-row>
        </el-row>
        <el-row v-if="toastData.portType === '1'">
          <el-row class="invite-label">邀约文案:</el-row>
          <el-row class="invite-content">{{ toastData.textContent }}</el-row>
        </el-row>
        <el-row>
          <el-row class="invite-label">备注信息:</el-row>
          <el-row class="invite-content">{{ toastData.remark }}</el-row>
        </el-row>
      </el-col>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, computed, unref, onMounted } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessage } from 'element-plus'
import { getInviteList, getInviteListExport } from '../../../api/configuration'

export default {
  name: 'inviteIndex',
  components: {
    DatePickerRange,
    Paging
  },
  setup() {
    const formVm = ref()
    const listHeader = ref([
      {
        k: 1,
        v: '人才ID',
        name: 'resumeId',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '人才姓名',
        name: 'resumeName',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '人才基本信息',
        name: 'resumeInfo',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '邀约投递职位',
        name: 'jobInfo',
        select: true,
        default: false
      },
      {
        k: 5,
        v: '邀约时间',
        name: 'inviteTime',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '所属单位',
        name: 'companyName',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '邀约人',
        name: 'inviteUsername',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '邀约方式',
        name: 'inviteWay',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '是否投递',
        name: 'isDelivery',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '是否查看',
        name: 'isRemindCheck',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '投递时间',
        name: 'deliveryTime',
        select: true,
        default: true
      },
      // {
      //   k: 11,
      //   v: '应聘状态',
      //   name: 'applyStatus',
      //   select: true,
      //   default: true
      // },
      {
        k: 12,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      loading: false,
      visible: false,
      toastData: <any>{},
      title: '',
      list: [],
      pagination: {
        total: 0
      },
      amount: {
        inviteTotal: '0',
        inviteResumeTotal: '0',
        inviteApplyTotal: '0'
      },
      formData: <any>{
        portType: '2',
        resumeId: '',
        resumeName: '',
        jobId: '',
        jobName: '',
        isDelivery: '-1',
        isRemindCheck: '-1',
        inviteUserId: '',
        inviteUsername: '',
        applyStatus: '-1',
        inviteWay: '-1',
        inviteTimeStart: '',
        inviteTimeEnd: '',
        pageSize: 20,
        page: 1
      }
    })

    const findItemData = (target, value) => target.find((item) => item.value === value)

    const queryNumber = ref('1')

    const queryNumberOptions = [
      { label: '姓名', value: '1', key: 'inviteUsername', placeholder: '请填写姓名' },
      { label: '账号ID', value: '2', key: 'inviteUserId', placeholder: '请填写账号ID' }
    ]

    const queryNumberData = computed(() => findItemData(queryNumberOptions, unref(queryNumber)))

    const queryNumberValue = computed({
      get() {
        return state.formData[unref(queryNumberData).key]
      },

      set(value: string) {
        state.formData[unref(queryNumberData).key] = value
      }
    })

    const queryNumberPlaceholder = computed(() => unref(queryNumberData).placeholder)

    const queryCompany = ref('1')

    const queryCompanyOptions = [
      { label: '单位名称', value: '1', key: 'companyName', placeholder: '请填写单位名称' },
      { label: '单位ID', value: '2', key: 'companyId', placeholder: '请填写单位ID' }
    ]

    const queryCompanyData = computed(() => findItemData(queryCompanyOptions, unref(queryCompany)))

    const companyValue = computed({
      get() {
        return state.formData[unref(queryCompanyData).key]
      },

      set(value: string) {
        state.formData[unref(queryCompanyData).key] = value
      }
    })

    const companyPlaceholder = computed(() => unref(queryCompanyData).placeholder)

    const getQuery = () => {
      const { key: numberKey } = unref(queryNumberData)
      const { key: companyKey } = unref(queryCompanyData)

      const {
        portType,
        resumeId,
        resumeName,
        jobId,
        jobName,
        isDelivery,
        isRemindCheck,
        inviteUserId,
        inviteUsername,
        applyStatus,
        inviteWay,
        inviteTimeStart,
        inviteTimeEnd,
        page,
        pageSize
      } = state.formData

      return {
        [numberKey]: state.formData[numberKey],
        [companyKey]: state.formData[companyKey],
        portType,
        resumeId,
        resumeName,
        jobId,
        jobName,
        isDelivery,
        isRemindCheck,
        inviteUserId,
        inviteUsername,
        applyStatus,
        inviteWay,
        inviteTimeStart,
        inviteTimeEnd,
        page,
        pageSize
      }
    }

    const getList = async () => {
      state.loading = true
      const { amount, list, pages } = await getInviteList(getQuery())
      state.list = list
      state.amount = amount
      state.pagination.total = pages.count
      state.loading = false
    }

    const handlePaginationChange = (data) => {
      state.formData.page = data.page
      state.formData.pageSize = data.limit
      getList()
    }

    const reset = () => {
      formVm.value.resetFields()
      state.formData.inviteTimeStart = ''
      state.formData.inviteTimeEnd = ''
      companyValue.value = ''
      queryNumberValue.value = ''
      getList()
    }

    const downloadExcel = async () => {
      state.loading = true
      getInviteListExport({ ...state.formData, export: 1 })
        .then(() => {
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const inviteDetail = (row) => {
      if (state.formData.portType === '1') {
        if (row.wayType === '1') {
          state.title = '运营端手动邀约详情:'
        } else {
          state.title = '运营端智能邀约详情:'
        }
      } else {
        state.title = '单位端邀约详情:'
      }
      row.portType = state.formData.portType
      state.toastData = row
      state.visible = true
    }

    onMounted(() => {
      // 弹窗告诉
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
    })

    return {
      ...toRefs(state),
      formVm,
      listHeader,
      getList,
      handlePaginationChange,
      reset,
      downloadExcel,
      inviteDetail,
      queryNumberValue,
      queryNumberPlaceholder,
      queryNumber,
      queryNumberOptions,
      companyValue,
      queryCompany,
      companyPlaceholder,
      queryCompanyOptions
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}
.count {
  padding: 0 15px;
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
}

.danger {
  color: #d9041a;
  font-weight: bold;
  padding-right: 5px;
}

.dialog-content-invite {
  font-size: 16px;
}

.padding-left-reset {
  :deep(.el-input__wrapper) {
    padding-left: 1px;
  }

  .el-select {
    max-width: 100px;

    :deep(.el-input__wrapper) {
      padding-left: 11px;
    }
  }
}

.invite-label {
  font-weight: bold;
  margin: 8px 8px 8px 15px;
  width: 15%;
}

.invite-content {
  margin: 8px 0;
  width: 78%;
}
</style>
