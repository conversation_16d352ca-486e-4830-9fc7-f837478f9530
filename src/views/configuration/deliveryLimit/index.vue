<template>
  <div class="admin-list">
    <div class="box">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="人才检索">
          <el-input
            v-model="searchForm.resumeKeyword"
            clearable
            placeholder="请输入人才ID或姓名"
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>
        <el-form-item label="单位检索">
          <el-input
            v-model="searchForm.companyKeyword"
            clearable
            placeholder="请输入单位ID或名称"
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>
        <el-form-item label="限制类型">
          <el-select v-model="searchForm.type" placeholder="请选择限制类型" filterable clearable>
            <el-option v-for="item in typeList" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="beginDate">
          <DatePickerRange v-model:start="searchForm.beginDate" v-model:end="searchForm.endDate" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAddAndEdit('add', '')">新增投递限制</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" border class="table" v-loading="tableLoading" size="small">
        <el-table-column align="center" prop="resumeId" label="人才ID">
          <template #default="{ row }">
            <el-tooltip
              popper-class="information-class"
              class="box-item"
              :content="row.resumeIdFull"
              placement="top-start"
            >
              {{ row.resumeId }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="resumeName" label="人才姓名/标签">
          <template #default="{ row }">
            <el-tooltip
              popper-class="information-class"
              class="box-item"
              :content="row.resumeNameFull"
              placement="top-start"
            >
              {{ row.resumeName }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="typeTxt" label="限制类型" />
        <el-table-column align="center" prop="companyName" label="禁投单位/标签">
          <template #default="{ row }">
            <el-tooltip
              popper-class="information-class"
              class="box-item"
              :content="row.companyNameFull"
              placement="top-start"
            >
              {{ row.companyName }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="addTime" label="创建时间" />
        <el-table-column align="center" prop="adminName" label="创建人" />
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="handleAddAndEdit('edit', scope.row)"
              >编辑</el-button
            >
            <el-button type="primary" link size="small" @click="handleDel(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <Paging :total="pages.total" @change="changePage"></Paging>
      </div>
    </div>
    <deliveryDialog
      ref="deliveryRef"
      @confirm="updateList"
      :deliveryData="deliveryData"
      :typeList="typeList"
      :companyTypeList="companyTypeList"
      :resumeTypeList="resumeTypeList"
      :messageTypeList="messageTypeList"
    ></deliveryDialog>
  </div>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import { getLimitlList, getParams, getDetail, jobApplyLimitDel } from '/@/api/configuration'
import deliveryDialog from './component/deliveryDialog.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

export default {
  name: 'positionList',
  components: {
    deliveryDialog,
    Paging,
    DatePickerRange
  },
  setup() {
    const deliveryRef = ref()
    const state = reactive({
      searchForm: {
        resumeKeyword: '',
        companyKeyword: '',
        type: '',
        beginDate: '',
        endDate: '',
        page: 1,
        pageSize: ''
      },
      list: [],
      tableLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      tableData: [],
      typeList: [],
      resumeTypeList: [],
      companyTypeList: [],
      messageTypeList: [],
      deliveryData: {}
    })

    // 搜索
    const search = () => {
      state.tableLoading = true
      getLimitlList(state.searchForm).then((res: any) => {
        state.list = res.list
        state.pages.size = res.pages.size
        state.pages.total = res.pages.count
        state.tableLoading = false
      })
    }

    // 重置
    const resetSearch = () => {
      state.searchForm = {
        resumeKeyword: '',
        companyKeyword: '',
        type: '',
        beginDate: '',
        endDate: '',
        page: 1,
        pageSize: ''
      }
      search()
    }

    const handleAddAndEdit = (type: any, row: any) => {
      if (type === 'edit') {
        getDetail({ id: row.id }).then((res: any) => {
          // console.log(res);
          state.deliveryData = Object.assign(res, { id: row.id })
          // console.log(state.deliveryData);
        })
      } else {
        state.deliveryData.id = ''
      }
      deliveryRef.value.open()
    }

    const handleDel = (row: any) => {
      // 先提示是否确认操作
      const txt = `删除后将不可恢复，确认要删除该次配置吗？`
      ElMessageBox.confirm(txt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        jobApplyLimitDel({ id: row.id }).then(() => {
          search()
        })
      })
    }

    onMounted(() => {
      getParams({}).then((res: any) => {
        state.typeList = res.typeList
        state.resumeTypeList = res.resumeTypeList
        state.companyTypeList = res.companyTypeList
        state.messageTypeList = res.messageTypeList
      })
      search()
    })

    const changePage = (r: any) => {
      state.searchForm.page = r.page
      state.searchForm.pageSize = r.limit
      search()
    }

    const updateList = () => {
      search()
    }

    return {
      ...toRefs(state),
      search,
      resetSearch,
      handleAddAndEdit,
      changePage,
      handleDel,
      deliveryRef,
      updateList
    }
  }
}
</script>

<style scoped lang="scss">
.paging {
  margin-top: 20px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.red {
  color: red;
}
.table {
  margin-top: 10px;
}
.el-card {
  border: none;
  padding: 0 30px;
  :deep(.el-card__header) {
    padding: 15px 0;
    border-bottom-color: #f2f2f2;
  }
  :deep(.el-card__body) {
    padding: 10px 0 30px;
  }
  .content {
    .title {
      border-left: 2px solid var(--color-primary);
    }
    .btn {
      width: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .cursor-default {
    :deep(.el-input__inner) {
      cursor: inherit;
    }
  }
}
</style>
