<template>
  <el-dialog v-model="visible" title="投递限制" width="750px" @close="resetRef">
    <div class="content">
      <div class="">
        <el-form ref="form1" label-width="100px" :model="formData" :rules="rules">
          <el-form-item label="限制类型：" prop="type">
            <el-select
              v-model="formData.type"
              class="w100"
              clearable
              :disabled="!!formData.id"
              placeholder="请选择限制类型"
            >
              <el-option v-for="item in typeList" :label="item.v" :value="item.k">{{
                item.v
              }}</el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="选择人才：" prop="resumeType">
            <el-radio-group v-model="formData.resumeType">
              <el-radio
                v-for="item in resumeTypeList"
                :key="item.k"
                :label="item.k"
                :value="item.k"
                >{{ item.v }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="formData.resumeType === '1'" label="" prop="resumeData">
            <el-input
              class="w200"
              clearable
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              placeholder="请录入人才ID，多个ID请换行"
              v-model="formData.resumeData"
            ></el-input>
          </el-form-item>

          <el-form-item v-else label="" prop="tag">
            <el-select v-model="formData.tag" class="w100" multiple clearable placeholder="请选择">
              <el-option
                v-for="(item, index) in taglist"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >{{ item.label }}</el-option
              >
            </el-select>
          </el-form-item>

          <el-form-item v-if="formData.type === '1'" label="禁投单位：" prop="companyType">
            <el-radio-group v-model="formData.companyType" @change="handleCompanyChange">
              <el-radio v-for="item in companyTypeList" :label="item.k" :value="item.k">{{
                item.v
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.type === '1' && formData.companyType === '2'"
            label=""
            prop="companyData"
          >
            <el-input
              class="w200"
              clearable
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              placeholder="​请录入单位ID，多个ID请换行"
              v-model="formData.companyData"
            ></el-input>
          </el-form-item>

          <el-form-item label="投递提示：" prop="messageType" v-if="formData.type === '1'">
            <el-radio-group v-model="formData.messageType">
              <el-radio v-for="item in messageTypeList" :label="item.k" :value="item.k">{{
                item.v
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="`限制说明(站内投递)：`" prop="" v-if="formData.type === '2'">
            <div>
              <p>
                对同一单位每天限投<span class="color-red">1</span>次，30天内限投<span
                  class="color-red"
                  >3</span
                >次；
              </p>
              <p>
                每天累计限投<span class="color-red">10</span>次，7天内限投<span class="color-red"
                  >50</span
                >次。
              </p>
            </div>
          </el-form-item>

          <el-form-item label="备注：" prop="remark">
            <el-input
              class="w200"
              clearable
              autosize
              type="textarea"
              placeholder="必填"
              v-model="formData.remark"
              maxlength="200"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="flex-end clearfix">
        <div class="fl">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs, toRaw, watch, nextTick, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { jobApplyLimitadd, jobApplyLimitEdit } from '/@/api/configuration'
import { getResumeTagList } from '/@/api/person'

export default {
  name: 'tagDialog',
  props: {
    deliveryData: {
      type: Object,
      default: () => {}
    },
    typeList: {
      type: Array,
      default() {
        return <any>[]
      }
    },
    resumeTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    companyTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    messageTypeList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  emits: ['confirm'],
  setup(props, { emit }) {
    // console.log('props',props);
    const form1 = ref()
    const state = reactive({
      visible: false,
      defaultList: [],
      list: [],
      taglist: [],
      formData: {
        id: '',
        // 限制类型（1禁止2限制）
        type: '1',
        // 人才限制类型（1指定人才2指定人才标签
        resumeType: '1',
        // 人才限制数据
        resumeData: '',
        // 单位限制类型（1全部合作单位2单位id，3单位标签）
        companyType: '2',
        // 单位限制数据
        companyData: '',
        // 投递提醒类型（1xxx2xxx)
        messageType: '',
        // 备注
        remark: '',
        // 人才标签 前端自定义参数
        tag: []
      },
      rules: {
        type: [{ required: true, message: '请选择标签', trigger: 'change' }],
        resumeType: [{ required: true, message: '请录入人才ID', trigger: 'change' }],
        resumeData: [{ required: true, message: '请录入人才ID', trigger: 'blur' }],
        tag: [{ required: true, message: '请选择', trigger: 'blur' }],
        companyType: [{ required: true, message: '请选择', trigger: 'blur' }],
        companyData: [{ required: true, message: '请录入单位ID', trigger: 'blur' }],
        messageType: [{ required: true, message: '请选择', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写', trigger: 'blur' }]
      }
    })

    watch(
      () => props.deliveryData,
      (newValue, oldValue) => {
        // let newj = JSON.stringify(newValue)
        let newData = newValue
        // console.log(`newValue  ${newj}`);
        if (!newData.id) return
        state.formData = {
          id: newData.id,
          type: newData.type,
          resumeType: newData.resumeType,
          resumeData: newData.resumeType === '1' ? newData.resumeData : '',
          companyType: newData.companyType,
          companyData: newData.companyData,
          messageType: newData.messageType,
          remark: newData.remark,
          // 如果是指定人才标签才对resumeData字段进行切割回显
          tag: newData.resumeType === '2' ? newData.resumeData.split(',') : []
        }
        // console.log(`state.formData  ${JSON.stringify(state.formData)}`);
      },
      { deep: true, immediate: true }
    )

    onMounted(() => {
      getResumeTagListFun({})
      // let deliveryData = JSON.parse(JSON.stringify((props as any).deliveryData))
      // let deliveryData = toRefs(props.deliveryData)
      // console.log(deliveryData);
    })

    const resetRef = () => {
      state.formData = {
        id: '',
        // 限制类型（1禁止2限制）
        type: '1',
        // 人才限制类型（1指定人才2指定人才标签
        resumeType: '1',
        // 人才限制数据
        resumeData: '',
        // 单位限制类型（1全部合作单位2单位id，3单位标签）
        companyType: '2',
        // 单位限制数据
        companyData: '',
        // 投递提醒类型（1xxx2xxx)
        messageType: '',
        // 备注
        remark: '',
        // 人才标签 前端自定义参数
        tag: []
      }
    }

    const getResumeTagListFun = (obj) => {
      getResumeTagList(obj).then((res) => {
        // console.log("res",res);
        state.taglist = res || []
      })
    }

    const open = () => {
      state.visible = true
    }

    const handleClose = () => {
      state.visible = false
    }

    const handleConfirm = () => {
      form1.value.validate((valid: any) => {
        // console.log('valid',valid);
        if (valid) {
          // emit('confirm', JSON.parse(JSON.stringify(toRaw(state.list))))
          state.formData.resumeData = state.formData.resumeData
          let obj = { ...state.formData }
          if (state.formData.resumeType === '2') {
            obj.resumeData = state.formData.tag.join(',')
          }
          // 如果是全部合作单位，不用传具体数据
          if (state.formData.companyType === '1') {
            obj.companyData = ''
          }
          delete obj.tag
          if (state.formData.id) {
            jobApplyLimitEdit(obj).then((res) => {
              // console.log(res);
              emit('confirm')
              handleClose()
            })
          } else {
            jobApplyLimitadd(obj).then((res) => {
              // console.log(res);
              emit('confirm')
              handleClose()
            })
          }
        }
      })
    }

    const handleCompanyChange = (value) => {
      if (value) {
        // console.log(value);
      }
    }

    return {
      form1,
      open,
      handleConfirm,
      handleClose,
      handleCompanyChange,
      resetRef,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss"></style>
