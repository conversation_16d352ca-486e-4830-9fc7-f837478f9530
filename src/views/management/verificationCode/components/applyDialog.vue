<template>
  <div class="dialog-main">
    <el-dialog
      title="提交申请"
      v-model="dialogVisible"
      width="60%"
      @close="cancel"
      :close-on-press-escape="false"
      close-on-click-modal="false"
    >
      <el-form :model="formData" label-width="120px" :rules="formRules" ref="formRef">
        <!-- 类型，1 手机 2 邮箱 -->
        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择类型">
            <el-option label="手机" value="1" />
            <el-option label="邮箱" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="手机号/邮箱" prop="verificationValue">
          <el-input v-model="formData.verificationValue" placeholder="请输入手机号/邮箱" />
        </el-form-item>

        <el-form-item label="凭证">
          <el-upload
            class="upload-demo"
            action="/upload/image"
            :file-list="fileList"
            :limit="5"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处/粘贴，或 <em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">
                请上传申请凭证，如后审批截图或沟通确认记录等；支持jpg、png、jpeg格式
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <div class="file-list">
          <div class="file-item" v-for="item in fileList" :key="item.id">
            <el-image
              class="file-thumb"
              :src="item.fullUrl"
              :preview-src-list="[item.fullUrl]"
              :initial-index="0"
              fit="cover"
              @click="handlePreview(item)"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><picture-filled /></el-icon>
                </div>
              </template>
            </el-image>
            <el-button
              class="delete-btn"
              type="danger"
              :icon="Delete"
              circle
              size="small"
              @click="handleUploadRemove(item.id)"
            />
          </div>
        </div>

        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" show-word-limit maxlength="200" v-model="formData.remark" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { apply } from '/@/api/verificationCodeLog'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { UploadFilled, PictureFilled, Delete } from '@element-plus/icons-vue'

const emits = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const licenseShow = ref(false)

const formRef = ref()

const fileList = <any>ref([])

const priviewUrl = ref('')

const formData = <any>ref({
  type: '',
  verificationValue: '',
  attachmentFileIds: computed(() => fileList.value.map((item: any) => item.id).join()),
  remark: ''
})

const formRules = ref({
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  verificationValue: [{ required: true, message: '请输入手机号/邮箱', trigger: 'blur' }],
  attachmentFileIds: [{ required: true, message: '请上传凭证', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
})

const dialogVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const cancel = () => {
  formRef.value.resetFields()
  formData.value = {
    type: '',
    verificationValue: '',
    attachmentFileIds: '',
    remark: ''
  }
  fileList.value = []
  dialogVisible.value = false
}

const handleSubmit = () => {
  formRef.value.validate().then(async () => {
    const content = `
    <div>提交申请成功后会往你的企业微信发送一个确认码，请确认后点击确认按钮</div>
    `
    ElMessageBox.confirm(content, '提示', {
      dangerouslyUseHTMLString: true
    })
      .then(async () => {
        await apply(formData.value)
        emits('update')
        cancel()
      })
      .catch(() => {})
  })
}

const handleUploadRemove = (uid: string) => {
  fileList.value = fileList.value.filter((item: any) => item.id !== uid)
}

const handleUploadSuccess = (res) => {
  fileList.value = [
    { name: res.data.name, id: res.data.id, fullUrl: res.data.fullUrl },
    ...fileList.value
  ]
}

const handleUploadError = (error: any) => {
  ElMessage.error('上传失败：' + error.message)
}

const handlePreview = (item) => {
  priviewUrl.value = item.fullUrl
  licenseShow.value = true
}

const handlePaste = async (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      // 出loading
      const loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (file) {
        // 这里需要实现文件上传逻辑
        const formData = new FormData()
        formData.append('file', file)
        try {
          const response = await fetch('/upload/image', {
            method: 'POST',
            body: formData
          })
          const result = await response.json()
          handleUploadSuccess(result)
          // 关闭loading
          loadingInstance.close()
        } catch (error) {
          handleUploadError(error)
          // 关闭loading
          loadingInstance.close()
        }
      }
    }
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style lang="scss" scoped>
.file-list {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;

  .file-item {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    height: 200px;

    &:hover {
      .delete-btn {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .file-thumb {
      width: 100%;
      height: 100%;
      display: block;
      object-fit: cover;
      cursor: pointer;
    }

    .delete-btn {
      display: block;
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 2;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      padding: 6px;
      border: none;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;

      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
  }
}

.upload-demo {
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.tips {
  color: var(--el-color-danger);
  font-size: 12px;
}

.mask {
  position: relative;
  color: #2e2c2d;
  font-size: 16px;
}

.mask-cover {
  background: rgba($color: #000000, $alpha: 0.5);
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.mask-content {
  position: fixed;
  top: 30%;
  left: 40%;
  height: 70%;
  z-index: 10000;
  .preImg {
    width: 500px;
    height: 500px;
  }
}
</style>
