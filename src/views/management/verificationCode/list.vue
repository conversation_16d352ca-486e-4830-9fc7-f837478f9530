<template>
  <div class="container">
    <el-form label-width="80px" ref="formRef" :model="formData">
      <div class="flex">
        <div class="span-3">
          <el-form-item label="关键字" prop="keyword">
            <el-input placeholder="请输入关键字" clearable v-model="formData.keyword" />
          </el-form-item>
        </div>

        <div class="span-3">
          <el-form-item label="创建时间" prop="addTimeStart">
            <DatePickerRange
              v-model:start="formData.addTimeStart"
              v-model:end="formData.addTimeEnd"
            />
          </el-form-item>
        </div>

        <div class="span-3">
          <el-form-item>
            <el-button type="primary" @click="getList">查询</el-button>
            <el-button @click="handleRestForm">重置</el-button>
            <el-button type="primary" @click="handleOpenDialog">提交申请</el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <el-table border class="mt-20" :data="tableData" v-loading="loading">
      <el-table-column prop="addTime" label="时间" />
      <el-table-column prop="name" label="申请人" />
      <el-table-column prop="typeText" label="类型" />
      <el-table-column prop="verificationValue" label="手机号/邮箱" />
      <el-table-column prop="statusText" label="状态" />
      <el-table-column label="附件" prop="imageList">
        <template #default="scope">
          <el-image
            :src="scope.row.imageList[0]"
            :preview-src-list="scope.row.imageList"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
    </el-table>

    <div class="mt-20">
      <Paging :total="totalCount" @change="handlePageChange" />
    </div>

    <ApplyDialog v-model="applyDialogVisible" @update="getList" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { getLogList } from '/@/api/verificationCodeLog'
import Paging from '/@/components/base/paging.vue'
import ApplyDialog from './components/applyDialog.vue'

const tableData = ref([])
const formRef = ref()
const formData = ref({
  keyword: '',
  addTimeStart: '',
  addTimeEnd: '',
  page: 1,
  pageSize: 20
})

const loading = ref(false)

const totalCount = ref(0)

const applyDialogVisible = ref(false)

// 接受路由参数

const getList = async () => {
  loading.value = true
  const { list, page } = await getLogList(formData.value)
  tableData.value = list
  totalCount.value = page.total
  loading.value = false
}

const handleRestForm = () => {
  formRef.value.resetFields()
  formData.value.page = 1
  formData.value.pageSize = 20
  formData.value.addTimeStart = ''
  formData.value.addTimeEnd = ''
  getList()
}

const handleOpenDialog = () => {
  applyDialogVisible.value = true
}

const handlePageChange = (data: { page: number; limit: number }) => {
  formData.value.page = data.page
  formData.value.pageSize = data.limit
  getList()
}

getList()
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--color-whites);
  padding: 20px;
}
</style>
