<template>
  <el-dialog v-model="dialogVisible" title="群组" width="30%" @close="closeDialog">
    <el-form :model="formData" :rules="rules" ref="formRef" v-loading="loading">
      <el-form-item label="群组名称" prop="groupName">
        <el-input
          v-model="formData.groupName"
          placeholder="请填写群组名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="群组排序" prop="sortNumber">
        <el-input
          v-model="formData.sortNumber"
          placeholder="请填写群组排序"
          oninput="value = value.replace(/[^\d]+/g, '')"
        />
      </el-form-item>
      <el-form-item label="群组说明" prop="description">
        <el-input v-model="formData.description" maxlength="200" show-word-limit type="textarea" />
      </el-form-item>
    </el-form>

    <div class="jc-center">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { createCompanyGroup, editCompanyGroup, getGroupEdit } from '/@/api/companyGroup'

const emits = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  }
})

const formRef = ref()

const formData = ref({
  groupName: '',
  sortNumber: '',
  description: ''
})

const loading = ref(false)

const getEdit = async () => {
  loading.value = true
  const resp = await getGroupEdit({ id: props.id })
  Object.keys(resp).forEach((key) => {
    formData.value[key] = resp[key]
  })
  loading.value = false
}

const dialogVisible = computed({
  get() {
    if (props.id && props.modelValue) {
      getEdit()
    }
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const closeDialog = () => {
  formRef.value.resetFields()
  dialogVisible.value = false
}

const handleSubmit = async () => {
  const postData = props.id ? { ...formData.value, id: props.id } : formData.value
  const api = props.id ? editCompanyGroup : createCompanyGroup

  await api(postData)
  emits('update')
  closeDialog()
}

const rules = ref({
  groupName: [{ required: true, message: '请录入群组名称', trigger: 'change' }],
  sortNumber: [{ required: true, message: '请录入群组序号', trigger: 'change' }]
})
</script>

<style lang="scss" scoped></style>
