<template>
  <div class="admin-list">
    <div class="box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item label="登录账户">
          <el-input v-model="searchForm.username" clearable @keyup.enter.native="search"></el-input>
        </el-form-item>
        <el-form-item label="工号">
          <el-input
            v-model="searchForm.jobNumber"
            clearable
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业微信id">
          <el-input
            v-model="searchForm.wxWorkUserid"
            clearable
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>
        <el-form-item label="账户姓名">
          <el-input v-model="searchForm.name" clearable @keyup.enter.native="search"></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="searchForm.departmentId" placeholder="请选择" filterable clearable>
            <el-option v-for="item in departmentList" :label="item.v" :value="item.k"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.positionId" placeholder="请选择" clearable filterable>
            <el-option-group
              v-for="department in positionList"
              :key="department.id"
              :label="department.name"
            >
              <el-option
                v-for="item in department.list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" filterable clearable>
            <el-option v-for="item in statustList" :label="item.v" :value="item.k"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="search">搜索</el-button>
          <el-button size="small" @click="resetSearch">重置</el-button>
          <el-button size="small" type="primary" @click="showAddForm">+ 添加账号</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" border class="table" v-loading="tableLoading" size="small">
        <el-table-column align="center" prop="name" label="姓名" />
        <el-table-column align="center" prop="statusTxt" label="状态" />
        <el-table-column align="center" prop="department" label="部门" />
        <el-table-column align="center" prop="position" label="角色名称" />
        <el-table-column align="center" prop="username" label="登录账户" />
        <el-table-column align="center" prop="jobNumber" label="工号" />
        <el-table-column align="center" prop="wxWorkUserid" label="企业微信id" />
        <el-table-column align="center" prop="addTime" label="创建时间" />
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="showAddForm(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              size="small"
              @click="submitChangeStatus(scope.row)"
              v-if="scope.row.status == 0"
              >启用</el-button
            >
            <el-button
              type="primary"
              link
              size="small"
              @click="submitChangeStatus(scope.row)"
              v-else
              >禁用</el-button
            >
            <el-popover
              :width="400"
              trigger="hover"
              placement="left"
              :content="scope.row.permissions"
            >
              <template #reference>
                <el-button type="primary" link size="small">权限</el-button>
              </template>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <Paging :total="pages.total" @change="changePage"></Paging>
      </div>
    </div>

    <el-dialog v-model="addFormVisible" title="添加/修改账号">
      <el-form :model="addForm" size="small" :label-width="120">
        <el-form-item label="姓名">
          <el-input v-model="addForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="addForm.positionId" placeholder="请选择" clearable filterable>
            <el-option-group
              v-for="department in positionList"
              :key="department.id"
              :label="department.name"
            >
              <el-option
                v-for="item in department.list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="登录账户">
          <el-input v-model="addForm.username" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="登录密码">
          <el-input v-model="addForm.password" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="工号">
          <el-input v-model="addForm.jobNumber" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="企业微信全部人员">
          <el-select placeholder="请选择" v-model="addForm.wxWorkUserid" clearable filterable>
            <el-option v-for="item in wxUserList" :label="item.v" :value="item.k"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业微信userid">
          <el-input v-model="addForm.wxWorkUserid" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submit" :loading="submitLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import {
  addAdmin,
  getAdminList,
  getAdminListParams,
  getPositionMenuList,
  changeStatus
} from '../../api/permissions'
import { getWxUser } from '../../api/admin'
import adminRouteSetting from './component/adminRouteSetting.vue'

export default {
  name: 'positionList',
  components: {
    adminRouteSetting,
    Paging
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const state = reactive({
      searchForm: {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        jobNumber: '',
        page: 1,
        pageSize: ''
      },
      list: [],
      departmentList: <any>[],
      wxUserList: <any>[],
      statustList: [
        {
          k: '',
          v: '全部'
        },
        {
          k: 1,
          v: '启用'
        },
        {
          k: 0,
          v: '禁用'
        }
      ],
      positionList: [],
      statusList: <any>[],
      tableLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      addForm: {
        id: '',
        name: '',
        username: '',
        positionId: '',
        password: '',
        wxWorkUserid: '',
        jobNumber: ''
      },
      addFormVisible: false,
      submitLoading: false,
      permissionsVisable: false,
      tableData: [],
      checkBoxList: {
        menu: {},
        route: {},
        action: {}
      },
      positionId: ''
    })

    // 搜索
    const search = () => {
      state.tableLoading = true
      getAdminList(state.searchForm).then((res: any) => {
        state.list = res.list
        state.pages.size = res.pages.size
        state.pages.total = res.pages.total
        state.tableLoading = false
      })
    }

    // 重置
    const resetSearch = () => {
      state.searchForm = {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        jobNumber: '',
        page: 1,
        pageSize: ''
      }
      search()
    }

    const showAddForm = (row: any) => {
      if (row) {
        state.addForm.id = row.id
        state.addForm.name = row.name
        state.addForm.positionId = row.positionId
        state.addForm.username = row.username
        state.addForm.jobNumber = row.jobNumber
        state.addForm.wxWorkUserid = row.wxWorkUserid
      } else {
        state.addForm = {
          id: '',
          name: '',
          username: '',
          positionId: '',
          password: '',
          wxWorkUserid: '',
          jobNumber: ''
        }
      }
      state.addFormVisible = true
    }

    const submitChangeStatus = (row: any) => {
      // 先提示是否确认操作
      const txt = `确定要${row.status === 1 ? '启用' : '禁用'}该用户吗？`
      ElMessageBox.confirm(txt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        changeStatus(row.id).then(() => {
          search()
        })
      })
    }

    onMounted(() => {
      getAdminListParams().then((res: any) => {
        state.positionList = res.positionList
        state.departmentList = res.departmentList
      })

      search()
      loadWxUser()
    })

    const loadWxUser = () => {
      getWxUser().then((res: any) => {
        state.wxUserList = res.map((item: any) => {
          return {
            k: item.userid,
            v: `${item.name}(${item.departmentName})`
          }
        })
      })
    }

    const submit = () => {
      state.submitLoading = true
      addAdmin(state.addForm)
        .then(() => {
          state.submitLoading = false
          search()
          state.addFormVisible = false
        })
        .catch((err) => {
          state.submitLoading = false
        })
    }

    const showPermissions = (row: any) => {
      getPositionMenuList(row.id).then((res: any) => {
        state.permissionsVisable = true
        state.tableData = res
        res.forEach((element: any) => {
          if (element.route.isSet == 1) {
            state.checkBoxList.route[`route_${element.route.id}`] = true
          } else {
            state.checkBoxList.route[`route_${element.route.id}`] = false
          }
          if (element.action) {
            element.action.forEach((action: any) => {
              if (action.isSet == 1) {
                state.checkBoxList.action[`action_${action.id}`] = true
              } else {
                state.checkBoxList.action[`action_${action.id}`] = false
              }
            })
          }
        })
        state.positionId = row.id
      })
    }

    const closePositions = () => {
      state.permissionsVisable = false
    }

    const changePage = (r: any) => {
      state.searchForm.page = r.page
      state.searchForm.pageSize = r.limit
      search()
    }
    return {
      ...toRefs(state),
      search,
      resetSearch,
      showAddForm,
      showPermissions,
      submit,
      changePage,
      closePositions,
      submitChangeStatus
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.red {
  color: red;
}
.table {
  margin-top: 10px;
}
.el-card {
  border: none;
  padding: 0 30px;
  :deep(.el-card__header) {
    padding: 15px 0;
    border-bottom-color: #f2f2f2;
  }
  :deep(.el-card__body) {
    padding: 10px 0 30px;
  }
  .content {
    .title {
      border-left: 2px solid var(--color-primary);
    }
    .btn {
      width: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .cursor-default {
    :deep(.el-input__inner) {
      cursor: inherit;
    }
  }
}
</style>
