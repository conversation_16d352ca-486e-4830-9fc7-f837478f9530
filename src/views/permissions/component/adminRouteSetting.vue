<template>
  <div>
    <el-table :data="tableData" :span-method="objectSpanMethod" border>
      <el-table-column label="一级菜单" align="right" width="150">
        <template #default="scope">
          <!-- <el-checkbox :label="scope.row.menu.name"></el-checkbox> -->
          <span>{{ scope.row.menu.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="二级菜单" width="150">
        <template #default="scope">
          <el-checkbox
            v-model="checkBoxList.route['route_' + scope.row.route.id]"
            :label="scope.row.route.name"
          ></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column label="权限">
        <template #default="scope">
          <el-checkbox
            v-model="checkBoxList.action['action_' + item.id]"
            :label="item.name"
            v-for="item in scope.row.action"
          ></el-checkbox>
        </template>
      </el-table-column>
    </el-table>
    <br />
    <el-button @click="cancel">取消</el-button>
    <el-button @click="submit" type="primary">确认</el-button>
  </div>
</template>

<script lang="ts">
import { toRaw } from 'vue'
import { setPositionMenuList } from '/@/api/permissions'

export default {
  name: 'routerList',
  props: {
    id: {
      type: String,
      default: () => ''
    },

    tableData: {
      type: Array,
      default: () => []
    },
    checkBoxList: {
      type: Object,
      default: () => {}
    }
  },
  setup(props: any, { emit }: any) {
    const objectSpanMethod = ({ row, columnIndex }: any) => {
      if (columnIndex === 0) {
        return {
          rowspan: row.menuRowspan,
          colspan: 1
        }
      }
    }

    const cancel = () => {
      emit('cancel')
    }

    const submit = () => {
      const checkBoxList = toRaw(props.checkBoxList)
      const menuList = []
      const actionList = []

      for (const key in checkBoxList.route) {
        if (checkBoxList.route[key]) {
          menuList.push(key.replace('route_', ''))
        }
      }
      for (const key in checkBoxList.action) {
        if (checkBoxList.action[key]) {
          actionList.push(key.replace('action_', ''))
        }
      }

      setPositionMenuList({
        id: props.id,
        menuList,
        actionList
      }).then(() => {
        emit('cancel')
      })
    }

    return {
      objectSpanMethod,
      cancel,
      submit
    }
  }
}
</script>

<style scoped lang="scss"></style>
