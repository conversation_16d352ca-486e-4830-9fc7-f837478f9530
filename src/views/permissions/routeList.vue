<template>
  <el-card class="box-card" shadow="never">
    <template #header>
      <div class="jc-between ai-center">
        <div class="font18 fw-bold ai-end line-1">路由管理</div>
        <el-button type="primary" @click="showAddMenu">添加</el-button>
      </div>
    </template>
    <div class="content" v-loading="loading">
      <el-table :data="tableData" :span-method="objectSpanMethod" border>
        <el-table-column label="一级菜单">
          <template #default="scope">
            <span>{{ scope.row.menu.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="二级菜单">
          <template #default="scope">
            <span>{{ scope.row.route.name }} -- {{ scope.row.route.key }}</span>
          </template>
        </el-table-column>
        <el-table-column label="权限(按钮/操作)">
          <template #default="scope">
            <p v-for="item in scope.row.action">{{ item.name }} -- {{ item.key }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" @click="showAddAction(scope.row)">添加权限</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="addFormMenuVisible" title="添加/修改菜单">
      <el-form :model="addFormMenu">
        <el-form-item label="父菜单" :label-width="120">
          <el-select v-model="addFormMenu.parentId" placeholder="请选择" filterable clearable>
            <el-option v-for="item in level1List" :key="item.k" :label="item.v" :value="item.k">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" :label-width="120">
          <el-input v-model="addFormMenu.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="key" :label-width="120">
          <el-input v-model="addFormMenu.key" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFormMenuVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddMenu" :loading="submitLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="addFormActionVisible" title="添加/修改菜单">
      <el-form :model="addFormAction">
        <el-form-item label="菜单名称" :label-width="120">
          <el-input v-model="addFormAction.menuName" disabled autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="名称" :label-width="120">
          <el-input v-model="addFormAction.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="key" :label-width="120">
          <el-input v-model="addFormAction.key" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFormActionVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddAction" :loading="submitLoading"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { addAction, addMenu, getRouteList } from '/@/api/permissions'

export default {
  name: 'routerList',
  setup() {
    const state = reactive({
      tableData: [],
      checkBoxList: {
        menu: {},
        router: {},
        button: {}
      },
      addFormMenuVisible: false,
      addFormActionVisible: false,
      addFormMenu: {
        parentId: '',
        name: '',
        key: ''
      },
      addFormAction: {
        adminMenuId: '',
        menuName: '',
        name: '',
        key: ''
      },
      level1List: [],
      submitLoading: false,
      loading: false
    })

    const loadList = () => {
      state.loading = true
      getRouteList().then((res: any) => {
        state.tableData = res
        const menuList: Array<any> = []
        state.loading = false
        state.level1List = res.reduce((a: any, b: any) => {
          if (!menuList.includes(b.menu.key)) {
            a.push({
              k: b.menu.id,
              v: b.menu.name
            })
          }
          menuList.push(b.menu.key)
          return a
        }, [])
      })
    }

    const objectSpanMethod = ({ row, columnIndex }: any) => {
      if (columnIndex === 0) {
        return {
          rowspan: row.menuRowspan,
          colspan: 1
        }
      }
    }

    const showAddMenu = (row: any) => {
      // 添加菜单
      state.addFormMenu.parentId = ''
      state.addFormMenu.key = ''
      state.addFormMenu.name = ''
      state.addFormMenuVisible = true
    }
    const showAddAction = (row: any) => {
      // 添加菜单
      state.addFormAction.menuName = row.route.name
      state.addFormAction.adminMenuId = row.route.id
      state.addFormAction.key = ''
      state.addFormAction.name = ''
      state.addFormActionVisible = true
    }

    const submitAddAction = () => {
      state.submitLoading = true
      addAction(state.addFormAction).then(() => {
        state.addFormActionVisible = false
        state.submitLoading = false
        loadList()
      })
    }

    const submitAddMenu = () => {
      state.submitLoading = true
      addMenu(state.addFormMenu).then(() => {
        state.addFormMenuVisible = false
        state.submitLoading = false
        loadList()
      })
    }

    onMounted(() => {
      loadList()
    })
    return {
      ...toRefs(state),
      objectSpanMethod,
      showAddMenu,
      submitAddAction,
      showAddAction,
      submitAddMenu
    }
  }
}
</script>

<style scoped lang="scss">
.el-card {
  border: none;
  padding: 0 30px;
  :deep(.el-card__header) {
    padding: 15px 0;
    border-bottom-color: #f2f2f2;
  }
  :deep(.el-card__body) {
    padding: 10px 0 30px;
  }
  .content {
    .title {
      border-left: 2px solid var(--color-primary);
    }
    .btn {
      width: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .cursor-default {
    :deep(.el-input__inner) {
      cursor: inherit;
    }
  }
}
</style>
