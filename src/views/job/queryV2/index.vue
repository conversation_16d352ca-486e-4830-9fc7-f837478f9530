<template>
  <div class="job-query">
    <div class="box">
      <FilterView @search="handleSearch" />

      &nbsp;&nbsp;

      <el-table :data="list" border size="small" v-loading="loading" ref="jobTable">
        <!--  单位所属城市	单位所属省份	所属单位	用人部门	职位名称	学科专业	招聘状态	初始发布时间	职位详情	关联公告 -->
        <el-table-column
          prop="city"
          align="center"
          header-align="center"
          label="单位所属城市"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="province"
          align="center"
          header-align="center"
          label="单位所属省份"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="company"
          align="center"
          header-align="center"
          label="所属单位"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="department"
          align="center"
          header-align="center"
          label="用人部门"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="name"
          align="center"
          header-align="center"
          label="职位名称"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="majorTxt"
          align="center"
          header-align="center"
          label="学科专业"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="statusTitle"
          align="center"
          header-align="center"
          label="招聘状态"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="firstReleaseTime"
          align="center"
          header-align="center"
          label="初始发布时间"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="refreshDate"
          align="center"
          header-align="center"
          label="发布时间"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="jobLink"
          align="center"
          header-align="center"
          label="职位详情"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="announcementLink"
          align="center"
          header-align="center"
          label="关联公告"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>

      <Pagination
        v-if="pagination.total > 0"
        class="mt-15"
        :total="pagination.total"
        @change="handlePaginationChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import FilterView from './components/filter.vue'
import Pagination from '/@/components/base/paging.vue'
import {
  getSliceJobList,
  batchJobReleaseAgain,
  batchJobOffline,
  batchJobEstablishment,
  changeDeliveryEducationLimit,
  batchDelDeliveryFileLimit,
  batchAddDeliveryFileLimit
} from '/@/api/job'
import { useStore } from '/@/store'

export default defineComponent({
  name: 'jobQueryV2',
  components: {
    FilterView,
    Pagination
  },
  setup() {
    const jobTable = ref()
    const isIndeterminate = ref(false)
    const establishmentFrom = ref()
    const formLabelWidth = '140px'
    const state = reactive({
      jobQueryBtn: <any>[],
      pageSize: 20,
      loading: false,
      formData: <any>{
        export: 0,
        page: 1,
        limit: 20
      },
      // 分页信息
      pagination: {
        total: 0,
        limit: 20,
        page: 1
      },
      list: []
      // 统计
    })
    const store = useStore()
    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )
    state.jobQueryBtn = requestOldRoutesAction.value.jobQuery ?? []

    const getList = async () => {
      state.loading = true
      getSliceJobList(state.formData)
        .then((resp: any) => {
          if (state.formData.export === 1) {
          } else {
            state.list = resp.list
            state.pagination.total = resp.page.count
          }
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    onMounted(() => {
      //
      // getList()
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
    })
    // 排序

    const handleSearch = (filter: any) => {
      state.formData = {
        ...state.formData,
        ...filter
      }
      getList()
    }
    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    return {
      establishmentFrom,
      handleSearch,
      formLabelWidth,
      getList,
      handlePaginationChange,
      jobTable,
      isIndeterminate,
      batchJobReleaseAgain,
      batchJobOffline,
      batchJobEstablishment,
      changeDeliveryEducationLimit,
      batchDelDeliveryFileLimit,
      batchAddDeliveryFileLimit,
      ...toRefs(state)
    } as any
  }
})
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;

  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}

.btns {
  .el-button + .el-button {
    margin-left: 0;
  }
}

.radio-box {
  margin-left: 20px;
}

.radio-item-box {
  margin-left: 20px;
  margin-bottom: 20px;
}
.dialog-footer {
  text-align: center;
}
</style>
