<template>
  <el-form ref="form" label-width="70px" :model="formData">
    <div class="flex">
      <el-form-item class="span-6" label="职位检索" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请填写职位名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label="公告检索" prop="announcement">
        <el-input
          v-model="formData.announcement"
          placeholder="请填写公告名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label="单位检索" prop="company">
        <el-input
          v-model="formData.company"
          placeholder="请填写单位名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label="需求专业" prop="majorId">
        <MajorCategory :deep="2" :multiple="true" v-model="formData.majorId" placeholder="不限" />
      </el-form-item>
      <el-form-item class="span-6" label="学历要求" prop="educationType">
        <Education multiple v-model="formData.educationType" placeholder="不限" />
      </el-form-item>
      <el-form-item class="span-6" label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetField">重置</el-button>
          <el-button @click="handleDownload">下载</el-button>
        </div>
      </el-form-item>
    </div>
    <div>
      <div class="flex">
        <el-form-item class="span-6" label="工作城市" prop="city">
          <Region multiple collapse-tags v-model="formData.city" placeholder="不限" />
          <!-- <el-input v-model="formData.city" placeholder="请输入城市"></el-input> -->
        </el-form-item>
        <el-form-item class="span-6" label="职位类型" prop="jobCategoryId">
          <JobCategory multiple v-model="formData.jobCategoryId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-6" label="工作性质" prop="natureType">
          <WorkNature v-model="formData.natureType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-6" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-6" label="发布时间" prop="releaseTimeStart">
          <DatePickerRange
            v-model:start="formData.releaseTimeStart"
            v-model:end="formData.releaseTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-6" label="刷新时间" prop="refreshTimeStart">
          <DatePickerRange
            v-model:start="formData.refreshTimeStart"
            v-model:end="formData.refreshTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-6" label="用人部门" prop="department">
          <el-input
            v-model="formData.department"
            placeholder="请填写用人部门"
            @keyup.enter="handleSearch"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="span-6" label="海外经历" prop="abroadType">
          <AbroadExperience v-model="formData.abroadType" placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item class="span-6" label="工作经验" prop="experienceType">
          <WorkExperience
            multiple
            v-model="formData.experienceType"
            placeholder="不限"
            :isLimit="false"
          />
        </el-form-item>
        <el-form-item class="span-6" label="职称要求" prop="titleType">
          <LevelTitle v-model="formData.titleType" multiple placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item class="span-6" label="审核状态" prop="auditStatus">
          <el-select v-model="formData.auditStatus" placeholder="不限">
            <el-option
              v-for="(item, index) in auditOption as any"
              :key="index"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-6" label="招聘状态" prop="status">
          <el-select v-model="formData.status" placeholder="不限">
            <el-option
              v-for="(item, index) in jobOption as any"
              :key="index"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-6" label="年龄要求" prop="ageType">
          <Age
            v-model="formData.ageType"
            placeholder="不限"
            :isLimit="true"
            @keyup.enter="handleSearch"
            clearable
          />
        </el-form-item>

        <el-form-item class="span-6" label="政治面貌" prop="politicalType">
          <Political multiple v-model="formData.politicalType" placeholder="不限" :isLimit="true" />
        </el-form-item>

        <el-form-item class="span-6" label="投递类型" prop="deliveryType">
          <DeliveryType v-model="formData.deliveryType" />
        </el-form-item>
        <el-form-item
          class="span-6"
          label="初始发布时间"
          label-width="89px"
          prop="firstReleaseTimeStart"
        >
          <DatePickerRange
            v-model:start="formData.firstReleaseTimeStart"
            v-model:end="formData.firstReleaseTimeEnd"
          />
        </el-form-item>

        <el-form-item class="span-6" label="编制类型" prop="establishmentType">
          <EstablishmentType v-model="formData.establishmentType" />
        </el-form-item>

        <el-form-item class="span-6" label="单位类型" prop="companyType">
          <CompanyTypeSelect v-model="formData.companyType" :multiple="true" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-6" label="单位性质" prop="companyNature">
          <CompanyNatureSelect v-model="formData.companyNature" :multiple="true" />
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script lang="ts">
import { reactive, toRefs, ref, onMounted, nextTick, defineComponent } from 'vue'
import MajorCategory from '/@select/majorCategory.vue'
import Education from '/@select/education.vue'
import Region from '/@select/region.vue'
import JobCategory from '/@select/jobCategory.vue'
import WorkNature from '/@select/workNature.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import WorkExperience from '/@select/workExperience.vue'
import LevelTitle from '/@select/levelTitle.vue'
import Age from '/@select/age.vue'
import Political from '/@select/political.vue'
import { getJobAuditStatusList, getJobStatusList } from '/@/api/job'
import { useRoute } from 'vue-router'
import DeliveryType from '/@/components/base/select/deliveryType.vue'
import EstablishmentType from '/@/components/base/select/establishmentType.vue'
import CompanyTypeSelect from '/@/components/base/select/companyType.vue'
import CompanyNatureSelect from '/@/components/base/select/companyNature.vue'

export default defineComponent({
  name: 'jobQueryFilter',
  components: {
    MajorCategory,
    Education,
    Region,
    JobCategory,
    WorkNature,
    DatePickerRange,
    AbroadExperience,
    WorkExperience,
    LevelTitle,
    Age,
    Political,
    DeliveryType,
    EstablishmentType,
    CompanyTypeSelect,
    CompanyNatureSelect
  },
  emits: ['search', 'download'],
  setup(props, { emit }) {
    const form = ref()
    const route = useRoute()

    const state = reactive({
      loading: false,
      showMore: false,
      auditOption: [],
      jobOption: [],
      formData: <any>{
        name: '',
        announcement: '',
        majorId: '',
        educationType: [],
        city: [],
        jobCategoryId: [],
        natureType: '',
        addTimeStart: '',
        addTimeEnd: '',
        releaseTimeStart: '',
        releaseTimeEnd: '',
        refreshTimeStart: '',
        refreshTimeEnd: '',
        department: '',
        abroadType: '',
        experienceType: '',
        titleType: '',
        auditStatus: '',
        status: '',
        ageType: '',
        sexType: '',
        politicalType: '',
        isArticle: '',
        creator: '',
        deliveryWay: '',
        deliveryType: '',
        contact: '',
        contactSynergy: '',
        companyType: [],
        companyNature: [],
        export: 0
      },
      params: {
        announcement: route.query.id
      }
    })
    const arrayToStringData = (data: any) => {
      return {
        ...data,
        city: Array.isArray(data.city) ? data.city.join() : '',
        jobCategoryId: Array.isArray(data.jobCategoryId) ? data.jobCategoryId.join() : '',
        educationType: Array.isArray(data.educationType) ? data.educationType.join() : '',
        establishmentType: Array.isArray(data.establishmentType)
          ? data.establishmentType.join()
          : '',
        experienceType: Array.isArray(data.experienceType) ? data.experienceType.join() : '',
        politicalType: Array.isArray(data.politicalType) ? data.politicalType.join() : '',
        companyType: Array.isArray(data.companyType) ? data.companyType.join() : '',
        companyNature: Array.isArray(data.companyNature) ? data.companyNature.join() : '',
        majorId: Array.isArray(data.majorId) ? data.majorId.join() : data.majorId || ''

        // 工作经验
      }
    }

    const handleResetField = () => {
      form.value.resetFields()
      state.formData.export = 0
      nextTick(() => {
        emit('search', arrayToStringData(state.formData))
      })
    }

    const handleDownload = () => {
      state.formData.export = 1
      emit('search', arrayToStringData(state.formData))
      state.formData.export = 0
    }

    const handleSearch = () => {
      state.formData.export = 0
      state.formData.announcement = state.params.announcement || state.formData.announcement
      emit('search', arrayToStringData(state.formData))
      state.params.announcement = ''
    }
    const getFilterOption = async () => {
      const [auditOption, jobOption] = await Promise.all([
        getJobAuditStatusList(),
        getJobStatusList()
      ])
      state.auditOption = auditOption
      state.jobOption = jobOption
    }

    onMounted(() => {
      getFilterOption()
    })

    return {
      form,
      handleDownload,
      handleSearch,
      handleResetField,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.show {
  margin-left: 10px;
  white-space: nowrap;
}
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
