<template>
  <div>
    <div class="box">
      <el-form class="flex" ref="form" :rules="formRules" :model="formData" label-width="100px">
        <div class="left flex-2">
          <div class="title line-1 mb-20 fs-13 fw-bold pl-15">基本信息</div>
          <div class="pl-20">
            <el-row :gutter="0" v-if="formData.announcementId != 0">
              <el-col :span="22">
                <el-form-item label="所属公告" prop="tile">
                  <div>{{ formData.announcementTitle }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row :gutter="10">
                  <el-col :span="22">
                    <el-form-item label="职位名称" prop="name">
                      <el-input v-model="formData.name" placeholder="请填写职位名称"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="职位类型" prop="jobCategoryId">
                  <JobCategory :multiple="false" v-model="formData.jobCategoryId" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="学历要求" prop="educationType">
                      <Education v-model="formData.educationType" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <el-form-item label="需求专业" prop="majorId">
                  <MajorCategory :deep="2" v-model="formData.majorId" />
                  <!-- 识别 -->
                  <el-row :gutter="5">
                    <el-col :span="20"
                      ><el-input
                        v-model="majorText"
                        placeholder="输入识别文案"
                        clearable
                        maxlength="2000"
                    /></el-col>
                    <el-col :span="4">
                      <el-button type="primary" @click="recognition">识别</el-button>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="工作性质" prop="natureType">
                      <WorkNature v-model="formData.natureType" placeholder="请选择" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <Salary
                  :data="{
                    wageType: formData.wageType,
                    wageId: formData.wageId,
                    minWage: formData.minWage,
                    maxWage: formData.maxWage,
                    isNegotiable: formData.isNegotiable
                  }"
                  @change="salarChage"
                />
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="其他要求">
                  <el-row>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="experienceType">
                        <Experience
                          v-model="formData.experienceType"
                          placeholder="请选择经验"
                          is-limit
                        />
                      </el-form-item>
                    </div>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="ageType">
                        <Age v-model="formData.ageType" placeholder="请选择年龄" is-limit />
                      </el-form-item>
                    </div>

                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="titleType">
                        <LevelTitle
                          v-model="formData.titleType"
                          placeholder="请选择职称"
                          is-limit
                        />
                      </el-form-item>
                    </div>

                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="politicalType">
                        <Political
                          v-model="formData.politicalType"
                          placeholder="请选择政治面貌"
                          is-limit
                        />
                      </el-form-item>
                    </div>
                    <div class="flex-1 mr-8">
                      <el-form-item label-width="0px" prop="abroadType">
                        <AbroadExperience
                          v-model="formData.abroadType"
                          placeholder="请选择经历"
                          is-limit
                        />
                      </el-form-item>
                    </div>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title line-1 mb-20 fs-13 fw-bold pl-15">职位详情</div>
          <div class="pl-26">
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="招聘人数" prop="amount">
                      <el-input v-model="formData.amount" placeholder="请输入招聘人数"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col class="mb-26" :xs="24" :sm="24" :md="11" :lg="11" :xl="11">
                <!-- <el-form-item label="用人部门">
                  <el-input v-model="formData.department" placeholder="请输入用人部门"></el-input>
                </el-form-item> -->
                <el-form-item label="工作地点" prop="provinceId">
                  <Address
                    :address="{
                      provinceId: formData.provinceId,
                      cityId: formData.cityId,
                      districtId: formData.districtId,
                      areaName: formData.areaName,
                      address: formData.address
                    }"
                    ref="address"
                    @confirm="handleAddress"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <el-row>
                  <el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22">
                    <el-form-item label="职位福利">
                      <el-input
                        readonly
                        class="cursor-default"
                        v-model="welfareText"
                        @click="openDialogWelfare"
                        placeholder="请选择职位福利"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-10" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="职位编制" prop="jobEstablishment">
                  <announcement-check-box
                    v-model="formData.establishmentType"
                    :check-box-list="establishmentTypeList"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="岗位职责" prop="duty">
                  <el-input
                    v-model="formData.duty"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写岗位职责(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="任职要求" prop="requirement">
                  <el-input
                    v-model="formData.requirement"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写任职要求(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item label="其他说明" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    maxlength="2000"
                    placeholder="请填写其他说明(0/2000)"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0">
              <el-col class="mb-26" :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                <el-form-item>
                  <span class="color-danger fs-12">
                    *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="mb-30">
              <el-form-item class="pb-30">
                <el-button
                  :loading="submitLoading"
                  class="btn"
                  type="primary"
                  @click="submit('save')"
                  >提交</el-button
                >
                <el-button class="btn" plain>预览</el-button>
                <el-button class="btn" plain @click="handleBack">取消</el-button>
              </el-form-item>
            </el-row>
          </div>
        </div>
        <div class="flex-1 right">
          <div class="title line-1 mb-20 fs-13 fw-bold pl-15">职位属性</div>
          <el-form-item label="截止日期" prop="periodDate">
            <DatePicker
              placeholder="选择日期"
              v-model="formData.periodDate"
              :disabledDate="handleDisabledDate"
            />
          </el-form-item>
          <el-form-item label="发布单位">
            <el-input disabled v-model="formData.companyName"></el-input>
          </el-form-item>
          <el-form-item label="单位类型">
            <el-input disabled v-model="formData.companyNatureTitle"></el-input>
          </el-form-item>
          <el-form-item label="用人部门" prop="department">
            <el-input v-model="formData.department"></el-input>
          </el-form-item>
          <el-form-item label="投递配置">
            <el-input disabled v-model="companyDeliveryTypeTxt"></el-input>
          </el-form-item>
          <ApplyMethods
            :isAnnouncement="false"
            v-model="applyMethodsData"
            isCooperation
            isEdit
            :accountType="companyDeliveryType"
          />
          <DeliveryLimitType v-model="formData.deliveryLimitType" />
          <el-form-item label="职位附件">
            <el-button class="button" type="primary" @click="openJobFileUpload"
              >+上传附件</el-button
            >
          </el-form-item>
          <JobFileUpload ref="jobFile" v-model="fileList" />

          <JobCooperate
            :companyId="formData.companyId"
            v-model="synergyData"
            identifyShow
            :email="formData.applyAddress"
            :hasAccount="hasAccount"
          />
        </div>
      </el-form>
    </div>
    <DialogWelfare
      :member-id="memberId"
      title="职位福利"
      ref="dialogWelfare"
      @confirm="handleWelfare"
    />
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, ref, onMounted, getCurrentInstance, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import JobCategory from '/@select/jobCategory.vue'
import Education from '/@select/education.vue'
import MajorCategory from '/@select/majorCategory.vue'
import WorkNature from '/@select/workNature.vue'
import Experience from '/@select/experience.vue'
import Salary from '/@/components/business/salary.vue'
import Age from '/@select/age.vue'
import LevelTitle from '/@select/levelTitle.vue'
import Political from '/@select/political.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import Address from '/@/components/business/address.vue'
import DatePicker from '/@/components/base/datePicker.vue'
import DialogWelfare from '/@/components/base/welfare.vue'
import { getJobDetails, jobEdit } from '/@/api/job'
import JobFileUpload from '../../cms/announcement/component/jobFileUpload.vue'
import DeliveryLimitType from '../../cms/job/components/deliveryLimitType.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import { getJobEstablishmentList } from '/@/api/cmsJob'
import JobCooperate from '/@/components/job/jobCooperate.vue'
import { aiRecognition } from '/@/api/major'

export default {
  name: 'jobEdit',
  components: {
    AnnouncementCheckBox,
    JobCategory,
    Education,
    MajorCategory,
    WorkNature,
    Experience,
    Salary,
    Age,
    LevelTitle,
    Political,
    AbroadExperience,
    Address,
    DatePicker,
    DialogWelfare,
    JobFileUpload,
    DeliveryLimitType,
    ApplyMethods,
    JobCooperate
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const { proxy } = getCurrentInstance() as any

    const form = ref()
    const dialogWelfare = ref()
    const address = ref()
    const jobFile = ref()
    const majorText = ref('')
    const state = reactive({
      loading: false,
      submitLoading: false,
      id: '', // 职位ID
      memberId: '',
      annualSalary: '',
      establishmentTypeList: [], // 职位编制列表
      formData: {
        jobId: '',
        isNegotiable: '',
        name: '', // 职位名称
        code: '', // 职位代码
        jobCategoryId: '', // 职位类型Id
        educationType: '', // 要求学历类型
        majorId: '', // 需求专业ID
        natureType: null, // 工作性质
        wageType: '', // 薪资类型:1月，2年，3日
        wageId: '', // 下拉薪资范围值
        amount: '', // 招聘人数
        provinceId: '',
        cityId: '',
        districtId: '',
        address: '',
        periodDate: '', // 有效期
        duty: '', // 岗位职责
        requirement: '', // 任职要求
        minWage: '',
        maxWage: '',
        experienceType: '', // 经验要求
        ageType: '',
        titleType: '', // 称职类型
        politicalType: '', // 政治面貌
        abroadType: '', // 海外经历
        department: '', // 用人部门
        remark: '', // 其他说明
        companyNatureTitle: '',
        companyName: '',
        welfareTag: '',
        areaName: '',
        annualSalary: '', // 年薪
        applyType: '',
        applyAddress: '',
        deliveryType: [] || '',
        deliveryWay: '',
        deliveryLimitType: '',
        extraNotifyAddress: '',
        establishmentType: [],
        jobContactId: '',
        jobContactSynergyIds: []
      },
      applyMethodsData: computed({
        get() {
          const {
            formData: { applyType, applyAddress, deliveryType, extraNotifyAddress, deliveryWay }
          } = state
          return { applyType, applyAddress, deliveryType, extraNotifyAddress, deliveryWay }
        },
        set(val: Object) {
          Object.keys(val).forEach((key) => {
            state.formData[key] = val[key]
          })
        }
      }),

      synergyData: computed({
        get() {
          const {
            formData: { jobContactId, jobContactSynergyIds }
          } = state
          return { jobContactId, jobContactSynergyIds }
        },
        set(val: object) {
          Object.keys(val).forEach((key) => {
            state.formData[key] = val[key]
          })
        }
      }),
      isCooperation: '',
      welfareText: '',
      welfareArray: [],
      fileList: [],
      companyDeliveryType: null,
      companyDeliveryTypeTxt: '',
      hasAccount: computed(() => state.formData.companySubAccountConfig?.total !== 0)
    })

    const formRules = ref({
      name: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],
      jobCategoryId: [{ required: true, message: '请选择职位类型', trigger: 'change' }],
      educationType: [{ required: true, message: '请选择学历要求', trigger: 'change' }],
      // majorId: [{ required: true, message: '请选择需求专业', trigger: 'change' }],
      // natureType: [{ required: true, message: '请选择工作性质', trigger: 'change' }],
      amount: [
        {
          required: true,
          message: '请输入招聘人数',
          trigger: 'blur'
        },
        {
          pattern: /^([1-9]\d{0,3}|\u82e5\u5e72)$/,
          message: '请输入数字或者“若干”',
          trigger: 'blur'
        }
      ],
      provinceId: [{ required: true, message: '请选择工作地点', trigger: 'change' }],
      periodDate: [{ required: true, message: '请输入有效期', trigger: 'change' }],
      duty: [{ required: true, message: '请输入岗位职责', trigger: 'blur' }],
      requirement: [{ required: true, message: '请输入任职要求', trigger: 'blur' }]
    })

    const handleEmpty = () => {
      Object.keys(state.formData).forEach((key: any) => {
        if (/^(-|0)/.test(state.formData[key])) {
          state.formData[key] = ''
        }
      })
    }
    // 职位编制
    const getJobEstablishment = () => {
      getJobEstablishmentList().then((resp: any) => {
        state.establishmentTypeList = resp
      })
    }
    getJobEstablishment()

    const getDetails = async () => {
      if (!state.id) return
      state.loading = true
      state.formData.jobId = state.id
      await getJobDetails({ id: state.id }).then((resp: any) => {
        state.loading = false
        state.formData = resp
        state.isCooperation = resp.isCooperation
        state.formData.majorId = resp.majorId ? resp.majorId.split(',') : resp.majorId

        state.formData.wageId = resp.wageId || ''

        // 职位福利标签
        state.formData.welfareTag = resp.welfareTage.map((item: any) => item.k).join(',')
        state.formData.remark = resp.remark

        // 回显
        state.formData.areaName = resp.areaName
        state.welfareText = resp.welfareTage.map((item: any) => item.v).join(',')
        state.welfareArray = resp.welfareTage

        // 获取地址
        state.memberId = resp.memberId
        address.value.getAddress(state.memberId)

        // 将后端默认值为“0”或者“-”处理成 ''
        handleEmpty()

        // 重新赋值职位ID
        state.formData.jobId = state.id

        // 职位附件
        state.fileList = resp.fileList
        state.companyDeliveryTypeTxt = resp.companyDeliveryTypeTxt
        state.companyDeliveryType = Number(resp.companyDeliveryType)
      })
    }

    onMounted(() => {
      if (route.params.id) {
        state.id = <string>route.params.id
      }
      getDetails()
    })

    const openDialogWelfare = () => {
      dialogWelfare.value.openDialog(state.welfareArray)
    }

    const handleWelfare = (welfare: any) => {
      state.welfareArray = welfare
      state.welfareText = welfare.map((item: any) => item.v).join('，')
      state.formData.welfareTag = welfare.map((item: any) => item.k).join(',')
    }

    const handleAddress = (ad: any) => {
      state.formData.provinceId = ad.provinceId
      state.formData.cityId = ad.cityId
      state.formData.districtId = ad.districtId
      state.formData.areaName = ad.areaName
      state.formData.address = ad.address
    }

    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() || time.getTime() > Date.now() + 365 * 24 * 60 * 60 * 1000
    }

    // 重置表单
    const formReset = () => {
      state.welfareText = ''
      form.value.resetFields()
      state.fileList = []
      state.formData.isNegotiable = '2'
      state.formData.wageId = ''
      setTimeout(() => {
        form.value.clearValidate()
      }, 10)
    }

    const submit = () => {
      if (Date.now() > new Date(state.formData.periodDate).getTime()) {
        state.submitLoading = true
        ElMessage({
          message: '截止日期不能小于当前时间',
          type: 'error',
          onClose: () => {
            state.submitLoading = false
          }
        })
        return
      }
      if (state.formData.minWage > state.formData.maxWage) {
        ElMessage.error('最低薪资大于最高薪资')
        return
      }
      form.value.validate((valid: any) => {
        if (valid) {
          state.submitLoading = true
          const majorId = state.formData.majorId ? state.formData.majorId.join() : ''
          const postData = {
            ...state.formData,
            majorId,
            fileIds: state.fileList?.map((item: any) => item.id).join()
          }

          Reflect.deleteProperty(postData, 'fileList')

          jobEdit(postData)
            .then(() => {
              state.submitLoading = false
              formReset()
              proxy.mittBus.emit('closeCurrentViewTag')
            })
            .catch(() => {
              state.submitLoading = false
            })
        }
      })
    }

    const handleBack = () => {
      router.back()
    }

    const salarChage = (data) => {
      Object.assign(state.formData, data)
    }

    const openJobFileUpload = () => {
      jobFile.value.openJobFileUpload()
    }

    const recognition = () => {
      aiRecognition(majorText.value).then((resp: any) => {
        state.formData.majorId = resp.majorIds
      })
    }

    return {
      dialogWelfare,
      address,
      form,
      handleWelfare,
      handleAddress,
      openDialogWelfare,
      submit,
      handleDisabledDate,
      handleBack,
      formRules,
      salarChage,
      ...toRefs(state),
      jobFile,
      openJobFileUpload,
      recognition,
      majorText
    } as any
  }
}
</script>

<style scoped lang="scss">
.box {
  border-radius: 10px;
  overflow: hidden;
  .left {
    background-color: #fff;
    padding: 20px 15px;
    border-right: 15px solid #f2f2f2;
  }
  .right {
    background-color: #fff;
    padding: 20px 15px;
  }
}
</style>
