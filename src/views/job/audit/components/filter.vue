<template>
  <el-form ref="form" label-width="70px" :model="formData">
    <div class="flex">
      <el-form-item class="span-6" label="职位检索" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请填写职位名称或编号"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        class="span-6"
        label="公告检索"
        prop="announcement"
        @keyup.enter="handleSearch"
        clearable
      >
        <el-input v-model="formData.announcement" placeholder="请填写公告名称或编号"></el-input>
      </el-form-item>
      <el-form-item
        class="span-6"
        label="单位检索"
        prop="company"
        @keyup.enter="handleSearch"
        clearable
      >
        <el-input v-model="formData.company" placeholder="请填写单位名称或编号"></el-input>
      </el-form-item>
      <!-- <el-form-item class="span-6" label="审核状态" prop="auditStatus">
        <Audit v-model="formData.auditStatus" placeholder="不限" />
      </el-form-item> -->
      <el-form-item class="span-6" label="发布模式" prop="isArticle">
        <ReleasePattern v-model="formData.isArticle" placeholder="不限" />
      </el-form-item>
      <el-form-item class="span-6" label="发布时间" prop="releaseTimeStart">
        <DatePickerRange
          v-model:start="formData.releaseTimeStart"
          v-model:end="formData.releaseTimeEnd"
        />
      </el-form-item>
      <el-form-item class="span-6" label="申请时间" prop="applyAuditTimeStart">
        <DatePickerRange
          v-model:start="formData.applyAuditTimeStart"
          v-model:end="formData.applyAuditTimeTimeEnd"
        />
      </el-form-item>
    </div>
    <div class="flex">
      <el-form-item class="span-6" label="创 建 人" prop="member">
        <el-input
          v-model="formData.member"
          placeholder="请填写创建人账号"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item class="span-6" label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetField">重置</el-button>
          <el-button @click="handleDownload">下载</el-button>
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, nextTick } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
// import Audit from '/@select/audit.vue'
import ReleasePattern from '/@select/jobReleasePattern.vue'

export default defineComponent({
  name: 'jobAuditFilter',
  components: {
    DatePickerRange,
    // Audit,
    ReleasePattern
  },
  emits: ['search', 'download'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      region: '',
      loading: false,
      formData: {
        name: '',
        announcement: '',
        company: '',
        auditStatus: '',
        releaseTimeStart: '',
        releaseTimeEnd: '',
        applyAuditTimeStart: '',
        applyAuditTimeEnd: '',
        member: '',
        isArticle: '',
        export: 0
      }
    })

    const handleResetField = () => {
      form.value.resetFields()
      nextTick(() => {
        emit('search', state.formData)
      })
    }

    const handleDownload = () => {
      state.formData.export = 1
      emit('search', state.formData)
    }
    const handleSearch = () => {
      state.formData.export = 0
      emit('search', state.formData)
    }

    return {
      form,
      handleDownload,
      handleSearch,
      handleResetField,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
