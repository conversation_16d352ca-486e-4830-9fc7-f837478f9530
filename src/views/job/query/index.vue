<template>
  <div class="job-query">
    <div class="box">
      <FilterView @search="handleSearch" />

      &nbsp;&nbsp;
      <el-switch
        v-model="isSimple"
        size="large"
        active-text="简版(只支持部分搜索条件和显示结果)"
        inactive-text="全功能"
      />
      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ amount.all }}</span>
          个职位&nbsp;&nbsp;面试邀约:
          <span class="danger">{{ amount.interview }}</span>
          次&nbsp;&nbsp;点击次数:
          <span class="danger">{{ amount.click }}</span
          >次&nbsp;&nbsp;平台投递:
          <span class="danger">{{ amount.platformNum }}</span>
          次&nbsp;&nbsp;邮箱投递:
          <span class="danger">{{ amount.emailNum }}</span>
          次 &nbsp;&nbsp;网址投递:
          <span class="danger">{{ amount.linkNum }}</span>
          次
        </div>
        <el-link :underline="false" type="primary" @click="handleOpenCustomColumn">选择列</el-link>
      </div>

      <el-table
        :data="list"
        border
        size="small"
        v-loading="loading"
        @sort-change="handleSortable"
        @selection-change="handleSelectionChange"
        ref="jobTable"
      >
        <el-table-column type="selection" width="40" align="center" />
        <template v-for="(item, index) in customColumns">
          <el-table-column
            v-if="item.select && item.k === 1"
            :key="index"
            prop="uid"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 2"
            :key="index"
            prop="sortName"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link class="fw-normal fs-12" :underline="false" @click="handleJobDetail(row.id)"
                >{{ row.name }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 3"
            :key="index"
            prop="basicInformation"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 4"
            :key="index"
            prop="announcementTitle"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <router-link
                :to="`/cms/announcementDetail/${row.announcementId}/${row.announcementStatus}`"
                >{{ row.announcementTitle }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 5"
            :key="index"
            prop="company"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <router-link :to="`/company/details?id=${row.companyId}`"
                >{{ row.company }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 6"
            :key="index"
            prop="statusTitle"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 7"
            :key="index"
            prop="sortApplyNum"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                v-if="row.jobApplyNum !== 0"
                @click="openBusiness(row.id)"
                class="bg-primary point"
                >{{ row.jobApplyNum }}</span
              >
              <span v-else class="bg-primary">{{ row.jobApplyNum }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 8"
            :key="index"
            prop="sortInterviewNum"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.jobInterviewNum }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 9"
            :key="index"
            prop="sortReleaseTime"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.releaseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 10"
            :key="index"
            prop="sortRefreshTime"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.realRefreshTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 11"
            :key="index"
            prop="sortAmount"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 12"
            :key="index"
            prop="sortClick"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.click }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 13"
            :key="index"
            prop="sortDownloadNum"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.downloadAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 14"
            :key="index"
            prop="department"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 15"
            :key="index"
            prop="auditStatusTitle"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 16"
            :key="index"
            prop="1"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>
                {{ row.isArticle == 1 ? '公告+职位' : '纯职位' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 17"
            :key="index"
            prop="creator"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 18"
            :key="index"
            prop="sortAddTime"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.addTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 19"
            :key="index"
            prop="sortPeriodDate"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.periodDate }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 20"
            :key="index"
            prop="sortDeleteDate"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.deleteTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 22"
            :key="index"
            prop="majorTxt"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 23"
            :key="index"
            prop="isMiniappTxt"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <isMiniappChange :value="row.isMiniapp" type="job" :id="row.id"></isMiniappChange>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 24"
            :key="index"
            prop="jobContact?.contact"
            align="center"
            header-align="center"
            :label="item.v"
          >
            <template #default="{ row }">
              <el-popover placement="top-start">
                <template #reference>
                  {{ row.jobContact?.contact }}
                </template>
                <div>
                  <div>职位联系人</div>
                  <div class="flex mt-5">
                    <span>{{ row.jobContact?.companyMemberType === '0' ? '主' : '子' }}</span>
                    <span>{{ row.jobContact?.contact }} / {{ row.jobContact?.department }}</span>
                  </div>
                  <div>{{ row.jobContact?.email }} {{ row.jobContact?.mobile }}</div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 25"
            :key="index"
            prop="jobContactSynergyNum"
            align="center"
            header-align="center"
            :label="item.v"
          >
            <template #default="{ row }">
              <div v-if="row.jobContactSynergyNum === 0">{{ row.jobContactSynergyNum }}</div>
              <el-popover v-else placement="top-start" :width="200">
                <template #reference>
                  {{ row.jobContactSynergyNum }}
                </template>
                <div>协同子账号</div>
                <div v-for="item in row.jobContactSynergy" :key="item.id">
                  <div class="flex mt-5">
                    <span class="color-primary mr-5">{{
                      item.companyMemberType === '0' ? '主' : '子'
                    }}</span>
                    <div>{{ item.contact }} / {{ item.department }}</div>
                  </div>
                  <div>{{ item.email }} {{ item.mobile }}</div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 21"
            :key="index"
            align="center"
            header-align="center"
            :label="item.v"
            width="180px"
          >
            <template #default="{ row }">
              <el-button @click="openBusiness(row.id)" type="primary" size="small">业务</el-button>
              <el-popover placement="left" :width="20" trigger="click">
                <template #reference>
                  <el-button plain size="small">...</el-button>
                </template>
                <div class="column btns">
                  <el-button
                    @click="handleRefresh(row.id)"
                    :disabled="row.status != 1"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >刷新
                  </el-button>

                  <!-- 待审核不允许编辑 -->
                  <el-button
                    :disabled="row.auditStatus == 7"
                    @click="handleEdit(row.id)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >编辑
                  </el-button>

                  <el-button
                    @click="openLogDialog(row.id)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >日志
                  </el-button>

                  <el-button
                    @click="handleReleaseAgain(row.id)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >再发布
                  </el-button>

                  <el-button
                    @click="handleOffline(row.id)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >下线
                  </el-button>
                  <!-- isShow 1:显示，2:隐藏 -->
                  <el-button
                    v-if="row.isShow == 2"
                    @click="handleChangeJobShow(row.id, 1)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >显示
                  </el-button>
                  <el-button
                    v-else
                    @click="handleChangeJobShow(row.id, 2)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    >隐藏
                  </el-button>
                  <el-button
                    @click="handleChangeAcademicQualifications(row)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                  >
                    {{ limitEducation(row.isLimitEducation) }}
                  </el-button>
                  <el-button
                    @click="handleChangeAttachmentRestrictions(row)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                  >
                    {{ limitFile(row.isLimitFile) }}
                  </el-button>
                  <el-button
                    v-if="
                      (row.status === 1 || row.status === 0) &&
                      jobQueryBtn.includes('deliveryInviteOpen')
                    "
                    :disabled="row.status === 0"
                    @click="openDeliveryInvite(row)"
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    active-value="1"
                    inactive-value="2"
                    >投递邀约
                  </el-button>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </template>
        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </el-table>
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            label="全选"
            class="mr-10"
            @change="jobSelectChange"
            :indeterminate="isIndeterminate"
            v-model="isCheckAll"
          ></el-checkbox>
          <el-select
            size="small"
            placeholder="批量操作"
            clearable
            filterable
            :disabled="!jobSelection.length"
            v-model="batchValue"
            @change="jobSelectBatch"
          >
            <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k" />
          </el-select>
        </div>
      </div>

      <Pagination
        v-if="pagination.total > 0"
        class="mt-15"
        :total="pagination.total"
        @change="handlePaginationChange"
      />
    </div>
    <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
    <LogDialog ref="logDialog" />
    <JobDetail ref="jobDetail" />
    <DeliveryInvite ref="deliveryInvite" />
    <DialogWelfare
      :member-id="memberId"
      title="职位福利"
      ref="dialogWelfare"
      @confirm="handleWelfare"
    />

    <el-dialog
      v-model="accountVisible"
      title="协同子账号&联系人 批量设置"
      class="account"
      @close="handleAccountDialogClose"
    >
      <div>设置成功，将替换职位原来的相关设置，请谨慎操作</div>
      <div class="mt-20 mb-20">所属单位：{{ checkCompanyName }}</div>
      <el-form :model="accountFrom">
        <JobCooperate
          v-if="accountVisible"
          hasAccount
          :companyId="companyId"
          v-model="synergyData"
        />
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAccountDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleBatchAccount">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  <el-dialog v-model="dialogFormVisible" title="职位编制">
    <el-form :model="establishmentFrom">
      <el-radio-group
        v-model="establishmentFrom.type"
        class="radio-box"
        @change="establishmentTypeChange"
      >
        <el-col><el-radio label="1">请选择编制类型</el-radio></el-col>
        <el-col class="radio-item-box">
          <announcement-check-box
            v-model="establishmentFrom.set"
            :disabled="establishmentDisabled"
            :check-box-list="establishmentTypeList"
          />
        </el-col>

        <el-col><el-radio label="2">取消职位编制(即清空该字段原有设置)</el-radio></el-col>
      </el-radio-group>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="establishmentSubmit"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onMounted, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import FilterView from './components/filter.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog.vue'
import LogDialog from './components/logDialog.vue'
import Pagination from '/@/components/base/paging.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import { useRouter } from 'vue-router'

import { getTableStagingField } from '/@/api/config'
import {
  getJobList,
  jobRefresh,
  jobReleaseAgain,
  jobOffline,
  changeJobShow,
  batchJobRefresh,
  batchChangeJobHide,
  batchChangeJobShow,
  batchJobReleaseAgain,
  batchJobOffline,
  batchJobEstablishment,
  batchAddDeliveryEducationLimit,
  batchDelDeliveryEducationLimit,
  changeDeliveryEducationLimit,
  changeDeliveryFileLimit,
  batchDelDeliveryFileLimit,
  batchAddDeliveryFileLimit,
  getSimpleJobList,
  accountBatchEdit,
  batchEditWelfare,
  batchEditWelfareCheck
} from '/@/api/job'
import DeliveryInvite from '/@/views/job/query/components/deliveryInvite.vue'
import { useStore } from '/@/store'
import { getJobEstablishmentList } from '/@/api/cmsJob'
import JobCooperate from '/@/components/job/jobCooperate.vue'
import DialogWelfare from '/@/components/base/welfare.vue'

export default defineComponent({
  name: 'jobQuery',
  components: {
    AnnouncementCheckBox,
    DeliveryInvite,
    FilterView,
    CustomColumnDialog,
    LogDialog,
    Pagination,
    JobDetail,
    isMiniappChange,
    JobCooperate,
    DialogWelfare
  },
  setup() {
    const dialogWelfare = ref()
    const customColumnDialog = ref()
    const deliveryInvite = ref()
    const logDialog = ref()
    const jobDetail = ref()
    const jobTable = ref()
    const router = useRouter()
    const isIndeterminate = ref(false)
    const establishmentFrom = ref()
    const formLabelWidth = '140px'
    const state = reactive({
      welfareArray: [],
      batchOptions: [
        { k: 1, v: '取消学历限制' },
        { k: 2, v: '添加学历限制' },
        { k: 3, v: '取消附件限制' },
        { k: 4, v: '添加附件限制' },
        { k: 5, v: '刷新职位' },
        { k: 6, v: '隐藏职位' },
        { k: 7, v: '显示职位' },
        { k: 8, v: '再发布职位' },
        { k: 9, v: '下线职位' },
        { k: 10, v: '协同子账号&联系人设置' },
        { k: 11, v: '职位编制设置' },
        { k: 12, v: '批量福利' }
      ],
      dialogFormVisible: false,
      establishmentDisabled: false,
      establishmentFrom: {
        type: '1',
        set: []
      },
      establishmentTypeList: [],
      jobQueryBtn: <any>[],
      isCheckAll: false, // 是否全选
      batchValue: null, // 全选列表选项值
      jobSelection: [], // 选项表
      pageSize: 20,
      loading: false,
      formData: <any>{
        export: 0,
        page: 1,
        limit: 20
      },
      // 分页信息
      pagination: {
        total: 0,
        limit: 20,
        page: 1
      },
      list: [],
      // 统计
      amount: {
        all: '',
        apply: '',
        interview: '',
        click: '',
        platformNum: '',
        emailNum: '',
        linkNum: ''
      },
      companyId: '',
      memberId: '',

      accountFrom: {
        jobContactId: '',
        jobContactSynergyIds: [],
        companyId: computed(() => state.companyId)
      },

      synergyData: computed({
        get() {
          const {
            accountFrom: { jobContactId, jobContactSynergyIds }
          } = state
          return { jobContactId, jobContactSynergyIds }
        },
        set(val: object) {
          Object.keys(val).forEach((key) => {
            state.accountFrom[key] = val[key]
          })
        }
      }),

      isSimple: true,
      accountVisible: false,
      checkCompanyName: ''
    })
    const store = useStore()
    const requestOldRoutesAction = <any>(
      computed(() => store.state.requestOldRoutes.requestOldRoutesAction)
    )
    state.jobQueryBtn = requestOldRoutesAction.value.jobQuery ?? []

    const customColumns = ref([
      {
        k: 1,
        v: '职位',
        name: 'id',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '职位名称',
        name: 'name',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '基本信息',
        name: 'basicInformation',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '关联公告',
        name: 'announcementTitle',
        select: true,
        default: true
      },
      {
        k: 5,
        v: '所属单位',
        name: 'company',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '招聘状态',
        name: 'statusTitle',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '投递次数',
        name: 'jobApplyNum',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '面试邀约',
        name: 'jobInterviewNum',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '发布时间',
        name: 'releaseTime',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '刷新时间',
        name: 'refreshTime',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '招聘人数',
        name: 'amount',
        select: false,
        default: false
      },
      {
        k: 12,
        v: '点击次数',
        name: 'click',
        select: false,
        default: false
      },
      {
        k: 13,
        v: '下载简历',
        name: 'downloadAmount',
        select: false,
        default: false
      },
      {
        k: 14,
        v: '用人部门',
        name: 'department',
        select: false,
        default: false
      },
      {
        k: 15,
        v: '审核状态',
        name: 'auditStatusTitle',
        select: false,
        default: false
      },
      {
        k: 16,
        v: '发布模式',
        name: 'isArticleTitle',
        select: false,
        default: false
      },
      {
        k: 17,
        v: '创建人',
        name: 'creator',
        select: false,
        default: false
      },
      {
        k: 18,
        v: '创建时间',
        name: 'addTime',
        select: false,
        default: false
      },
      {
        k: 19,
        v: '下线时间',
        name: 'periodDate',
        select: false,
        default: false
      },
      {
        k: 20,
        v: '删除时间',
        name: 'deleteDate',
        select: false,
        default: false
      },
      {
        k: 22,
        v: '学科专业',
        name: 'majorTxt',
        select: false,
        default: false
      },
      {
        k: 23,
        v: '是否小程序',
        name: 'isMiniappTxt',
        select: false,
        default: false
      },
      {
        k: 24,
        v: '职位联系人',
        name: 'contact',
        select: false,
        default: false
      },

      {
        k: 25,
        v: '协同子账号',
        name: 'contactSynergyNum',
        select: false,
        default: false
      },
      {
        k: 21,
        v: '操作',
        name: 'operation',
        disabled: true,
        select: true,
        default: true
      }
    ])

    onMounted(() => {
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
      getTableStagingField('jobQuery').then((resp: any) => {
        if (!resp.value) return
        const value = resp.value.split(',')
        customColumns.value = customColumns.value.map((item: any) => {
          return {
            ...item,
            select: value.includes(item.name)
          }
        })
      })
    })

    const getList = async () => {
      state.loading = true
      if (state.isSimple) {
        await getSimpleJobList({
          ...state.formData,
          majorId:
            state.formData.majorId instanceof Array
              ? state.formData.majorId.join()
              : state.formData.majorId,
          isCooperation: 1
        }).then((resp: any) => {
          state.list = resp.list
          state.amount = resp.amount ?? {}
          state.pagination.total = Number(resp.page?.count)
        })
        state.loading = false
      } else {
        await getJobList({
          ...state.formData,
          majorId:
            state.formData.majorId instanceof Array
              ? state.formData.majorId.join()
              : state.formData.majorId,
          isCooperation: 1
        }).then((resp: any) => {
          state.list = resp.list
          state.amount = resp.amount
          state.pagination.total = Number(resp.page?.count)
        })
        state.loading = false
      }
    }

    const openDialogWelfare = () => {
      dialogWelfare.value.openDialog([])
    }

    const handleWelfare = async (welfare: any) => {
      const batchSelectJobIds = state.jobSelection.map((item: any) => item.id)?.join(',')
      const welfareTag = welfare.map((item: any) => item.k).join(',')
      state.welfareArray = welfare
      await batchEditWelfare({
        jobIds: batchSelectJobIds,
        welfareIds: welfareTag
      }).then(() => {
        getList()
      })
    }

    // 提示框
    function messageBox(title, message, callback) {
      ElMessageBox({
        title,
        message,
        showCancelButton: true,
        beforeClose(action, instance, done) {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            callback()
              .then(() => {
                instance.confirmButtonLoading = false
                done()
                getList()
              })
              .catch(() => {
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
    }

    // 排序
    const handleSortable = ({ prop, order }) => {
      // 删除干扰参数
      const deleteKey = [
        'sortName',
        'sortApplyNum',
        'sortInterviewNum',
        'sortRefreshTime',
        'sortReleaseTime',
        'sortDownloadNum',
        'sortAmount',
        'sortClick',
        'sortAddTime',
        'sortPeriodDate',
        'sortDeleteDate'
      ]
      deleteKey.forEach((key: string) => {
        Reflect.deleteProperty(state.formData, key)
      })
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getList()
    }
    // 选择列表变化
    const handleSelectionChange = (data: any) => {
      state.jobSelection = data
      if (data.length === state.list.length) {
        state.isCheckAll = true
        isIndeterminate.value = false
      } else {
        state.isCheckAll = false
        isIndeterminate.value = data.length > 0
      }
    }

    const handleSearch = (filter: any) => {
      state.formData = {
        ...state.formData,
        ...filter
      }
      getList()
    }
    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }
    const handleOpenCustomColumn = () => {
      customColumnDialog.value.open('jobQuery')
    }
    const openLogDialog = (id: string) => {
      logDialog.value.open(id)
    }

    //职位编制
    const getJobEstablishment = () => {
      getJobEstablishmentList().then((resp: any) => {
        state.establishmentTypeList = resp
      })
    }
    getJobEstablishment()

    const openDeliveryInvite = (row: object) => {
      deliveryInvite.value.open(row)
    }

    const handleEdit = (id: string) => {
      router.push({
        path: `/job/edit/${id}`
      })
    }

    const openBusiness = (id: string) => {
      router.push({
        path: `/job/query/business/${id}`
      })
    }

    // 刷新
    const handleRefresh = (id: string) => {
      messageBox('提示', '是否刷新该职位？', async () => {
        await jobRefresh({ id })
      })
    }
    // 再发布
    const handleReleaseAgain = (id: string) => {
      messageBox('提示', '确认再次发布吗？', async () => {
        await jobReleaseAgain({ id })
      })
    }

    const handleOfflinePrompt = (callback) => {
      ElMessageBox.prompt('请填写职位下线原因', '提示', {
        showCancelButton: true,
        inputPattern: /[\S]/,
        inputErrorMessage: '请填写职位下线原因',
        beforeClose(action, instance, done) {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const { inputValue } = instance
            callback(inputValue, instance, done)
          } else {
            done()
          }
        }
      })
    }

    const handleEstablishment = () => {
      state.dialogFormVisible = true
    }

    const establishmentTypeChange = () => {
      state.establishmentFrom.set = []
      if (state.establishmentFrom.type === '1') {
        state.establishmentDisabled = false
      } else {
        state.establishmentDisabled = true
      }
    }

    const establishmentSubmit = () => {
      const { jobSelection } = state
      const ids = jobSelection.map((item: any) => item.id).join()
      if (state.establishmentFrom.type === '1' && state.establishmentFrom.set.length <= 0) {
        ElMessage({
          message: '请选择编制类型',
          type: 'warning',
          duration: 5000
        })
        return
      }
      batchJobEstablishment({ id: ids, ...state.establishmentFrom }).then((resp) => {
        state.dialogFormVisible = false
        getList()
      })
    }

    // 下线
    const handleOffline = (id: string) => {
      handleOfflinePrompt(async (value, instance, done) => {
        try {
          await jobOffline({ id, reason: value })
          instance.confirmButtonLoading = false
          done()
          getList()
        } catch {
          instance.confirmButtonLoading = false
        }
      })
    }
    // 显示职位
    const handleChangeJobShow = (id: string, isShow: any) => {
      // isShow 1:显示，2:隐藏
      const message = `确认${isShow === 2 ? '隐藏' : '显示'}该职位吗？`
      messageBox('提示', message, async () => {
        await changeJobShow({ id, isShow })
      })
    }
    // 全选按钮
    const jobSelectChange = () => {
      jobTable.value.toggleAllSelection()
    }

    const batchCatch = async (api: Function, params: Object = {}) => {
      const { jobSelection } = state
      const ids = jobSelection.map((item: any) => item.id).join()
      const { content } = await api({ ids, ...params })
      if (content) {
        ElMessageBox.alert(content, '提示', {
          dangerouslyUseHTMLString: true,
          showConfirmButton: false
        })
      }
      state.batchValue = null
      getList()
    }

    // 全选选项改变
    const jobSelectBatch = async (val) => {
      const options = {
        1: batchDelDeliveryEducationLimit,
        2: batchAddDeliveryEducationLimit,
        3: batchDelDeliveryFileLimit,
        4: batchAddDeliveryFileLimit,
        5: batchJobRefresh,
        6: batchChangeJobHide,
        7: batchChangeJobShow,
        8: batchJobReleaseAgain,
        9: batchJobOffline,
        11: batchJobEstablishment,
        12: batchEditWelfare
      }

      state.batchValue = null

      if (options[val]) {
        if (val === 9) {
          handleOfflinePrompt(async (value, instance, done) => {
            try {
              await batchCatch(options[val], { reason: value })
              done()
            } finally {
              instance.confirmButtonLoading = false
            }
          })
          return
        } else if (val === 11) {
          handleEstablishment()
          return
        } else if (val === 12) {
          const { jobSelection } = state
          const jobIds = jobSelection.map((item: any) => item.id).join()
          const res = await batchEditWelfareCheck({ jobIds })
          if (res.companyMemberId) {
            state.memberId = res.companyMemberId
            openDialogWelfare()
          }
          return
        }

        batchCatch(options[val])
      }

      if (val === 10) {
        const companyIds = state.jobSelection.map((item: any) => item.companyId)

        const subUsed = state.jobSelection.every((item: any) => item.subUsed === 0)

        const isSame = companyIds.every((item: any) => item === companyIds[0])
        state.companyId = <any>companyIds[0]

        state.checkCompanyName = state.jobSelection[0].company
        if (!isSame) {
          ElMessage.warning('请选择同一单位的职位！')
          return
        }

        if (subUsed) {
          ElMessage.warning('请先添加子账号！')
          return
        }
        state.accountVisible = true
      }
    }

    const handleBatchAccount = async () => {
      const batchSelectJobIds = state.jobSelection.map((item: any) => item.id)?.join(',')
      await accountBatchEdit({ ...state.accountFrom, jobIds: batchSelectJobIds })
      state.accountVisible = false
      getList()
    }

    // 职位详情
    const handleJobDetail = (id: string) => {
      jobDetail.value.open(id)
    }
    // 学历限制
    const limitEducation = (status: any) => {
      if (status === 2) {
        return '添加学历限制'
      }
      return '取消学历限制'
    }
    const handleChangeLimit = (row, status, changeLimit) => {
      const { id } = row
      const normalState = status === 2
      const changeType = normalState ? 1 : 2
      const data = { id, isLimit: changeType }
      const message = `确认${normalState ? '添加' : '取消'}该职位限制吗？`
      messageBox('提示', message, async () => {
        await changeLimit(data)
      })
    }
    // 修改学历限制
    const handleChangeAcademicQualifications = async (row) => {
      handleChangeLimit(row, row.isLimitEducation, changeDeliveryEducationLimit)
    }
    // 修改附件限制
    const limitFile = (status: any) => {
      if (status === 2) {
        return '添加附件限制'
      }
      return '取消附件限制'
    }

    const handleChangeAttachmentRestrictions = (row) => {
      handleChangeLimit(row, row.isLimitFile, changeDeliveryFileLimit)
    }

    const handleAccountDialogClose = () => {
      state.accountFrom.jobContactId = ''
      state.accountFrom.jobContactSynergyIds = []
      state.accountVisible = false
    }

    return {
      dialogWelfare,
      openDialogWelfare,
      handleWelfare,
      establishmentFrom,
      establishmentSubmit,
      establishmentTypeChange,
      handleSearch,
      formLabelWidth,
      getList,
      handleSortable,
      handleOpenCustomColumn,
      deliveryInvite,
      openDeliveryInvite,
      customColumnDialog,
      logDialog,
      openLogDialog,
      customColumns,
      handleEdit,
      handleRefresh,
      openBusiness,
      handleOffline,
      handleReleaseAgain,
      handlePaginationChange,
      handleChangeJobShow,
      jobDetail,
      handleJobDetail,
      handleSelectionChange,
      jobSelectChange,
      jobTable,
      handleChangeAcademicQualifications,
      handleChangeAttachmentRestrictions,
      jobSelectBatch,
      isIndeterminate,
      batchJobReleaseAgain,
      batchJobOffline,
      batchJobEstablishment,
      batchEditWelfare,
      limitEducation,
      limitFile,
      changeDeliveryEducationLimit,
      batchDelDeliveryFileLimit,
      batchAddDeliveryFileLimit,
      handleBatchAccount,
      handleAccountDialogClose,
      ...toRefs(state)
    } as any
  }
})
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;

  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}

.btns {
  .el-button + .el-button {
    margin-left: 0;
  }
}

.radio-box {
  margin-left: 20px;
}

.radio-item-box {
  margin-left: 20px;
  margin-bottom: 20px;
}
.dialog-footer {
  text-align: center;
}
</style>
