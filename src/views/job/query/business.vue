<template>
  <div class="box">
    <el-tabs type="card" v-model="active" @tab-change="handleTabChange">
      <el-tab-pane label="收到的简历" name="1">
        <el-form ref="receivedForm" :model="receivedFormData" class="mt-10" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="receivedFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="receivedFormData.addTimeStart"
              v-model:end="receivedFormData.addTimeEnd"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource v-model="receivedFormData.deliveryWay" placeholder="全部" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getApplyData">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table size="small" :data="resumeList" border v-loading="loading">
          <el-table-column
            prop="uid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmentTitle"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deliveryWayTxt"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="1"
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-popover placement="top" width="650px" trigger="click">
                <template #reference>
                  <el-link class="fs-13" :underline="false" type="primary"
                    >查看进度
                    <i class="el-icon-caret-bottom"></i>
                  </el-link>
                </template>
                <el-steps
                  :active="row.schedule"
                  finish-status="success"
                  process-status="wait"
                  align-center
                >
                  <el-step title="已投递" :description="row.scheduleTimeList[0]"></el-step>
                  <el-step title="被查看" :description="row.scheduleTimeList[1]"></el-step>
                  <el-step title="通过初筛" :description="row.scheduleTimeList[2]"></el-step>
                  <el-step title="面试" :description="row.scheduleTimeList[3]"></el-step>
                </el-steps>
              </el-popover>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="receivedPaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="面试邀约" name="2">
        <el-form ref="inviteForm" :model="inviteFormData" class="mt-10" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="inviteFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="invitationTimeStart">
            <DatePickerRange
              v-model:start="inviteFormData.invitationTimeStart"
              v-model:end="inviteFormData.invitationTimeEnd"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getInviteData">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table size="small" :data="inviteList" border>
          <el-table-column
            prop="uid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewTime"
            align="center"
            header-align="center"
            label="面试时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="面试详情"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-popover placement="top" width="400px" trigger="click">
                <template #reference>
                  <el-link
                    @click="handleGetInterviewDetail(row.id)"
                    class="fs-13"
                    :underline="false"
                    type="primary"
                    >查看</el-link
                  >
                </template>
                <div class="px-15">
                  <div class="fw-bold my-15 flex">
                    面试信息
                    <span class="invite-amount"
                      >{{ interviewDetail.invitationStatusAmount }}面</span
                    >
                  </div>
                  <div class="mb-10">面试职位：{{ interviewDetail.jobName }}</div>
                  <div class="mb-10">面试时间：{{ interviewDetail.interviewTime }}</div>
                  <div class="mb-10">面试地址：{{ interviewDetail.address }}</div>
                  <div class="mb-10">联 系 人：{{ interviewDetail.contact }}</div>
                  <div class="mb-10">联系电话：{{ interviewDetail.telephone }}</div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="invitePaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane label="下载的简历" name="3">
        <el-form ref="downloadForm" :model="downloadFormData" class="mt-10" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="downloadFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="downloadTimeStart">
            <DatePickerRange
              v-model:start="downloadFormData.downloadTimeStart"
              v-model:end="downloadFormData.downloadTimeEnd"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item>
            <ResumeType v-model="downloadFormData.resumeType" placeholder="简历类型" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getDownloadData">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="downloadList" border size="small">
          <el-table-column
            prop="uid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeTypeTitle"
            align="center"
            header-align="center"
            label="简历类型"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmenName"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="downloadTime"
            align="center"
            header-align="center"
            label="下载时间"
            show-overflow-tooltip
          ></el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="downloadPaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import ResumeSource from '/@select/resumeSource.vue'
import ResumeType from '/@select/resumeType.vue'
import Pagination from '/@/components/base/paging.vue'

import {
  getApplyList,
  getInvitationList,
  getResumeDownloadList,
  getCompanyInterviewDetail
} from '/@/api/job'

export default {
  name: 'jobQueryBuness',
  components: { DatePickerRange, ResumeSource, ResumeType, Pagination },
  props: {
    data: {}
  },
  emits: ['confirm'],
  setup() {
    const route = useRoute()
    const state = reactive({
      loading: false,
      active: '1',
      jobId: '',
      receivedFormData: {
        resumeName: '',
        addTimeStart: '',
        addTimeEnd: '',
        deliveryWay: '',
        page: 1,
        limit: 20
      },
      inviteFormData: {
        resumeName: '',
        invitationTimeStart: '',
        invitationTimeEnd: '',
        page: 1,
        limit: 20
      },
      // 面试信息
      interviewDetail: <any>{},
      downloadFormData: {
        resumeName: '',
        downloadTimeStart: '',
        downloadTimeEnd: '',
        resumeType: '',
        page: 1,
        limit: 20
      },
      pagination: {
        total: 0
      },
      resumeList: [],
      inviteList: [],
      downloadList: []
    })

    const getApplyData = () => {
      state.loading = true
      getApplyList({
        jobId: state.jobId,
        ...state.receivedFormData
      }).then((resp: any) => {
        state.loading = false
        state.resumeList = resp.list
        state.pagination.total = Number(resp.page.count)
      })
    }

    const getInviteData = () => {
      state.loading = true
      getInvitationList({
        jobId: state.jobId,
        ...state.inviteFormData
      }).then((resp: any) => {
        state.loading = false
        state.inviteList = resp.list
        state.pagination.total = Number(resp.page.count)
      })
    }

    const getDownloadData = () => {
      state.loading = true
      getResumeDownloadList({
        jobId: state.jobId,
        ...state.downloadFormData
      }).then((resp: any) => {
        state.loading = false
        state.downloadList = resp.list
        state.pagination.total = Number(resp.page.count)
      })
    }

    onMounted(() => {
      const { id } = route.params
      if (id) {
        state.jobId = String(id)
        getApplyData()
      }
    })

    const handleGetInterviewDetail = (id: string) => {
      getCompanyInterviewDetail({ applyId: id }).then((resp: any) => {
        state.interviewDetail = resp
      })
    }

    const receivedForm = ref()
    const inviteForm = ref()
    const downloadForm = ref()
    const handleClose = () => {
      receivedForm.value.resetFields()
      inviteForm.value.resetFields()
      downloadForm.value.resetFields()
    }

    const handleTabChange = () => {
      switch (state.active) {
        case '1':
          getApplyData()
          break
        case '2':
          getInviteData()
          break
        case '3':
          getDownloadData()
          break
        default:
          break
      }
    }

    const receivedPaginationChange = (data: any) => {
      state.receivedFormData.limit = data.limit
      state.receivedFormData.page = data.page
      getApplyData()
    }
    const invitePaginationChange = (data: any) => {
      state.inviteFormData.limit = data.limit
      state.inviteFormData.page = data.page
      getInviteData()
    }
    const downloadPaginationChange = (data: any) => {
      state.downloadFormData.limit = data.limit
      state.downloadFormData.page = data.page
      getDownloadData()
    }

    return {
      receivedForm,
      inviteForm,
      downloadForm,
      handleGetInterviewDetail,
      getApplyData,
      getInviteData,
      getDownloadData,
      handleClose,
      handleTabChange,
      receivedPaginationChange,
      invitePaginationChange,
      downloadPaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.invite-amount {
  font-size: 10px;
  color: #02a7f0;
  background-color: #f2f2f2;
  align-self: center;
  padding: 1px 8px;
  border-radius: 2px;
  margin-left: 10px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
  }
}
</style>
