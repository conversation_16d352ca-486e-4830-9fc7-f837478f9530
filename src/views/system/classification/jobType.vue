<template>
  <div class="main">
    <el-card>
      <div class="button-box">
        <el-button type="primary" size="small" @click="addJob">+ 添加</el-button>
        <el-button v-show="!expandAll" size="small" @click="expandAll = true">展开所有</el-button>
        <el-button v-show="expandAll" size="small" @click="expandAll = false">折叠所有</el-button>
      </div>
      <div class="table-box">
        <el-table
          :data="tableData"
          align="center"
          row-key="id"
          :expand-row-keys="expandRowKeys"
          v-if="refreshTable"
          class="table"
          v-loading="loading"
        >
          <el-table-column type="selection" />
          <el-table-column label="分类名称" prop="name" width="300"> </el-table-column>
          <el-table-column label="ID" prop="id"> </el-table-column>
          <el-table-column label="别名" prop="spell"> </el-table-column>
          <el-table-column label="排序" prop="sort"> </el-table-column>
          <el-table-column label="操作" width="210">
            <template #default="scope">
              <el-row>
                <el-col :span="6">
                  <el-button
                    size="small"
                    v-show="scope.row.parentId === '0'"
                    @click="addClass(scope.row)"
                    >添加子分类</el-button
                  >
                </el-col>
                <el-col :span="6">
                  <el-button size="small" @click="editJobType(scope.row)">编辑</el-button> </el-col
                ><el-col :span="6">
                  <el-button size="small">删除</el-button>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-dialog v-model="addDialog" title="添加分类">
      <el-form :model="form">
        <el-form-item label="所属分类"> {{ currentName }} </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="form.name"> </el-input>
        </el-form-item>
        <el-form-item label="排序">
          <el-input v-model="form.sort"> </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="comfirmAdd(form)">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, nextTick, onMounted, reactive, toRefs } from 'vue'
import { addJobType, getJobTypeList } from '/@/api/system'
import Sortable from 'sortablejs'

export default defineComponent({
  name: 'jobType',
  components: {},
  setup() {
    const state = <any>reactive({
      tableData: [],
      addDialog: false,
      refreshTable: true,
      expandAll: false,
      expandKeys: [],
      loading: false,
      expandRowKeys: computed(() => (state.expandAll ? state.expandKeys : [])),
      form: {
        name: '',
        sort: '',
        id: '',
        parentId: '',
        spell: ''
      },
      currentName: ''
    })
    const getData = async () => {
      state.loading = true
      const res = await getJobTypeList()
      const keys = <string[]>res.reduce((previous: any, current: any) => {
        const result = [...previous]
        result.push(current.id)
        state.loading = false
        return result
      }, [])

      state.tableData = res
      state.expandKeys = keys
    }
    getData()
    const addJob = () => {
      state.currentName = '顶级'
      state.addDialog = true
    }
    const comfirmAdd = async (data: Object) => {
      await addJobType(data)
      state.addDialog = false
      state.form.name = ''
      state.form.sort = ''
      getData()
    }
    const cancel = () => {
      state.addDialog = false
      state.form.name = ''
      state.form.sort = ''
    }
    const editJobType = (data: any) => {
      const { name, sort, parentId, id } = data
      state.form.name = name
      state.form.sort = sort
      state.form.parentId = parentId
      state.form.id = id
      state.currentName = '顶级'
      if (parentId !== '0') {
        const current = <any>state.tableData.filter((item: any) => item.id === parentId)[0]
        state.currentName = current.name
      }
      state.addDialog = true
    }
    const addClass = (data: any) => {
      state.addDialog = true
      state.currentName = data.name
      state.form.parentId = data.id
    }

    // 方法初始化拖拽
    const rowDrop = () => {
      const body = <any>document.querySelector('.el-table__body-wrapper tbody')
      if (state.refreshTable) {
        Sortable.create(body, {
          animation: 150,
          onEnd(evt: any) {
            const newArr = state.tableData
            const { oldIndex, newIndex } = evt
            const currRow = newArr.splice(oldIndex, 1)[0]
            newArr.splice(newIndex - currRow.children.length, 0, currRow)
            state.tableData = []
            nextTick(() => {
              state.tableData = newArr
            })
          }
        })
      }
    }

    onMounted(() => {
      rowDrop()
    })
    return {
      ...toRefs(state),
      addJob,
      comfirmAdd,
      cancel,
      editJobType,
      addClass
    }
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  margin-bottom: 70px;
  .el-row {
    justify-content: space-around;
    .el-col {
      max-width: none;
      .el-button--mini {
        padding: 7px 10px;
      }
    }
  }
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
