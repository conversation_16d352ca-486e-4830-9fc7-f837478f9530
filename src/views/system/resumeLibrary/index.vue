<template>
  <div class="box">
    <el-card>
      <div class="container">
        <div class="title">简历完善度限制</div>

        <div class="single-input">
          <span>简历完善度 ≧</span>
          <span class="input-text" v-if="completeState">{{ complete[0].value }}</span>
          <el-input
            v-else
            v-model="complete[0].value"
            ref="completeRef"
            placeholder="50～100的整数"
            @input="formatInputValue('complete')"
          />
          <span>%，方可进入人才库</span>
        </div>

        <el-button v-if="completeState" type="primary" @click="changeState('complete')">
          设置
        </el-button>
        <el-button v-if="!completeState" type="primary" @click="setCompleteValue">保存</el-button>
        <el-button v-if="!completeState" type="info" @click="reset('complete')">取消</el-button>
      </div>

      <div class="container">
        <div class="single-input">
          <span>职位邀约投递次数：</span>
          <span class="input-text" v-if="inviteState">{{ invite[0].value }}</span>
          <el-input
            v-else
            v-model="invite[0].value"
            ref="inviteRef"
            placeholder="正整数（不包括零）"
            @input="formatInputValue('invite')"
          />
          <span>次 / 日</span>
        </div>

        <el-button v-if="inviteState" type="primary" @click="changeState('invite')">设置</el-button>
        <el-button v-if="!inviteState" type="primary" @click="setInviteValue">保存</el-button>
        <el-button v-if="!inviteState" type="info" @click="reset('invite')">取消</el-button>
      </div>

      <div class="container resume-download">
        <div class="title">简历下载消耗点数</div>

        <el-table :data="download" :border="true">
          <el-table-column prop="name" label="简历类型" align="center"></el-table-column>
          <el-table-column prop="value" label="消耗点数（点/份）" align="center">
            <template #default="scope">
              <span class="input-text" v-if="downloadState">{{ scope.row.value }}</span>

              <el-input
                v-else
                v-model="scope.row.value"
                :ref="scope.$index === 0 ? 'downloadRef' : ''"
                placeholder="正整数（包括零）"
                @input="formatInputValue('download')"
              />
            </template>
          </el-table-column>
        </el-table>

        <el-button v-if="downloadState" type="primary" @click="changeState('download')">
          设置
        </el-button>
        <el-button v-if="!downloadState" type="primary" @click="setDownloadValue">保存</el-button>
        <el-button v-if="!downloadState" type="info" @click="reset('download')">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref, nextTick } from 'vue'
import { getResumeLibrary, setResumeLibrary } from '/@/api/system'
import { ElMessage } from 'element-plus'

export default {
  setup() {
    const inviteRef = ref()
    const completeRef = ref()
    const downloadRef = ref()

    const state = reactive({
      inviteState: true,
      completeState: true,
      downloadState: true,

      complete: [{ key: 'resumeLibraryCompleteMin', value: '' }],

      invite: [{ key: 'resumeLibraryCompanyInviteCountDay', value: '' }],

      download: [
        { key: 'resumeLibraryDownloadPointElite', name: '精英简历', value: '' },
        { key: 'resumeLibraryDownloadPointHighQuality', name: '优质简历', value: '' },
        { key: 'resumeLibraryDownloadPointOrdinary', name: '普通简历', value: '' }
      ],

      initData: {
        resumeLibraryCompanyInviteCountDay: '',
        resumeLibraryCompleteMin: '',
        resumeLibraryDownloadPointElite: '',
        resumeLibraryDownloadPointHighQuality: '',
        resumeLibraryDownloadPointOrdinary: ''
      }
    })

    const formatInputValue = (type: any) => {
      if (type === 'download') {
        state[type].forEach((item, index) => {
          state[type][index].value = item.value.replace(/^0[0]+/, '0').replace(/[^\d]/g, '')
        })
      } else {
        state[type].forEach((item, index) => {
          state[type][index].value = item.value.replace(/^0+|[^\d]/g, '')
        })
      }
    }

    const regList = (type: any) => {
      switch (type) {
        case 'complete':
          return new RegExp('^([5-9][0-9]|100)$')
        case 'invite':
          return new RegExp('^[1-9]([0-9]?)+$')
        case 'download':
          return new RegExp('^[0-9]([0-9]?)+$')
        default:
          return false
      }
    }

    const validRules = (type: any) => {
      state[type].forEach((item, index) => {
        if (item.value) {
          const value = item.value.match(/[1-9][0-9]*|^0$/)[0]
          state[type][index].value = value
        }
      })

      const reg = regList(type)

      const error = state[type].filter((value) => {
        return reg ? !reg.test(value.value) : ''
      })

      return error.length === 0
    }

    const init = async () => {
      state.initData = await getResumeLibrary()

      const { resumeLibraryCompanyInviteCountDay, resumeLibraryCompleteMin, ...data } =
        state.initData

      state.invite[0].value = resumeLibraryCompanyInviteCountDay
      state.complete[0].value = resumeLibraryCompleteMin
      state.download.forEach((item, index) => {
        state.download[index].value = data[item.key] === '' ? 0 : data[item.key]
      })
    }

    const setValue = async (array: Array<any>) => {
      const data = {}

      const result = array.filter((value) => {
        return value.value !== state.initData[value.key]
      })

      if (result.length) {
        result.forEach((item) => {
          state.initData[item.key] = item.value
          data[item.key] = state.initData[item.key]
        })

        await setResumeLibrary(data)
      }
    }

    const setCompleteValue = () => {
      const { complete, completeState } = state

      if (validRules('complete')) {
        setValue(complete)
        state.completeState = !completeState
      } else ElMessage.error('简历完善度请输入50～100的整数。')
    }

    const setInviteValue = () => {
      const { invite, inviteState } = state

      if (validRules('invite')) {
        setValue(invite)
        state.inviteState = !inviteState
      } else ElMessage.error('职位邀约投递的次数请输入正整数（不包括零）。')
    }

    const setDownloadValue = () => {
      const { download, downloadState } = state

      if (validRules('download')) {
        setValue(download)
        state.downloadState = !downloadState
      } else ElMessage.error('消耗点数请输入正整数（包括0）。')
    }

    const changeState = (type: string) => {
      switch (type) {
        case 'complete':
          state.completeState = !state.completeState
          nextTick(() => completeRef.value.focus())
          break
        case 'download':
          state.downloadState = !state.downloadState
          nextTick(() => downloadRef.value.focus())
          break
        case 'invite':
          state.inviteState = !state.inviteState
          nextTick(() => inviteRef.value.focus())
          break
        default:
          break
      }
    }

    const reset = (type: any) => {
      const { initData } = state

      state[type].forEach((item, index) => {
        state[type][index].value = initData[item.key]
      })

      switch (type) {
        case 'invite':
          state.inviteState = !state.inviteState
          break
        case 'complete':
          state.completeState = !state.completeState
          break
        case 'download':
          state.downloadState = !state.downloadState
          break
        default:
          break
      }
    }

    onMounted(() => {
      init()
    })

    return {
      ...toRefs(state),
      completeRef,
      downloadRef,
      inviteRef,
      formatInputValue,
      changeState,
      setCompleteValue,
      setDownloadValue,
      setInviteValue,
      reset
    }
  }
}
</script>

<style lang="scss" scoped>
.el-input {
  width: 150px;
  height: 35px;
  line-height: 35px;
  font-size: 15px;
}
.input-text {
  margin: 0 11px;
  line-height: 35px;
  font-size: 15px;
}
.title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
.container {
  margin-bottom: 60px;
  .el-button {
    &.el-button {
      margin-left: 0;
      margin-right: 12px;
    }
  }
}
.single-input {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .el-input {
    margin: 0 11px;
  }
}
.el-table {
  width: 350px;
  margin-bottom: 15px;
}
</style>
