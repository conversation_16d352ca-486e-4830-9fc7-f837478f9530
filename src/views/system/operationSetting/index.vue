<template>
  <div class="main">
    <el-tabs v-model="activeName" type="border-card" v-loading="loading">
      <el-tab-pane label="首页-竖版导航栏--特色专栏" name="first">
        <el-form label-width="50px" :inline="true">
          <span>专题展</span>
          <br />
          <br />
          <el-form-item label="更多">
            <el-input v-model="pcHomeSubNavColumnSpecial.moreLink" class="url" />
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeSubNavColumnSpecial">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in pcHomeSubNavColumnSpecial.list" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.name" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deletePcHomeSubNavColumnSpecial(index)"
                >删除</el-button
              >
              <el-button type="primary" @click="upPcHomeSubNavColumnSpecial(index)">上移</el-button>
              <el-button type="warning" @click="downPcHomeSubNavColumnSpecial(index)"
                >下移</el-button
              >
            </el-form-item>
          </div>
          <el-divider />
          <span>情报局</span>
          <br />
          <br />
          <el-form-item label="更多">
            <el-input v-model="pcHomeSubNavColumnCIA.moreLink" class="url" />
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeSubNavColumnCIA">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in pcHomeSubNavColumnCIA.list" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.name" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deletePcHomeSubNavColumnCIA(index)">删除</el-button>
              <el-button type="primary" @click="upPcHomeSubNavColumnCIA(index)">上移</el-button>
              <el-button type="warning" @click="downPcHomeSubNavColumnCIA(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-divider />
          <span>资讯栏目</span>
          <br />
          <br />
          <el-form-item label="更多">
            <el-input v-model="pcHomeSubNavColumnServiceStation.moreLink" class="url" />
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeSubNavColumnServiceStation"
              >新增一条</el-button
            >
          </el-form-item>
          <div v-for="(domain, index) in pcHomeSubNavColumnServiceStation.list" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.name" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deletePcHomeSubNavColumnServiceStation(index)"
                >删除</el-button
              >
              <el-button type="primary" @click="upPcHomeSubNavColumnServiceStation(index)"
                >上移</el-button
              >
              <el-button type="warning" @click="downPcHomeSubNavColumnServiceStation(index)"
                >下移</el-button
              >
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('homeSubNavColumn')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="热门专题" name="second">
        <el-form label-width="50px" :inline="true">
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeHotTopic">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in pcHomeHotTopic" :key="domain.key">
            <!-- 第一条特殊一点  -->
            <el-form-item label="标题">
              <el-input v-model="domain.title" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>

            <el-form-item v-if="index == 0">
              <!--图片上传 + 显示-->
              <el-upload
                class="avatar-uploader"
                action="/upload/image"
                :show-file-list="false"
                :on-success="handleImageSuccessPcHomeHotTopic"
                :before-upload="beforeImageUpload"
              >
                <img v-if="domain.img" :src="domain.img" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>

            <el-form-item v-if="index != 0">
              <!-- 切换按钮     0非热门,1热门         -->
              <el-switch
                v-model="domain.hot"
                active-text="热门"
                active-value="1"
                inactive-value="0"
                inactive-text="非热门"
              >
              </el-switch>
              &nbsp;&nbsp;
              <el-button type="danger" @click="deletePcHomeHotTic(index)">删除</el-button>
              <el-button type="primary" @click="upPcHomeHotTic(index)">上移</el-button>
              <el-button type="warning" @click="downPcHomeHotTic(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('homeHotTopic')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="首页-人才专场-栏目下拉框" name="three">
        <el-form label-width="50px" :inline="true">
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeNavTalentTag">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in pcHomeNavTalentTag" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.name" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deletePcHomeNavTalentTag(index)">删除</el-button>
              <el-button type="primary" @click="upPcHomeNavTalentTag(index)">上移</el-button>
              <el-button type="warning" @click="downPcHomeNavTalentTag(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('homeNavTalentTag')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="二级栏目-备份" name="four">
        <el-form label-width="50px">
          <el-form-item label="链接">
            <el-input
              v-model="pcSecondColumnReserved.targetLink"
              placeholder="图片链接"
              class="title"
            />
          </el-form-item>
          <el-form-item label="图片">
            <el-upload
              class="avatar-uploader"
              action="/upload/image"
              :show-file-list="false"
              :on-success="handleImageSuccessPcSecondColumnReserved"
              :before-upload="beforeImageUpload"
            >
              <img
                v-if="pcSecondColumnReserved.imageLink"
                :src="pcSecondColumnReserved.imageLink"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('secondColumnReserved')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="首页-活动" name="five">
        <el-form label-width="50px" :inline="true">
          <el-form-item label="">
            <el-button type="primary" @click="addPcHomeActivity">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in pcHomeActivity" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.title" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item label="时间">
              <el-input v-model="domain.time" placeholder="时间" class="url" />
            </el-form-item>
            <!--       是否热门     -->
            <el-form-item>
              <el-switch
                v-model="domain.isRun"
                active-text="进行中"
                active-value="1"
                inactive-value="0"
                inactive-text="非进行中"
              >
              </el-switch>
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deletePcHomeActivity(index)">删除</el-button>
              <el-button type="primary" @click="upPcHomeActivity(index)">上移</el-button>
              <el-button type="warning" @click="downPcHomeActivity(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('homeActivity')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="h5-右上角菜单栏-专题" name="six">
        <el-form label-width="50px" :inline="true">
          <el-form-item label="">
            <el-button type="primary" @click="addH5SpecialSubject">新增一条</el-button>
          </el-form-item>
          <div v-for="(domain, index) in h5SpecialSubject" :key="domain.key">
            <el-form-item label="标题">
              <el-input v-model="domain.name" placeholder="标题" class="title" />
            </el-form-item>
            <el-form-item label="链接">
              <el-input v-model="domain.url" placeholder="链接" class="url" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deleteH5SpecialSubject(index)">删除</el-button>
              <el-button type="primary" @click="upH5SpecialSubject(index)">上移</el-button>
              <el-button type="warning" @click="downH5SpecialSubject(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('h5SpecialSubject')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="移动-职位-热门搜索" name="seven">
        <!-- 都是input       -->
        <el-form label-width="50px" :inline="true">
          <el-form-item label="">
            <el-button type="primary" @click="addH5HotSearchListJob">新增一条</el-button>
          </el-form-item>
          <div v-for="(name, index) in h5HotSearchListJob" :key="index">
            <el-form-item label="标题">
              <el-input v-model="h5HotSearchListJob[index]" placeholder="名称" class="title" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="deleteH5HotSearchListJob(index)">删除</el-button>
              <el-button type="primary" @click="upH5HotSearchListJob(index)">上移</el-button>
              <el-button type="warning" @click="downH5HotSearchListJob(index)">下移</el-button>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit('h5HotSearchListJob')">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { getOperationConfig, setOperationConfig } from '/@/api/system'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'systemOperationSetting',
  setup() {
    const state = reactive({
      loading: false,
      activeName: 'first',
      pcHomeSubNavColumnSpecial: {
        moreLink: '',
        list: []
      },
      pcHomeSubNavColumnCIA: {
        moreLink: '',
        list: []
      },
      pcHomeSubNavColumnServiceStation: {
        moreLink: '',
        list: []
      },
      pcHomeHotTopic: [],
      pcHomeNavTalentTag: [],
      pcSecondColumnReserved: { title: '', url: '' },
      pcHomeActivity: [],
      h5SpecialSubject: [],
      h5HotSearchListJob: []
    })

    const onSubmit = (key) => {
      switch (key) {
        case 'homeSubNavColumn':
          // 发起三个请求一起提交
          Promise.all([
            setOperationConfig({
              key: 'pc_home_sub_nav_column_special',
              value: state.pcHomeSubNavColumnSpecial
            }),
            setOperationConfig({
              key: 'pc_home_sub_nav_column_cia',
              value: state.pcHomeSubNavColumnCIA
            }),
            setOperationConfig({
              key: 'pc_home_sub_nav_column_service_station',
              value: state.pcHomeSubNavColumnServiceStation
            })
          ]).then((res) => {
            console.log(res)
          })

          break
        case 'homeHotTopic':
          setOperationConfig({
            key: 'pc_home_hot_topic',
            value: state.pcHomeHotTopic
          }).then((res) => {
            console.log(res)
          })
          break
        case 'homeNavTalentTag':
          setOperationConfig({
            key: 'pc_home_nav_talent_tag',
            value: state.pcHomeNavTalentTag
          }).then((res) => {
            console.log(res)
          })
          break
        case 'secondColumnReserved':
          setOperationConfig({
            key: 'pc_second_column_reserved',
            value: state.pcSecondColumnReserved
          }).then((res) => {
            console.log(res)
          })
          break
        case 'homeActivity':
          setOperationConfig({
            key: 'pc_home_activity',
            value: state.pcHomeActivity
          }).then((res) => {
            console.log(res)
          })
          break
        case 'h5SpecialSubject':
          setOperationConfig({
            key: 'h5_special_subject',
            value: state.h5SpecialSubject
          }).then((res) => {
            console.log(res)
          })
          break
        case 'h5HotSearchListJob':
          setOperationConfig({
            key: 'h5_hot_search_list_job',
            value: state.h5HotSearchListJob
          }).then((res) => {
            console.log(res)
          })
          break
        default:
          break
      }
    }

    const addPcHomeSubNavColumnSpecial = () => {
      state.pcHomeSubNavColumnSpecial.list.push({
        name: '',
        url: ''
      })
    }

    const addPcHomeHotTopic = () => {
      state.pcHomeHotTopic.push({
        title: '',
        url: ''
      })
    }

    const addPcHomeNavTalentTag = () => {
      state.pcHomeNavTalentTag.push({
        title: '',
        url: ''
      })
    }

    const addPcHomeActivity = () => {
      state.pcHomeActivity.push({
        title: '',
        url: '',
        time: '',
        isRun: 0
      })
    }

    const addPcHomeSubNavColumnCIA = () => {
      state.pcHomeSubNavColumnCIA.list.push({
        name: '',
        url: ''
      })
    }

    const addPcHomeSubNavColumnServiceStation = () => {
      state.pcHomeSubNavColumnServiceStation.list.push({
        name: '',
        url: ''
      })
    }

    const addH5SpecialSubject = () => {
      state.h5SpecialSubject.push({
        name: '',
        url: ''
      })
    }

    const addH5HotSearchListJob = () => {
      state.h5HotSearchListJob.push('')
    }

    // 给个confirm
    const deletePcHomeSubNavColumnSpecial = (index) => {
      state.pcHomeSubNavColumnSpecial.list.splice(index, 1)
    }

    const deletePcHomeSubNavColumnCIA = (index) => {
      state.pcHomeSubNavColumnCIA.list.splice(index, 1)
    }

    const deletePcHomeSubNavColumnServiceStation = (index) => {
      state.pcHomeSubNavColumnServiceStation.list.splice(index, 1)
    }

    const deletePcHomeHotTic = (index) => {
      state.pcHomeHotTopic.splice(index, 1)
    }

    const deletePcHomeNavTalentTag = (index) => {
      state.pcHomeNavTalentTag.splice(index, 1)
    }

    const deletePcHomeActivity = (index) => {
      state.pcHomeActivity.splice(index, 1)
    }

    const deleteH5SpecialSubject = (index) => {
      state.h5SpecialSubject.splice(index, 1)
    }

    const deleteH5HotSearchListJob = (index) => {
      state.h5HotSearchListJob.splice(index, 1)
    }

    const upPcHomeSubNavColumnSpecial = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeSubNavColumnSpecial.list[index]
      state.pcHomeSubNavColumnSpecial.list[index] = state.pcHomeSubNavColumnSpecial.list[index - 1]
      state.pcHomeSubNavColumnSpecial.list[index - 1] = temp
    }

    const upPcHomeSubNavColumnCIA = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeSubNavColumnCIA.list[index]
      state.pcHomeSubNavColumnCIA.list[index] = state.pcHomeSubNavColumnCIA.list[index - 1]
      state.pcHomeSubNavColumnCIA.list[index - 1] = temp
    }

    const upPcHomeSubNavColumnServiceStation = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeSubNavColumnServiceStation.list[index]
      state.pcHomeSubNavColumnServiceStation.list[index] =
        state.pcHomeSubNavColumnServiceStation.list[index - 1]
      state.pcHomeSubNavColumnServiceStation.list[index - 1] = temp
    }

    const upPcHomeNavTalentTag = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeNavTalentTag[index]
      state.pcHomeNavTalentTag[index] = state.pcHomeNavTalentTag[index - 1]
      state.pcHomeNavTalentTag[index - 1] = temp
    }

    const upPcHomeActivity = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeActivity[index]
      state.pcHomeActivity[index] = state.pcHomeActivity[index - 1]
      state.pcHomeActivity[index - 1] = temp
    }

    const upPcHomeHotTopic = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.pcHomeHotTopic[index]
      state.pcHomeHotTopic[index] = state.pcHomeHotTopic[index - 1]
      state.pcHomeHotTopic[index - 1] = temp
    }

    const upH5SpecialSubject = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.h5SpecialSubject[index]
      state.h5SpecialSubject[index] = state.h5SpecialSubject[index - 1]
      state.h5SpecialSubject[index - 1] = temp
    }

    const upH5HotSearchListJob = (index) => {
      if (index === 0) {
        return
      }
      const temp = state.h5HotSearchListJob[index]
      state.h5HotSearchListJob[index] = state.h5HotSearchListJob[index - 1]
      state.h5HotSearchListJob[index - 1] = temp
    }

    const upPcHomeHotTic = (index) => {
      if (index === 1) {
        return
      }
      const temp = state.pcHomeHotTopic[index]
      state.pcHomeHotTopic[index] = state.pcHomeHotTopic[index - 1]
      state.pcHomeHotTopic[index - 1] = temp
    }

    const downPcHomeSubNavColumnSpecial = (index) => {
      if (index === state.pcHomeSubNavColumnSpecial.list.length - 1) {
        return
      }
      const temp = state.pcHomeSubNavColumnSpecial.list[index]
      state.pcHomeSubNavColumnSpecial.list[index] = state.pcHomeSubNavColumnSpecial.list[index + 1]
      state.pcHomeSubNavColumnSpecial.list[index + 1] = temp
    }

    const downPcHomeSubNavColumnCIA = (index) => {
      if (index === state.pcHomeSubNavColumnCIA.list.length - 1) {
        return
      }
      const temp = state.pcHomeSubNavColumnCIA.list[index]
      state.pcHomeSubNavColumnCIA.list[index] = state.pcHomeSubNavColumnCIA.list[index + 1]
      state.pcHomeSubNavColumnCIA.list[index + 1] = temp
    }

    const downPcHomeNavTalentTag = (index) => {
      if (index === state.pcHomeNavTalentTag.length - 1) {
        return
      }
      const temp = state.pcHomeNavTalentTag[index]
      state.pcHomeNavTalentTag[index] = state.pcHomeNavTalentTag[index + 1]
      state.pcHomeNavTalentTag[index + 1] = temp
    }

    const downPcHomeSubNavColumnServiceStation = (index) => {
      if (index === state.pcHomeSubNavColumnServiceStation.list.length - 1) {
        return
      }
      const temp = state.pcHomeSubNavColumnServiceStation.list[index]
      state.pcHomeSubNavColumnServiceStation.list[index] =
        state.pcHomeSubNavColumnServiceStation.list[index + 1]
      state.pcHomeSubNavColumnServiceStation.list[index + 1] = temp
    }

    const downPcHomeHotTopic = (index) => {
      if (index === state.pcHomeHotTopic.length - 1) {
        return
      }
      const temp = state.pcHomeHotTopic[index]
      state.pcHomeHotTopic[index] = state.pcHomeHotTopic[index + 1]
      state.pcHomeHotTopic[index + 1] = temp
    }

    const downPcHomeActivity = (index) => {
      if (index === state.pcHomeActivity.length - 1) {
        return
      }
      const temp = state.pcHomeActivity[index]
      state.pcHomeActivity[index] = state.pcHomeActivity[index + 1]
      state.pcHomeActivity[index + 1] = temp
    }

    const downH5SpecialSubject = (index) => {
      if (index === state.h5SpecialSubject.length - 1) {
        return
      }
      const temp = state.h5SpecialSubject[index]
      state.h5SpecialSubject[index] = state.h5SpecialSubject[index + 1]
      state.h5SpecialSubject[index + 1] = temp
    }

    const downH5HotSearchListJob = (index) => {
      if (index === state.h5HotSearchListJob.length - 1) {
        return
      }
      const temp = state.h5HotSearchListJob[index]
      state.h5HotSearchListJob[index] = state.h5HotSearchListJob[index + 1]
      state.h5HotSearchListJob[index + 1] = temp
    }

    const downPcHomeHotTic = (index) => {
      if (index === state.pcHomeHotTopic.length - 1) {
        return
      }
      const temp = state.pcHomeHotTopic[index]
      state.pcHomeHotTopic[index] = state.pcHomeHotTopic[index + 1]
      state.pcHomeHotTopic[index + 1] = temp
    }

    /**
     * 只能上传图片，在这里限制一下
     */
    const beforeImageUpload = (file) => {
      // 检查一下，只能上传图片

      // 判断上传格式*****************
      if (file.type != 'image/jpeg' && file.type != 'image/png' && file.type != 'image/jpg') {
        ElMessage.error('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 5

      // 图片处理成80x60
      // const reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (e) {
      //   const image = new Image();
      //   image.src = e.target.result;
      //   image.onload = function () {
      //     const canvas = document.createElement('canvas');
      //     const ctx = canvas.getContext('2d');
      //     canvas.width = 80;
      //     canvas.height = 60;
      //     ctx.drawImage(image, 0, 0, 80, 60);
      //     const base64 = canvas.toDataURL('image/jpeg', 0.7);
      //   };
      // };

      if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 5MB!')
        return false
      }

      return true
    }

    const handleImageSuccessPcSecondColumnReserved = (res) => {
      // 235x110
      state.pcSecondColumnReserved.imageLink = `${res.data.fullUrl}?imageView2/1/w/235/h/100`
    }
    const handleImageSuccessPcHomeHotTopic = (res) => {
      // 七牛
      state.pcHomeHotTopic[0].img = `${res.data.fullUrl}?imageView2/1/w/80/h/60`
    }

    const init = () => {
      // 先出页面loading
      state.loading = true

      getOperationConfig().then((res) => {
        // 有好几个部分需要重新构建
        state.pcHomeSubNavColumnSpecial.moreLink = res.pcHomeSubNavColumnSpecial.moreLink
        state.pcHomeSubNavColumnCIA.moreLink = res.pcHomeSubNavColumnCIA.moreLink
        state.pcHomeSubNavColumnServiceStation.moreLink =
          res.pcHomeSubNavColumnServiceStation.moreLink ?? ''
        state.pcHomeSubNavColumnSpecial.list = res.pcHomeSubNavColumnSpecial.list
        state.pcHomeSubNavColumnCIA.list = res.pcHomeSubNavColumnCIA.list
        state.pcHomeSubNavColumnServiceStation.list = res.pcHomeSubNavColumnServiceStation.list
        state.pcHomeHotTopic = res.pcHomeHotTopic
        state.pcHomeNavTalentTag = res.pcHomeNavTalentTag
        state.pcSecondColumnReserved = res.pcSecondColumnReserved
        state.pcHomeActivity = res.pcHomeActivity
        state.h5SpecialSubject = res.h5SpecialSubject
        state.h5HotSearchListJob = res.h5HotSearchListJob

        state.loading = false
      })
    }

    onMounted(() => {
      init()
    })

    return {
      ...toRefs(state),
      onSubmit,
      addPcHomeSubNavColumnSpecial,
      addPcHomeHotTopic,
      deletePcHomeSubNavColumnSpecial,
      deletePcHomeSubNavColumnCIA,
      deletePcHomeSubNavColumnServiceStation,
      deletePcHomeHotTic,
      beforeImageUpload,
      handleImageSuccessPcHomeHotTopic,
      handleImageSuccessPcSecondColumnReserved,
      addPcHomeNavTalentTag,
      deletePcHomeNavTalentTag,
      addPcHomeActivity,
      deletePcHomeActivity,
      deleteH5SpecialSubject,
      addH5SpecialSubject,
      addPcHomeSubNavColumnCIA,
      addPcHomeSubNavColumnServiceStation,
      addH5HotSearchListJob,
      deleteH5HotSearchListJob,
      upPcHomeHotTic,
      downPcHomeHotTic,
      upPcHomeSubNavColumnSpecial,
      downPcHomeSubNavColumnSpecial,
      upPcHomeSubNavColumnCIA,
      downPcHomeSubNavColumnCIA,
      upPcHomeSubNavColumnServiceStation,
      downPcHomeSubNavColumnServiceStation,
      upPcHomeHotTopic,
      downPcHomeHotTopic,
      upPcHomeNavTalentTag,
      downPcHomeNavTalentTag,
      upPcHomeActivity,
      downPcHomeActivity,
      upH5SpecialSubject,
      downH5SpecialSubject,
      upH5HotSearchListJob,
      downH5HotSearchListJob
    }
  }
})
</script>

<style lang="scss" scoped>
.title {
  width: 450px;
}

.url {
  width: 500px;
}
</style>
