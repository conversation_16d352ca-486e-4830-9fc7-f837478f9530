<template>
  <div class="main">
    <div class="box">
      <!-- 搜索表单 -->
      <el-form :model="searchForm" label-width="100px" @submit.native.prevent>
        <div class="flex">
          <el-form-item class="span-4" label="关键字" prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              placeholder="输入机器人名称或备注"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </div>
      </el-form>

      <!-- 操作按钮 -->
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="default" @click="resetForm">重置</el-button>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="handleAdd">新增机器人</el-button>
      </el-row>

      <!-- 机器人列表 -->
      <el-table :data="robotList" border size="small" align="center" v-loading="tableLoading">
        <el-table-column prop="addTime" label="创建时间" min-width="160" />
        <el-table-column prop="name" label="机器人名称" min-width="120" />
        <el-table-column prop="token" label="Webhook token" min-width="200" />
        <el-table-column prop="key" label="key" min-width="200" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column label="操作" fixed="right" width="280">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" class="button-margin">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="viewLogs(row)" class="button-margin">
              查看日志
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" class="button-margin">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="paging">
        <Paging
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="searchForm.pageSize"
          @change="handlePageChange"
        />
      </div>

      <!-- 添加/编辑机器人弹窗 -->
      <el-dialog
        :title="dialogType === 'add' ? '新增机器人' : '编辑机器人'"
        v-model="dialogVisible"
        width="500px"
        @close="resetDialog"
      >
        <el-form ref="dialogForm" :model="dialogData" :rules="dialogRules" label-width="100px">
          <el-form-item label="机器人名称" prop="name">
            <el-input v-model="dialogData.name" placeholder="请输入机器人名称" />
          </el-form-item>
          <el-form-item label="Webhook token" prop="token">
            <el-input v-model="dialogData.token" placeholder="请输入Webhook token" />
          </el-form-item>
          <el-form-item label="key" prop="key">
            <el-input v-model="dialogData.key" placeholder="请输入key" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="dialogData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              确 定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 日志查看弹窗 -->
      <el-dialog title="机器人日志" v-model="logDialogVisible" width="900px">
        <el-table :data="logList" border size="small" v-loading="logLoading">
          <el-table-column
            prop="msgContent"
            label="消息内容"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column prop="status" label="发送状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === '1' ? 'success' : 'danger'">
                {{ row.status === '1' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="response" label="响应详情" min-width="300">
            <template #default="{ row }">
              <div v-if="row.response">
                <el-tooltip
                  effect="dark"
                  placement="top"
                  :content="formatResponse(row.response)"
                  :hide-after="0"
                >
                  <div class="response-preview">{{ formatResponse(row.response) }}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="addTime" label="发送时间" width="160" />
        </el-table>
        <div class="paging">
          <div class="pagination-simple">
            <el-button
              :disabled="logPagination.page <= 1"
              @click="handleLogPageChange(logPagination.page - 1)"
            >
              上一页
            </el-button>
            <span class="page-info">第 {{ logPagination.page }} 页</span>
            <el-button
              :disabled="logList.length < 100"
              @click="handleLogPageChange(logPagination.page + 1)"
            >
              下一页
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 定义组件名称
defineOptions({
  name: 'wxWorkRobot',
  inheritAttrs: false
})

import { onMounted, ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Paging from '/@/components/base/paging.vue'
import { getRobotList, addRobot, deleteRobot, getRobotLogList } from '/@/api/wxWorkRobot'

// 搜索表单数据
const searchForm = reactive({
  keyword: '',
  page: 1,
  pageSize: 20
})

// 表格数据
const robotList = ref([])
const tableLoading = ref(false)
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 20
})

// 弹窗数据
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const dialogForm = ref()

const dialogData = reactive({
  id: '',
  name: '',
  token: '',
  key: '',
  remark: ''
})

// 表单验证规则
const dialogRules = {
  name: [{ required: true, message: '请输入机器人名称', trigger: 'blur' }],
  token: [{ required: true, message: '请输入Webhook token', trigger: 'blur' }],
  key: [{ required: true, message: '请输入key', trigger: 'blur' }]
}

// 日志相关数据
const logDialogVisible = ref(false)
const logList = ref([])
const logLoading = ref(false)
const logPagination = reactive({
  page: 1
})
const currentRobotId = ref('')

// 方法定义
const handleSearch = () => {
  pagination.page = 1
  searchForm.page = 1
  getTableList()
}

const resetForm = () => {
  searchForm.keyword = ''
  pagination.page = 1
  searchForm.page = 1
  getTableList()
}

const getTableList = async () => {
  try {
    tableLoading.value = true
    console.log(searchForm)
    const res = await getRobotList({
      keyword: searchForm.keyword,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    robotList.value = res.list || []
    pagination.total = res.page?.count || 0
  } catch (error) {
    console.error('获取机器人列表失败:', error)
  } finally {
    tableLoading.value = false
  }
}

const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  // 使用深拷贝来避免直接引用
  Object.assign(dialogData, JSON.parse(JSON.stringify(row)))
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认删除该机器人吗？', '提示', {
      type: 'warning'
    })
    await deleteRobot(row.id)
    getTableList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除机器人失败:', error)
    }
  }
}

const resetDialog = () => {
  // 重置所有字段
  dialogData.id = ''
  dialogData.name = ''
  dialogData.token = ''
  dialogData.key = ''
  dialogData.remark = ''
  if (dialogForm.value) {
    dialogForm.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!dialogForm.value) return

  try {
    await dialogForm.value.validate()
    submitLoading.value = true

    const submitData = { ...dialogData }
    // 新增时删除 id 字段
    if (dialogType.value === 'add') {
      delete submitData.id
    }

    await addRobot(submitData)
    ElMessage.success(dialogType.value === 'add' ? '添加成功' : '修改成功')
    dialogVisible.value = false
    getTableList()
  } catch (error: any) {
    console.error('保存机器人失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handlePageChange = (page: any) => {
  pagination.page = page.page
  getTableList()
}

// 查看日志相关方法
const viewLogs = async (row: any) => {
  logDialogVisible.value = true
  await getRobotLogs(row)
}

const formatResponse = (response: string) => {
  try {
    const parsed = JSON.parse(response)
    // 格式化错误信息
    if (parsed.errcode !== 0) {
      return `错误码: ${parsed.errcode}, 错误信息: ${parsed.errmsg}`
    }
    // 成功状态
    return '发送成功'
  } catch (e) {
    return response || '无响应数据'
  }
}

const getRobotLogs = async (item: any) => {
  try {
    logLoading.value = true
    const res = await getRobotLogList({
      token: item.token,
      id: item.id,
      page: logPagination.page,
      pageSize: 100
    })
    logList.value = res.list || []
  } catch (error) {
    ElMessage.error('获取日志失败')
  } finally {
    logLoading.value = false
  }
}

const handleLogPageChange = (page: number) => {
  logPagination.page = page
  getRobotLogs({ token: currentRobotId.value })
}

// 初始化
onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}

.button-margin {
  margin: 0 5px;
}

.paging {
  margin-top: 20px;
}

.response-preview {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: #666;
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.pagination-simple {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.page-info {
  font-size: 14px;
  color: #666;
}
</style>
