<template>
  <div class="main">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="学科列表" name="first">
        <el-table
          :data="majorList"
          row-key="k"
          style="width: 100%; margin-bottom: 20px"
          border
          v-loading="majorTableLoading"
        >
          <el-table-column prop="v" label="名称" sortable />
          <el-table-column align="center" prop="code" label="code" sortable />
          <el-table-column align="center" prop="level" label="等级" sortable />
          <el-table-column align="center" prop="aiTextTxt" label="字典" sortable />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="识别字典" name="second">
        <el-form :inline="true" label-width="100px" :model="dictionarySearchForm">
          <el-form-item label="名字">
            <el-input
              v-model="dictionarySearchForm.name"
              clearable
              @keyup.enter.native="dictionarySearch"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="dictionarySearch">搜索</el-button>
            <!-- <el-button  @click="resetSearch">重置</el-button> -->
            <el-button type="primary" @click="showAddDictionaryForm">添加</el-button>
            <el-button type="primary" @click="downloadExcel">下载</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="dictionaryList"
          border
          v-loading="dictionaryTableLoading"
          type="border-card"
        >
          <el-table-column align="center" prop="name" label="字典" />
          <el-table-column align="center" prop="synonym" label="同义词" />
          <el-table-column align="center" prop="majorName" label="识别学科" />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-button type="primary" size="small" @click="showAddDictionaryForm(scope.row.id)"
                >编辑</el-button
              >
              <!-- 删除 -->
              <el-button type="danger" size="small" @click="deleteDictionary(scope.row.id)"
                >删除</el-button
              >
              <!-- 修改同义词 -->
              <el-button type="info" size="small" @click="showAddSynonymForm(scope.row)"
                >修改同义词</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <Paging :total="dictionaryListPages.total" @change="changePageDictionary"></Paging>
        </div>
        <el-dialog v-model="dialogVisibleDictionary" title="需求专业" fullscreen>
          <el-input v-model="dictionaryAddForm.name"></el-input>
          <majorLevel2
            v-model="dictionaryAddForm.checked"
            v-model:select="dictionaryAddForm.select"
          ></majorLevel2>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisibleDictionary = false">取消</el-button>
              <el-button type="primary" @click="handleDictionaryConfirm">确定</el-button>
            </span>
          </template>
        </el-dialog>
        <el-dialog v-model="dialogVisibleSynonym" title="修改同义词(回车添加)">
          <p>字典：{{ synonymForm.name }}</p>
          <br />
          <p>识别学科：{{ synonymForm.majorName }}</p>
          <br />
          <el-input-tag v-model="synonymForm.synonym" clearable placeholder="请输入同义词" />
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisibleSynonym = false">取消</el-button>
              <el-button type="primary" @click="handleSynonymConfirm">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane label="识别日志" name="three">
        <el-dialog v-model="testVisible" title="测试识别" width="50%" center>
          <el-form-item label="识别文本">
            <el-input v-model="testForm.text" type="textarea" />
          </el-form-item>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="testVisible = false">取消</el-button>
              <el-button type="primary" @click="onSubmitTest">识别</el-button>
            </span>
          </template>
        </el-dialog>
        <el-form :inline="true" :model="logSearchForm">
          <el-form-item label="识别文本">
            <el-input
              v-model="logSearchForm.text"
              clearable
              @keyup.enter.native="logSearch"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="logSearch">搜索</el-button>
            <el-button type="primary" @click="testSignle">测试</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="logList" border v-loading="logTableLoading" type="border-card">
          <el-table-column align="center" prop="addTime" label="时间" />
          <el-table-column align="center" prop="text" label="识别文本" />
          <el-table-column align="center" label="击中字典">
            <template #default="scope">
              <el-tag v-for="item in scope.row.dictionaryNames">{{ item }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="majorNames" label="识别学科" />
          <el-table-column align="center" prop="typeTxt" label="类型" />
        </el-table>
        <div class="paging">
          <Paging :total="logListPages.total" @change="changePageLog"></Paging>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import {
  getList,
  getAiTextList,
  getAiTextDetail,
  addAiTex,
  getAiLogList,
  aiRecognition,
  editSynonym,
  deleteAiText
} from '/@/api/major'
import Paging from '/@/components/base/paging.vue'
import majorLevel2 from '/@select/majorLevel2.vue'

export default defineComponent({
  name: 'systemMajorList',
  components: { Paging, majorLevel2 },
  setup() {
    const state = reactive({
      majorList: [],
      defaultProps: {
        children: 'children',
        label: 'v'
      },
      activeName: 'first',
      dictionaryAddForm: { checked: [], select: [], name: '', id: 0 },
      dictionaryList: [],
      dictionarySearchForm: { name: '', page: 1, pageSize: '', export: 0 },
      dictionaryTableLoading: false,
      dialogVisibleDictionary: false,
      dictionaryListPages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      logList: [],
      logSearchForm: { text: '', page: 1, pageSize: '' },
      testForm: { text: '' },
      logTableLoading: false,
      dialogVisiblelog: false,
      logListPages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      majorTableLoading: false,
      testVisible: false,
      dialogVisibleSynonym: false,
      synonymForm: { synonym: [], id: 0, name: '', majorName: '' }
    })

    // 字典列表搜索
    const dictionarySearch = () => {
      state.dictionaryTableLoading = true
      getAiTextList(state.dictionarySearchForm).then((r) => {
        state.dictionaryList = r.list
        state.dictionaryListPages.size = r.pages.size
        state.dictionaryListPages.total = r.pages.total
        console.log(r)
        state.dictionaryTableLoading = false
      })
    }

    // 字典日志搜索
    const logSearch = () => {
      state.logTableLoading = true
      getAiLogList(state.logSearchForm).then((r) => {
        state.logList = r.list
        state.logListPages.size = r.pages.size
        state.logListPages.total = r.pages.total
        state.logTableLoading = false
      })
    }

    const changePageDictionary = (r: any) => {
      state.dictionarySearchForm.page = r.page
      state.dictionarySearchForm.pageSize = r.limit
      dictionarySearch()
    }

    const changePageLog = (r: any) => {
      state.logSearchForm.page = r.page
      state.logSearchForm.pageSize = r.limit
      logSearch()
    }

    const showAddDictionaryForm = (id: number) => {
      state.dictionaryAddForm = { checked: [], select: [], name: '', id: 0 }
      if (id) {
        getAiTextDetail(id).then((r) => {
          state.dictionaryAddForm.id = id
          const majorIds = r.majorList.map((item) => {
            return item.id
          })
          state.dictionaryAddForm.select = majorIds
          state.dictionaryAddForm.checked = majorIds
          state.dictionaryAddForm.name = r.name
          state.dialogVisibleDictionary = true
        })
      } else {
        state.dialogVisibleDictionary = true
      }
      // console.log(row)
    }

    const handleDictionaryConfirm = () => {
      const data = {
        id: state.dictionaryAddForm.id,
        name: state.dictionaryAddForm.name,
        majorIds: state.dictionaryAddForm.checked.join(',')
      }
      addAiTex(data).then((r) => {
        state.dialogVisibleDictionary = false
        dictionarySearch()
      })
    }
    const init = () => {
      state.majorTableLoading = true
      getList({}).then((res) => {
        state.majorTableLoading = false
        state.majorList = res
      })
      dictionarySearch()
      logSearch()
    }

    const testSignle = () => {
      state.testVisible = true
    }

    const onSubmitTest = () => {
      aiRecognition(state.testForm.text).then((r: any) => {
        state.testVisible = false
        state.testForm.text = ''
        if (r.majorIds.length === 0) {
          // 提示没有匹配的字典
          ElMessage({
            message: '没有匹配的字典',
            type: 'warning'
          })
        }
        logSearch()
      })
    }

    const showAddSynonymForm = (obj: any) => {
      state.synonymForm.id = obj.id
      state.synonymForm.synonym = obj.synonym
      state.synonymForm.name = obj.name
      state.synonymForm.majorName = obj.majorName
      state.dialogVisibleSynonym = true
    }

    const handleSynonymConfirm = () => {
      editSynonym(state.synonymForm).then((r) => {
        state.dialogVisibleSynonym = false
        state.synonymForm.synonym = []
        state.synonymForm.name = ''
        state.synonymForm.majorName = ''

        dictionarySearch()
      })
    }

    const downloadExcel = () => {
      state.dictionaryTableLoading = true
      state.dictionarySearchForm.export = 1
      getAiTextList(state.dictionarySearchForm).then((r) => {
        state.dictionaryTableLoading = false
        state.dictionarySearchForm.export = 0
      })
    }

    const deleteDictionary = (id: number) => {
      ElMessageBox.confirm('确定删除该字典吗？').then(() => {
        deleteAiText(id).then((r) => {
          dictionarySearch()
        })
      })
    }

    onMounted(() => {
      init()
    })

    return {
      ...toRefs(state),
      dictionarySearch,
      changePageDictionary,
      showAddDictionaryForm,
      handleDictionaryConfirm,
      logSearch,
      changePageLog,
      testSignle,
      onSubmitTest,
      showAddSynonymForm,
      handleSynonymConfirm,
      downloadExcel,
      deleteDictionary
    }
  }
})
</script>

<style lang="scss" scoped>
.main {
  border-top: 20px;
}
</style>
