import mitt from 'mitt'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import VueGridLayout from 'vue-grid-layout'
import screenShort from 'vue-web-screen-shot'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import JsonViewer from 'vue-json-viewer'
import App from './App.vue'
import router from './router'
import { key, store } from './store'
import '/@/theme/index.scss'
import { globalComponentSize } from '/@/utils/componentSize'
import { directive } from '/@/utils/directive'

const app = createApp(App)
app
  .use(router)
  .use(store, key)
  .use(ElementPlus, { locale: zhCn, size: globalComponentSize })
  .use(screenShort, { enableWebRtc: false })
  .use(VueGridLayout)
  .use(JsonViewer)
  .mount('#app')

app.config.globalProperties.mittBus = mitt()

directive(app)
