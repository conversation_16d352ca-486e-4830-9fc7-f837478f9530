#### 🚧 安装 cnpm、yarn

- 复制代码(桌面 cmd 运行) `npm install -g cnpm --registry=https://registry.npm.taobao.org`
- 复制代码(桌面 cmd 运行) `npm install -g yarn`

#### ⚡ 使用说明

# 安装依赖

cnpm install

# 运行项目

cnpm run dev

# 打包发布

cnpm run build

```

#### 🍉 git 命令

- 在本地新建一个分支：`git branch newBranch`
- 切换到你的新分支：`git checkout newBranch`
- 将新分支发布在 github、gitee 上：`git push origin newBranch`
- 在本地删除一个分支：`git branch -d newBranch`
- 在 github 远程端删除一个分支：`git push origin :newBranch (分支名前的冒号代表删除)`
- 注意删除远程分支后，如果有对应的本地分支，本地分支并不会同步删除！
```
