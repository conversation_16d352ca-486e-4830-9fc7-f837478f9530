#!/bin/zsh

currentDirectory="${PWD}"

backupDirectory="${PWD}/backup"
buildDirectory="${PWD}/backup/build"

if [ ! -d "${backupDirectory}" ];then
  mkdir -p "${backupDirectory}"
fi

if [ -d "${buildDirectory}" ];then
  rm -rf "${buildDirectory}"
fi

if [ "$?" = "0" ];then
  rsync -a --exclude={".git","backup"} ./ "${buildDirectory}"
fi

if [ "$?" = "0" ];then
  cd "${buildDirectory}"

  rm -rf node_modules package-lock.json

  npm config set registry https://registry.npmmirror.com/
  npm cache clean --force
  npm install
  npm run build
fi

if [ "$?" = "0" ];then
  if [ -d "${currentDirectory}/dist" ];then
    datetime=$(date +%s)

    mv "${currentDirectory}/dist" "${backupDirectory}/${datetime}"
  fi

  mv "${buildDirectory}/dist" "${currentDirectory}"
fi

if [ "$?" = "0" ];then
  echo -e "\e[32mServer built successfully!\e[0m"
else
  echo -e "\e[31mServer build failure!\e[0m"
fi
