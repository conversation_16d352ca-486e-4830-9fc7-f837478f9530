# 特殊需求配置系统 API 接口文档（最终版）

## 📋 接口概述

**基础URL**: `/admin/special-need/`
**返回格式**: 统一使用 `{msg, result, data}` 格式

- `result`: 1=成功, 0=失败
- `msg`: 提示信息
- `data`: 返回数据

---

## 🔧 字段替换配置接口

### 1. 获取配置列表

```
GET /admin/special-need/config-index
```

**请求参数**:

```javascript
{
  "page": 1,           // 页码
  "pageSize": 20,      // 每页数量
  "name": "配置名称",   // 可选，模糊搜索
  "type": "job",       // 可选，类型筛选
  "targetId": 123456,  // 可选，目标ID
  "status": 1          // 可选，状态筛选
}
```

**响应示例**:

```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "电子科技大学专业修改",
        "type": "job",
        "typeText": "职位信息",
        "targetId": 301480,
        "targetInfo": "职位ID: 301480",
        "fieldName": "major",
        "fieldText": "专业要求",
        "fieldValue": "马克思主义理论、心理学或学校其他学科专业背景",
        "platform": "ALL",
        "platformText": "全平台",
        "status": 1,
        "statusText": "启用",
        "activeStatusText": "生效中",
        "addTime": "2024-01-01 10:00:00"
      }
    ],
    "pages": {
      "total": 50,
      "limit": 20,
      "page": 1
    }
  }
}
```

### 2. 创建配置

```
POST /admin/special-need/config-create
```

**请求参数**:

```javascript
{
  "SpecialNeedConfig": {
    "name": "配置名称",
    "type": "job",                    // job|announcement|company
    "targetId": 301480,
    "fieldName": "major",             // 字段名称
    "fieldValue": "新的字段值",
    "platform": "ALL",               // ALL|PC|H5|MINI
    "status": 1,
    "startTime": "2024-01-01 00:00:00",
    "endTime": "2024-12-31 23:59:59",
    "remark": "备注信息"
  }
}
```

**响应示例**:

```javascript
{
  "msg": "配置创建成功",
  "result": 1,
  "data": {
    "id": 123
  }
}
```

### 3. 更新配置

```
POST /admin/special-need/config-update?id={id}
```

请求参数同创建配置

### 4. 删除配置

```
POST /admin/special-need/config-delete?id={id}
```

### 5. 批量操作

```
POST /admin/special-need/config-batch-status
```

**请求参数**:

```javascript
{
  "ids": [1, 2, 3],
  "status": 1
}
```

### 6. 获取字段选项

```
GET /admin/special-need/get-field-options?type=job
```

**响应示例**:

```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "education": "学历要求",
    "major": "专业要求",
    "amount": "招聘人数",
    "applyType": "报名方式"
  }
}
```

---

## 🚫 投递限制配置接口

### 1. 获取限制列表

```
GET /admin/special-need/limit-index
```

**响应示例**:

```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "中科院金属所投递限制",
        "companyId": 89616,
        "companyInfo": "单位ID: 89616",
        "limitType": "count",
        "limitTypeText": "次数限制",
        "limitDescription": "15天内最多投递1次",
        "errorMessage": "您已超出该单位简历投递次数限制，无法再行投递",
        "status": 1,
        "statusText": "启用"
      }
    ],
    "pages": {
      "total": 10,
      "limit": 20,
      "page": 1
    }
  }
}
```

### 2. 创建投递限制

```
POST /admin/special-need/limit-create
```

**次数限制示例**:

```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "中科院金属所投递限制",
    "companyId": 89616,
    "limitType": "count",
    "timeLimit": 15,
    "countLimit": 1,
    "errorMessage": "您已超出该单位简历投递次数限制，无法再行投递"
  }
}
```

**条件限制示例**:

```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "海外经历限制",
    "companyId": 60,
    "limitType": "condition",
    "conditionField": "isAbroad",
    "conditionValue": "notAllowed",
    "errorMessage": "对不起，您暂不符合招聘要求，建议尝试其他机会！"
  }
}
```

---

## 🛠️ 工具接口

### 1. 测试配置

```
POST /admin/special-need/test-config
```

**请求参数**:

```javascript
{
  "type": "job",
  "targetId": 301480,
  "platform": "PC"
}
```

### 2. 导出配置

```
GET /admin/special-need/export-config
```

直接下载JSON文件

### 3. 导入配置

```
POST /admin/special-need/import-config
```

上传JSON文件

---

## 📚 数据字典

### 配置类型 (type)

- `job` - 职位信息
- `announcement` - 公告信息
- `company` - 单位信息

### 字段名称 (field_name)

- `major` - 专业要求
- `education` - 学历要求
- `amount` - 招聘人数
- `applyType` - 报名方式

### 平台类型 (platform)

- `ALL` - 全平台
- `PC` - PC端
- `H5` - H5端
- `MINI` - 小程序

### 限制类型 (limit_type)

- `count` - 次数限制
- `condition` - 条件限制

---

## ✅ 使用示例

### 示例1: 修改职位专业要求

```javascript
POST /admin/special-need/config-create
{
  "SpecialNeedConfig": {
    "name": "电子科技大学专业修改",
    "type": "job",
    "targetId": 301480,
    "fieldName": "major",
    "fieldValue": "马克思主义理论、心理学或学校其他学科专业背景",
    "platform": "ALL"
  }
}
```

### 示例2: 设置投递限制

```javascript
POST /admin/special-need/limit-create
{
  "SpecialNeedApplyLimit": {
    "name": "中科院金属所投递限制",
    "companyId": 89616,
    "limitType": "count",
    "timeLimit": 15,
    "countLimit": 1,
    "errorMessage": "您已超出该单位简历投递次数限制，无法再行投递"
  }
}
```

---

## ⚠️ 注意事项

1. **参数格式**: 创建和更新时需要包装在对应的模型名称下
2. **返回格式**: 统一使用 `{msg, result, data}` 格式
3. **分页格式**: 使用 `{total, limit, page}` 格式
4. **错误处理**: `result=0` 时查看 `msg` 字段获取错误信息
5. **ID准确性**: 确保 target_id 和 company_id 正确
6. **事务处理**: 所有写操作都包含事务处理，确保数据一致性
