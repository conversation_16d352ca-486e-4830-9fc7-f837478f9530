{"name": "new_gaoxiao_admin_pc_vue", "version": "1.0.0", "scripts": {"start": "vite", "test": "cross-env NODE_ENV_PROXY=test vite", "dev": "cross-env NODE_ENV_PROXY=dev vite", "pre": "cross-env NODE_ENV_PROXY=pre vite", "dong": "cross-env NODE_ENV_PROXY=dong vite", "chuan": "cross-env NODE_ENV_PROXY=chuan vite", "gray": "cross-env NODE_ENV_PROXY=gray vite", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/", "develop": "./docs/server_build/develop", "release": "./docs/server_build/release"}, "dependencies": {"@interactjs/core": "^1.10.27", "@interactjs/utils": "^1.10.27", "@popperjs/core": "^2.11.8", "axios": "^1.6.0", "babel-eslint": "^10.1.0", "core-js": "^3.33.3", "countup.js": "^2.3.2", "cropperjs": "^1.5.12", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.0.0", "element-plus": "^2.9.5", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pdfjs-dist": "^2.16.105", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.0", "screenfull": "^6.0.2", "sortable.js": "^0.3.0", "sortablejs": "^1.15.6", "splitpanes": "^3.1.1", "throttle-debounce": "^5.0.0", "vue": "^3.5.13", "vue-clipboard3": "2.0.0", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^9.8.0", "vue-json-viewer": "3", "vue-router": "^4.2.5", "vue-web-screen-shot": "1.3.5", "vue3-print-nb": "0.1.4", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "wangeditor": "4.7.15"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/clipboard": "^2.0.1", "@types/node": "^20.9.0", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-vue": "^4.4.0", "@vue/compiler-sfc": "^3.5.13", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "prettier": "^3.0.3", "sass": "^1.70.0", "sass-loader": "^14.1.0", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^9.3.2", "webpack": "^5.89.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}