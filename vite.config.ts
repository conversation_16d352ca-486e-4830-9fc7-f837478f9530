import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import type { UserConfig } from 'vite'
import viteCompression from 'vite-plugin-compression'
import basicSsl from '@vitejs/plugin-basic-ssl'
import { loadEnv } from './src/utils/viteBuild'

const pathResolve = (dir: string): any => {
  return resolve(__dirname, '.', dir)
}

const { VITE_PORT, VITE_OPEN, VITE_PUBLIC_PATH } = loadEnv()

const targetOptions = {
  test: 'https://test.admin.gcjob.jugaocai.com',
  dev: 'https://dev.admin.gcjob.jugaocai.com',
  pre: 'https://pre.admin.gcjob.jugaocai.com',
  gray: 'https://gray.admin.gcjob.jugaocai.com',
  dong: 'https://admin.gaoxiaojob.dong',
  chuan: 'http://local.admin.gcjob.jugaocai.com',
  release: 'https://admin.gaoxiaojob.com'
}

const { NODE_ENV, NODE_ENV_PROXY = 'release' } = process.env

const base = NODE_ENV === 'production' ? VITE_PUBLIC_PATH : './'

const target = targetOptions[NODE_ENV_PROXY] ?? targetOptions.release

const alias: Record<string, string> = {
  '/@': pathResolve('/src/'),
  '/@select': pathResolve('/src/components/base/select/')

  // for windows path
  // '/@': pathResolve('src/'),
  // '/@select': pathResolve('src/components/base/select/')
}

const rollupOptions = {
  output: {
    manualChunks(id: string) {
      const re = /\/node_modules\/([^\\/]+)\//

      if (re.test(id)) {
        const result = re.exec(id)
        return result ? result[1] : null
      }

      return null
    }
  }
}

const viteConfig: UserConfig = {
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler', // or 'modern',
        silenceDeprecations: ['legacy-js-api']
      }
    }
  },
  plugins: [vue(), viteCompression(), basicSsl()],
  root: process.cwd(),
  resolve: { alias },
  base,
  optimizeDeps: {
    include: [
      'element-plus/es',
      'element-plus/es/locale/lang/zh-cn',
      'element-plus/es/locale/lang/en',
      'element-plus/es/locale/lang/zh-tw'
    ]
  },
  server: {
    host: '0.0.0.0',
    port: VITE_PORT,
    open: VITE_OPEN,
    proxy: {
      '^/api': {
        target,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '^/upload': {
        target,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/upload/, 'upload')
      }
    }
  },
  build: {
    outDir: 'dist',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions
  },
  define: {
    __VUE_I18N_LEGACY_API__: JSON.stringify(false),
    __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
    __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false)
  }
}

export default viteConfig
